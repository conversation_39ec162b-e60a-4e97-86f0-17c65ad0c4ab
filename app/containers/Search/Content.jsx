/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { useEffect, useRef } from 'react';
import { useImmer } from 'use-immer';

import {
  UILoading as Loading,
  UITextSearch as TextSearch,
} from '@xlab-team/ui-components';
import Icon from 'components/common/UIIconXlab';
import AbstractServices from 'services/Abstract';
import {
  WrapperInputSearchColumn,
  ValueSearchColumn,
  LabelSearchColumn,
  WrapperFilterTextSearch,
  WrapperSearchColumn,
  WrapperSuggestion,
} from './styled';
import SuggestionList from './_UI/SuggestionList';
import { getTranslateMessage } from '../Translate/util';
import TRANSLATE_KEY from '../../messages/constant';
import { get, isFunction } from 'lodash';
import { useDeepCompareEffect } from '../../hooks';

const labelSearch = getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'Search');

function Content(props) {
  const listRef = useRef(null);

  const [state, setStateCommon] = useImmer({
    value: '',
    isLoading: false,
    data: [],
    total: 0,
  });

  const setState = params => {
    setStateCommon(draft => {
      Object.keys(params).forEach(key => {
        draft[key] = params[key];
      });
    });
  };

  useDeepCompareEffect(() => {
    if (props.isOpen) {
      setState({ isLoading: true, data: [], value: '' });
      fetchDataSuggestion('');
    }
  }, [props.isOpen, props.config]);

  const onSearch = value => {
    if (listRef?.current) {
      listRef.current.scrollToTop();
    }

    setState({ value });
    fetchDataSuggestion(value);
  };

  const fetchDataSuggestion = async (value, opts = {}) => {
    const { more = false, nextPage } = opts;

    const params = {
      data: {
        ...props.config,
        search: value.trim(),
      },
    };

    if (more && nextPage) {
      params.data.page = nextPage;
    }

    setState({ isLoading: true });

    // prettier-ignore
    let getSuggestionService = () => AbstractServices.tableSuggestion[props.suggestionType](params).then(
      ({ list = [], total } = {}) => ({ data: list, total })
    );

    if (isFunction(props.suggestionService)) {
      getSuggestionService = () => props.suggestionService(params);
    }

    const res = await getSuggestionService();

    let { data = [] } = res || {};

    if (more) {
      data = [...state.data, ...data];
    }

    setState({ isLoading: false, data: data || [], total: res?.total || 0 });
  };

  const handleFetchMore = () => {
    if (!props.infinite) return;

    const limit = get(props, 'config.limit');
    const total = get(state, 'total');

    if (!limit || !total) return;

    const totalPages = Math.ceil(state.total / props.config.limit);
    const currentPage = Math.ceil(state.data.length / props.config.limit);
    const nextPage = currentPage + 1;

    if (nextPage < totalPages) {
      fetchDataSuggestion(state.value, { more: true, nextPage });
    }
  };

  const onGoDetail = value => {
    props.callback('SEARCH_GOTO', value);
  };

  const onAddFilter = () => {
    // console.log(state.value);
    props.callback('SEARCH_ADD_FILTER', state.value);
  };

  // console.log('state', state);

  const onEnter = () => {
    if (state.value.length > 0 && props.isAddFilter) {
      onAddFilter();
    }
  };
  return (
    <WrapperSearchColumn show>
      <WrapperInputSearchColumn
        isGoTo={props.isGoTo}
        data-test="input-search-column"
      >
        <div>
          <TextSearch
            data-testid="search-input-filter"
            classDiv="search-filter"
            focus
            placeholder={props.inputSearchLabel || labelSearch}
            isTimeout
            onChange={onSearch}
            value={state.value}
            // focus
            // use="full-border"
            type="text"
            onEnter={onEnter}
          />
        </div>
      </WrapperInputSearchColumn>
      {state.value.length > 0 && props.isAddFilter && (
        <WrapperFilterTextSearch onClick={onAddFilter}>
          <Icon name="filter" size="20px" />
          <LabelSearchColumn style={{ paddingLeft: '10px' }}>
            {getTranslateMessage(
              TRANSLATE_KEY._INFO_FILTER_BY_NAME,
              `Filter ${props.moduleLabel} by`,
              { module: props.moduleLabel },
            )}{' '}
            <ValueSearchColumn>{state.value}</ValueSearchColumn>
          </LabelSearchColumn>
        </WrapperFilterTextSearch>
      )}
      <WrapperSuggestion isBorder={state.value.length > 0}>
        <Loading
          isLoading={state.isLoading}
          isWhite={state.data.length === 0}
        />

        <SuggestionList
          ref={listRef}
          isGoTo={props.isGoTo}
          data={state.data}
          onClick={onGoDetail}
          onScrollToEnd={handleFetchMore}
          isShowAvatar={props.isShowAvatar}
          labelNodata={props.labelNodata}
          header={props.listSuggestionHeader}
        />
      </WrapperSuggestion>
    </WrapperSearchColumn>
  );
}

export default Content;
