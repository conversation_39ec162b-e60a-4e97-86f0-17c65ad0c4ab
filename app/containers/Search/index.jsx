/* eslint-disable react/prop-types */
import React from 'react';
import { connect } from 'react-redux';

import {
  usePopupState,
  bindTrigger,
  bindMenu,
} from 'material-ui-popup-state/hooks';
import { UIIconButton } from '@xlab-team/ui-components';

import { PopoverWrapper } from './styled';
import Content from './Content';
import { updateValue } from '../../redux/actions';
import { getTranslateMessage } from '../Translate/util';
import TRANSLATE_KEY from '../../messages/constant';

const labelSearch = getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'SEARCH');

function Search(props) {
  const { PopoverComponent } = props;

  const popupStateSearch = usePopupState({
    variant: 'popover',
    popupId: 'search-popup-popover',
  });

  const callback = (type, value) => {
    popupStateSearch.close();
    const data = {
      type,
      data: value,
      callbackPrefix: props.moduleConfig.key,
    };

    if (props.callback) {
      props.callback(type, value);
    }

    props.callbackSearch(data);
  };

  const horizontal = props.use === 'detail' ? 'left' : 'center';

  const renderDropdownButton = () => {
    if (typeof props.dropdownButton === 'function') {
      return props.dropdownButton({
        bindTrigger: () => bindTrigger(popupStateSearch),
      });
    }

    return (
      <UIIconButton
        data-test="search"
        className="action-item"
        variant="contained"
        {...bindTrigger(popupStateSearch)}
        iconName="search"
        iconColor={props.use === 'detail' ? 'blue' : ''}
        size="24px"
        isVertical
        color={props.color}
        disabled={props.disabled}
      >
        {props.use === 'detail' ? null : labelSearch}
      </UIIconButton>
    );
  };

  return (
    <>
      {renderDropdownButton()}

      {!props.disabled && (
        <PopoverComponent
          {...bindMenu(popupStateSearch)}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          {...props.popoverProps}
        >
          <Content
            isGoTo={props.isGoTo}
            // isGoto={props.isGoto}
            isAddFilter={props.isAddFilter}
            isOpen={popupStateSearch.isOpen}
            isShowAvatar={props.isShowAvatar}
            suggestionType={props.suggestionType}
            config={props.config}
            callback={callback}
            moduleLabel={props.moduleLabel}
            inputSearchLabel={props.inputSearchLabel}
            infinite={props.infinite}
            labelNodata={props.labelNodata}
            suggestionService={props.suggestionService}
            listSuggestionHeader={props.listSuggestionHeader}
          />
        </PopoverComponent>
      )}
    </>
  );
}

function mapDispatchToProps(dispatch, props) {
  const { moduleConfig = {} } = props;
  const version = moduleConfig.key;
  return {
    callbackSearch: params => {
      dispatch(updateValue(`${version}@@TO_COMMON_FILTER_SEARCH`, params));
    },
  };
}

Search.defaultProps = {
  isGoTo: true,
  suggestionType: 'suggestion',
  config: {},
  moduleConfig: {},
  // isGoto: true,
  isAddFilter: true,
  disabled: false,
  PopoverComponent: PopoverWrapper,
  dropdownButton: null,
  inputSearchLabel: undefined,
  popoverProps: {},
};

export default connect(
  null,
  mapDispatchToProps,
)(Search);
