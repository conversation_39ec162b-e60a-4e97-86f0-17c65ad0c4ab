/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import Box from 'components/Templates/LayoutContent/Box';
import styled from 'styled-components';
import { Grid } from '@material-ui/core';
import InputPreview from '../../../../components/Atoms/InputPreview';
import { useGetObjects } from '../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/hooks/useGetObjects';
import { isEqualObject } from '../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/helper';
import { OBJECT_TYPE } from '../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/constant';
import { SEGMENT_STATUS } from '../../../../components/common/constant';
import { useActiveObj } from '../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/hooks/useActiveObj';
import { ERROR_CODE } from '../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/config';
import { getModelLink } from '../../../../modules/Dashboard/Profile/PredictiveModel/Detail/utils';

const FromModelName = ({ data, isViewMode = false }) => {
  const WrapTextLable = styled.span`
    color: #000000;
    font-size: 12px;
  `;

  const WrapTextValue = styled.span`
    font-weight: 500;
    cursor: pointer;
    color: #378bc9;
    font-size: 12px;
    white-space: nowrap;
    font-weight: 500;
    max-width: 600px;
    display: block;
    text-overflow: hidden;
    overflow: hidden;
    text-overflow: ellipsis;
  `;

  const [isExistModel, setIsExistModel] = useState(true);

  const { allObjects } = useGetObjects();
  const { addErrors } = useActiveObj();

  useEffect(() => {
    if (isViewMode) {
      const { id } = data;
      const model = allObjects.find(obj =>
        isEqualObject(obj, {
          type: OBJECT_TYPE.predictiveModel,
          modelId: id,
        }),
      );

      if (
        model &&
        model.settings &&
        model.settings.status === SEGMENT_STATUS.REMOVE
      ) {
        setIsExistModel(false);
        addErrors([{ code: ERROR_CODE.NOT_AVAILABLE_OR_LINKED }]);
      }
    }
  }, []);

  const handleClick = () => {
    const modelLink = getModelLink({ modelId: data.id });

    window.open(modelLink, '_blank');
  };
  return (
    <Box boxShadow={false} isBorderTop>
      <Grid container style={{ padding: '20px 15px' }}>
        <Grid
          item
          xs={2}
          style={{
            flexBasis: 'unset',
            width: '150px',
            maxWidth: 'none',
            paddingTop: isViewMode ? '0.5rem' : '',
          }}
        >
          <WrapTextLable>From model name</WrapTextLable>
        </Grid>

        <Grid item xs={10}>
          <InputPreview
            color={!isExistModel && 'red'}
            type="input"
            value={data.name}
            isViewMode={isViewMode}
          >
            <WrapTextValue onClick={handleClick}>{data.name}</WrapTextValue>
          </InputPreview>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FromModelName;
