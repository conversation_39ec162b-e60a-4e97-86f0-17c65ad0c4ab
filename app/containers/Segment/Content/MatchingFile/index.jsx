/* eslint-disable indent */
/* eslint-disable no-param-reassign  */
import { Grid } from '@material-ui/core';
import parse from 'html-react-parser';
import UIUploadFile from 'components/common/UIUploadFile';
import Box from 'components/Templates/LayoutContent/Box';
import NoFile from 'modules/Dashboard/Settings/Uploads/BO/UploadsV2/ContentV0/UIMapping/NoFile';
import PropTypes from 'prop-types';
import { Disabled } from 'components/Atoms/Disabled';
import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import SegmentServices from 'services/Segment';
import BusinessObject from 'services/BusinessObject';
import { useImmer } from 'use-immer';
import styled from 'styled-components';
import UISelect from 'components/form/UISelectCondition';
import { StyledDivivder } from '../../../../modules/Dashboard/Settings/Uploads/BO/Uploads/ContentV0/styles';
import { init, reset, updateValue } from '../../../../redux/actions';
import { safeParse } from '../../../../utils/web/utils';
import {
  makeSelectDroppedFiles,
  makeSelectMainCreateSegment,
  makeSelectMainMatchingFile,
} from './selectors';
import Settings from './Settings';
import { translate, translations } from '@antscorp/antsomi-locales';
import { getTranslateMessage } from '../../../Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import {
  MAP_UPDATE_METHODS,
  UPDATE_METHODS_COLLECTION,
  UPDATE_METHODS_OPTION,
  UPDATE_METHODS_SEGMENT,
} from './Settings/constants';
import {
  DropdownButnComponent,
  WrapperLabelOption,
  WrapperSelectMethod,
  WrapperSettings,
} from './Settings/styled';
import { FileList } from '../../../../components/common/UIUploadFile/FileList';
import { StyledP } from '../../../../components/common/UIUploadFile/styled';
import InputPreview from '../../../../components/Atoms/InputPreview';
import { DividerDot } from 'containers/Segment/Content/Condition/_UI/styled';
// hooks
import { useActiveObj } from '../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/hooks/useActiveObj';
import { ERROR_CODE } from '../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/config';
export const DelimiterType = styled.p`
  /* margin-right: 90px; */
`;
const WrapperFileList = styled.div`
  text-align: left;
`;

const DividerDash = styled.div`
  height: 1px;
  width: 100%;
  border-top: 1px dashed #ced3d9;
  margin-bottom: 13px;
`;

const WrapperLableUpload = styled.div`
  font-size: 12px;
  color: #000;
  font-weight: bold;
`;

export const WrapperSelectDisable = styled.div`
  background-color: #f5f5f5;
`;

const MAP_TITLE = {
  lanelComma: getTranslateMessage(TRANSLATE_KEY._OPTION_COMMA, 'Comma'),
  lanelTab: getTranslateMessage(TRANSLATE_KEY._OPTION_TAB, 'Tab'),
  titleDelimiter: getTranslateMessage(
    TRANSLATE_KEY._TITL_DELIMITER_TYPE,
    'Delimiter type',
  ),
  supportFormat: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_UPLOAD_FILE,
    'Support file format .csv .xls .xlsx <br />Delimiter of file format CSV must be a tab',
  ),
  fileNotExist: getTranslateMessage(
    TRANSLATE_KEY._NOTI_FILE_NOT_EXIST,
    'File does not exist',
  ),
};
const MatchingFile = ({
  itemTypeId,
  droppedFiles,
  disabled,
  main,
  moduleConfig,
  handleDrop,
  condition,
  getInfoFile,
  onValidate,
  labels = {},
  use,
  isPlanning,
  mainSegment,
  reset,
  handleChangeMethod,
  computeSchedule = {},
  boxShadow,
  isBorderTop,
  files,
  isDisableUpload,
  isSegmentJourney,
  isViewMode = false,
  viewData,
}) => {
  const [state, setState] = useImmer({
    isLoading: false,
    // droppedFiles: [],
    importId: 0,
    fileHeaders: [],
    extension: safeParse((condition || {}).extension, ''),
    delimiterCode: safeParse(
      (condition || {}).delimiter === '\t' ? 'tab' : 'comma',
      '',
    ),
    optionDeliter: [
      {
        value: 'Comma',
        label: MAP_TITLE.lanelComma,
      },
      {
        value: 'Tab',
        label: MAP_TITLE.lanelTab,
      },
    ],
    deliterType: {
      tab: { value: 'Tab', label: MAP_TITLE.lanelTab },
      comma: { value: 'Comma', label: MAP_TITLE.lanelComma },
    },
    errorMessage: [],
  });

  const { info, saveInfo } = useActiveObj();

  useEffect(
    () => () => {
      reset();
    },
    [],
  );

  useEffect(() => {
    if (
      isViewMode &&
      info &&
      !info.isExist &&
      !info.errors.map(err => err.code).includes(ERROR_CODE.NONE_PERMISSION)
    ) {
      setState(draft => {
        draft.errorMessage = [MAP_TITLE.fileNotExist];
      });
    }
  }, []);

  useEffect(() => {
    // reset method if updateMethod = dynamic and create a segment in journey
    if (computeSchedule.type === 'dynamic' && isSegmentJourney) {
      handleChangeMethod({ method: 'replace' });
    }
  }, [computeSchedule.type, isSegmentJourney]);

  useEffect(() => {
    if (files && files.length) {
      handleDrop({
        droppedFiles: files,
        design: mainSegment.design,
      });
    }
  }, [files]);

  useEffect(() => {
    if (droppedFiles.length > 0) {
      setState(draft => {
        draft.isLoading = true;
      });
      const formData = new FormData();
      formData.append('file', droppedFiles[0]);
      const params = { itemTypeId, data: formData };
      const uploadFile = async () => {
        const res = await SegmentServices.upload(params);
        // const res = await BusinessObject.dataTable.upload(params);
        // console.log({ res });
        setState(draft => {
          draft.isLoading = false;
        });
        if (res.code === 200) {
          const uploadData = safeParse(res.data[0], {});
          const headerFiles = safeParse(uploadData.headerFiles, []);
          setState(draft => {
            draft.fileHeaders = headerFiles.map(item => ({
              ...item,
              value: item.headerCode,
              label: item.headerCode,
            }));
            draft.extension = uploadData.extension;
            draft.delimiterCode = uploadData.delimiterCode;
          });
          getInfoFile(uploadData);
          onValidate();
        } else {
          // const notification = NOTI.fail(res);
          // addNotification(notification);
        }
      };
      uploadFile();
    }
  }, [droppedFiles]);
  // console.log({ mainSegment });
  const callback = (type, data) => {
    // console.log({ type, data });
    switch (type) {
      case 'HANDLE_DROP':
        // setDroppedFiles(data);
        // console.log({ data });
        // console.log({ droppedFiles });
        handleDrop({
          droppedFiles: data,
          design: mainSegment.design,
        });
        // setState(draft => {
        //   draft.droppedFiles = data;
        // });

        break;
      default:
        break;
    }
  };
  return (
    <Box
      className="row width-100 m-x-0 m-top-0"
      boxShadow={boxShadow}
      isBorderTop={isBorderTop}
    >
      {/* <Box className="row width-100 m-x-0 m-top-2" disabled={disabled}> */}
      <Grid container style={{ padding: '0px 15px' }}>
        <Grid
          item
          xs={4}
          style={{
            flexBasis: 'unset',
            width: isPlanning ? '426px' : '280px',
            padding: '0px 20px 0px 0px',
          }}
        >
          <p className="m-top-0">
            <WrapperLableUpload>{labels.titlUploadFile}</WrapperLableUpload>
          </p>
          <Disabled disabled={disabled || isDisableUpload}>
            <div style={{ marginTop: 10, marginRight: 18 }}>
              {!isViewMode && (
                <UIUploadFile
                  callback={callback}
                  isPlanning={isPlanning}
                  // fileName={main.originalFileName}
                />
              )}
              <WrapperFileList>
                {!isPlanning ? (
                  <FileList
                    files={
                      main.originalFileName
                        ? [
                            {
                              name: main.originalFileName,
                              errors: state.errorMessage,
                            },
                          ]
                        : []
                    }
                  />
                ) : (
                  <Grid container spacing={3}>
                    <Grid item xs={6}>
                      <StyledP>{parse(MAP_TITLE.supportFormat)}</StyledP>
                      <DividerDash style={{ marginTop: '13px' }} />
                      <FileList
                        files={
                          main.originalFileName
                            ? [
                                {
                                  name: main.originalFileName,
                                  errors: state.errorMessage,
                                },
                              ]
                            : []
                        }
                      />
                    </Grid>
                    <Grid item xs={6}>
                      {state.extension === 'csv' ? (
                        <>
                          <DividerDash />
                          <Grid
                            item
                            xs={4}
                            style={{ flexBasis: 'unset', maxWidth: '100%' }}
                          >
                            <DelimiterType
                              style={{
                                marginTop: 0,
                                marginBottom: '11px',
                                fontSize: '12px',
                                color: '#666',
                              }}
                            >
                              {MAP_TITLE.titleDelimiter}
                            </DelimiterType>
                          </Grid>
                          <Grid
                            item
                            xs={8}
                            style={{
                              flexBasis: 'unset',
                              maxWidth: '100%',
                              marginBottom: '18px',
                            }}
                          >
                            <WrapperSelectDisable>
                              <UISelect
                                disabled
                                isSearchable={false}
                                className="disabled"
                                use="tree"
                                options={state.optionDeliter}
                                value={state.deliterType[state.delimiterCode]}
                                // onChange={onChangeSelect}
                                labelWidth="242px"
                                required
                              />
                            </WrapperSelectDisable>
                          </Grid>
                        </>
                      ) : null}
                      {/* chỉ display khi update và chọn file mới hoặc khi update segment từng update (có thể dùng design) */}
                      {/* {((main.method === 'create' && droppedFiles.length !== 0) ||
                (main.method !== 'create' && main.method !== '') ||
                (mainSegment.design === 'update' && main.method !== 'create')) && ( */}
                      {(mainSegment.design === 'create' &&
                        droppedFiles.length > 0 &&
                        computeSchedule.type === 'dynamic') ||
                      mainSegment.design === 'update' ? (
                        <>
                          {state.extension !== 'csv' && <DividerDash />}
                          <Grid
                            item
                            xs={4}
                            style={{ flexBasis: 'unset', maxWidth: '100%' }}
                          >
                            <p
                              style={{
                                margin: '0px 0 5px',
                                fontSize: '12px',
                                color: '#666',
                              }}
                            >
                              {labels.updateMethod}:
                            </p>
                          </Grid>
                          <Grid
                            item
                            xs={8}
                            style={{ flexBasis: 'unset', maxWidth: '100%' }}
                          >
                            <WrapperSelectMethod>
                              <UISelect
                                width="242px"
                                className="w-50"
                                isSearchable={false}
                                use="tree"
                                options={UPDATE_METHODS_OPTION(
                                  use === 'segment'
                                    ? UPDATE_METHODS_SEGMENT
                                    : UPDATE_METHODS_COLLECTION,
                                )}
                                value={
                                  MAP_UPDATE_METHODS(
                                    use === 'segment'
                                      ? UPDATE_METHODS_SEGMENT
                                      : UPDATE_METHODS_COLLECTION,
                                  )[main.method || 'replace']
                                }
                                DropdownButnComponent={prop => (
                                  <DropdownButnComponent>
                                    {getTranslateMessage(
                                      safeParse(prop.value, {}).translateCode,
                                      safeParse(prop.value, {}).label,
                                    ) ??
                                      translate(
                                        translations._USER_GUIDE_SELECT_A_METHOD,
                                        'Select a method',
                                      )}
                                  </DropdownButnComponent>
                                )}
                                onChange={value => {
                                  handleChangeMethod({ method: value.value });
                                }}
                                // disabled={disabled || computeSchedule.type === 'static'}
                              />
                            </WrapperSelectMethod>
                          </Grid>
                        </>
                      ) : null}
                    </Grid>
                  </Grid>
                )}
              </WrapperFileList>
            </div>
          </Disabled>
          {!isPlanning && (
            <Grid container className="d-flex align-items-center m-top-4">
              {state.extension === 'csv' ? (
                <>
                  <DividerDot
                    nonPosition
                    style={{
                      marginRight: 18,
                      marginBottom: 13,
                    }}
                  />
                  <Grid
                    item
                    xs={4}
                    style={{ flexBasis: 'unset', maxWidth: '100%' }}
                  >
                    <DelimiterType
                      style={{
                        marginTop: 0,
                        marginBottom: '11px',
                        fontSize: '12px',
                        color: '#666',
                      }}
                    >
                      {MAP_TITLE.titleDelimiter}
                    </DelimiterType>
                  </Grid>
                  <Grid
                    item
                    xs={8}
                    style={{
                      flexBasis: 'unset',
                      maxWidth: '100%',
                      marginBottom: '18px',
                    }}
                  >
                    <InputPreview
                      type="input"
                      isViewMode={isViewMode}
                      value={state.deliterType[state.delimiterCode].label}
                    >
                      <WrapperSelectDisable>
                        <UISelect
                          disabled
                          isSearchable={false}
                          className="disabled"
                          use="tree"
                          options={state.optionDeliter}
                          value={state.deliterType[state.delimiterCode]}
                          // onChange={onChangeSelect}
                          labelWidth="242px"
                          required
                        />
                      </WrapperSelectDisable>
                    </InputPreview>
                  </Grid>
                </>
              ) : null}
              {/* chỉ display khi update và chọn file mới hoặc khi update segment từng update (có thể dùng design) */}
              {/* {((main.method === 'create' && droppedFiles.length !== 0) ||
            (main.method !== 'create' && main.method !== '') ||
            (mainSegment.design === 'update' && main.method !== 'create')) && ( */}
              {(mainSegment.design === 'create' &&
                droppedFiles.length > 0 &&
                computeSchedule.type === 'dynamic') ||
              mainSegment.design === 'update' ? (
                <>
                  {state.extension !== 'csv' && <DividerDash />}
                  <Grid
                    item
                    xs={4}
                    style={{ flexBasis: 'unset', maxWidth: '100%' }}
                  >
                    <p
                      style={{
                        margin: '0px 0 5px',
                        fontSize: '12px',
                        color: '#666',
                      }}
                    >
                      {labels.updateMethod}:
                    </p>
                  </Grid>
                  <Grid
                    item
                    xs={8}
                    style={{ flexBasis: 'unset', maxWidth: '100%' }}
                  >
                    <InputPreview
                      type="input"
                      isViewMode={isViewMode}
                      value={
                        MAP_UPDATE_METHODS(
                          use === 'segment'
                            ? UPDATE_METHODS_SEGMENT
                            : UPDATE_METHODS_COLLECTION,
                        )[main.method || 'replace']?.label
                      }
                    >
                      <WrapperSelectMethod>
                        <UISelect
                          width="242px"
                          className="w-50"
                          isSearchable={false}
                          use="tree"
                          options={UPDATE_METHODS_OPTION(
                            use === 'segment'
                              ? UPDATE_METHODS_SEGMENT
                              : UPDATE_METHODS_COLLECTION,
                          )}
                          value={
                            MAP_UPDATE_METHODS(
                              use === 'segment'
                                ? UPDATE_METHODS_SEGMENT
                                : UPDATE_METHODS_COLLECTION,
                            )[main.method || 'replace']
                          }
                          DropdownButnComponent={prop => (
                            <DropdownButnComponent>
                              {getTranslateMessage(
                                safeParse(prop.value, {}).translateCode,
                                safeParse(prop.value, {}).label,
                              ) ??
                                translate(
                                  translations._USER_GUIDE_SELECT_A_METHOD,
                                  'Select a method',
                                )}
                            </DropdownButnComponent>
                          )}
                          onChange={value => {
                            handleChangeMethod({ method: value.value });
                          }}
                          // disabled={disabled || computeSchedule.type === 'static'}
                        />
                      </WrapperSelectMethod>
                    </InputPreview>
                  </Grid>
                </>
              ) : null}
            </Grid>
          )}
        </Grid>
        <Grid
          item
          xs={8}
          className="d-flex"
          style={{ flexBasis: 'unset', maxWidth: 'none', flex: 1 }}
        >
          <StyledDivivder orientation="vertical" flexItem />

          {droppedFiles.length === 0 && main.fileName === '' ? (
            <NoFile
              title={getTranslateMessage(
                TRANSLATE_KEY._,
                'The system will map and update information according to your uploaded file.',
              )}
            />
          ) : (
            // <Settings
            //   isLoading={state.isLoading}
            //   fileHeaders={state.fileHeaders}
            //   droppedFiles={droppedFiles}
            //   moduleConfig={moduleConfig}
            // />
            <Settings
              use={use}
              isLoading={state.isLoading}
              fileHeaders={state.fileHeaders}
              droppedFiles={droppedFiles}
              itemTypeId={itemTypeId}
              moduleConfig={moduleConfig}
              labels={labels}
              isViewMode={isViewMode}
              viewData={viewData}
            />
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

MatchingFile.propTypes = {
  itemTypeId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  droppedFiles: PropTypes.array,
  disabled: PropTypes.bool,
  main: PropTypes.object,
  condition: PropTypes.object,
  moduleConfig: PropTypes.object,
  handleDrop: PropTypes.func,
  onValidate: PropTypes.func,
  getInfoFile: PropTypes.func,
  labels: PropTypes.object,
  use: PropTypes.oneOf(['segment', 'collection']),
  mainSegment: PropTypes.object,
  reset: PropTypes.func,
};

const mapStateToProps = createStructuredSelector({
  // isLoading: makeSelectDashboardLoading(),
  main: makeSelectMainMatchingFile(),
  droppedFiles: makeSelectDroppedFiles(),
  mainSegment: makeSelectMainCreateSegment(),
});

const mapDispatchToProps = (dispatch, props) => {
  const PREFIX = props.moduleConfig.key;
  return {
    // init: params => {
    //   dispatch(init(`${PREFIX}@@MATCHING_FILE`, params));
    // },
    reset: () => dispatch(reset(`${PREFIX}@@RESET_MATCHING_FILES`)),
    handleDrop: data => dispatch(updateValue(`${PREFIX}@@HANDLE_DROP`, data)),
    getInfoFile: data =>
      dispatch(updateValue(`${PREFIX}@@GET_INFO_FILE`, data)),
    onValidate: data => dispatch(updateValue(`${PREFIX}@@VALIDATE`, data)),
    handleChangeMethod: data =>
      dispatch(updateValue(`${PREFIX}@@HANDLE_CHANGE_METHOD`, data)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(MatchingFile);
