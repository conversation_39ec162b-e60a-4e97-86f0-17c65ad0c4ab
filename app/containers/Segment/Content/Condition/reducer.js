/* eslint-disable arrow-body-style */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
// import produce from 'immer';
import { OrderedMap } from 'immutable';

import { generateKey, safeParse } from 'utils/common';
import { deleteRefinesBySourceId } from 'components/common/UIPerformEvent/utils';
import {
  Types,
  MAP_SEGMENT_TYPES,
  PERF_EVENT_OPERATORS,
  MAP_SEMATIC,
  PERF_EVENT_OPERATORS_V2,
  UNIQUE_OPERATORS,
} from './constants';
import {
  initItem,
  initItemValue,
  initItemRefinePropertiesValue,
  initFirtTimeValueDatimeV2,
  parseDataRangePickAPI,
  initTimeRangeV2,
  filterEventAttrSegment,
  getDataInclude,
} from './utils';
import {
  getOperatorByProperty,
  getFirstOperator,
  getFirstSemantic,
} from './utils.conditions';
import { OPERATORS_CODE } from './operators';
import { LensTwoTone } from '@material-ui/icons';
import { format } from 'date-fns';
import dayjs from 'dayjs';

const initialState = OrderedMap({});
// const defaultStateString = JSON.stringify(initialState);

const conditionReducerFor = moduleConfig => {
  const prefix = moduleConfig.key;

  const conditionReducer = (state = initialState, action) => {
    switch (action.type) {
      // ------------ GENERAL ---------------
      // case `${prefix}_${Types.ADD_BLOCK}`: {
      //   const { data } = action.payload;
      //   return state.setIn([generateKey()], OrderedMap({}));
      // }
      case `${prefix}_${Types.ADD_GROUP}`: {
        // scheduleType dùng cho init timeRange (static = Custom, dynamic = Last 14 days)
        const { data, scheduleType } = action.payload;
        const { conditionType, selected } = data;
        // return state.setIn([generateKey()], initItem(conditionType));
        const tmp = initItem(conditionType, scheduleType, {
          v2: true,
          selected,
        });
        // console.log('tmp', conditionType, tmp.toJS());
        return state.setIn([generateKey()], tmp);
        // return state;
      }
      case `${prefix}_${Types.INIT_RULES}`: {
        const { data } = action.payload;
        return data;
      }
      case `${prefix}_${Types.RESET_RULES}`: {
        console.log('RESET_RULES');
        // const { data } = action.payload;
        return initialState;
      }
      case `${prefix}_${Types.CLONE_GROUP}`: {
        const { data } = action.payload;
        const { groupIndex } = data;
        const cloneItem = state.getIn([groupIndex]);
        return state.setIn([generateKey()], cloneItem);
      }
      case `${prefix}_${Types.DELETE_GROUP}`: {
        const { data } = action.payload;
        const { groupIndex } = data;
        return state.deleteIn([groupIndex]);
      }
      case `${prefix}_${Types.ADD_ITEM}`: {
        const { data } = action.payload;
        const { groupIndex, conditionType, selected } = data;
        return state.setIn(
          [groupIndex, generateKey()],
          initItemValue(conditionType, '', { v2: true, selected }),
        );
      }
      case `${prefix}_${Types.DELETE_ITEM}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex } = data;
        const groupDelete = state.getIn([groupIndex]);
        if (groupDelete.size > 1) {
          return state.deleteIn([groupIndex, itemIndex]);
        }
        return state.deleteIn([groupIndex]);
      }
      case `${prefix}_${Types.CHANGE_TYPE}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value } = data;
        return state.setIn([groupIndex, itemIndex, 'conditionType'], value);
      }
      // ------------ END GENERAL ---------------

      // ------------ COMP_PROP ---------------
      case `${prefix}_${Types.COMP_PROP_CHANGE_PROPERTY}`: {
        const { data } = action.payload;
        // console.log('hello every body', data);
        const { groupIndex, itemIndex, value } = data;
        const oldItem = safeParse(
          state.getIn([groupIndex, itemIndex, 'property']),
          null,
        );
        if (oldItem === null || oldItem.value !== value.value) {
          const operators = getOperatorByProperty(value);

          let tempt = state
            .getIn([groupIndex, itemIndex])
            .withMutations(map => {
              map
                .set('name', '')
                .set('value', '')
                .set('valueEnd', '')
                .set('initValue', '')
                .set('property', value)
                .set('error', 0)
                .set('dataType', value.dataType)
                .set('operator', getFirstOperator(operators))
                .set('operators', operators);
            });
          if (data.datimeV2 && value.dataType === 'datetime') {
            const firtSematic = getFirstSemantic(MAP_SEMATIC.map);
            const operatorsWithSematic = getOperatorByProperty(firtSematic);
            tempt = tempt.set('semantics', MAP_SEMATIC.list);
            tempt = tempt.set('semantic', getFirstSemantic(MAP_SEMATIC.map));
            tempt = tempt.set(
              'operator',
              getFirstOperator(operatorsWithSematic),
            );
            tempt = tempt.set('operators', operatorsWithSematic);
            tempt = tempt.set('value', initFirtTimeValueDatimeV2());
          }

          return state.setIn([groupIndex, itemIndex], tempt);
        }
        return state;
      }
      case `${prefix}_${Types.COMP_PROP_CHANGE_OPERATOR}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value } = data;
        const currentRule = state.getIn([groupIndex, itemIndex]);
        const dataType = currentRule.get('dataType');
        let tempt = state.getIn([groupIndex, itemIndex]).withMutations(map => {
          map
            .set('value', '')
            .set('valueEnd', '')
            .set('initValue', '')
            .set('error', 0)
            .set('operator', value);
        });
        if (dataType === 'datetime') {
          if (value.value === OPERATORS_CODE.BETWEEN) {
            tempt = tempt.set(
              'value',
              parseDataRangePickAPI(initTimeRangeV2()),
            );
          } else if (
            value.value === OPERATORS_CODE.EQUALS ||
            value.value === OPERATORS_CODE.NOT_EQUALS ||
            value.value === OPERATORS_CODE.AFTER_DATE ||
            value.value === OPERATORS_CODE.BEFORE_DATE
          ) {
            tempt = tempt.set('value', initFirtTimeValueDatimeV2());
          }
        }
        return state.setIn([groupIndex, itemIndex], tempt);
      }
      case `${prefix}_${Types.COMP_PROP_CHANGE_SEMANTIC}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value } = data;
        const oldItem = safeParse(
          state.getIn([groupIndex, itemIndex, 'property']),
          null,
        );
        if (oldItem === null || oldItem.value !== value.value) {
          const operators = getOperatorByProperty(value);
          const tempt = state
            .getIn([groupIndex, itemIndex])
            .withMutations(map => {
              map
                .set('value', initFirtTimeValueDatimeV2())
                .set('valueEnd', '')
                .set('initValue', '')
                .set('error', 0)
                .set('semantic', value)
                .set('operator', getFirstOperator(operators))
                .set('operators', operators);
            });
          return state.setIn([groupIndex, itemIndex], tempt);
        }
        return state;
      }
      case `${prefix}_${Types.COMP_PROP_CHANGE_VALUE}`: {
        const { data } = action.payload;
        const {
          groupIndex,
          itemIndex,
          value,
          name = 'value',
          label,
          extendValue,
        } = data;
        const currentRule = state.getIn([groupIndex, itemIndex]);
        const operator = currentRule.get('operator');
        let valueTmp = [];
        if (data.datimeV2) {
          if (value.date) {
            valueTmp.push({
              date: value.date,
              dateType: value.option.dateType,
              calculationDate: value.option.calculationDate,
              calculationType: value.option.calculationType,
              value: value.option.value,
              key: 'fromDate',
            });
          } else if (value.timeRange) {
            valueTmp = parseDataRangePickAPI(value.timeRange);
          }
        }
        let tempt = state.getIn([groupIndex, itemIndex]).withMutations(map => {
          map
            .set('error', 0)
            .set(name, data.datimeV2 ? valueTmp : value)
            .set('name', label);
        });
        if (operator.value === OPERATORS_CODE.BETWEEN && data.datimeV2) {
          tempt = tempt.set('valueEnd', valueTmp);
        }
        if (extendValue && extendValue.length > 0) {
          tempt = tempt.set('extendValue', extendValue);
        }
        return state.setIn([groupIndex, itemIndex], tempt);
        // return state.setIn([groupIndex, itemIndex, name], value);
      }

      // ------------ END COMP_PROP ---------------

      // ------------ END PERF_EVENT ---------------

      case `${prefix}_${Types.PERF_EVENT_CHANGE_PROPERTY}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value } = data;
        const oldItem = safeParse(
          state.getIn([groupIndex, itemIndex, 'property']),
          null,
        );
        const computeType = safeParse(
          state.getIn([groupIndex, itemIndex, 'computeType']),
          null,
        );
        if (oldItem === null || oldItem.value !== value.value) {
          // const { eventActionId, eventCategoryId } = value.value;
          const operators = PERF_EVENT_OPERATORS;

          let tempt = state
            .getIn([groupIndex, itemIndex])
            .withMutations(map => {
              map
                .set('value', '')
                .set('valueEnd', '')
                .set('initValue', '')
                // .set('keyEvent', `${eventCategoryId}-${eventActionId}`)
                .set('property', value)
                .set('error', 0)
                .set('isInitDataSources', false)
                .set('dataSources', [])
                .set('dataType', value.dataType)
                .set('operator', {}) // no use getFirstOperator
                .set('refineWithProperties', OrderedMap({}));
            });
          if (computeType.value !== 'event_counter') {
            tempt = tempt.set('eventAttribute', {});
          } else {
            tempt = tempt.set('operator', operators[0]);
            tempt = tempt.set('operators', operators);
          }
          return state.setIn([groupIndex, itemIndex], tempt);
        }
        return state;
      }
      case `${prefix}_${Types.PERF_EVENT_CHANGE_OPERATOR}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value } = data;
        const currentRule = state.getIn([groupIndex, itemIndex]);
        const dataType = currentRule.get('dataType');
        const computeType = currentRule.get('computeType');
        let tempt = state.getIn([groupIndex, itemIndex]).withMutations(map => {
          map
            .set('value', '')
            .set('valueEnd', '')
            .set('initValue', '')
            .set('operator', value);
        });
        if (dataType === 'datetime') {
          if (value.value === OPERATORS_CODE.BETWEEN) {
            tempt = tempt.set(
              'value',
              parseDataRangePickAPI(initTimeRangeV2()),
            );
          } else if (
            value.value === OPERATORS_CODE.EQUALS ||
            value.value === OPERATORS_CODE.NOT_EQUALS ||
            value.value === OPERATORS_CODE.AFTER_DATE ||
            value.value === OPERATORS_CODE.BEFORE_DATE
          ) {
            tempt = tempt.set('value', initFirtTimeValueDatimeV2());
          }
          if (computeType.value === 'unique_list_count') {
            tempt = tempt.set('value', '1');
            tempt = tempt.set('valueEnd', '1');
          }
        }
        return state.setIn([groupIndex, itemIndex], tempt);
      }
      case `${prefix}_${Types.PERF_EVENT_CHANGE_NUMBER_TIME}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value, name = 'value' } = data;
        const currentRule = state.getIn([groupIndex, itemIndex]);
        const operator = currentRule.get('operator');
        let valueTmp = [];
        if (data.datimeV2) {
          if (value.date) {
            valueTmp.push({
              date: value.date,
              dateType: value.option.dateType,
              calculationDate: value.option.calculationDate,
              calculationType: value.option.calculationType,
              value: value.option.value,
              key: 'fromDate',
            });
          } else if (value.timeRange) {
            valueTmp = parseDataRangePickAPI(value.timeRange);
          }
        }
        // if (
        //   computeType.value === 'event_counter' ||
        //   (computeType.value === 'unique_list_count' &&
        //     eventAttribute.dataType === 'number')
        // ) {
        //   if (Number(value) <= 0) {
        //     valueChange = '1';
        //   }
        // }
        let tempt = state.getIn([groupIndex, itemIndex]).withMutations(map => {
          map.set('error', 0).set(name, data.datimeV2 ? valueTmp : value);
          // .set('valueBase', undefined)
          // .set('valueEndBase', undefined);
        });
        if (operator.value === OPERATORS_CODE.BETWEEN && data.datimeV2) {
          tempt = tempt.set('valueEnd', valueTmp);
        }
        return state.setIn([groupIndex, itemIndex], tempt);
        // return state.setIn([groupIndex, itemIndex, name], value);
      }

      case `${prefix}_${Types.PERF_EVENT_CHANGE_TIME_WINDOW}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value, object } = data;
        // object === 'operator|value|start_date|end_date';

        if (object === 'start_date') {
          const end_date = state.getIn([
            groupIndex,
            itemIndex,
            'timeWindow',
            'end_date',
          ]);
          if (parseInt(value) > parseInt(end_date)) {
            return state
              .setIn([groupIndex, itemIndex, 'timeWindow', 'start_date'], value)
              .setIn([groupIndex, itemIndex, 'timeWindow', 'end_date'], value);
          }
        }
        return state.setIn(
          [groupIndex, itemIndex, 'timeWindow', object],
          value,
        );
      }
      case `${prefix}_${Types.PERF_EVENT_CHANGE_TIME_RANGE}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value } = data;
        return state.setIn(
          [groupIndex, itemIndex, 'timeRange'],
          value.timeRange,
        );
      }
      case `${prefix}_${Types.PERF_EVENT_CHANGE_AGGREGATION}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value } = data;
        return state.setIn([groupIndex, itemIndex, 'aggregationType'], value);
      }
      case `${prefix}_${Types.PERF_EVENT_CHANGE_REFINE_PROPERTIES}`: {
        const { type, data } = action.payload;
        const { groupIndex, itemIndex, refineIndex, value } = data;
        if (type === 'INIT') {
          return state.setIn(
            [groupIndex, itemIndex, 'refineWithProperties'],
            data.conditions,
          );
        }
        if (type === 'CHANGE_PROPERTY') {
          const oldItem = safeParse(
            state.getIn([
              groupIndex,
              itemIndex,
              'refineWithProperties',
              refineIndex,
              'property',
            ]),
            null,
          );

          if (oldItem === null || oldItem.value !== value.value) {
            const operators = getOperatorByProperty(value);
            let tempt = state
              .getIn([
                groupIndex,
                itemIndex,
                'refineWithProperties',
                refineIndex,
              ])
              .withMutations(map => {
                map
                  .set('value', '')
                  .set('valueEnd', '')
                  .set('initValue', '')
                  .set('property', value)
                  .set('error', 0)
                  .set('dataType', value.dataType)
                  .set('operator', getFirstOperator(operators))
                  .set('operators', operators);
              });
            if (data.datimeV2 && value.dataType === 'datetime') {
              const firtSematic = getFirstSemantic(MAP_SEMATIC.map);
              const operatorsWithSematic = getOperatorByProperty(firtSematic);
              tempt = tempt.set('semantics', MAP_SEMATIC.list);
              tempt = tempt.set('semantic', getFirstSemantic(MAP_SEMATIC.map));
              tempt = tempt.set(
                'operator',
                getFirstOperator(operatorsWithSematic),
              );
              tempt = tempt.set('operators', operatorsWithSematic);
              tempt = tempt.set('value', initFirtTimeValueDatimeV2());
            }
            return state.setIn(
              [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
              tempt,
            );
          }
        } else if (type === 'CHANGE_OPERATOR') {
          const currentRule = state.getIn([
            groupIndex,
            itemIndex,
            'refineWithProperties',
            refineIndex,
          ]);
          const dataType = currentRule.get('dataType');
          let tempt = state
            .getIn([groupIndex, itemIndex, 'refineWithProperties', refineIndex])
            .withMutations(map => {
              map
                .set('value', '')
                .set('valueEnd', '')
                .set('initValue', '')
                .set('operator', value);
            });
          if (dataType === 'datetime') {
            if (value.value === OPERATORS_CODE.BETWEEN) {
              tempt = tempt.set(
                'value',
                parseDataRangePickAPI(initTimeRangeV2()),
              );
            } else if (
              value.value === OPERATORS_CODE.EQUALS ||
              value.value === OPERATORS_CODE.NOT_EQUALS ||
              value.value === OPERATORS_CODE.AFTER_DATE ||
              value.value === OPERATORS_CODE.BEFORE_DATE
            ) {
              tempt = tempt.set('value', initFirtTimeValueDatimeV2());
            }
          }
          return state.setIn(
            [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
            tempt,
          );
        } else if (type === 'CHANGE_VALUE') {
          const { name = 'value' } = data;
          const currentRule = state.getIn([
            groupIndex,
            itemIndex,
            'refineWithProperties',
            refineIndex,
          ]);
          const operator = currentRule.get('operator');
          let valueTmp = [];
          if (data.datimeV2) {
            if (value.date) {
              valueTmp.push({
                date: value.date,
                dateType: value.option.dateType,
                calculationDate: value.option.calculationDate,
                calculationType: value.option.calculationType,
                value: value.option.value,
                key: 'fromDate',
              });
            } else if (value.timeRange) {
              valueTmp = parseDataRangePickAPI(value.timeRange);
            }
          }
          let tempt = state
            .getIn([groupIndex, itemIndex, 'refineWithProperties', refineIndex])
            .withMutations(map => {
              map.set('error', 0).set(name, data.datimeV2 ? valueTmp : value);
            });
          if (operator.value === OPERATORS_CODE.BETWEEN && data.datimeV2) {
            tempt = tempt.set('valueEnd', valueTmp);
          }
          return state.setIn(
            [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
            tempt,
          );
          // return state.setIn(
          //   [groupIndex, itemIndex, 'refineWithProperties', refineIndex, name],
          //   value,
          // );
        } else if (type === 'ADD_ITEM') {
          return state.setIn(
            [groupIndex, itemIndex, 'refineWithProperties', generateKey()],
            initItemRefinePropertiesValue(),
          );
        } else if (type === 'DELETE_ITEM') {
          return state.deleteIn(
            [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
            value,
          );
        } else if (type === 'CHANGE_SEMANTIC') {
          const operators = getOperatorByProperty(value);
          const tempt = state
            .getIn([groupIndex, itemIndex, 'refineWithProperties', refineIndex])
            .withMutations(map => {
              map
                .set('value', initFirtTimeValueDatimeV2())
                .set('valueEnd', '')
                .set('initValue', '')
                .set('semantic', value)
                .set('operator', getFirstOperator(operators))
                .set('operators', operators);
            });
          return state.setIn(
            [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
            tempt,
          );
        }

        return state;
      }
      case `${prefix}_${Types.PERF_EVENT_CHANGE_SOURCE}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value } = data;
        const currentRule = state.getIn([groupIndex, itemIndex]);
        let dataRefine = currentRule.get('refineWithProperties');

        if (value.isDeleteRefine) {
          dataRefine = deleteRefinesBySourceId(
            currentRule.get('refineWithProperties'),
            value.sourceIdWillDeleted,
          );
        }

        const tempt = state
          .getIn([groupIndex, itemIndex])
          .withMutations(map => {
            map.set('errorDataSources', 0);
            map.set('isInitDataSources', false);
            map.set('dataSources', value.dataSourceSelected);
            map.set(
              'refineWithProperties',
              value.dataSourceSelected.length === 0
                ? OrderedMap({})
                : dataRefine,
            );
          });
        return state.setIn([groupIndex, itemIndex], tempt);
      }
      case `${prefix}_${Types.PERF_EVENT_INIT_EVENT_ATTRIBUTE}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, group, dataLookup } = data;
        const currentRule = state.getIn([groupIndex, itemIndex]);
        const computeType = currentRule.get('computeType');
        const eventAttribute = currentRule.get('eventAttribute');
        const filterData = filterEventAttrSegment(
          group.list,
          getDataInclude(computeType.value),
          computeType.value,
        );
        let valueEvent = {};
        // init value eventattribute
        if (eventAttribute && Object.keys(eventAttribute).length > 0) {
          valueEvent =
            dataLookup.map[
              `${eventAttribute.propertyName}-${eventAttribute.itemTypeId}`
            ];
        }
        let tempt = state.getIn([groupIndex, itemIndex]).withMutations(map => {
          map
            .set('optionEventAttribute', filterData)
            .set('eventAttribute', valueEvent);
        });
        if (
          valueEvent &&
          Object.keys(valueEvent).length > 0 &&
          valueEvent.dataType === 'string'
        ) {
          tempt = tempt.set('operators', getOperatorByProperty(valueEvent));
        }
        if (computeType.value === 'unique_list_count') {
          tempt = tempt.set('operators', UNIQUE_OPERATORS);
        }

        return state.setIn([groupIndex, itemIndex], tempt);
      }
      case `${prefix}_${Types.PERF_EVENT_CHANGE_EVENT_ATTRIBUTE}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value } = data;
        const currentRule = state.getIn([groupIndex, itemIndex]);
        const computeType = currentRule.get('computeType');
        const operators =
          value.dataType === 'string'
            ? getOperatorByProperty(value)
            : PERF_EVENT_OPERATORS_V2[value.dataType];
        let tempt = state.getIn([groupIndex, itemIndex]).withMutations(map => {
          map
            .set('eventAttribute', value)
            .set(
              'operator',
              computeType.value === 'unique_list_count'
                ? UNIQUE_OPERATORS.list[0]
                : operators && operators.list[0],
            )
            .set(
              'operators',
              computeType.value === 'unique_list_count'
                ? UNIQUE_OPERATORS
                : operators,
            )
            .set('value', '')
            .set('dataType', value.dataType);
        });
        if (value.dataType === 'datetime') {
          const firtSematic = getFirstSemantic(MAP_SEMATIC.map);
          const operatorsWithSematic =
            computeType.value === 'unique_list_count'
              ? UNIQUE_OPERATORS
              : getOperatorByProperty(firtSematic);
          tempt = tempt.set('semantics', MAP_SEMATIC.list);
          tempt = tempt.set('semantic', getFirstSemantic(MAP_SEMATIC.map));
          tempt = tempt.set('operator', getFirstOperator(operatorsWithSematic));
          tempt = tempt.set('operators', operatorsWithSematic);
          tempt = tempt.set(
            'value',
            computeType.value === 'unique_list_count'
              ? '1'
              : initFirtTimeValueDatimeV2(),
          );
        }
        return state.setIn([groupIndex, itemIndex], tempt);
      }
      case `${prefix}_${Types.PERF_EVENT_CHANGE_SEMANTIC}`: {
        const { data } = action.payload;
        const { groupIndex, itemIndex, value } = data;
        const oldItem = safeParse(
          state.getIn([groupIndex, itemIndex, 'property']),
          null,
        );
        const currentRule = state.getIn([groupIndex, itemIndex]);
        const computeType = currentRule.get('computeType');
        if (oldItem === null || oldItem.value !== value.value) {
          const operators =
            computeType.value === 'unique_list_count'
              ? UNIQUE_OPERATORS
              : getOperatorByProperty(value);
          const tempt = state
            .getIn([groupIndex, itemIndex])
            .withMutations(map => {
              map
                .set(
                  'value',
                  computeType.value === 'unique_list_count'
                    ? '1'
                    : initFirtTimeValueDatimeV2(),
                )
                .set('valueEnd', '')
                .set('initValue', '')
                .set('error', 0)
                .set('semantic', value)
                .set(
                  'operator',
                  computeType.value === 'unique_list_count'
                    ? UNIQUE_OPERATORS.list[0]
                    : getFirstOperator(operators),
                )
                .set('operators', operators);
            });
          return state.setIn([groupIndex, itemIndex], tempt);
        }
        return state;
      }
      // ------------ END PERF_EVENT ---------------

      default:
        return state;
    }
  };
  return conditionReducer;
};

export default conditionReducerFor;
