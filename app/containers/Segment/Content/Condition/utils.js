/* eslint-disable no-else-return */
/* eslint-disable prefer-destructuring */
/* eslint-disable camelcase */
import {
  initDateValue,
  toEntryAPI as toEntryTimeRangeAPI,
  toEntryFE as toEntryTimeRangeUI,
} from 'components/Templates/CalendarSelection/utils';
import _isEmpty from 'lodash/isEmpty';
import {
  validateItemConditionV0,
  buildValueConditionFromUISegment,
} from 'containers/Filters/utils';
import { Map, OrderedMap } from 'immutable';
import { generateKey, safeParse } from 'utils/common';
import { STATUS_ITEM_CODE } from 'utils/constants';
// import { MAP_AGGREGATION_TYPE } from '../../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Attributes/Create/Design/Settings/ComputedAttribute/utils.map';

import {
  safeParseArrayNumber,
  safeParseArrayString,
} from 'utils/web/attribute';
import { PortalDate, formatDate } from '../../../../utils/date';
import {
  MAP_PERF_EVENT_OPERATORS,
  MAP_PERF_EVENT_TIME_WINDOW,
  MAP_SEGMENT_TYPES,
  MAP_SEGMENT_VALUES,
  MAP_SEMATIC,
  PERF_EVENT_TIME_WINDOW,
  UNIQUE_OPERATORS,
} from './constants';
import {
  buildValueConditionFromAPI,
  buildValueConditionFromUI,
  getFirstSemantic,
  getOperatorByProperty,
  getOperatorByValue,
} from './utils.conditions';
import { MAP_AUTO_UPDATE } from '../../../../components/Templates/CalendarSelection/hook';
import { format, subDays } from 'date-fns';
// eslint-disable-next-line import/no-duplicates
import { mapRangeTime } from '../../../../components/Templates/CalendarSelection/utils';
import dayjs from 'dayjs';
import { OPERATORS_CODE } from './operators';
import { MAP_ATTR_TYPE } from '../../../modals/ModalComputedType/utils';
import { getTranslateMessage } from '../../../Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import { isEmpty } from 'lodash';

const DATETIME_FORMAT_VIEW = 'yyyy-MM-dd HH:mm:ss';
const MAP_AGGREGATION_TYPE = {
  sum: {
    label: getTranslateMessage(TRANSLATE_KEY._MEASURE_TYPE_SUM, 'Sum'),
    value: 'sum',
  },
  max: {
    label: getTranslateMessage(TRANSLATE_KEY._MEASURE_TYPE_MAX, 'Max'),
    value: 'max',
  },
  min: {
    label: getTranslateMessage(TRANSLATE_KEY._MEASURE_TYPE_MIN, 'Min'),
    value: 'min',
  },
  avg: {
    label: getTranslateMessage(TRANSLATE_KEY._MEASURE_TYPE_AVERAGE, 'Average'),
    value: 'avg',
  },
};
function getDataSource(inputs = []) {
  if (inputs.length === 0) {
    return [];
  }
  if (inputs.length === 1 && inputs[0] == '-1') {
    return [];
  }
  return inputs;
}

export function isComputedAttr(type) {
  if (type === MAP_SEGMENT_VALUES.comp_prop || MAP_SEGMENT_VALUES.comp_attr) {
    return true;
  }
  return false;
}

export function commonValidateItemCondition(item, type) {
  const property = item.get('property');
  const operator = item.get('operator');
  const value = item.get('value');
  const valueEnd = item.get('valueEnd');

  if (type === MAP_SEGMENT_VALUES.comp_attr) {
    return validateItemConditionV0(type, property, operator, value, valueEnd);
  }
  const dataSources = item.get('dataSources');
  let status = validateItemConditionV0(
    // type,
    property,
    operator,
    value,
    valueEnd,
  );
  if (Array.isArray(dataSources) && dataSources.length > 0) {
    status = true;
  }
  return status;
}

export function validateItemCondition(item) {
  if (item.get('conditionType') === null) {
    return false;
  }
  return commonValidateItemCondition(item, item.get('conditionType').value);
}

export const initItem = (conditionType, scheduleType, more) => {
  const key = generateKey();

  return OrderedMap({
    [key]: initItemValue(conditionType, scheduleType, more),
  });
};

export const initItemRefinePropertiesValue = () =>
  initItemValue(MAP_SEGMENT_TYPES.comp_attr);

export const initItemValue = (conditionType, scheduleType, more) => {
  let result = Map({});
  if (conditionType.value === MAP_SEGMENT_VALUES.comp_attr) {
    result = Map({
      value: '',
      conditionType,
      property: null,
      operator: null,
      operators: [],
      dataType: 'string',
    });
  } else if (conditionType.value === MAP_SEGMENT_VALUES.perf_event) {
    result = Map({
      conditionType,
      property: null,
      operator: null,
      dataType: 'number',
      value: 1,
      valueEnd: 1,
      refineWithProperties: OrderedMap({}),
      timeWindow: Map({
        operator: PERF_EVENT_TIME_WINDOW[0],
        value: '7',
        start_date: `${new Date().getTime()}`,
        end_date: `${new Date().getTime()}`,
      }),
      conversionWindow: {
        value: 1,
        type: 'days',
        absolute: false,
        options: 'after',
      },
      timeRange:
        more && more.v2
          ? initTimeRangeDefaultV2()
          : initDateValue(scheduleType),
      computeType: safeParse(more && more.selected, {}),
    });
  }

  if (more && more.property) {
    result = result.setIn(['property'], more.property);
  }
  return result;
};

export function toConditionCompPropAPI(item, v2 = false) {
  if (validateItemCondition(item)) {
    const statusItemCode = safeParse(
      item.get('statusItemCode'),
      STATUS_ITEM_CODE.ACTIVE,
    );
    const objValue = buildValueConditionFromUI(item, v2);
    let tempt = {};
    if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
      tempt = {
        condition_type: item.get('conditionType').value,
        property_name: item.get('property').propertyCode,
        item_type_id: item.get('property').itemTypeId,
        // data_type: item.get('property').dataType,
        data_type:
          item.get('property').itemDataType || item.get('property').dataType,
        operator: item.get('operator').value,
        extendValue: item.get('extendValue'),
        countryIds: item.get('countryIds'),
        ...objValue,
      };
      if (
        v2 &&
        item.get('property') &&
        item.get('property').dataType === 'datetime'
      ) {
        tempt.version = 'v2';
        tempt.semantics = item.get('semantic') && item.get('semantic').format;
      }
    } else {
      tempt = item.get('backup');
      if (tempt !== null && tempt !== undefined) {
        tempt = { ...tempt, ...objValue };
        return tempt;
      }
    }
    return tempt;
  }
  return null;
}
const isDateValid = dateStr => !isNaN(new Date(dateStr));

export function toConditionCompPropAPIV2(item, reformatDatetime = false) {
  if (validateItemCondition(item)) {
    const statusItemCode = safeParse(
      item.get('statusItemCode'),
      STATUS_ITEM_CODE.ACTIVE,
    );

    const dataType =
      item.get('property').itemDataType || item.get('property').dataType;
    const operator = item.get('operator').value;

    const formatItemValue = (value, isEndDay = false) =>
      reformatDatetime &&
        dataType === 'datetime' &&
        isDateValid(value) &&
        new Date(value).getTime() === +value
        ? formatDate(
          isEndDay
            ? new Date(value).setHours(23, 59, 59, 999)
            : new Date(value).setHours(0, 0, 0, 0),
          DATETIME_FORMAT_VIEW,
        )
        : value;
    item = item.set(
      'value',
      formatItemValue(item.get('value'), operator === 'after_date'),
    );
    item = item.set('valueEnd', formatItemValue(item.get('valueEnd'), true));
    const objValue = buildValueConditionFromUI(item);

    let tempt = {};
    if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
      tempt = {
        condition_type: item.get('conditionType').value,
        column: item.get('property').propertyCode,
        property_name: item.get('property').propertyCode,
        item_type_id: item.get('property').itemTypeId,
        // data_type: item.get('property').dataType,
        data_type: dataType,
        operator,
        is_custom_function:
          item.get('property').computeType === 'custom_function',
        extendValue: item.get('extendValue'),
        ...objValue,
      };
    } else {
      tempt = item.get('backup');
      if (tempt !== null && tempt !== undefined) {
        tempt = { ...tempt, ...objValue };
        return tempt;
      }
    }
    return tempt;
  }
  return null;
}

function toConditionCompPropUI(
  item,
  conditionType,
  mapGroupItemAttributes,
  mapInfo = {},
  reformatDatetime = false,
  v2 = false,
) {
  // console.log('mapInfo', mapInfo);
  const keyLookup = `${item.property_name}-${safeParse(item.item_type_id, 0)}`;
  let property = mapGroupItemAttributes[keyLookup];
  if (property !== undefined) {
    // console.log('getOperatorByValue(`${property.dataType}-${item.operator}`)', `${property.itemDataType}-${item.operator}`,'--',getOperatorByValue(`${property.itemDataType}-${item.operator}`));
    const objValue = buildValueConditionFromAPI(item, property);
    const convertDatetime = value =>
      reformatDatetime &&
        property.dataType === 'datetime' &&
        isDateValid(value) &&
        +value != value
        ? new Date(value).getTime()
        : value;
    const dataTemp = {
      conditionType,
      ...objValue,
      property,
      operator: getOperatorByValue(`${property.itemDataType}-${item.operator}`),
      operators: getOperatorByProperty(property, objValue.semantic),
      dataType: property.dataType,
      backup: item,
      initValue: convertDatetime(objValue.initValue),
      value: convertDatetime(objValue.value),
      valueEnd: convertDatetime(objValue.valueEnd),
    };
    if (!item.version && property.dataType === 'datetime' && v2) {
      const objValueV2 = toMigareOperatorV1toV2(item);
      dataTemp.operator = getOperatorByValue(
        `${property.itemDataType}-${objValueV2.operator}`,
      );
      dataTemp.semantic = objValueV2.semantic;
      dataTemp.semantics = objValueV2.semantics;
      dataTemp.value = objValueV2.value;
      dataTemp.operators = objValueV2.operators;
    }
    const tempt = OrderedMap(dataTemp);
    return tempt;
    // eslint-disable-next-line no-else-return
  } else {
    property = mapInfo[keyLookup];
    if (property !== undefined) {
      property.dataType = safeParse(property.dataType, item.data_type);
      const objValue = buildValueConditionFromAPI(item, property);
      const tempt = OrderedMap({
        conditionType,
        ...objValue,
        property,
        statusItemCode: property.statusItemCode,
        operator: getOperatorByValue(`${property.dataType}-${item.operator}`),
        operators: getOperatorByProperty(property),
        dataType: property.dataType,
        backup: item,
      });

      return tempt;
    }
  }
  return null;
}

function toTimeWindowAPI(timeWindow) {
  const operator = timeWindow.get('operator');
  let value = '';
  if (operator.value === 'last') {
    value = `${timeWindow.get('value')}`;
  } else if (operator.value === 'between') {
    const start = `${timeWindow.get('start_date')}`;
    const end = `${timeWindow.get('end_date')}`;
    value = `${start}_${end}`;
  }
  const tempt = {
    type: 'event',
    property_name: 'tracked_time',
    data_type: 'datetime',
    operator: operator.value,
    value,
  };
  return tempt;
}

function toTimeWindowUI(timeWindow) {
  const { operator, value } = timeWindow;

  const currentTime = new Date().getTime();
  let uiValue = '1';
  let start_date = `${currentTime}`;
  let end_date = `${currentTime}`;
  if (operator === 'last') {
    uiValue = value;
  } else if (operator === 'between') {
    const rangeDate = value.split('_');
    if (rangeDate.length === 2) {
      start_date = rangeDate[0];
      end_date = rangeDate[1];
    }
  }
  return Map({
    operator: MAP_PERF_EVENT_TIME_WINDOW[operator],
    value: uiValue,
    start_date,
    end_date,
  });
}

function toRefinePropertiesAPI(refineProperties, v2 = false) {
  // console.log('refineProperties', refineProperties, refineProperties.toJS());
  const rule = { AND: [] };

  if (refineProperties.size > 0) {
    const isInit = refineProperties.first().get('isInit');
    if (isInit === true) {
      return refineProperties.first().get('backup');
    }
    refineProperties.forEach(item => {
      if (commonValidateItemCondition(item)) {
        const statusItemCode = safeParse(
          item.get('statusItemCode'),
          STATUS_ITEM_CODE.ACTIVE,
        );
        let tempt = {};
        const objValue = buildValueConditionFromUI(item, v2);
        if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
          tempt = {
            type: item.get('property').type,
            property_name: item.get('property').name,
            data_type: item.get('property').itemDataType,
            // fe_data_type: item.get('property').dataType,
            item_type_id:
              item.get('property').itemTypeId === 0
                ? null
                : item.get('property').itemTypeId,
            operator: item.get('operator').value,
            ...objValue,
          };
          if (
            v2 &&
            item.get('property') &&
            item.get('property').dataType === 'datetime'
          ) {
            tempt.version = 'v2';
            tempt.semantics =
              item.get('semantic') && item.get('semantic').format;
          }
        } else {
          tempt = item.get('backup');

          if (tempt !== null && tempt !== undefined) {
            tempt = { ...tempt, ...objValue };
          }
        }

        if (tempt !== null) {
          rule.AND.push(tempt);
        }

        // const objValue = buildValueConditionFromUI(item);
        //
        // const tempt = {
        //   type: item.get('property').type,
        //   property_name: item.get('property').name,
        //   data_type: item.get('property').itemDataType,
        //   // fe_data_type: item.get('property').dataType,
        //   item_type_id:
        //     item.get('property').itemTypeId === 0
        //       ? null
        //       : item.get('property').itemTypeId,
        //   operator: item.get('operator').value,
        //   ...objValue,
        // };
        //
        // rule.AND.push(tempt);
      }
    });
  }

  return rule;
}

function toRefinePropertiesUI(objRules) {
  let conditions = OrderedMap({});
  const rules = safeParse(objRules.AND, []);

  if (rules.length === 0) {
    return conditions;
  }

  rules.forEach(item => {
    const tempt = OrderedMap({
      // ...objValue,
      // property,
      // statusItemCode: property.statusItemCode,
      // operator: getOperatorByValue(`${property.dataType}-${item.operator}`),
      // operators: getOperatorByProperty(property),
      // dataType: property.dataType,
      backup: item,
      isInit: true,
    });

    conditions = conditions.set(generateKey(), tempt);

    // let property =
    //   mapItem[`${item.property_name}-${safeParse(item.item_type_id, 0)}`];
    // if (property === undefined) {
    //   property =
    //     mapInfoEventProperty[
    //       `${item.property_name}-${safeParse(item.item_type_id, 0)}`
    //     ];
    // }
    // if (property !== undefined) {
    //   const objValue = buildValueConditionFromAPI(item, property);
    //   const tempt = OrderedMap({
    //     ...objValue,
    //     property,
    //     statusItemCode: property.statusItemCode,
    //     operator: getOperatorByValue(`${property.dataType}-${item.operator}`),
    //     operators: getOperatorByProperty(property),
    //     dataType: property.dataType,
    //     backup: item,
    //     isInit: true,
    //   });

    //   conditions = conditions.set(generateKey(), tempt);
    // }
  });
  return conditions;
}

export function toConditionPerfEventAPI(item, v2 = false) {
  // console.log('validateItemCondition(item) ===>', validateItemCondition(item));
  // console.log('validateItemCondition(item) ===>', item.toJS());
  if (validateItemCondition(item)) {
    // const value = item.get('value');

    const property = item.get('property');
    const computeType = safeParse(item.get('computeType'), {});
    const eventAttribute = safeParse(item.get('eventAttribute'), {});
    const semantic = safeParse(item.get('semantic'), null);
    const statusItemCode = safeParse(
      item.get('statusItemCode'),
      STATUS_ITEM_CODE.ACTIVE,
    );
    let tempt = {};
    const objValue = buildValueConditionFromUISegment(
      item,
      v2,
      eventAttribute,
      computeType,
    );
    if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
      // let insight_property_ids = [-1];
      let insight_property_ids = [];
      const dataSources = safeParse(item.get('dataSources'), []);
      if (Array.isArray(dataSources) && dataSources.length > 0) {
        insight_property_ids = safeParseArrayNumber(dataSources);
      }
      tempt = {
        condition_type: item.get('conditionType').value,
        event_category_id: property.eventCategoryId,
        event_action_id: property.eventActionId,
        event_tracking_name: property.eventTrackingCode,
        data_type: 'number',
        insight_property_ids,
        operator: item.get('operator').value,
        ...objValue,
        refine_with_properties: toRefinePropertiesAPI(
          item.get('refineWithProperties'),
          v2,
        ),
        // time_window: toTimeWindowAPI(item.get('timeWindow')),
        // time_window: null,
        time_range: v2
          ? toEntryTimeRangeAPIV2(safeParse(item.get('timeRange'), {}))
          : toEntryTimeRangeAPI(
            safeParse(item.get('timeRange'), initDateValue()),
          ),
      };
      if (v2) {
        tempt.aggregation = computeType.value;
        tempt.version = 'v2';
        if (computeType.value === 'aggregation') {
          tempt.aggregation = safeParse(item.get('aggregationType'), {}).value;
        }
        if (computeType.value !== 'event_counter') {
          tempt.compute_property_name =
            eventAttribute.eventPropertyName || eventAttribute.propertyName;
          tempt.compute_item_type_id = eventAttribute.itemTypeId;
          tempt.compute_data_type = eventAttribute.dataType;
        }
        if (semantic) {
          tempt.semantics = semantic.format;
        }
      }
      // console.log('tempt ===>', tempt);
    } else {
      tempt = item.get('backup');

      if (tempt !== null && tempt !== undefined) {
        tempt = { ...tempt, ...objValue };
        return tempt;
      }
    }
    return tempt;
  }
  return null;
}

function toRuleDataSourceUI(dataSources = []) {
  if (Array.isArray(dataSources) === false) {
    return [];
  }
  if (dataSources.length === 1 && dataSources[0] == -1) {
    return [];
  }
  return safeParseArrayString(dataSources);
}
export function toEntryTimeRangeAPIV2(data) {
  return {
    version: 'v2',
    calendar: {
      ...data,
    },
  };
}
function toConditionPerfEventUI(
  item,
  conditionType,
  mapEventSchema,
  // eventProperty,
  mapInfoEventSchema = {},
  // mapInfoEventProperty = {},
  // mapInfoDataSource = {},
) {
  const keySchema = `${item.event_category_id}-${item.event_action_id}`;
  let property = mapEventSchema[keySchema];
  // const mapEventProperty = safeParse(eventProperty[keySchema], {
  //   map: {},
  //   list: [],
  // });
  // console.log(
  //   'item.time_range',
  //   item.time_range,
  //   toEntryTimeRangeUI(item.time_range),
  // );
  if (property === undefined) {
    property = mapInfoEventSchema[keySchema];
  }
  let propertyTmp = {};
  // / case event attribute la property
  if (
    item.aggregation !== 'event_counter' &&
    item.version === 'v2' &&
    item.compute_data_type
  ) {
    propertyTmp = {
      dataType: item.compute_data_type,
    };
  }
  if (property !== undefined) {
    const objValue = buildValueConditionFromAPI(
      item,
      item.compute_data_type ? propertyTmp : property,
    );
    const tmp = {
      conditionType,
      property,
      statusItemCode: property.statusItemCode,
      operator:
        item.aggregation && item.aggregation !== 'event_counter'
          ? getOperatorByValue(
              `${item.compute_data_type || item.data_type}-${item.operator}`,
            )
          : MAP_PERF_EVENT_OPERATORS[item.operator],
      dataSources: toRuleDataSourceUI(item.insight_property_ids),
      isInitDataSources: true,
      // keyEvent: `${property.eventCategoryId}-${property.eventActionId}`,
      dataType: 'number',
      ...objValue,
      refineWithProperties: OrderedMap({
        'data-init': OrderedMap({
          backup: item.refine_with_properties,
          isInit: true,
        }),
      }),
      // toRefinePropertiesUI(
      // item.refine_with_properties,
      // mapEventProperty.map,
      // safeParse(mapInfoEventProperty[keySchema], {}),
      // ),
      // refineWithProperties: OrderedMap({}),
      // timeWindow: toTimeWindowUI(item.time_window),
      timeRange:
        item && item.time_range.version
          ? item.time_range.calendar
          : toMigareTimeRangeV1toV2(item.time_range),
      backup: item,
      computeType: MAP_ATTR_TYPE.event_counter,
      // computeType: item.aggregation && MAP_ATTR_TYPE[item.aggregation]
      //   ? MAP_ATTR_TYPE[item.aggregation]
      //   : MAP_ATTR_TYPE.event_counter,
    };
    if (item.aggregation && MAP_ATTR_TYPE[item.aggregation]) {
      tmp.computeType = MAP_ATTR_TYPE[item.aggregation];
      // tmp.aggregationType = MAP_AGGREGATION_TYPE[item.aggregation];
    } else if (item.aggregation && MAP_AGGREGATION_TYPE[item.aggregation]) {
      tmp.computeType = MAP_ATTR_TYPE.aggregation;
      tmp.aggregationType = MAP_AGGREGATION_TYPE[item.aggregation];
    }
    if (item.aggregation !== 'event_counter' && item.version === 'v2') {
      const eventAttribute = {
        propertyName: item.compute_property_name,
        itemTypeId: item.compute_item_type_id,
        dataType: item.compute_data_type || item.data_type,
      };
      tmp.eventAttribute = eventAttribute;
      tmp.operators =
        item.aggregation === 'unique_list_count'
          ? UNIQUE_OPERATORS
          : getOperatorByProperty(eventAttribute, objValue.semantic);
      tmp.dataType = eventAttribute.dataType;
    }
    // case value < 1
    //   if (
    //   item.aggregation === 'event_counter' ||
    //   item.aggregation === 'unique_list_count' ||
    //   !item.aggregation
    // ) {
    //   if (Number(tmp.value) < 1) {
    //     tmp.valueBase = tmp.value;
    //   }
    //   if (tmp.valueEnd && Number(tmp.valueEnd) < 1) {
    //     tmp.valueEndBase = tmp.valueEnd;
    //   }
    // }
    const tempt = Map({ ...tmp });
    return tempt;
  }
  return null;
}

export function toConditionAPI(
  conditions,
  v2 = false,
  reformatDatetime = false,
  objectType = '',
) {
  const rules = { OR: [] };
  conditions.forEach(condition => {
    const rule = { AND: [] };
    condition.forEach(item => {
      const conditionType = safeParse(item.get('conditionType'), {});
      if (conditionType.value === MAP_SEGMENT_VALUES.comp_attr) {
        const tempt = safeParse(
          objectType === 'BO_DATA_TABLE' && v2
            ? toConditionCompPropAPIV2(item, reformatDatetime)
            : toConditionCompPropAPI(item, v2),
          null,
        );
        if (tempt !== null) {
          rule.AND.push(tempt);
        }
      } else if (conditionType.value === MAP_SEGMENT_VALUES.perf_event) {
        const tempt = toConditionPerfEventAPI(item, v2);

        if (tempt !== null) {
          rule.AND.push(tempt);
        }
      }
    });
    if (rule.AND.length > 0) {
      rules.OR.push(rule);
    }
  });
  // }
  return rules;
}

export function toConditionUI(objRules, data, reformatDatetime = false, v2) {
  let conditions = OrderedMap({});
  const rules = [];
  const ruleOR = safeParse(objRules.OR, []);
  ruleOR.forEach(itemOr => {
    const ruleAND = safeParse(itemOr.AND, []);
    if (ruleAND.length > 0) {
      rules.push(ruleAND);
    }
  });

  if (rules.length === 0) {
    return conditions;
  }
  rules.forEach(rule => {
    let condition = OrderedMap({});
    rule.forEach(item => {
      let tmpType = item.condition_type;
      if (tmpType === 'comp_prop') {
        tmpType = 'comp_attr';
        // eslint-disable-next-line no-param-reassign
        item.condition_type = 'comp_attr';
      }
      const conditionType = data.map.conditionType[tmpType];
      if (tmpType === MAP_SEGMENT_VALUES.comp_attr) {
        const tempt = toConditionCompPropUI(
          item,
          conditionType,
          data.map.itemAttribute,
          data.info.itemAttribute,
          reformatDatetime,
          v2,
        );
        if (tempt !== null) {
          condition = condition.set(generateKey(), tempt);
        }
      } else if (item.condition_type === MAP_SEGMENT_VALUES.perf_event) {
        const tempt = toConditionPerfEventUI(
          item,
          conditionType,
          data.map.eventSchema,
          data.info.eventSchema,
          // eventProperty,
          // mapInfoEventSchema,
          // mapInfoEventProperty,
          // mapInfoDataSource,
        );

        if (tempt !== null) {
          condition = condition.set(generateKey(), tempt);
        }
      }
    });
    if (condition.size > 0) {
      conditions = conditions.setIn([generateKey()], condition);
    }
  });
  return conditions;
}

export function setRuleByVersion(objRules, version) {
  if (objRules.has(version)) {
    const tempt = objRules.setIn([version], OrderedMap({}));

    return tempt;
  }
  return objRules;
}

export function getEventPropertyFromRule(objRules) {
  const response = [];
  const rules = [];
  const ruleOR = safeParse(objRules.OR, []);
  ruleOR.forEach(itemOr => {
    const ruleAND = safeParse(itemOr.AND, []);
    if (ruleAND.length > 0) {
      rules.push(ruleAND);
    }
  });

  if (rules.length === 0) {
    return [];
  }

  rules.forEach(rule => {
    rule.forEach(item => {
      response.push({
        eventPropertyName: item.condition_property_name,
        itemTypeId: item.item_type_id === 0 ? null : item.item_type_id,
      });
    });
  });
  return response;
}

function getRefineWithProperties(refine) {
  const res = [];
  const ruleAND = safeParse(refine.AND, []);
  if (ruleAND.length > 0) {
    ruleAND.forEach(item => {
      res.push({
        eventPropertyName: item.property_name,
        itemTypeId: item.item_type_id,
      });
    });
  }
  return res;
}

export function getConditionTypeFromRule(rulesOR) {
  const output = {
    perf_event: [],
    comp_attr: [],
    map_refine_with_properties: {},
  };

  rulesOR.forEach(ruleOR => {
    const rulesAND = safeParse(ruleOR.AND, []);
    rulesAND.forEach(ruleAND => {
      if (ruleAND.condition_type === 'perf_event') {
        output.perf_event.push({
          eventCategoryId: ruleAND.event_category_id,
          eventActionId: ruleAND.event_action_id,
        });
        // output.map_refine_with_properties.push(
        //   ...getRefineWithProperties(
        //     safeParse(ruleAND.map_refine_with_properties, {}),
        //   ),
        // );
        output.map_refine_with_properties[
          `${ruleAND.event_category_id}-${ruleAND.event_action_id}`
        ] = getRefineWithProperties(
          safeParse(ruleAND.refine_with_properties, {}),
        );
      } else if (ruleAND.condition_type === MAP_SEGMENT_VALUES.comp_attr) {
        output.comp_attr.push(ruleAND.property_name);
      }
    });
  });
  return output;
}

export function getInputLookupFromRule(rulesOR) {
  const output = {
    perf_event: [],
    comp_attr: [],
    data_sources: [],
    map_refine_with_properties: {},
  };
  const arrDataSource = [];

  rulesOR.forEach(ruleOR => {
    const rulesAND = safeParse(ruleOR.AND, []);
    rulesAND.forEach(ruleAND => {
      if (ruleAND.condition_type === 'perf_event') {
        output.perf_event.push({
          eventCategoryId: ruleAND.event_category_id,
          eventActionId: ruleAND.event_action_id,
        });
        // output.map_refine_with_properties.push(
        //   ...getRefineWithProperties(
        //     safeParse(ruleAND.map_refine_with_properties, {}),
        //   ),
        // );
        output.map_refine_with_properties[
          `${ruleAND.event_category_id}-${ruleAND.event_action_id}`
        ] = getRefineWithProperties(
          safeParse(ruleAND.refine_with_properties, {}),
        );
        arrDataSource.push(
          ...getDataSource(safeParse(ruleAND.insight_property_ids, [])),
        );
      } else if (ruleAND.condition_type === MAP_SEGMENT_VALUES.comp_attr) {
        output.comp_attr.push({
          itemTypeId: ruleAND.item_type_id,
          itemPropertyName: ruleAND.property_name,
        });
      }
    });
  });
  output.data_sources = [...new Set(arrDataSource)];
  return output;
}

export const toMigareTimeRangeV1toV2 = dateTime => {
  if (_isEmpty(dateTime)) return null;

  const {
    value = {},
    selection_type = 'custom',
    today_upto = 0,
    yesterday_upto = 0,
    auto_update_to = 'fixed',
    time_step = 0,
    is_tranform_to_custom = false,
  } = dateTime;
  const dataOut = {
    startDate: {
      date: '',
      calculationDate: '',
      value: 0,
      calculationType: '',
      dateType: 'today',
    },
    endDate: {
      date: '',
      calculationDate: '',
      value: 0,
      calculationType: '',
      dateType: 'today',
    },
  };
  const startDate = format(new Date(value.from_date), 'yyyy-MM-dd HH:mm:ss');
  const endDate = format(new Date(value.to_date), 'yyyy-MM-dd HH:mm:ss');
  if (selection_type === 'custom') {
    switch (auto_update_to) {
      case MAP_AUTO_UPDATE.today.value:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.fixed.value,
            startDate,
            calculationDate: '',
            calculationType: '',
            value: 0,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
        });
        break;
      case MAP_AUTO_UPDATE.fixed.value:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.fixed.value,
            startDate,
            calculationDate: '',
            calculationType: '',
            value: 0,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.fixed.value,
            endDate,
            calculationDate: '',
            calculationType: '',
            value: 0,
          },
        });
        break;
      case MAP_AUTO_UPDATE.yesterday.value:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.fixed.value,
            startDate,
            calculationDate: '',
            calculationType: '',
            value: 0,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 1,
          },
        });
        break;
      default:
        break;
    }
  } else {
    switch (selection_type) {
      case 'today':
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
        });
        break;
      case 'yesterday':
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.today.value,
            startDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 1,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 1,
          },
        });
        break;
      case mapRangeTime.this_week_Mon2Today.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: 'first_day_of_week_mon_sun',
            startDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
        });
        break;
      case mapRangeTime.this_week_Sun2Today.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: 'first_day_of_week_sun_sat',
            startDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
        });
        break;
      case mapRangeTime.last_7_days.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.today.value,
            startDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 7,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 1,
          },
        });
        break;
      case mapRangeTime.last_week_Sun2Sat.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: 'first_day_of_week_sun_sat',
            startDate: '',
            calculationDate: 'weeks',
            calculationType: 'minus',
            value: 1,
          },
          end: {
            dataType: 'last_day_of_week_sun_sat',
            endDate: '',
            calculationDate: 'weeks',
            calculationType: 'minus',
            value: 1,
          },
        });
        break;
      case mapRangeTime.last_week_Mon2Sun.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: 'first_day_of_week_mon_sun',
            startDate: '',
            calculationDate: 'weeks',
            calculationType: 'minus',
            value: 1,
          },
          end: {
            dataType: 'last_day_of_week_mon_sun',
            endDate: '',
            calculationDate: 'weeks',
            calculationType: 'minus',
            value: 1,
          },
        });
        break;
      case mapRangeTime.last_14_days.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.today.value,
            startDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 14,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 1,
          },
        });
        break;
      case mapRangeTime.this_month.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: 'first_day_of_month',
            startDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
        });
        break;
      case mapRangeTime.last_30_days.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.today.value,
            startDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 30,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 1,
          },
        });
        break;
      case mapRangeTime.last_month.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: 'first_day_of_month',
            startDate: '',
            calculationDate: 'months',
            calculationType: 'minus',
            value: 1,
          },
          end: {
            dataType: 'last_day_of_month',
            endDate: '',
            calculationDate: 'months',
            calculationType: 'minus',
            value: 1,
          },
        });
        break;
      case mapRangeTime.this_quarter.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: 'first_day_of_quarter',
            startDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
        });
        break;
      case mapRangeTime.last_quarter.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: 'first_day_of_quarter',
            startDate: '',
            calculationDate: 'quarters',
            calculationType: 'minus',
            value: 1,
          },
          end: {
            dataType: 'last_day_of_quarter',
            endDate: '',
            calculationDate: 'quarters',
            calculationType: 'minus',
            value: 1,
          },
        });
        break;
      case mapRangeTime.all_time.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.today.value,
            startDate: '',
            calculationDate: 'years',
            calculationType: 'minus',
            value: 2,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
        });
        break;
      case mapRangeTime.days_up_to_today.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.today.value,
            startDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: today_upto - 1,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          },
        });
        break;
      case mapRangeTime.days_up_to_yesterday.id:
        mapValueTotimeRangeV2({
          dataOut,
          start: {
            dataType: MAP_AUTO_UPDATE.today.value,
            startDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: yesterday_upto,
          },
          end: {
            dataType: MAP_AUTO_UPDATE.today.value,
            endDate: '',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 1,
          },
        });
        break;
      default:
        break;
    }
  }
  return dataOut;
};
function mapValueTotimeRangeV2(data) {
  const { dataOut, start, end } = data;
  dataOut.startDate.dateType = start.dataType;
  dataOut.startDate.date = start.startDate;
  dataOut.startDate.calculationType = start.calculationType;
  dataOut.startDate.calculationDate = start.calculationDate;
  dataOut.startDate.value = start.value;
  dataOut.endDate.dateType = end.dataType;
  dataOut.endDate.calculationType = end.calculationType;
  dataOut.endDate.calculationDate = end.calculationDate;
  dataOut.endDate.date = end.endDate;
  dataOut.endDate.value = end.value;

  return dataOut;
}

export const initTimeRangeDefaultV2 = () => {
  const toDay = new PortalDate();
  const dateStart = new Date(subDays(toDay, 14));
  const dateEnd = new Date(subDays(toDay, 1));
  return {
    startDate: {
      date: format(dateStart.setHours(0, 0, 0), 'yyyy-MM-dd HH:mm:ss'),
      // date: format(
      //   date(subDays(toDay, 14)).setHours(0, 0, 0),
      //   'yyyy-MM-dd HH:mm:ss',
      // ),
      calculationDate: 'days',
      value: 0,
      calculationType: 'minus',
      dateType: 'fixed',
    },
    endDate: {
      // date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      date: format(dateEnd.setHours(23, 59, 59), 'yyyy-MM-dd HH:mm:ss'),
      calculationDate: 'days',
      value: 0,
      calculationType: 'minus',
      dateType: 'fixed',
    },
  };
};
export const initTimeRangeV2 = () => {
  return {
    startDate: {
      date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      calculationDate: 'days',
      value: 0,
      calculationType: 'minus',
      dateType: 'today',
    },
    endDate: {
      date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      calculationDate: 'days',
      value: 0,
      calculationType: 'minus',
      dateType: 'today',
    },
  };
};
export const initFirtTimeValueDatimeV2 = () => {
  return [
    {
      dateType: 'today',
      calculationDate: 'days',
      calculationType: 'minus',
      value: 0,
      // date: `${dayjs().format('YYYYMMDDHHmmss')}`,
      date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      key: 'fromDate',
    },
  ];
};
export const parseDataRangePickAPI = value => {
  const dataOut = [];
  const { endDate, startDate } = value;
  dataOut.push({
    date: startDate.date,
    dateType: startDate.dateType,
    calculationDate: startDate.calculationDate,
    calculationType: startDate.calculationType,
    value: startDate.value,
    key: 'fromDate',
  });
  dataOut.push({
    date: endDate.date,
    dateType: endDate.dateType,
    calculationDate: endDate.calculationDate,
    calculationType: endDate.calculationType,
    value: endDate.value,
    key: 'toDate',
  });
  return dataOut;
};
export const parseDataRangePickUI = value => {
  const dataOut = {};
  if (value && value.length > 0) {
    value.forEach(each => {
      const key = each.key === 'toDate' ? 'endDate' : 'startDate';
      dataOut[key] = {
        // date: `${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}`,
        date: each.date,
        calculationDate: each.calculationDate,
        value: each.value,
        calculationType: each.calculationType,
        dateType: each.dateType,
      };
    });
  }
  return dataOut;
};
export const toMigareOperatorV1toV2 = data => {
  const { operator, time_unit } = data;

  const dataOut = {
    semantics: MAP_SEMATIC.list,
    semantic: getFirstSemantic(MAP_SEMATIC.map),
    operator,
    value: '',
  };
  let splitValue = [];
  if (
    operator === OPERATORS_CODE.BETWEEN ||
    operator === OPERATORS_CODE.BETWEEN_TIME_AGO
  ) {
    splitValue = data.value.split(' AND ');
  }
  switch (operator) {
    case OPERATORS_CODE.EQUAL_TIME_AGO:
    case OPERATORS_CODE.NOT_EQUAL_TIME_AGO:
    case OPERATORS_CODE.AFTER_TIME_AGO:
    case OPERATORS_CODE.BEFORE_TIME_AGO:
      dataOut.operator = mapOperator[operator];
      dataOut.value = [
        {
          dateType: 'today',
          calculationDate: 'days',
          calculationType: 'minus',
          value: data.value,
          date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
          // date: `${dayjs().format('YYYYMMDDHHmmss')}`,
          key: 'fromDate',
        },
      ];
      if (time_unit === 'HOUR') {
        dataOut.semantic = MAP_SEMATIC.map.date_hour;
        dataOut.value = [
          {
            dateType: 'today',
            calculationDate: 'hours',
            calculationType: 'minus',
            value: data.value,
            // date: `${dayjs().format('YYYYMMDDHHmmss')}`,
            date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
            key: 'fromDate',
          },
        ];
      }

      break;
    case OPERATORS_CODE.BETWEEN_TIME_AGO:
      dataOut.operator = mapOperator[operator];
      dataOut.value = [
        {
          dateType: 'today',
          calculationDate: 'days',
          calculationType: 'minus',
          value: splitValue[0],
          date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
          key: 'fromDate',
        },
        {
          dateType: 'today',
          calculationDate: 'days',
          calculationType: 'minus',
          value: splitValue[1],
          date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
          key: 'toDate',
        },
      ];
      if (time_unit === 'HOUR') {
        dataOut.semantic = MAP_SEMATIC.map.date_hour;
        dataOut.value = [
          {
            dateType: 'today',
            calculationDate: 'hours',
            calculationType: 'minus',
            value: 1,
            date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
            // date: `${dayjs().format('YYYYMMDDHHmmss')}`,
            key: 'fromDate',
          },
          {
            dateType: 'today',
            calculationDate: 'hours',
            calculationType: 'minus',
            value: 1,
            date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
            // date: `${dayjs().format('YYYYMMDDHHmmss')}`,
            key: 'toDate',
          },
        ];
      }
      break;
    case OPERATORS_CODE.EQUALS:
    case OPERATORS_CODE.NOT_EQUALS:
    case OPERATORS_CODE.BEFORE_DATE:
    case OPERATORS_CODE.AFTER_DATE:
      dataOut.value = [
        {
          dateType: 'fixed',
          calculationDate: 'days',
          calculationType: 'minus',
          value: 0,
          date: format(new Date(data.value), 'yyyy-MM-dd HH:mm:ss'),
          // date: `${dayjs(data.value).format('YYYYMMDDHHmmss')}`,
          key: 'fromDate',
        },
      ];
      break;
    case OPERATORS_CODE.BETWEEN:
      dataOut.value = [
        {
          dateType: 'fixed',
          calculationDate: 'days',
          calculationType: 'minus',
          value: 0,
          date: format(new Date(Number(splitValue[0])), 'yyyy-MM-dd HH:mm:ss'),
          key: 'fromDate',
        },
        {
          dateType: 'fixed',
          calculationDate: 'days',
          calculationType: 'minus',
          value: 0,
          date: format(new Date(Number(splitValue[1])), 'yyyy-MM-dd HH:mm:ss'),
          key: 'toDate',
        },
      ];
    default:
      break;
  }
  dataOut.operators = getOperatorByProperty(dataOut.semantic);
  return dataOut;
};
const mapOperator = {
  between_time_ago: OPERATORS_CODE.BETWEEN,
  before_time_ago: OPERATORS_CODE.BEFORE_DATE,
  after_time_ago: OPERATORS_CODE.AFTER_DATE,
  equal_time_ago: OPERATORS_CODE.EQUALS,
  not_equal_time_ago: OPERATORS_CODE.NOT_EQUALS,
};
export function filterEventAttrSegment(
  eventPropsList,
  dataTypeInclude,
  dataComputeType,
) {
  let data = [];
  if (!isEmpty(dataTypeInclude)) {
    eventPropsList.forEach(item => {
      if (!isEmpty(item.options)) {
        const temptOptions = [];
        item.options.forEach(option => {
          if (dataTypeInclude.includes(option.itemDataType)) {
            if (COMPUTE_TYPE_NOT_VCF.includes(dataComputeType)) {
              if (option.computeType !== 'custom_function') {
                temptOptions.push(option);
              }
            } else {
              temptOptions.push(option);
            }
          }
        });
        if (temptOptions.length > 0) {
          const temptItem = { ...item, options: temptOptions };
          data.push(temptItem);
        }
      } else if (dataTypeInclude.includes(item.itemDataType)) {
        data.push(item);
      }
    });
  } else {
    data = eventPropsList;
  }

  return data;
}
const COMPUTE_TYPE_NOT_VCF = [
  'most_frequent',
  'first',
  'last',
  'unique_list_count',
  'aggregation',
];
const DATATYPE_INCLUDE = {
  event_counter: [],
  aggregation: ['number'],
  unique_list: ['number', 'string'],
  unique_list_count: ['number', 'string', 'datetime'],
  most_frequent: ['number', 'string'],
  first: ['number', 'string', 'datetime'],
  last: ['number', 'string', 'datetime'],
  default: [],
};
export const getDataInclude = type =>
  DATATYPE_INCLUDE[type] || DATATYPE_INCLUDE.default;
export const DAYSOFMILISECOND = 86400000;
