/* eslint-disable indent */
import styled, { css } from 'styled-components';
import colors from 'utils/colors';
import Grid from '@material-ui/core/Grid';

export const StyleFooter = styled.div`
  background-color: #fff;
  width: 100%;
`;
export const GridStyled = styled(Grid)`
  margin-left: -30px;
`;
export const TextCenter = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const DividerDot = styled.div`
  /*
  * width: container - (container - total left - total right) - paddingRight;
  * */
  width: calc(100% - calc(100% - 150px - 83.333333%) - 15px);

  position: absolute;
  top: 0px;
  height: 1px; /* Height of the line */
  background: repeating-linear-gradient(
    to right,
    #ced3d9 0,
    #ced3d9 1px,
    transparent 2px,
    transparent 6px
  ); /* Adjust the transparent length for spacing */

  ${props =>
    props.nonPosition &&
    css`
      width: 100%;
      position: unset;
    `}
`;

export const StyleWapperListFilter = styled.div`
  position: relative;
  /* .private-overlay {
    padding-bottom: 30px !important;
    padding-top: 30px !important;
  } */
`;
export const StyleGird = styled(Grid)`
  // overflow-y: auto;
  ::-webkit-scrollbar {
    -webkit-appearance: none;
    height: 4px !important;
  }
`;
export const Wrapper = styled.div`
  overflow-x: hidden;
  align-items: center;
  cursor: pointer;
  display: flex;
  min-width: 0;
  /* min-height: 38px; */
  .chip-container {
    margin: 0px 4px 0 0px;
    overflow: hidden;
    font-size: 12px;
    font-family: Roboto-Bold;
    /* background-color: #fff;
    border-radius: 2px;
    border-top: 1px solid rgba(0, 0, 0, 0.12);
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    display: block;
    height: 64px;
    user-select: none; */
  }
`;

export const WrapperPreviewSource = styled.div`
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    position: relative;
    gap: 10px;
  .predicates-container {
    display: flex;
    align-items: center;
    /* ${props => css`
      flex-wrap: ${() => (props.isFilter ? 'wrap' : 'nowrap')};
    `} */
    flex-wrap: wrap;
    text-overflow: ellipsis;
    /* width: 100%; */
    gap: 4px 0;
    /* padding-bottom: 8px; */
    overflow: hidden;
  }
`;

export const MaterialChip = styled.div`
  position: relative;
  font-family: 'Roboto';
  margin: 0;
  background-color: #cae5fe;
  color: #000000;

  ${({ isError }) =>
    isError &&
    `background: #ed5240 !important;
    cursor: not-allowed !important;`}

  border-radius: 999px;
  display: flex;
  padding: 5px 10px;
  align-items: center;
  height: 24px;
  max-width: 122px;

  .content-chip {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &:hover .delete-button {
    display: flex;
  }

  &:hover .content-chip {
    width: calc(100% - 5px);
  }

  .delete-button {
    position: absolute;
    top: 50%;
    right: 0px;
    transform: translate(-6px, -50%);
    display: none;
    border: 0;
    cursor: pointer;
    outline: none;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    width: 16px;
    height: 16px;
    border-radius: 999px;
    cursor: pointer;

    span {
      font-size: 1rem !important;
    }
  }
`;
export const ContainerWrapperInputValue = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  ${({ justifyContent }) =>
    justifyContent && `justify-content: ${justifyContent};`}
  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: solid !important;
  }
`;

export const StyleWrapperInputValue = styled.div`
  max-width: 100%;
  /* max-width: 50%; */
  flex: 1;
  .div-span-and {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    position: unset;
  }
  .private-form__control {
    width: 100%;
  }
  input.MuiInputBase-input {
    padding: 5px 0 5px 5px;
  }
  .MuiInputBase-root {
    width: 100%;
    height: 32px;
  }
  .button-dropdown-ui-select {
    white-space: nowrap;
  }
  .ui-button-dropndown {
    height: 32px;
  }
  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: solid !important;
  }
  .MuiIconButton-label .MuiSvgIcon-root {
    font-size: 16px;
  }
`;

export const StyleSpan = styled.span`
  color: ${colors.tertiary500};
`;

export const DivLoading = styled.div`
  height: 30px;
  width: 20%;
  position: relative;
  .private-overlay {
    padding: 0 !important;
  }
`;

export const StyleSpanAnd = styled.span`
  white-space: nowrap;
  padding: 0 10px;
`;

export const StyledGrid = styled(Grid)`
  .view-mode-value {
    /* margin-left: 20px; */
  }
`;

export const WrapperContent = styled.div`
  display: flex;
  align-items: center;
`;
