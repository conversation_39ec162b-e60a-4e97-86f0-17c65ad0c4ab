import { OBJECT_TYPE } from '../../../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/constant';

export const checkExistItem = (item, allObjects, type, key) => {
  let isExist;
  const listObjectByType = allObjects
    .filter(obj => obj.type === type)
    .find(
      event =>
        event.settings.eventTrackingName ===
        item.get(key || 'property').eventTrackingName,
    );

  return isExist;
};
