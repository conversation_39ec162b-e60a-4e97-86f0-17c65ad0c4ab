/* eslint-disable no-nested-ternary */
/* eslint-disable no-return-assign */
/* eslint-disable react/prop-types */
import React, { useEffect, useMemo, useState } from 'react';
import Grid from '@material-ui/core/Grid';
import SelectTree from 'components/form/UISelectCondition';
import ConditionError from 'containers/Filters/AddFilter/FormCondition/ConditionError';
import ConditionValue from 'containers/Filters/AddFilter/FormCondition/ConditionValue';
import { getTranslateMessage } from 'containers/Translate/util';
import TRANSLATE_KEY from 'messages/constant';
import { connect } from 'react-redux';
import { safeParse } from 'utils/common';
import { STATUS_ITEM_CODE } from 'utils/constants';
import {
  perfEventChangeAggregation,
  perfEventChangeEventAttribute,
  perfEventChangeNumberTime,
  perfEventChangeOperator,
  perfEventChangeProperty,
  perfEventChangeSemantic,
} from '../../action';
import { PERF_EVENT_OPERATORS } from '../../constants';
import { useStyles } from '../GeneralContainerCondition';
import { Title } from '../HeaderCondition/styled';
import {
  ContainerWrapperInputValue,
  GridStyled,
  StyleSpan,
  StyleWrapperInputValue,
} from '../styled';
import { LIST_AGGREGATION_TYPE } from '../../../../../../modules/Dashboard/ApiHub/BusinessObject/Detail/Attributes/Create/Design/Settings/ComputedAttribute/utils.map';
import WrapperDisable from '../../../../../../components/common/WrapperDisableContent';
import { OPERATORS_CODE } from '../../operators';
import InputPreview from '../../../../../../components/Atoms/InputPreview';
import { useGetObjects } from '../../../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/hooks/useGetObjects';
import { OBJECT_TYPE } from '../../../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/constant';
import { SEGMENT_STATUS } from '../../../../../../components/common/constant';

const displayFormat = {
  group: '.',
  decimal: ',',
  decimalPlace: 2,
};

const labelPerfEvent = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_EVENT,
  'Perform event',
);

const labelSelectEvent = getTranslateMessage(
  TRANSLATE_KEY._TITL_PERFORM_EVENT,
  'Select event',
);

const labelEvent = getTranslateMessage(TRANSLATE_KEY._TITL_EVENT, 'Events');
const labelSelectOperator = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_OPERATOR,
  'Select operator',
);

const labelTimes = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_TIME,
  'time(s)',
);
const labelAggregation = getTranslateMessage(
  TRANSLATE_KEY._,
  'Aggregation type',
);
const labelEventAttribute = getTranslateMessage(
  TRANSLATE_KEY._,
  'Select event attribute',
);

const noExistEvent = getTranslateMessage(
  TRANSLATE_KEY._,
  'Event not available',
);

const noExistAttribute = getTranslateMessage(
  TRANSLATE_KEY._,
  'Attribute not available',
);

const styleHeight = { height: '32px' };

const SelectEvent = props => {
  const classes = useStyles();
  const {
    item,
    options,
    property,
    groupIndex,
    itemIndex,
    version,
    divDisabled,
    chipTextViewModeFn = () => {},
    isHasEventOnPortal,
  } = props;

  const [isHasExistEventAttr, setIsHasExistAttr] = useState(true);
  const { allObjects = [] } = useGetObjects();

  useEffect(() => {
    if (props.isViewMode) {
      const eventAttribute = safeParse(props.item.get('eventAttribute'), {});
      const attr = allObjects.find(
        obj =>
          obj.type === OBJECT_TYPE.eventAttribute &&
          obj.eventPropertyName === eventAttribute.propertyName,
      );

      if (attr) {
        const isExist =
          attr.settings && attr.settings.status !== SEGMENT_STATUS.REMOVE;
        setIsHasExistAttr(isExist);
      }
    }
  }, []);

  const changeProperty = value => {
    const data = {
      groupIndex,
      itemIndex,
      value,
    };
    props.changeProperty({ version, data });
    props.callback('PER_EVENT_CHANGE_EVENT_PROPERTY', { value });
    props.callback('UPDATE_CONDITIONS', data);
  };
  const changeEventAttribute = value => {
    const data = {
      groupIndex,
      itemIndex,
      value,
    };
    props.changeEventAttribute({ version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };
  const changeOperator = value => {
    const data = {
      groupIndex,
      itemIndex,
      value,
    };
    props.changeOperator({ version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };
  const changeAggregationtype = value => {
    const data = {
      groupIndex,
      itemIndex,
      value,
    };
    props.changeAggregationType({ version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };
  const changeNumberTime = value => {
    const data = {
      groupIndex,
      itemIndex,
      value,
    };
    if (value.date || value.timeRange) {
      data.datimeV2 = true;
    }
    props.changeNumberTime({ version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };

  const changeNumberTimeOther = (name, value) => {
    const data = {
      groupIndex,
      itemIndex,
      value,
      name,
    };
    props.changeNumberTime({ version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };
  const changeSemantic = value => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
      value,
    };
    props.changeSemantic({ version, data });
    props.callback('UPDATE_CONDITIONS', { version, data });
  };
  // const property = safeParse(props.item.get('property'), null);
  const operator = safeParse(props.item.get('operator'), null);
  const operators = safeParse(props.item.get('operators'), []);
  const computeType = safeParse(props.item.get('computeType'), {});
  const aggregationType = safeParse(props.item.get('aggregationType'), {});
  const eventAttribute = safeParse(props.item.get('eventAttribute'), {});
  const dataType = safeParse(props.item.get('dataType'), null);
  const optionEventAttribute = safeParse(
    props.item.get('optionEventAttribute'),
    [],
  );
  const statusItemCode = safeParse(
    (props.item.get('property') || {}).statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );
  const isShowRowGap = useMemo(() => {
    if (!computeType) return false;
    return ['unique_list_count', 'last', 'first', 'most_frequent'].includes(
      computeType?.value,
    );
  }, [computeType]);
  return (
    <Grid
      container
      item
      xs={12}
      className={classes.paddingX}
      style={{ rowGap: isShowRowGap ? '15px' : '0' }}
    >
      <GridStyled
        item
        xs={12}
        sm={2}
        style={{ alignItems: 'center' }}
        className={classes.commonDisplayTextRight}
      >
        <Title>{labelPerfEvent}</Title>
      </GridStyled>
      {(computeType.value !== 'event_counter' || !props.isViewMode) && (
        <Grid id item xs={4} sm={3} className={classes.paddingLeft}>
          <InputPreview
            type="input"
            isViewMode={props.isViewMode}
            value={property && property.label}
            errors={!isHasEventOnPortal ? [noExistEvent] : []}
          >
            <SelectTree
              onlyParent={props.onlyParent}
              // displayFormat={displayFormat}
              use="tree"
              // isMulti
              // isSearchable
              options={options}
              isParentOpen={props.isParentOpen}
              value={property}
              onChange={changeProperty}
              placeholder={labelSelectEvent}
            />
            <ConditionError
              keyError={1}
              error={props.item.get('error')}
              objectLabel={labelEvent}
              statusItemCode={statusItemCode}
              item={item}
              isError={property === null}
            />
          </InputPreview>
        </Grid>
      )}

      {computeType.value !== 'event_counter' ? (
        <>
          <Grid
            style={{ marginBottom: '15px' }}
            item
            xs={12}
            sm={2}
            className={classes.commonDisplayTextRight}
          >
            {computeType.value === 'aggregation' && (
              <Title>{labelEventAttribute}</Title>
            )}
          </Grid>
          <Grid item xs={4} sm={3} className={classes.paddingLeft}>
            {computeType.value === 'aggregation' && (
              <InputPreview
                type="input"
                isViewMode={props.isViewMode}
                value={eventAttribute.label || eventAttribute.propertyName}
                errors={!isHasExistEventAttr ? [noExistAttribute] : []}
              >
                <SelectTree
                  onlyParent={props.onlyParent}
                  // displayFormat={displayFormat}
                  use="tree"
                  // isMulti
                  // isSearchable
                  options={optionEventAttribute}
                  isParentOpen={props.isParentOpen}
                  value={eventAttribute}
                  onChange={changeEventAttribute}
                  placeholder={labelSelectEvent}
                />
                <ConditionError
                  keyError={1}
                  error={props.item.get('error')}
                  objectLabel={labelEvent}
                  statusItemCode={statusItemCode}
                  item={item}
                  isError={Object.keys(eventAttribute).length === 0}
                />
              </InputPreview>
            )}
          </Grid>
          <Grid item xs={4} sm={1} />
          {computeType.value !== 'aggregation' ? (
            <>
              <GridStyled
                item
                xs={12}
                sm={2}
                className={classes.commonDisplayTextRight}
              >
                <Title>{labelEventAttribute}</Title>
              </GridStyled>
              {!props.isViewMode ? (
                <>
                  <Grid
                    item
                    xs={4}
                    sm={dataType === 'datetime' ? 2 : 3}
                    className={classes.paddingLeft}
                  >
                    <SelectTree
                      onlyParent={props.onlyParent}
                      // displayFormat={displayFormat}
                      use="tree"
                      // isMulti
                      // isSearchable
                      options={optionEventAttribute}
                      isParentOpen={props.isParentOpen}
                      value={eventAttribute}
                      onChange={changeEventAttribute}
                      placeholder={labelSelectEvent}
                    />
                    <ConditionError
                      keyError={1}
                      error={props.item.get('error')}
                      objectLabel={labelEvent}
                      statusItemCode={statusItemCode}
                      item={item}
                      isError={Object.keys(eventAttribute).length === 0}
                    />
                  </Grid>
                  {dataType === 'datetime' && (
                    <Grid item xs={4} sm={2} className={classes.paddingLeft}>
                      <WrapperDisable disabled={divDisabled}>
                        <SelectTree
                          onlyParent={props.onlyParent}
                          use="tree"
                          // isMulti
                          isSearchable={false}
                          options={item.get('semantics')}
                          value={item.get('semantic')}
                          // value={item.get('operator')}
                          onChange={changeSemantic}
                          // placeholder={labelSelecSemantic}
                        />
                        {/* <ConditionError keyError={2} error={error} /> */}
                      </WrapperDisable>
                    </Grid>
                  )}
                </>
              ) : (
                chipTextViewModeFn({
                  item: props.item,
                  property: eventAttribute,
                  isCheckAttribute: isHasExistEventAttr,
                })
              )}
            </>
          ) : (
            <>
              <GridStyled
                item
                xs={12}
                sm={2}
                className={classes.commonDisplayTextRight}
              >
                <Title>{labelAggregation}</Title>
              </GridStyled>
              {!props.isViewMode ? (
                <Grid item xs={4} sm={3} className={classes.paddingLeft}>
                  <SelectTree
                    onlyParent={props.onlyParent}
                    use="tree"
                    // isMulti
                    isSearchable={false}
                    options={LIST_AGGREGATION_TYPE}
                    value={aggregationType}
                    onChange={changeAggregationtype}
                    placeholder={labelSelectOperator}
                  />
                  <ConditionError
                    keyError={1}
                    error={props.item.get('error')}
                    isError={Object.keys(aggregationType).length === 0}
                  />
                </Grid>
              ) : (
                chipTextViewModeFn({
                  item: props.item,
                  property: aggregationType,
                })
              )}
            </>
          )}

          {!props.isViewMode && (
            <>
              <Grid
                item
                xs={4}
                sm={
                  (operator && operator.value === OPERATORS_CODE.BETWEEN) ||
                  (computeType.value === 'unique_list_count' &&
                    dataType === 'datetime')
                    ? 2
                    : 3
                }
                className={classes.paddingLeft}
              >
                <SelectTree
                  onlyParent={props.onlyParent}
                  use="tree"
                  // isMulti
                  isSearchable={false}
                  options={property ? operators.list : []}
                  isParentOpen={props.isParentOpen}
                  value={operator}
                  onChange={changeOperator}
                  placeholder={labelSelectOperator}
                />
                <ConditionError keyError={2} error={props.item.get('error')} />
              </Grid>
              {Object.keys(eventAttribute).length > 0 ? (
                <>
                  <Grid
                    item
                    xs={4}
                    sm={
                      operator &&
                      operator.value === OPERATORS_CODE.BETWEEN &&
                      dataType === 'number'
                        ? 4
                        : 3
                    }
                    className={classes.paddingLeft}
                  >
                    <ContainerWrapperInputValue>
                      <StyleWrapperInputValue>
                        {/* <InputNumberFormat
                      // className="form-control private-form__control"
                      hasNote={false}
                      type="number"
                      value=""
                      onChange={onChangeValue}
                      returnName={false}
                      // placeholder="Input number"
                      translateCode={props.translateCode}
                      displayFormat={displayFormat}
                      name="Input number"
                    /> */}
                        <ConditionValue
                          className="p-left-0"
                          width="100%"
                          maxWidth="100%"
                          item={item}
                          disabled={divDisabled}
                          dataType={item.get('dataType')}
                          onlyParent={props.onlyParent}
                          changeValue={changeNumberTime}
                          changeOtherValue={changeNumberTimeOther}
                          isShowLabel={false}
                          isUseLabel={false}
                          error={props.item.get('error')}
                        />
                      </StyleWrapperInputValue>
                    </ContainerWrapperInputValue>
                  </Grid>
                  {computeType.value === 'unique_list_count' && (
                    <Grid item xs={4} sm={1}>
                      <ContainerWrapperInputValue style={styleHeight}>
                        <StyleSpan>{labelTimes}</StyleSpan>
                      </ContainerWrapperInputValue>
                    </Grid>
                  )}
                </>
              ) : null}
            </>
          )}
        </>
      ) : !props.isViewMode ? (
        <>
          <Grid item xs={4} sm={3} className={classes.paddingLeft}>
            <SelectTree
              onlyParent={props.onlyParent}
              use="tree"
              // isMulti
              isSearchable={false}
              options={property ? PERF_EVENT_OPERATORS : []}
              isParentOpen={props.isParentOpen}
              value={operator}
              onChange={changeOperator}
              placeholder={labelSelectOperator}
            />
            <ConditionError keyError={2} error={props.item.get('error')} />
          </Grid>
          {property ? (
            <>
              <Grid item xs={4} sm={3} className={classes.paddingLeft}>
                <ContainerWrapperInputValue>
                  <StyleWrapperInputValue>
                    {/* <InputNumberFormat
                    // className="form-control private-form__control"
                    hasNote={false}
                    type="number"
                    value=""
                    onChange={onChangeValue}
                    returnName={false}
                    // placeholder="Input number"
                    translateCode={props.translateCode}
                    displayFormat={displayFormat}
                    name="Input number"
                  /> */}
                    <ConditionValue
                      className="p-left-0"
                      width="100%"
                      maxWidth="100%"
                      item={item}
                      disabled={divDisabled}
                      dataType={item.get('dataType')}
                      onlyParent={props.onlyParent}
                      changeValue={changeNumberTime}
                      changeOtherValue={changeNumberTimeOther}
                      isShowLabel={false}
                      isUseLabel={false}
                      error={props.item.get('error')}
                    />
                  </StyleWrapperInputValue>
                </ContainerWrapperInputValue>
              </Grid>
              <Grid item xs={4} sm={1}>
                <ContainerWrapperInputValue style={styleHeight}>
                  <StyleSpan>{labelTimes}</StyleSpan>
                </ContainerWrapperInputValue>
              </Grid>
            </>
          ) : null}
        </>
      ) : (
        chipTextViewModeFn({ item: props.item, isCheckEvent: true })
      )}
    </Grid>
  );
};

const mapDispatchToProps = (dispatch, props) => {
  const MODULE_CONFIG = props.moduleConfig;
  return {
    changeProperty: payload =>
      dispatch(perfEventChangeProperty(MODULE_CONFIG, payload)),
    changeOperator: payload =>
      dispatch(perfEventChangeOperator(MODULE_CONFIG, payload)),
    changeNumberTime: payload =>
      dispatch(perfEventChangeNumberTime(MODULE_CONFIG, payload)),
    changeAggregationType: payload =>
      dispatch(perfEventChangeAggregation(MODULE_CONFIG, payload)),
    changeEventAttribute: payload =>
      dispatch(perfEventChangeEventAttribute(MODULE_CONFIG, payload)),
    changeSemantic: payload =>
      dispatch(perfEventChangeSemantic(MODULE_CONFIG, payload)),
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(React.memo(SelectEvent));
