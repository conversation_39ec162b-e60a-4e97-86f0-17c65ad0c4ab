/* eslint-disable consistent-return */
/* eslint-disable import/order */
/* eslint-disable no-else-return */
/* eslint-disable indent */
/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import { FormHelperText } from '@material-ui/core';
import Grid from '@material-ui/core/Grid';
import {
  UIButton,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import ModalConfirmDeleteSource from 'components/common/UIPerformEvent/BlockConditionPerformEvent/DataSources/ModalConfirmDeleteSource';
import { convertConditionRefineToArrayString } from 'components/common/UIPerformEvent/BlockConditionPerformEvent/DataSources/utils';
import { validatePropertiesRefines } from 'components/common/UIPerformEvent/utils.validate';
import UISuggestionList from 'components/common/UISuggestionList';
import { Nodata } from 'components/form/UISelectCondition/DropdownAction/Tree/styled';
import ConditionError from 'containers/Filters/AddFilter/FormCondition/ConditionError';
import GroupAttribute from 'containers/Filters/AddFilter/GroupAttribute';
import { onSearchFilter } from 'containers/Filters/AddFilter/utils';
import { getTranslateMessage } from 'containers/Translate/util';
import {
  bindMenu,
  bindTrigger,
  usePopupState,
} from 'material-ui-popup-state/hooks';
import TRANSLATE_KEY from 'messages/constant';
import { useImmer } from 'use-immer';
import { STATUS_SOURCE_CODE } from 'utils/constants';
import { useStyles } from '../../GeneralContainerCondition';
import { Title } from '../../HeaderCondition/styled';
import {
  DivLoading,
  GridStyled,
  StyleWapperListFilter,
  WrapperPreviewSource,
} from '../../styled';
import ItemPreviewSourceSelected from './ItemPreviewSourceSelected';
import { useFetchDataByEvent } from './useFetchDataByEvent';

const style = {
  minWidth: '300px',
  // maxWidth: '300px',
  maxHeight: '380px',
  // minHeight: '380px',
  overflowX: 'hidden',
  overflowY: 'auto',
  // fontSize: '13px',
};

const initState = {
  searchValue: '',
  searchList: [],

  open: false,
  tempDataSources: [],
  sourceName: '',
  arrayRefineWillDeleted: [],
  sourceId: 0,
  errors: [],
};

const labelAnySource = getTranslateMessage(
  TRANSLATE_KEY._TITL_IN_ANY_SOURCE,
  'In any source of',
);

const labelAddSource = getTranslateMessage(
  TRANSLATE_KEY._ACT_ADD_SOURCE,
  'Add source',
);

const SelectDataSources = props => {
  let isMounted = true;
  const classes = useStyles();
  const {
    eventValue,
    sourcesSelected,
    isInitDataSources,
    refineWithProperties,
    isHasEventOnPortal,
    isUseTemplateConfig,
  } = props;
  // console.log('props: ', props);
  // console.log('eventValue', eventValue);
  let isDisable = true;

  if (eventValue !== null && typeof eventValue === 'object') {
    if (Object.keys(eventValue).length > 0) {
      isDisable = false;
    }
  }

  const popupStateMenu = usePopupState({
    variant: 'popover',
    popupId: 'menu-filter-popup-popover',
  });

  const [state, setState] = useImmer(initState);

  const { group, isLoading } = useFetchDataByEvent(
    isInitDataSources,
    eventValue,
    sourcesSelected,
    isHasEventOnPortal,
    isUseTemplateConfig,
  );
  useEffect(
    () => () => {
      isMounted = false;
    },
    [],
  );

  useEffect(() => {
    if (!isLoading) {
      let arr = [];

      group.list.forEach(tmp => {
        arr.push(tmp.value);
      });
      const isAllSourceNoExist = sourcesSelected.every(
        source => !group.map[source],
      );

      // use case khi use template tatic ở portal khác
      // và tồn tại event sẽ sử dụng tất cả các source hiện có có trên portal này chứ ko dùng các source trong config của template
      if (isUseTemplateConfig && !isHasEventOnPortal) {
        arr = [];
      }

      if (
        isInitDataSources !== true ||
        (isUseTemplateConfig && isAllSourceNoExist)
      ) {
        props.onChange({
          dataSourceSelected: arr,
          temp: false,
          isDeleteRefine: false,
        });
      }
    }
  }, [group, isLoading]);
  useEffect(() => {
    if (group.list.length > 0) {
      let errors = [];
      sourcesSelected.forEach(each => {
        const item = group.map[each];
        if (
          item &&
          item.statusItemCode === STATUS_SOURCE_CODE.NO_LONGER_ASSIGN_EVENT
        ) {
          errors = [
            'The source colored with red no more exists in any of model-source',
          ];
        }
      });
      setState(draft => {
        draft.errors = errors;
      });
    }
  }, [sourcesSelected, group]);

  const callback = (type, data) => {
    if (type === 'DELETE_SOURCE_ITEM') {
      const array = sourcesSelected;
      const { inValid, refineBelongtoSourceId } = validatePropertiesRefines(
        refineWithProperties,
        array[data],
      );

      if (
        refineWithProperties.size === 0 ||
        inValid ||
        !refineBelongtoSourceId
      ) {
        props.onChange({
          dataSourceSelected: [
            ...array.slice(0, data),
            ...array.slice(data + 1),
          ],
          isDeleteRefine: false,
        });
      } else {
        let sourceName = '--';
        const sourceId = array[data];
        const tempData = [...array.slice(0, data), ...array.slice(data + 1)];

        if (group.map[sourceId]) {
          sourceName = group.map[sourceId].label;
        }

        setState(draft => {
          draft.open = true;
          draft.sourceName = sourceName;
          draft.sourceId = sourceId;
          draft.arrayRefineWillDeleted = convertConditionRefineToArrayString(
            refineWithProperties,
            sourceId,
            tempData,
          );
          draft.tempDataSources = tempData;
        });
      }
    } else if (type === 'SELECT_ATRR') {
      popupStateMenu.close();
      const array = sourcesSelected;
      array.push(data.value);
      props.onChange({ dataSourceSelected: [...array], isDeleteRefine: false });
    } else if (type === 'CONFIRM_DELETE_SOURCE') {
      if (data === true) {
        props.onChange({
          dataSourceSelected: state.tempDataSources,
          isDeleteRefine: true,
          sourceIdWillDeleted: state.sourceId,
        });
      }
    }
    // onChange;
  };

  const onSearch = valueSearch => {
    const textSearch = valueSearch.trim().toLowerCase();
    setState(draft => {
      draft.searchValue = valueSearch;
    });
    if (textSearch.length > 0) {
      const { arrayGroupAttrs } = onSearchFilter(group.list, [], valueSearch);
      setState(draft => {
        draft.searchList = arrayGroupAttrs;
      });
    } else {
      setState(draft => {
        draft.searchList = [];
      });
    }
  };

  const toggleModal = () => {
    setState(draft => {
      draft.open = false;
    });
  };

  if (!isMounted) {
    return null;
  }

  const renderContentSelectDataSources = () => {
    if (!isDisable && isLoading && isMounted) {
      return (
        <DivLoading>
          <Loading isLoading={isLoading} size={20} />
        </DivLoading>
      );
    }

    return (
      <WrapperDisable disabled={isDisable}>
        {sourcesSelected.map((source, index) => {
          const tmp = group.map[source] || {};
          const isError =
            tmp.statusItemCode === STATUS_SOURCE_CODE.NO_LONGER_ASSIGN_EVENT;
          if (tmp !== undefined) {
            return (
              <ItemPreviewSourceSelected
                index={index}
                label={tmp.label}
                value={tmp.value}
                callback={callback}
                item={tmp}
                key={source}
                isError={isError}
                isViewMode={props.isViewMode}
              />
            );
          }
        })}
        {!props.isViewMode && (
          <UIButton
            variant="contained"
            {...bindTrigger(popupStateMenu)}
            iconName="add"
            reverse
            theme="outline"
            borderRadius="1rem"
            // style={styleButton}
          >
            {labelAddSource}
          </UIButton>
        )}
        <UISuggestionList
          popoverProps={{
            ...bindMenu(popupStateMenu),
          }}
          searchProps={{
            onChange: onSearch,
            value: state.searchValue,
          }}
          style={style}
        >
          <StyleWapperListFilter style={{ maxWidth: 300, overflow: 'hidden' }}>
            {group.list.length === 0 ||
            (state.searchValue.length > 0 && state.searchList.length === 0) ? (
              <Nodata height="150px">
                {getTranslateMessage(TRANSLATE_KEY._INFO_NO_DATA, 'No data')}
              </Nodata>
            ) : null}
            {state.searchValue.length > 0 ? (
              <GroupAttribute
                // hasTopBorder={isRenderBorder()}
                groups={state.searchList}
                callback={callback}
                itemsDisabled={sourcesSelected}
              />
            ) : (
              <GroupAttribute
                // hasTopBorder={isRenderBorder()}
                groups={group.list}
                callback={callback}
                itemsDisabled={sourcesSelected}
              />
            )}
          </StyleWapperListFilter>
        </UISuggestionList>
      </WrapperDisable>
    );
  };

  return (
    <Grid container item xs={12} className={classes.paddingX}>
      <GridStyled
        item
        xs={12}
        sm={2}
        className={classes.commonDisplayTextRight}
      >
        <Title>{labelAnySource}</Title>
      </GridStyled>
      <Grid item xs={12} sm={10} className={classes.paddingLeft}>
        <WrapperPreviewSource>
          {renderContentSelectDataSources()}
        </WrapperPreviewSource>
        <ConditionError keyError={3} error={props.error} />
        <FormHelperText id="component-helper-text" error={!!state.errors[0]}>
          {state.errors}
        </FormHelperText>
      </Grid>
      <ModalConfirmDeleteSource
        isOpen={state.open}
        toggle={toggleModal}
        callback={callback}
        sourceName={state.sourceName}
        conditionRefine={state.arrayRefineWillDeleted}
      />
    </Grid>
  );
};

SelectDataSources.defaultProps = {
  sourcesSelected: [],
};
export default SelectDataSources;
