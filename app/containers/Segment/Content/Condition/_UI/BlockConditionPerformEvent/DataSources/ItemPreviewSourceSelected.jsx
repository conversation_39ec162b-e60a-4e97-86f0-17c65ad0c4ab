/* eslint-disable react/prop-types */
import React from 'react';
import { UITippy } from '@xlab-team/ui-components';

import { Icon } from '@antscorp/antsomi-ui';
import { MaterialChip, Wrapper } from '../../styled';

const ItemPreviewSourceSelected = props => {
  const { index, label, value, isError } = props;

  const onClickButtonDelete = () => {
    props.callback('DELETE_SOURCE_ITEM', index);
  };

  return (
    <div
      // className="predicates-container m-bottom-1"
      className="predicates-container"
      key={`${label}-${index.toString()}`}
    >
      <Wrapper id={`item-rule-${value}`}>
        <div className="chip-container" style={{ margin: 0 }}>
          <UITippy content={label} arrow distance={10}>
            <MaterialChip variant="contained" isError={isError}>
              <div className="content-chip">{label}</div>
              {!props.isViewMode && (
                <div className="delete-button">
                  <Icon
                    onClick={onClickButtonDelete}
                    type="icon-ants-remove-light"
                    size={10}
                    color="#005EB8"
                  />
                </div>
              )}
            </MaterialChip>
          </UITippy>
        </div>
      </Wrapper>
    </div>
  );
};

export default ItemPreviewSourceSelected;
