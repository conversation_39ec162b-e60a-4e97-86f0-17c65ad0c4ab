/* eslint-disable no-empty */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import Grid from '@material-ui/core/Grid';
import {
  UIWrapperDisable as WrapperDisable,
  UITippy,
} from '@xlab-team/ui-components';
import { getTranslateMessage } from 'containers/Translate/util';
import TRANSLATE_KEY from 'messages/constant';
import { connect } from 'react-redux';
import styled from 'styled-components';
import { useImmer } from 'use-immer';
import { safeParse } from 'utils/common';
import {
  STATUS_ITEM_CODE,
  ARCHIVE_STATUS,
  REMOVE_STATUS,
  TYPE_ATTRIBUTE,
} from 'utils/constants';
import { perfEventChangeSource } from '../../action';
import ContainerCondition, { useStyles } from '../GeneralContainerCondition';
import HeaderCondition from '../HeaderCondition';
import DataSources from './DataSources';
import Refine from './Refine/index';
import SelectEvent from './SelectEvent';
import TimeRange from './TimeRange';
import { formatValueItemByDataType } from '../../../../../Filters/ItemRule/Preview/utils';
import {
  filterDeep,
  makeArrayToLabelFilter,
} from '../../../../../../utils/web/utils';
import { Chip } from '@material-ui/core';
import {
  SelectedConditionTextMode,
  SubContentTooltip,
} from '../../../../../../components/common/UINodeFilter/styled';
import ConditionError from '../../../../../Filters/AddFilter/FormCondition/ConditionError';
import {
  getLabelFull,
  getLabelMulti,
} from '../../../../../../components/form/AutoSuggestion/utils';
import { StyledGrid, GridStyled, StyleSpan, WrapperContent } from '../styled';
import { useGetObjects } from '../../../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/hooks/useGetObjects';
import { OBJECT_TYPE } from '../../../../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/constant';
import { labelLeftV2 } from 'modules/Dashboard/Profile/Segment/Create/constants';
import { TreeLabelValueContainer } from '../../../../../../components/form/UISelectInline/DropdownAction/Tree/styled';
import Icon from '@antscorp/icons/main';
import moment from 'moment';
import { DAYSOFMILISECOND } from '../../utils';

// const labelInclude = getTranslateMessage(
//   TRANSLATE_KEY._TITL_INCLUDE_PEOPLE,
//   'Include people that',
// );
// const labelOrInclude = getTranslateMessage(
//   TRANSLATE_KEY._TITL_OR_MATCH_PEOPLE,
//   'Or match people that',
// );

export const Title = styled.div`
  color: #7f7f7f;
`;

const PERF_EVENT_TIME_WINDOW = [
  {
    value: 'last',
    label: 'within last',
    translateCode: TRANSLATE_KEY._DURATION_WITHIN_LAST,
  },
  {
    value: 'between',
    label: 'between',
    translateCode: TRANSLATE_KEY._DURATION_BETWEEN,
  },
];

const labelPerfEvent = getTranslateMessage(
  TRANSLATE_KEY._TITL_PERFORM_EVENT,
  'Perform event',
);

const labelAnd = getTranslateMessage(TRANSLATE_KEY._ACT_AND, 'And');

const OPRETORS = [
  { label: 'Contains', value: 'contains' },
  { label: 'Equal', value: 'equal' },
  { label: 'Not is equal', value: 'not_is_equal' },
];

const BlockConditionPerformEvent = props => {
  let isMounted = true;
  const {
    item,
    options,
    groupIndex,
    itemIndex,
    version,
    computeSchedule,
    useForecastSession = false,
  } = props;
  const { list: listEvents, map: mapEvents } = options;

  const { allObjects = [] } = useGetObjects();

  const classes = useStyles();
  const currentDate = new Date();
  const [state, setState] = useImmer({
    searchValue: '',
    valueInput: '',
    dataSources: {
      isLoading: false,
      list: [],
      map: {},
      sourcesSelected: [],
    },
    eventsSchema: {
      list: [],
      map: {},
    },
    dataEvents: {
      list: [],
      value: {},
      map: {},
    },
    dataOperators: {
      list: OPRETORS,
      value: {},
    },
    timeWindows: {
      list: PERF_EVENT_TIME_WINDOW,
      value: PERF_EVENT_TIME_WINDOW[0],
    },
    tempValue: {
      dataSources: {
        list: [],
        map: {},
      },
    },
    isHasEventOnPortal: true, // use case -- use template on other portal
  });

  const gridRef = useRef(0);

  const { left, right } = useMemo(() => {
    if (props.isFirstElement) {
      const fcStyle = { padding: '4px 50px 10px 10px' };

      return {
        left: {
          paddingTop: 4,
          paddingBottom: 10,
          fontSize: '12px',
          color: '#000000',
        },
        right: useForecastSession ? fcStyle : { padding: '4px 15px 10px 0px' },
      };
    }

    const leftStyle = {
      paddingBottom: 10,
      paddingRight: useForecastSession ? 50 : 15,
    };
    const rightStyle = {
      paddingBottom: 10,
      paddingLeft: useForecastSession ? 10 : 0,
      paddingRight: useForecastSession ? 50 : 15,
    };

    return {
      left: leftStyle,
      right: rightStyle,
    };
  }, [props.isFirstElement, useForecastSession]);

  const filteredOptions = useMemo(
    () =>
      filterDeep(
        options,
        (_value, _key, option) => {
          const { status, type } = option;

          return (
            ![ARCHIVE_STATUS, REMOVE_STATUS].includes(Number(status)) ||
            type !== TYPE_ATTRIBUTE.COMPUTED
          );
        },
        {
          childrenPath: 'options',
        },
      ),
    [options],
  );

  useEffect(() => {
    if (isMounted) {
      setState(draft => {
        draft.dataEvents.list = listEvents;
        draft.dataEvents.map = mapEvents;
      });
    }

    return () => {
      isMounted = false;
    };
  }, [listEvents]);

  useEffect(() => {
    if (props.isViewMode && allObjects && allObjects.length) {
      const eventObject = allObjects.find(
        obj =>
          obj.type === OBJECT_TYPE.event &&
          obj.settings?.eventTrackingName ===
            props.item.get('property').eventTrackingCode,
      );

      if (
        eventObject &&
        eventObject.settings &&
        eventObject.settings.status === 3
      ) {
        setState(draft => {
          draft.isHasEventOnPortal = false;
        });
      }
    }
  }, [allObjects]);

  const callback = (type, data) => {
    // console.log('type, data ===>', type, data);
    // if (type === 'SELECT_ATRR') {
    //   setState(draft => {
    //     draft.dataEvents.value = data;
    //   });
    // } else if (type === 'CLOSE_MODAL') {
    // } else if (type === 'DELETE_ITEM') {
    //   console.log('DELETE_ITEM, data ===>', type, data);
    // } else {
    //   if (type === 'ADD_ITEM') {
    //   }
    //   props.callback(type, data);
    // }
    props.callback(type, data);
  };

  const changeSource = (value, isCallback = true) => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
      value,
    };
    props.changeSource({ version, data });
    if (isCallback) {
      props.callback('UPDATE_CONDITIONS', data);
    }
  };

  const deleteItem = useCallback(() => {
    props.callback('DELETE_ITEM', { groupIndex, itemIndex });
  }, []);

  const property = safeParse(props.item.get('property'), null);
  // const error = item.get('error');
  const statusItemCode = safeParse(
    (props.item.get('property') || {}).statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );
  const divDisabled = statusItemCode !== STATUS_ITEM_CODE.ACTIVE;
  // console.log('statusItemCode', statusItemCode, error, property);
  let labelLeft = props.labels.include;
  let isShowLabelAND = false;

  if (!props.isFirstBlock) {
    if (props.isFirstElement) {
      labelLeft = props.labels.orInclude;
    } else {
      labelLeft = labelAnd;
      isShowLabelAND = true;
    }
  } else if (props.isFirstBlock && !props.isFirstElement) {
    labelLeft = labelAnd;
    isShowLabelAND = true;
  }

  // console.log("item.get('refineWithProperties')", JSON.stringify(item.get('refineWithProperties')));
  const getSelectedConditionTextMode = info => {
    const {
      item: listItem,
      property: moreProperty,
      isCheckEvent = false,
    } = info;

    const operator = safeParse(listItem.get('operator'), null);
    const propertyData =
      moreProperty || safeParse(listItem.get('property'), null);
    const initValue = formatValueItemByDataType(listItem, true, true);
    const value = Array.isArray(initValue)
      ? makeArrayToLabelFilter(initValue)
      : initValue;

    const statusItem = safeParse(
      (listItem.get('property') || {}).statusItemCode,
      STATUS_ITEM_CODE.ACTIVE,
    );
    if (
      operator &&
      [
        'after_date',
        'before_date',
        'contains',
        'doesnt_contain',
        'start_with',
        'not_start_with',
        'end_with',
        'not_end_with',
        'equals',
        'not_equals',
      ].includes(operator.value)
    ) {
      return (
        <div style={{ marginLeft: 15 }}>
          <Chip
            className="view-mode-value"
            variant="outlined"
            label={
              <SelectedConditionTextMode>
                <span className="property-text">
                  {propertyData.label}&nbsp;
                </span>
                <span className="operator-text">{operator.label}&nbsp;</span>
                <span className="property-text">{`"${value}"`}</span>
                {/* <span style={{ position: 'relative', top: '2px' }}>
                  <ConditionError
                    style={{ width: '100%' }}
                    statusItemCode={statusItem}
                    item={listItem}
                    isErrorIcon
                    isCheckEvent={isCheckEvent}
                  />
                </span> */}
              </SelectedConditionTextMode>
            }
          />
          <div
            style={{ marginLeft: '10px' }}
            className="is--text--error p-top-1"
          >
            <ConditionError
              style={{ width: '100%' }}
              statusItemCode={statusItem}
              item={listItem}
              isErrorIcon
            />
          </div>
        </div>
      );
    }

    if (operator && ['matches', 'not_matches'].includes(operator.value)) {
      const label = safeParse(listItem.get('value'), null);
      const valueMulti = getLabelMulti(
        label,
        gridRef && gridRef.current && gridRef.current.offsetWidth,
        true,
        ['array_string', 'string'].includes(propertyData.dataType),
      );

      const valueTooltip = getLabelFull(
        label,
        true,
        ['array_string', 'string'].includes(propertyData.dataType),
      );

      return (
        <div style={{ marginLeft: 15 }}>
          <Chip
            className="view-mode-value"
            variant="outlined"
            label={
              <SelectedConditionTextMode>
                <span className="property-text">{`${
                  filteredOptions[0] ? `${filteredOptions[0].label} >>` : ''
                } ${propertyData.label} `}</span>
                <span className="operator-text">{operator.label}&nbsp;</span>
                <span className="property-text">{valueMulti.labels}&nbsp;</span>
                {valueMulti.labelMore && (
                  <>
                    <span className="operator-text">and</span>
                    <UITippy
                      content={valueTooltip.labels}
                      showPopupWhenClick
                      subContent={
                        <SubContentTooltip>
                          Click <span>{valueMulti.labelMore}</span> to view more
                        </SubContentTooltip>
                      }
                    >
                      <span className="text-more">
                        {valueMulti.labelMore}&nbsp;
                      </span>
                    </UITippy>
                  </>
                )}
                {/* <span style={{ position: 'relative', top: '2px' }}>
                  <ConditionError
                    style={{ width: '100%' }}
                    statusItemCode={statusItem}
                    item={listItem}
                    isErrorIcon
                  />
                </span> */}
              </SelectedConditionTextMode>
            }
          />
        </div>
      );
    }

    return (
      <div style={{ marginLeft: 15 }}>
        <Chip
          className="view-mode-value"
          variant="outlined"
          label={
            <SelectedConditionTextMode>
              <span className="property-text">{propertyData.label}&nbsp;</span>
              <span className="operator-text">
                {operator.label.replace(/\bis\b/gi, '').trim()}&nbsp;
              </span>
              <span className="property-text">{value}</span>
              {/* <span style={{ position: 'relative', top: '2px' }}>
                <ConditionError
                  style={{ width: '100%' }}
                  statusItemCode={statusItem}
                  item={listItem}
                  isErrorIcon
                />
              </span> */}
            </SelectedConditionTextMode>
          }
        />
        <div>
          <ConditionError
            style={{ width: '100%' }}
            statusItemCode={statusItem}
            item={listItem}
            isErrorIcon
            isCheckEvent={isCheckEvent}
            objectLabel={isCheckEvent ? 'Event' : ''}
          />
        </div>
        {propertyData && Number(propertyData.status) === 4 && (
          <div
            style={{ marginLeft: '10px' }}
            className="is--text--error p-top-1"
          >
            This attribute is archived
          </div>
        )}
      </div>
    );
  };

  const renderBlockLeft = useCallback(() => {
    if (isShowLabelAND) {
      return (
        <>
          <Grid container>
            <Grid
              item
              xs={2}
              style={useForecastSession ? left : { ...labelLeftV2, ...left }}
            />
            <Grid
              item
              xs={10}
              style={{
                paddingBottom: 10,
                color: '#000000',
                fontSize: '12px',
                paddingLeft: useForecastSession ? 10 : 0,
              }}
            >
              {labelLeft}
              {/* <span style={{ color: 'red' }}> *</span> */}
            </Grid>
          </Grid>

          <Grid
            item
            xs={2}
            style={useForecastSession ? left : { ...labelLeftV2, ...left }}
          />
        </>
      );
    }

    return (
      <Grid
        item
        xs={2}
        style={useForecastSession ? left : { ...labelLeftV2, ...left }}
      >
        {labelLeft}
        <span style={{ color: 'red' }}> *</span>
      </Grid>
    );
  }, [labelLeft, left, isShowLabelAND, useForecastSession]);

  return (
    <>
      {renderBlockLeft()}
      <Grid item xs={10} style={right}>
        <ContainerCondition>
          <HeaderCondition
            item={item}
            label={labelPerfEvent}
            showButtonClose={!props.isViewMode}
            onClick={deleteItem}
          />
          <WrapperDisable disabled={props.isViewMode ? false : divDisabled}>
            <StyledGrid
              container
              item
              xs={12}
              // className="m-top-1 p-bottom-3"
              style={{ overflowY: 'auto', padding: '15px 0', gap: '15px' }}
            >
              <SelectEvent
                callback={callback}
                options={options}
                item={item}
                property={property}
                moduleConfig={props.moduleConfig}
                groupIndex={groupIndex}
                itemIndex={itemIndex}
                isViewMode={props.isViewMode}
                chipTextViewModeFn={getSelectedConditionTextMode}
                isHasEventOnPortal={state.isHasEventOnPortal}
                isUseTemplateConfig={props.isUseTemplateConfig}
                // mapEventsTracking={dataEventsTracking.map}
              />

              <DataSources
                eventValue={property}
                sourcesSelected={item.get('dataSources')}
                error={item.get('errorDataSources')}
                isInitDataSources={item.get('isInitDataSources')}
                onChange={changeSource}
                refineWithProperties={item.get('refineWithProperties')}
                isViewMode={props.isViewMode}
                isHasEventOnPortal={state.isHasEventOnPortal}
                isUseTemplateConfig={props.isUseTemplateConfig}
              />
              <Refine
                // title="Where"
                // callback={callback}
                refineWithProperties={item.get('refineWithProperties')}
                moduleConfig={props.moduleConfig}
                isJNTactic={props.isJNTactic}
                groupIndex={groupIndex}
                itemIndex={itemIndex}
                eventValue={property}
                callback={callback}
                sourcesSelected={item.get('dataSources')}
                disabledEventConditions={
                  props.disabledEventConditions ||
                  (item.get('dataSources') &&
                    item.get('dataSources').length === 0)
                }
                isViewMode={props.isViewMode}
                chipTextViewModeFn={getSelectedConditionTextMode}
              />
              {/* <TimeWindow
                callback={callback}
                timeWindow={item.get('timeWindow')}
                moduleConfig={props.moduleConfig}
                groupIndex={groupIndex}
                itemIndex={itemIndex}
              /> */}
              <TimeRange
                callback={callback}
                initData={item.get('timeRange')}
                moduleConfig={props.moduleConfig}
                groupIndex={groupIndex}
                itemIndex={itemIndex}
                computeSchedule={computeSchedule}
                isViewMode={props.isViewMode}
                styleContent={
                  props.isViewMode && {
                    marginTop: '-7px',
                  }
                }
              />
              {property && (
                <Grid container item xs={12} className={classes.paddingX}>
                  <GridStyled
                    item
                    xs={2}
                    className={`${classes.commonDisplayTextRight} height-100`}
                    // sm={1}
                  />
                  <Grid
                    className={classes.paddingLeft}
                    style={{ marginTop: '-5px' }}
                    item
                    xs={10}
                  >
                    <WrapperContent>
                      <Icon
                        type="icon-ants-info-outline"
                        style={{
                          color: '#FFBF60',
                          fontSize: '14px',
                          marginRight: '10px',
                        }}
                      />
                      <StyleSpan>
                        {`Data for this condition is only taken within TTL range (${moment(
                          currentDate.getTime() -
                            (property.ttl - 1) * DAYSOFMILISECOND,
                        ).format('ll')} - ${moment().format('ll')})`}
                      </StyleSpan>
                    </WrapperContent>
                  </Grid>
                </Grid>
              )}
            </StyledGrid>
            {/* <div className="p-x-6 p-top-0 p-bottom-6">
              <ConditionError item={item} statusItemCode={statusItemCode} />
            </div> */}
          </WrapperDisable>
        </ContainerCondition>
      </Grid>
    </>
  );
};

const mapDispatchToProps = (dispatch, props) => {
  const MODULE_CONFIG = props.moduleConfig;
  return {
    changeSource: payload =>
      dispatch(perfEventChangeSource(MODULE_CONFIG, payload)),
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(React.memo(BlockConditionPerformEvent));
