/* eslint-disable no-cond-assign */
/* eslint-disable no-param-reassign */
import parse from 'html-react-parser';
import { addMessageToQueue } from '../../utils/web/queue';
import {
  getObjectPropSafely,
  random,
} from '../../components/common/UIEditorPersonalization/utils.3rd';
import { safeParse } from '../../utils/common';
import { REGEX_CONVERT_CSS } from './constants';
import { patternHandlers, TAG_TYPE } from '@antscorp/antsomi-ui';
import {
  genShortLinkDemo,
  genShortLinkV2Demo,
} from '../../components/common/UIEditorPersonalization/Input/utils';

/**
 * Replaces `&nbsp;` entities and processes merge tags within the input string.
 * Handles different data types including arrays and objects by recursively
 * applying replacements. Specifically processes strings to substitute merge
 * tags with visual representations or personalized short links based on
 * specified tag types. If an error occurs during processing, it logs the error
 * and returns the original input.
 *
 * @param {string|Array|Object} str - The input to process, can be a string,
 *                                    array, or object.
 * @param {Object} personalizationData - Data used for generating personalized
 *                                       short links.
 * @returns {string|Array|Object} - The processed input with replacements made,
 *                                  maintaining the original input type.
 */
// Danh sách các thẻ hợp lệ
const validTags = [
  'html',
  'head',
  'body',
  'h1',
  'h2',
  'h3',
  'h4',
  'h5',
  'h6',
  'p',
  'br',
  'hr',
  'pre',
  'b',
  'i',
  'u',
  'strong',
  'em',
  'mark',
  'small',
  'del',
  'ins',
  'sub',
  'sup',
  'ul',
  'ol',
  'li',
  'dl',
  'dt',
  'dd',
  'img',
  'audio',
  'video',
  'source',
  'track',
  'picture',
  'a',
  'link',
  'table',
  'caption',
  'thead',
  'tbody',
  'tfoot',
  'tr',
  'td',
  'th',
  'form',
  'input',
  'textarea',
  'button',
  'select',
  'option',
  'label',
  'fieldset',
  'legend',
  'header',
  'footer',
  'article',
  'section',
  'nav',
  'aside',
  'main',
  'figure',
  'figcaption',
  'details',
  'summary',
  'dialog',
  'menu',
  'style',
  'script',
  'meta',
  'title',
  'base',
  'span',
  'div',
  'iframe',
  'embed',
  'object',
];

/**
 * Kiểm tra và parse HTML.
 * @param {string} html - Chuỗi HTML đầu vào.
 * @returns {ReactNode|string} - Nội dung parse được hoặc chuỗi input nếu không hợp lệ.
 */
const sanitizeHTML = html => {
  try {
    // Regular expression để tìm tất cả các thẻ trong chuỗi
    const tagRegex = /<\s*\/?\s*([^\s<>\/]+)/g;
    let match;
    while ((match = tagRegex.exec(html)) !== null) {
      const tagName = match[1].toLowerCase();
      // Kiểm tra thẻ có hợp lệ hay không
      if (!validTags.includes(tagName)) {
        // Nếu tìm thấy thẻ không hợp lệ, trả về chuỗi gốc
        return html; // Trả về text input
      }
    }
    // Nếu tất cả thẻ hợp lệ, parse và trả về kết quả
    return parse(html);
  } catch (e) {
    // Lỗi khi parse cú pháp
    return html;
  }
};

export const replaceNbsps = (str, personalizationData) => {
  try {
    if (!str) return str;

    if (Array.isArray(str)) {
      str.forEach((item, idx) => {
        str[idx] = replaceNbsps(item, personalizationData);
      });
    } else if (typeof str === 'object') {
      Object.keys(str).forEach(key => {
        str[key] = replaceNbsps(str[key], personalizationData);
      });
    } else if (typeof str === 'string') {
      let result = str;

      // Substitude merge tags visual representation in the preview.
      Object.values(patternHandlers).forEach(patternInfo => {
        let match;

        const { pattern, handler } = patternInfo;
        const regex = new RegExp(pattern, 'g');

        while ((match = regex.exec(str)) !== null) {
          const { isValid, tagData } = handler(match);

          if (!isValid || !tagData) return;

          if (tagData.type === TAG_TYPE.SHORT_LINK) {
            result = result.replace(
              tagData.value,
              genShortLinkDemo(tagData.value),
            );
          }
          if (tagData.type === TAG_TYPE.SHORT_LINK_V2) {
            result = result.replace(
              tagData.value,
              genShortLinkV2Demo(
                tagData.value,
                tagData.shortener,
                personalizationData,
              ),
            );
          }
        }
      });

      return sanitizeHTML(result);
    }
  } catch (error) {
    addMessageToQueue({
      func: 'replaceNbsps',
      path: 'app/containers/UIPreview/utils.js',
      data: error.stack,
    });
  }

  return str;
};

export const getInsertPattern = (id, code, label, bgColor) => {
  return `<button id="${id}" contenteditable="false" class="insert-word" style="padding: 5px 8px; color: white; font-size: inherit; outline: none; border: 1px solid transparent; border-radius: 1.2rem; min-height: 24px; margin-top: 2px; cursor: pointer; background: ${bgColor}" value=${code
    .replace(/\s/gm, '&nbsp;')
    .replace(
      /"/gm,
      '&quot;',
    )}>${label}<em class="editor-icon-remove" style="color: white;font-style: normal; marign-left: 4px; position: relative; z-index: 1;font-size: 1rem;font-weight: bold">x</em></button>`;
};
export const convertHtmlText = (
  htmlText,
  format = 'raw',
  state,
  objectWidgetInput = {},
) => {
  // console.log('objectWidgetInput', objectWidgetInput);
  const result = htmlText;
  try {
    if (format && format.toLowerCase() === 'raw') {
      // pattern find all button insert word
      const regexInsertPattern = /<button .*?class="insert-word.*?".*? value=(.*?)>(.*?)<\/button>/gim;
      // pattern find all symbol `"`
      const regexQuot = /&quot;/gm;
      const regexSpace = /&nbsp;/gm;
      const regexSpaceV2 = /nbsp;/gm;
      const replacer = (_, p1) => {
        // replace `"` from value -> replace &quot; -> replace &nbsp;
        return p1
          .replace(/"/gm, '')
          .replace(regexQuot, '"')
          .replace(regexSpace, ' ')
          .replace(regexSpaceV2, ' ')
          .replace(/&amp;/gm, '');
      };
      if (result === null || result === undefined) {
        return htmlText;
      }
      try {
        return result.replace(regexInsertPattern, replacer);
      } catch (error) {
        addMessageToQueue({
          path: 'app/components/common/UIEditorPersonalization/utils.js',
          func: 'convertHtmlText 1',
          data: error.stack,
        });
        return htmlText;
      }
    }
    if (format && format.toLowerCase() === 'html') {
      const insertPattern = /#{.*?}/gm;
      if (insertPattern.test(htmlText)) {
        const htmlReplacer = matcher => {
          // const regex = /(\w+.\w+)|(\(\w+)\)|"(.*?)"/gmi;
          // const regex = /(\w+.\w+)|(\(\w+)\)|"(.*?)"/gim;
          // const regex = /(\w+)|(\(\w+)\)|"(.*?)"/gim;
          // const found = matcher.match(regex);
          const found = matcher.replace(/#|{|}|/g, '').split('.');
          if (found && found.length) {
            let bgColor = '#ccc';
            if (found[0] === 'customer') {
              bgColor = '#3C39BA';
            } else if (found[0] === 'visitor') {
              bgColor = '#009000';
            } else if (found[0] === 'event') {
              bgColor = '#FBAC00';
            } else if (found[0] === 'objectWidget') {
              bgColor = '#24ABB9';
            } else if (found[0] === 'productTemplate') {
              bgColor = '#9238B5';
            }
            const label = getObjectPropSafely(() => {
              const foundGroupAttribute = state.personalizationData[found[0]];
              let foundAttribute = '';
              let codeTag = '';
              if (found.length === 2) {
                if (
                  found[0] === 'objectWidget' &&
                  found[1].startsWith('custom_')
                ) {
                  bgColor = '#1890F0';
                  const contentWidget = found[1].substring(7);
                  // remove custom_ for get right content
                  if (contentWidget.length > 0) {
                    if (objectWidgetInput[contentWidget] !== undefined) {
                      return objectWidgetInput[contentWidget].name;
                    }
                    return contentWidget;
                  }
                } else {
                  foundAttribute =
                    foundGroupAttribute &&
                    foundGroupAttribute.map[found[1].split('||')[0]];
                  codeTag = found[1]?.split('||')?.[0];
                }
              } else if (found.length === 3) {
                const splitString = found[2].split('||');
                foundAttribute =
                  foundGroupAttribute &&
                  foundGroupAttribute.map[`${found[1]}.${splitString[0]}`];
                codeTag = `${found[1]}.${splitString[0]}`;
              }
              // console.log('foundAttribute', found, foundAttribute, codeTag);
              if (foundAttribute) {
                return foundAttribute.label;
              }
              // bgColor = '#ccc';
              // return `${found[0]}.${codeTag}`;
              return `${codeTag}`;
            });
            let tag = '';
            if (found.length === 2) {
              if (
                found[0] === 'objectWidget' &&
                found[1].startsWith('custom_')
              ) {
                bgColor = '#1890F0';
                tag = found[1].substring(7);
                // remove custom_ for get right content
                if (tag.length > 0) {
                  if (objectWidgetInput[tag] !== undefined) {
                    return objectWidgetInput[tag].name;
                  }
                  return getInsertPattern(
                    random(8),
                    matcher,
                    label || tag,
                    bgColor,
                  );
                }
              } else {
                const cut = cut && cut.map[found[1].split('||')[0]];
                tag = found[1]?.split('||')[0];
              }
            } else if (found.length === 3) {
              const cut = `${found[1]}.${found[2]}`;
              tag = cut?.split('||')[0];
            } else {
              tag = found[1]?.split('||')[0];
            }
            return getInsertPattern(random(8), matcher, label || tag, bgColor);
            // return getInsertPattern(random(8), matcher, label, bgColor);
          }
          return matcher;
        };
        try {
          return `${result.replace(insertPattern, htmlReplacer)}`;
        } catch (error) {
          addMessageToQueue({
            path: 'app/components/common/UIEditorPersonalization/utils.js',
            func: 'convertHtmlText 2',
            data: error.stack,
          });
          console.log(error);
          return htmlText;
        }
      }
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/components/common/UIEditorPersonalization/utils.js',
      func: 'convertHtmlText 3',
      data: error.stack,
    });
    //
  }
  return result;
};

// eslint-disable-next-line no-unused-vars
export const renderDocumentContent = (state, isUseRenderHtmlCustom = false) => {
  const dataHtml = safeParse(state.html, '');
  const dataCss = safeParse(state.css, '');
  const dataJs = safeParse(state.js, '');
  try {
    if (/<html>/.test(dataHtml)) {
      console.log(dataHtml);
      return dataHtml;
    }
    const documentContents = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="X-UA-Compatible" content="ie=edge">
      <style>
        ${dataCss}
        .hide-pointer {
          pointer-events: none !important;
        }
      </style>
    </head>
    <body class="hide-pointer">
    <style type='text/css'>
    a{
      pointer-events: none !important;
    }
        </style>
      ${dataHtml}
      <script type="text/javascript">
        ${dataJs}
      </script>
    </body>
    </html>
    `;
    return documentContents;
  } catch (error) {
    addMessageToQueue({
      path: 'app/components/common/UIPopupEditorMultiTab/utils.js',
      func: 'renderDocumentContent',
      data: error.stack,
    });
    return dataHtml;
  }
};
export const renderPreviewTemplate = (state, IFRAME) => {
  // console.log('renderPreviewTemplate', state, IFRAME);
  if (IFRAME) {
    const { document } = IFRAME.contentWindow;

    if (document !== null) {
      const documentContents = renderDocumentContent(state);
      document.open();
      document.write(documentContents);
      document.close();
    }
  }
};
export const toAPIPopupEditorMultiTab = data => {
  const dataHtml = safeParse(data.html, '');
  const dataCss = safeParse(data.css, '');
  const dataJs = safeParse(data.js, '');
  return { content: { html: dataHtml, css: dataCss, js: dataJs } };
};
export const toUiPopupEditorMultiTab = data => {
  const { content = {} } = data;
  const dataToUI = {
    activeTab: 'html',
    html: '',
    css: '',
    js: '',
  };
  if (typeof content === 'string') {
    dataToUI.html = content;
    dataToUI.css = '';
    dataToUI.js = '';
    dataToUI.activeTab = 'html';
  } else if (typeof content === 'object') {
    const dataHtml = safeParse(content.html, '');
    const dataCss = safeParse(content.css, '');
    const dataJs = safeParse(content.js, '');
    dataToUI.html = dataHtml;
    dataToUI.css = dataCss;
    dataToUI.js = dataJs;
    dataToUI.activeTab = 'html';
  }
  return dataToUI;
};

export const handleConvertContentHTML = contentHTML => {
  let result = contentHTML;
  Object.keys(REGEX_CONVERT_CSS).forEach(key => {
    const regex = new RegExp(key, 'i');
    result = result.replace(regex, REGEX_CONVERT_CSS[key]);
  });

  return result;
};

export const processTemplateScript = (template, onLoadDone) => {
  try {
    let scrScript = [];
    let noneSrcScript = [];
    const parser = new DOMParser();
    const dom = parser.parseFromString(template, 'text/html');

    const evalScript = () => {
      // eslint-disable-next-line no-eval
      noneSrcScript.map(eval);
      onLoadDone();
    };
    const loadScript = (src, onload) => {
      const script = document.createElement('script');
      script.onload = onload;
      script.src = src;

      document.head.appendChild(script);
    };

    const getScripts = el => {
      const arrScript = Array.from(el.children).filter(
        s => s.tagName === 'SCRIPT',
      );
      scrScript = scrScript.concat(arrScript.map(s => s.src).filter(Boolean));
      noneSrcScript = noneSrcScript.concat(
        arrScript.filter(s => !s.src).map(s => s.textContent),
      );
    };

    getScripts(dom.head);
    getScripts(dom.body);

    let count = 0;
    const onload = () => {
      // eslint-disable-next-line no-plusplus
      if (++count === scrScript.length) {
        evalScript();
      }
    };

    if (scrScript && scrScript.length) {
      scrScript.map(src => loadScript(src, onload));
    } else {
      evalScript();
    }
  } catch (error) {
    onLoadDone();
  }
};

const isValidImageUrl = url => {
  return /^(!http|data:image\/)/.test(url);
};

export const preProcessTemplate = template => {
  const parser = new DOMParser();
  const dom = parser.parseFromString(template, 'text/html');

  const images = dom.querySelectorAll('img');

  images.forEach(img => {
    if (!isValidImageUrl(img.src)) {
      img.remove();
    }
  });

  return dom.documentElement.innerHTML;
};

function isImageOk(img) {
  // During the onload event, IE correctly identifies any images that
  // weren’t downloaded as not complete. Others should too. Gecko-based
  // browsers act like NS4 in that they report this incorrectly.
  if (!img.complete) {
    return false;
  }

  // However, they do have two very useful properties: naturalWidth and
  // naturalHeight. These give the true size of the image. If it failed
  // to load, either of these should be zero.
  if (img.naturalWidth === 0) {
    return false;
  }

  // No other way of checking: assume it’s ok.
  return true;
}

export const processTemplateToEl = ({
  el,
  template,
  isCapture,
  onUpdateDone,
  listKey,
}) => {
  const updateNewInnerHTML = newHTMLContent => {
    const regexValueDynamic = /&lt;([^&]+)&gt;/g;
    let convertedHTML = newHTMLContent;
    // build dynamic text to text span tag
    const listDynamic = newHTMLContent.match(regexValueDynamic);

    if (listDynamic && listDynamic.length && listKey.length) {
      listDynamic.forEach(dynamic => {
        listKey.forEach(key => {
          if (dynamic.includes(key)) {
            convertedHTML = convertedHTML.replace(
              dynamic,
              `<span class='${key}'></span>`,
            );
          }
        });
      });
    }

    if (el) {
      el.innerHTML = convertedHTML;
    }

    if (onUpdateDone) {
      onUpdateDone(convertedHTML);
    }
  };

  try {
    if (!el || !template) return;

    const iframe = document.createElement('iframe');
    iframe.srcdoc = template;
    iframe.style.display = 'none';

    el.appendChild(iframe);

    iframe.onload = () => {
      if (isCapture) {
        const images = iframe.contentDocument.querySelectorAll('img');

        images.forEach(img => {
          if (!isImageOk(img)) {
            img.remove();
          }
        });

        iframe.contentDocument
          .querySelectorAll(
            'meta,title,script,a.zalo_zinstant_map_image_slide_item_link',
          )
          .forEach(item => item.remove());

        updateNewInnerHTML(iframe.contentDocument.documentElement.innerHTML);

        iframe.remove();
      } else {
        updateNewInnerHTML(iframe.contentDocument.documentElement.innerHTML);
        iframe.remove();
      }
    };
  } catch (e) {
    updateNewInnerHTML(template);
  }
};
