import _ from 'lodash';
import { OBJECT_TYPE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/constant';
import { isEqualObject } from '../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/helper';
import { convertCamelcaseToSnakecase, snakeToCamel } from '../../../utils/common';

export const buildGroupAttrs = objectSettings => {
  const data = [
    {
      groupId: -1007,
      groupName: 'Visitor',
      groupCode: 'user',
      itemTypeName: 'users',
      properties: [],
    },
    {
      groupId: -1003,
      groupName: 'Customer',
      groupCode: 'customer',
      itemTypeName: 'customers',
      properties: [],
    },
  ];

  const listPropertiesVisitor = objectSettings
    .filter(
      obj =>
        obj.type === OBJECT_TYPE.dataObjectAttr && obj.itemTypeId === -1007,
    )
    .map(object => snakeToCamel(object.settings));

  const listPropertiesCustomer = objectSettings
    .filter(
      obj =>
        obj.type === OBJECT_TYPE.dataObjectAttr && obj.itemTypeId === -1003,
    )
    .map(object => snakeToCamel(object.settings));

  data[0].properties = listPropertiesVisitor;
  data[1].properties = listPropertiesCustomer;

  return data;
};
