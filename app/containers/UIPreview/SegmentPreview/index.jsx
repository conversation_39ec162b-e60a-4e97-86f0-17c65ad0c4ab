/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { useFetchDetailSegment } from './useFetchDetailSegment';
import { WrapperSegmentPreview } from './styles';
import Name from 'containers/Segment/Content/Name';
import GeneralSetting from 'containers/Segment/Content/GeneralSettings';
import InputMethod from 'containers/Segment/Content/InputMethod';
import DropdownViewAccess from '../../../components/common/DropdownViewAccess';
import Condition from 'containers/Segment/Content/Condition';
import MatchingFile from 'containers/Segment/Content/MatchingFile';
import { compose } from 'redux';
import SegmentMember from 'containers/Segment/Content/SegmentMember';
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { MAP_INPUT_METHOD } from '../../../modules/Dashboard/Profile/Segment/Create/utils';
import { getObjectPropSafely, safeParse } from '../../../utils/common';
import {
  getListDone,
  init,
  initDone,
  reset,
  updateValue,
} from '../../../redux/actions';
import { connect } from 'react-redux';
import { MODULE_CONFIG } from './config';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import reducer from './reducer';
import saga from './saga';
import { createStructuredSelector } from 'reselect';
import {
  makeSelectConfigSegment,
  makeSelectMainSegment,
  makeSelectSegmentCondition,
} from './selector';
import { WrapperSegmentMembers } from '../../Segment/Content/styles';
import ComputationSchedule from 'containers/Segment/Content/ComputationSchedule';
import { defaultRepeatByOptions } from '../../../components/Organisms/ComputationSchedule/constant';
import { getCurrentUserId } from '../../../utils/web/cookie';
import SegmentNotificationSetup from 'containers/Segment/Content/NotificationSetup';
import FromModelName from 'containers/Segment/Content/FromModelName';
import { toConditionUI } from '../../Segment/Content/Condition/utils';
import { toMatchingFileFE } from '../../Segment/Content/MatchingFile/utils';
import { initRules } from '../../Segment/Content/Condition/action';
import { MAP_SEGMENT_TYPES } from '../../Segment/Content/Condition/constants';
import { mapGroupItemAttributeUniqueWithItemTypeId } from '../../../services/map';
import { toEventTrackingFE } from '../../../modules/Dashboard/Profile/Segment/Create/_reducer/utils';
import { UILoading as Loading } from '@xlab-team/ui-components';
import { buildGroupAttrs } from './utils';
import { isEqualObject } from '../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/helper';
import { OBJECT_TYPE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/constant';
import { getFullEventTracking } from '../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/UI/JourneyGoalSettings/utils';
import { ERROR_CODE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/config';
import JTService from 'services/JourneyTemplate';
import PermServices from 'services/Operate';
import { DATA_ACCESS_OBJECT } from '../../../utils/constants';
import { useDeepCompareEffect } from '../../../hooks';

const MAP_TITLE = {
  // Name
  name: getTranslateMessage(TRANSLATE_KEY._TITL_AUDIENCE_NAME, 'Audience Name'),
  placeholderDescription: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_DESCRIBE_SEGMENT,
    'Describe your segment',
  ),

  // Condition
  include: getTranslateMessage(
    TRANSLATE_KEY._TITL_INCLUDE_PEOPLE,
    'Include people that',
  ),
  orInclude: getTranslateMessage(
    TRANSLATE_KEY._TITL_OR_MATCH_PEOPLE,
    'Or match people that',
  ),

  // GeneralSetting
  generalSetting: getTranslateMessage(
    TRANSLATE_KEY._TITL_UPDATE_SEGMENT,
    'Update Segment',
  ),
  static: getTranslateMessage(
    TRANSLATE_KEY._TITL_STATIC_SEGMENT,
    'Static segment - not update',
  ),
  dynamic: getTranslateMessage(
    TRANSLATE_KEY._TITL_DYNAMIC_SEGMENT,
    'Dynamic segment - update every',
  ),

  // SegmentMember
  segmentMember: getTranslateMessage(
    TRANSLATE_KEY._TITL_SEGMENT_MEMBER,
    'Segment Members',
  ),
  limit: getTranslateMessage(
    TRANSLATE_KEY._SEGMENT_MEMBER_LIMIT,
    'Limit-sort segment,then get top of',
  ),
  limitNotice: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_NOTICE_LIMIT_SEGMENT,
    '<b>Notice:</b> The limit setting is not applied for the audiences that journeys automatically add to this segment later.',
  ),
  member: getTranslateMessage(TRANSLATE_KEY._TITL_MEMBER, 'member(s)'),

  // MatchingFile
  titlUploadFile: getTranslateMessage(
    TRANSLATE_KEY._TITL_UPLOAD_AUDIENCE_FILE,
    'Upload your audiences file',
  ),
  titlSettings: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_SEGMENT_CREATE_CRITERIA,
    'Create criteria to find suitable audiences for this segment, using into from your file',
  ),
  titlDescription: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_SEGMENT_CREATE_CRITERIA_INTRO,
    'This system will look for audiences satisfying your criteria and add them to this segment',
  ),
  titlNote: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_SEGMENT_CREATE_CRITERIA_NOTE,
    `<b>Note:</b>
    </br> - From the uploaded file, only audience whose ID is existing in this system can be added to the to-be-created segment
    </br> - The uploaded file will be stored for 6 months since the last time used, after that you could not use it anymore.
    `,
  ),
  addMore: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_CRITERIA, 'Add criteria'),
  updateMethod: getTranslateMessage(
    TRANSLATE_KEY._TITL_CHOOSE_UPDATE_METHOD,
    'Choose update method',
  ),
  // InputMethod
  inputMethod: getTranslateMessage(
    TRANSLATE_KEY._TITL_MEMBER_INPUT_METHOD,
    'Member input method',
  ),

  // ComputationSchedule
  scheduleTitle: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Computation Schedule',
  ),
  title: getTranslateMessage(
    TRANSLATE_KEY._TITL_OFFEN_SEGMENT_UPDATE,
    'How often do you want your segment to be updated?',
  ),
};

const SegmentPreview = props => {
  const {
    segmentId,
    main,
    rules,
    settings,
    mainReducer,
    mode,
    templateObjSettings,
    isExistSegment,
    addErrors = () => {},
  } = props;

  const [{ data }, { setData }] = useFetchDetailSegment({
    segmentId,
    mode,
    isExistSegment,
  });

  const { isLoading } = mainReducer;

  const [viewAccessErrors, setViewAccessErrors] = useState([]);

  useEffect(() => {
    return () => {
      props.reset();
    };
  }, [segmentId]);

  useDeepCompareEffect(() => {
    (async () => {
      if (data) {
        props.init({
          activeRow: data,
          design: 'update',
          itemTypeId: data.item_type_id,
        });
      } else if (settings && mode === 'use' && !isExistSegment) {
        props.init({
          activeRow: settings,
          design: 'update',
          itemTypeId: settings.item_type_id,
          isOnlyUpdateReducer: true,
        });

        const errors = [];
        const BOInfo = {};
        const listView = [];

        const groupAttrs = buildGroupAttrs(templateObjSettings);
        const events = getFullEventTracking(templateObjSettings);
        const dataViewObject = templateObjSettings.find(obj =>
          isEqualObject(obj, {
            viewId: settings.view_id,
            type: OBJECT_TYPE.objectView,
          }),
        );

        if (dataViewObject) {
          listView.push(dataViewObject.settings);

          if (dataViewObject.settings.status === 3) {
            errors.push(
              getTranslateMessage(TRANSLATE_KEY._, 'Data view does not exist'),
            );
            addErrors([{ code: ERROR_CODE.MISSING_FIELD }]);
          }
        } else {
          const itemTypeDisplay =
            settings.item_type_id === -1003 ? 'Customer' : 'Visitor';
          const itemTypeId = settings.item_type_id;

          // check bo có tồn tại trên portal hiện tại không ???
          const { data: dataValidate } = await JTService.validateIngredients({
            objectConfig: [
              {
                type: 'business_object',
                fe_mapping: {
                  itemTypeId,
                  type: 'business_object',
                },
                itemTypeDisplay,
              },
            ],
          });

          const { errorMessage, isExist } = dataValidate?.[0] || {};

          BOInfo.itemDisplayMultilang = itemTypeDisplay;
          BOInfo.itemTypeDisplay = itemTypeDisplay;
          BOInfo.itemTypeId = itemTypeId;
          BOInfo.portalId = '';

          if (!isExist) {
            let code = '';
            let errContent = '';

            if (errorMessage === 'NO_PERMISSION') {
              code = ERROR_CODE.NONE_PERMISSION;
              errContent = getTranslateMessage(
                TRANSLATE_KEY._,
                'You do not have permission for this object',
              );
            } else if (errorMessage === 'NOT_EXIST') {
              code = ERROR_CODE.MISSING_FIELD;
              errContent = getTranslateMessage(
                TRANSLATE_KEY._,
                'Data object does not exist',
              );
            }
            addErrors([{ code }]);
            errors.push(errContent);
          }
        }

        props.updateDataAccessView({
          design: 'update',
          activeRow: settings,
          preSelect: {},
          owner: listView,
          shareWithMe: [],
          BOInfo,
          errors,
        });

        props.initGroupAttrs(groupAttrs);
        // props.updateListEvent(events);
        const groupItemAttrs = mapGroupItemAttributeUniqueWithItemTypeId(
          groupAttrs,
        );

        const eventSchema = toEventTrackingFE(events.data);

        const { segment_type: type, conditions } = settings;
        // console.log({ configure });
        // const { configure } = reducer;

        if (type === 1) {
          // console.log(conditions);
          const rulesData = toConditionUI(safeParse(conditions, {}), {
            map: {
              conditionType: MAP_SEGMENT_TYPES,
              itemAttribute: groupItemAttrs.map,
              eventSchema: eventSchema.map,
            },
            info: {},
          });
          // console.log(rules);
          props.initRuleSegemmnt({ data: rulesData });
        } else if (type === 2) {
          props.initMatchFile({
            data: toMatchingFileFE(conditions, 'update'),
          });
        }
        props.initDone();
      }
    })();
  }, [data, settings]);

  const ownerId = main.activeRow && +main.activeRow.c_user_id;

  return (
    <>
      {main.activeRow && (
        <WrapperSegmentPreview>
          <Loading isLoading={isLoading} />
          <Name
            name={main.name}
            description={main.description}
            itemTypeId={main.itemTypeId}
            labels={MAP_TITLE}
            isViewMode
          />
          {Number(main.activeRow.segment_type) !== 5 && (
            <GeneralSetting
              use="segment"
              disabledDynamic={false}
              initData={main.initDataComputeSchedule}
              computeSchedule={main.computeSchedule}
              labels={MAP_TITLE}
              isViewMode
            />
          )}
          <InputMethod
            labels={MAP_TITLE}
            type={main.type}
            isUploadStep={props.isUploadStep}
            mapOptions={MAP_INPUT_METHOD}
            isViewMode
          />
          {[1, 2].includes(getObjectPropSafely(() => main.type)) && (
            <DropdownViewAccess
              value={main.viewData}
              viewAccessInfo={main.viewInfoMapping}
              isShowLabelLeft
              leftStyles={{
                flexBasis: 'unset',
                width: '150px',
                maxWidth: 'none',
                paddingTop: '0.5rem',
              }}
              containerStyles={{
                padding: '0px 20px 20px',
                alignItem: 'center',
              }}
              errors={main.viewErrors}
              isViewMode
            />
          )}
          {main.type === 1 ? (
            <Condition
              use="segment"
              moduleConfig={MODULE_CONFIG}
              activeRow={main.activeRow}
              rules={rules}
              labels={MAP_TITLE}
              boxShadow={false}
              isJNTactic
              isBorderTop
              isViewMode
              isUseTemplateConfig={mode === 'use'}
              callback={() => {}}
              // eventSchema={main.eventSchema}
              // dataGroupAttrs={main.groupItemAttrs}
              // computeSchedule={main.computeSchedule}
            />
          ) : main.type === 2 ? (
            <MatchingFile
              use="segment"
              moduleConfig={MODULE_CONFIG}
              condition={main.activeRow.conditions}
              itemTypeId={main.itemTypeId}
              labels={MAP_TITLE}
              computeSchedule={main.computeSchedule}
              boxShadow={false}
              isBorderTop
              isViewMode
              design="update"
            />
          ) : null}
          {main.type !== 0 &&
            ![3, 4].includes(main.type) &&
            Number(main.activeRow.segment_type) !== 5 && (
              <WrapperSegmentMembers>
                <SegmentMember
                  use="segment"
                  itemTypeId={main.itemTypeId}
                  initData={main.initDataSegmentMember}
                  callback={props.callback}
                  labels={MAP_TITLE}
                  boxShadow={false}
                  isBorderTop
                  isViewMode
                />
              </WrapperSegmentMembers>
            )}
          {main.computeSchedule.type === 'dynamic' &&
            Number(main.activeRow.segment_type) !== 5 && (
              <ComputationSchedule
                isLoading={main.computeSchedule.isLoading}
                labels={MAP_TITLE}
                computeSchedule={main.computeSchedule}
                callback={props.callback}
                design="update"
                boxShadow={false}
                isBorderTop
                isJNTactic
                isViewMode
              />
            )}
          {Number(main.activeRow.segment_type) !== 5 && (
            <SegmentNotificationSetup
              value={main.notificationSetup}
              ownerId={ownerId}
              boxShadow={false}
              isBorderTop
              repeatByOptions={defaultRepeatByOptions}
              isViewMode
            />
          )}
          {Number(main.activeRow.segment_type) === 5 &&
            main.activeRow.object_info && (
              <FromModelName data={main.activeRow.object_info} isViewMode />
            )}
        </WrapperSegmentPreview>
      )}
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  main: makeSelectConfigSegment(),
  rules: makeSelectSegmentCondition(),
  mainReducer: makeSelectMainSegment(),
});

const mapDispatchToProps = (dispatch, props) => {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    initDone: params => {
      dispatch(initDone(MODULE_CONFIG.key));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key));
    },
    updateDataAccessView: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@UPDATE_DATA_ACCESS_VIEW@@`, params),
      );
    },
    initRuleSegemmnt: params => {
      dispatch(initRules(MODULE_CONFIG, params));
    },
    initMatchFile: params => {
      dispatch(init(`${MODULE_CONFIG.key}@@MATCHING_FILE`, params));
    },
    initGroupAttrs: params => {
      dispatch(getListDone(MODULE_CONFIG.key, params));
    },
    updateListEvent: params => {
      dispatch(getListDone(`${MODULE_CONFIG.key}_EVENT_TRACKING`, params));
    },
  };
};

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({
  key: MODULE_CONFIG.key,
  reducer,
});

const withSaga = injectSaga({
  key: MODULE_CONFIG.key,
  saga,
});

export default compose(
  withReducer,
  withConnect,
  withSaga,
)(SegmentPreview);
