/* eslint-disable no-nested-ternary */
/* eslint-disable no-undef */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import React, { Fragment, memo, useEffect, useMemo, useState } from 'react';
import { useFetchDataDestination } from './useFetchDataDestination';
import { useImmer } from 'use-immer';
import {
  CHANNEL_FILE_TRANSFER,
  MAX_TO_SEND_BROADCAST,
  MINUTE_OPTIONS,
  TYPE_DELIVERY_INTERVAL,
  initDefaultDataConfig,
  mapLabelDeliveryInterval,
  mapValueToFE,
} from '../../../modules/Dashboard/MarketingHub/Destination/Create/utils';
import _, { get } from 'lodash';
import {
  UICheckbox,
  UILoading as Loading,
  UIButton,
  UITabs,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import FormHelperText from '@material-ui/core/FormHelperText';
import UIFrequencyCapping from 'components/common/UIFrequencyCappingV2';
import {
  generateKey,
  getObjectPropSafely,
  safeParse,
} from '../../../utils/common';
import { validateAll } from '../../modals/ModalCreateEventCategory';
import { Grid } from '@material-ui/core';
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import RadioGroup from '@material-ui/core/RadioGroup';
import VariantTab from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/_UI/UIVariant/index';
import {
  AccordionCustom,
  AccordionDetailsCustom,
  AccordionSummaryCustom,
  BlockTitle,
  DisplayContent,
  HeadingAccordion,
  LabelText,
  MarkerCircle,
  StyleCheckboxWrapper,
  StyleContentLoading,
  SubHeadingAccordion,
  Title,
  Wrapper,
  useStyles,
} from './styles';
import {
  WrapperFixWidth,
  WrapperLabelOption,
  WrapperTitleItem,
} from '../../Segment/Content/SegmentMember/styled';
import UISelect from 'components/form/UISelectCondition';
import { UINumberStyled } from '../../../modules/Dashboard/MarketingHub/Destination/Create/styles';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Radio from '@material-ui/core/Radio';
import InputPreview from '../../../components/Atoms/InputPreview';
import {
  checkIsHiddenInputComponent,
  getDataMappingGroupConfigs,
  getSelectedTemplate,
  getWidthCustomInput,
} from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils';
import classNames from 'classnames';
import {
  buildValueFormVariantFromCache,
  initDefaultData,
  initVariants,
  validateVariantContent,
} from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.state';
import { CATALOG_CODE } from '../constants';
import { Divider } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/styles';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { LIST_FIELD_BY_CATALOG } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/constants';
import _isEmpty from 'lodash/isEmpty';
import _cloneDeep from 'lodash/cloneDeep';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { MODULE_CONFIG as MODULE_CONFIG_JOURNEY } from '../../../modules/Dashboard/MarketingHub/Journey/Create/config';
import { MODULE_CONFIG as MODULE_CONFIG_BLAST_CAMPAIGN } from '../../../modules/Dashboard/MarketingHub/Journey/Create/BlastCampaign/config';
import Destination from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination';
import { convertKeyToSnakeCase, flattentBranch } from './utils';
import { checkIsNewUIDestination } from '../../../modules/Dashboard/MarketingHub/Destination/Detail/utils';
import { Checkbox } from '@antscorp/antsomi-ui';

const MAP_TITLE = {
  notiAlertEdit: getTranslateMessage(
    TRANSLATE_KEY._NOTI_ALERT_EDIT_DESTINATION,
    'If you edit this destination will affect the input of other features.',
  ),
  notiNotCreateDest: getTranslateMessage(
    TRANSLATE_KEY._NOTI_NOT_CREATE_DESTINATION,
    'Do not exist any catalog, to create a destination required have must a catalog',
  ),
  titlGeneralInfomation: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_INFORMATION,
    'General Information',
  ),
  titlConfigFields: getTranslateMessage(
    TRANSLATE_KEY._TITL_CONFIGURE_FIELD,
    'Configure fields',
  ),
  titlGeneralSetting: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_SETTING,
    'General Setting',
  ),
  titlFrequencyCapping: getTranslateMessage(
    TRANSLATE_KEY._TITL_FREQUENCY_CAPPING,
    'Frequency Capping',
  ),
  titlDeliveredRate: getTranslateMessage(
    TRANSLATE_KEY._TITLE_DES_DELIVERED_RATE,
    'Delivered rate',
  ),
  titlDeliveredInterval: getTranslateMessage(
    TRANSLATE_KEY._DES_DELIVERY_INTERVAL,
    'Delivery Interval Setting',
  ),
  possible: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_AS_FAST_AS_POSSIBLE,
    'As fast as possible',
  ),
  limitSentRate: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_LIMIT_SEND_RATE,
    'Limit send rate',
  ),
  timePerson: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_TIME_PER_SECOND,
    'time(s) per second',
  ),
  titleAdvancedSetting: getTranslateMessage(
    TRANSLATE_KEY._DES_ADV_PUSH_SETTING,
    'Advanced Push Settings (Optional)',
  ),
};

const styleRightContent = {
  padding: '0.8rem',
  paddingTop: 0,
};

const styleLeftContent = {
  fontSize: '12px',
  maxWidth: '158px',
  padding: 0,
  color: '#666',
  paddingTop: '0.5rem',
};

const CheckBoxCustom = ({
  name,
  disabled,
  size = 'small',
  onChange,
  checked,
  label,
}) => {
  // eslint-disable-next-line no-shadow
  const onChangeLocal = checked => {
    onChange(name, checked);
  };
  return (
    <UICheckbox
      color="primary"
      size={size}
      disabled={disabled}
      checked={checked}
      onChange={onChangeLocal}
      label={label}
      className="label-delivery-interval-settings"
    />
  );
};

const InputComponent = memo(props => (
  <Grid
    container
    className={`${props.classes.item} ${
      props.classes.isCustomSpace &&
      props.isViewMode &&
      props.catalogCode === 'trello'
        ? 'custom-space'
        : ''
    }`}
    style={{
      ...props.style,
      ...(props.isHideField ? { padding: 0 } : {}),
      display: props.isHidden && 'none',
    }}
  >
    {typeof props.componentEl === 'function' && props.componentEl(props)}
  </Grid>
));

const DestinationPreview = props => {
  const {
    destinationId,
    actionId,
    settings,
    isOtherPortal = false,
    moduleConfig,
    mode,
    workflowSettings,
    templateObjSettings,
    isExistDestination,
  } = props;

  const [data, setData] = useImmer(initDefaultData());
  const [destinationData, setDestinationData] = useState({});
  const [{ state }, { setState }] = useFetchDataDestination({
    destinationId,
    mode,
    settings,
    isExistDestination,
  });

  const {
    dataConfig,
    isLoading,
    isInitDone,
    activeDestCatalog,
    channelCode,
    channelId,
    catalogCode,
    catalogId,
  } = state;

  const classes = useStyles();

  useEffect(() => {
    let nodeData = {};
    if (mode === 'save') {
      nodeData = props.nodes
        ? props.nodes.getIn([actionId, 'destination'])
        : {};
    } else if (mode === 'use') {
      const listBranch = flattentBranch(workflowSettings, []);
      const nodeDestination = listBranch.find(
        branch => branch.actionId === actionId,
      );

      if (nodeDestination) {
        const newInitData = {
          role: 'RESET',
          isFetchInfoData: false,
          isUseTemplate: true,
          lookup: {},
        };
        let listVariants = [];
        if (
          nodeDestination.metadata.variants &&
          nodeDestination.metadata.variants.length
        ) {
          listVariants = nodeDestination.metadata.variants;
        } else {
          listVariants = nodeDestination.metadata.variantIds.map(
            variantId =>
              (
                templateObjSettings.find(
                  templateObj => templateObj.variantId === variantId,
                ) || {}
              ).settings,
          );
        }
        const newListVariant = convertKeyToSnakeCase(listVariants);

        newInitData.lookup.variants = newListVariant;
        newInitData.lookup.campaign = [];
        nodeData = newInitData;
      }
    }
    setDestinationData(nodeData);
  }, [
    props.nodes,
    props.workflowSettings,
    props.mode,
    destinationId,
    templateObjSettings,
  ]);

  const isNewUI = checkIsNewUIDestination(
    getObjectPropSafely(() => channelId, ''),
    getObjectPropSafely(() => catalogCode, ''),
  );

  const onChangeData = (name, value) => {
    switch (name) {
      case 'destination':
        if (mode === 'save') {
          setDestinationData(value);
        }
        break;

      default:
        break;
    }
  };

  const onMemoInitdata = useMemo(() => dataConfig.frequencyCapping.value, [
    isInitDone,
  ]);
  const onMemoComponenId = useMemo(() => generateKey(), [isInitDone]);

  const canSendBroadcast = useMemo(
    () =>
      getObjectPropSafely(
        () => activeDestCatalog.catalogInput.canSendBroadcast,
        false,
      ),
    [activeDestCatalog],
  );

  const intervalBroadcastSetting = useMemo(
    () =>
      getObjectPropSafely(
        () => activeDestCatalog.catalogSetting.intervalBroadcastSetting,
        {},
      ),
    [activeDestCatalog],
  );

  const activeNode = {
    channelCode,
    catalogCode,
    value: catalogId,
    channelId,
    catalogId,
  };

  const isValidateActiveNode = useMemo(() => {
    return (
      !!activeNode.channelCode &&
      !!activeNode.catalogCode &&
      !!activeNode.catalogId &&
      !!activeNode.channelId
    );
  }, [activeNode]);

  // const { isSendBroadcast, fromNumber, toNumber } =
  //   intervalBroadcastSetting || {};

  if (mode === 'use' && !isExistDestination) {
    return (
      <Grid container className="p-all-4">
        <Grid item xs={2} style={styleLeftContent}>
          <div style={{}}>
            Destination name<span style={{ color: 'red' }}> *</span>
          </div>
        </Grid>
        <Grid item xs={10} style={{ color: 'red', paddingTop: '0.5rem' }}>
          {settings.destinationName}
        </Grid>
      </Grid>
    );
  }

  const renderDeliveredFields = channelIdDes => {
    if (+channelIdDes === CHANNEL_FILE_TRANSFER) {
      const { value = '' } = getObjectPropSafely(
        () => dataConfig.deliveryInterval.mapOptions,
        {},
      );
      const valueTemp = getObjectPropSafely(
        () => dataConfig.deliveryInterval.value,
        1,
      );
      const label = mapLabelDeliveryInterval[value] || '';
      const mapValueMinute =
        MINUTE_OPTIONS.find(minuteOption => minuteOption.value === valueTemp) ||
        MINUTE_OPTIONS[0];

      return (
        <>
          <Grid
            item
            xs={3}
            style={{
              display: 'flex',
              alignItems: 'center',
              ...styleLeftContent,
            }}
          >
            <Title>{MAP_TITLE.titlDeliveredInterval}</Title>
          </Grid>
          <Grid item xs={9}>
            <WrapperDisable disabled={dataConfig.deliveryInterval.disabled}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <WrapperFixWidth width="250px">
                  <InputPreview
                    isViewMode
                    type="input"
                    value={dataConfig.deliveryInterval.mapOptions.label}
                  >
                    <UISelect
                      onlyParent
                      use="tree"
                      isSearchable={false}
                      options={dataConfig.deliveryInterval.options}
                      value={dataConfig.deliveryInterval.mapOptions}
                      onChange={() => {}}
                      placeholder={getTranslateMessage(
                        TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                        'Select an item',
                      )}
                      fullWidthPopover
                      disabled={false}
                    />
                  </InputPreview>
                </WrapperFixWidth>
                {value !== TYPE_DELIVERY_INTERVAL.AFTER_COMPLETED && (
                  <>
                    <LabelText>
                      {getTranslateMessage(TRANSLATE_KEY._EVERY, 'every')}
                    </LabelText>
                    <WrapperFixWidth width="100px">
                      {value === TYPE_DELIVERY_INTERVAL.MINUTE ? (
                        <UISelect
                          onlyParent
                          use="tree"
                          isSearchable={false}
                          options={MINUTE_OPTIONS}
                          value={mapValueMinute}
                          onChange={() => {}}
                          placeholder={getTranslateMessage(
                            TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                            'Select an item',
                          )}
                          fullWidthPopover
                          disabled={false}
                        />
                      ) : (
                        <UINumberStyled
                          name="times"
                          onChange={() => {}}
                          value={valueTemp}
                          min={1}
                          // max={100}
                          defaultValue={1}
                          width={100}
                        />
                      )}
                    </WrapperFixWidth>
                    <LabelText>{label}</LabelText>
                  </>
                )}
              </div>
            </WrapperDisable>
          </Grid>
        </>
      );
    }

    return (
      <>
        {canSendBroadcast && (
          <Grid container>
            <Grid
              item
              xs={3}
              style={{
                ...styleLeftContent,
                display: 'flex',
                alignItems: 'flex-start',
                marginTop: -12,
                marginBottom: 15,
                paddingTop: '0.3rem',
              }}
            >
              <Title>{MAP_TITLE.titlDeliveredInterval}</Title>
            </Grid>
            <Grid item xs={9} style={{ marginTop: -10, marginBottom: 15 }}>
              <WrapperDisable disabled={dataConfig.deliveryBroadcast.disabled}>
                <StyleCheckboxWrapper>
                  <RadioGroup
                    className="p-left-3"
                    aria-label="deliveryIntervalSettings"
                    name="deliveryInterval_1"
                  >
                    <FormControlLabel
                      style={{ pointerEvents: 'none' }}
                      value="onDeliveryIntervalSettings"
                      control={
                        <Checkbox
                          name="onCheckingBroadCast"
                          checked={dataConfig.deliveryBroadcast.isBroadcast}
                          disabled
                          onChange={() => {}}
                        >
                          {getTranslateMessage(
                            TRANSLATE_KEY._,
                            'Enable Broadcast',
                          )}
                        </Checkbox>
                        // <CheckBoxCustom
                        //   checked={dataConfig.deliveryBroadcast.isBroadcast}
                        //   // checked={
                        //   //   !_.isEmpty(intervalBroadcastSetting) && isSendBroadcast
                        //   //     ? isSendBroadcast
                        //   //     : dataConfig.deliveryBroadcast.isBroadcast
                        //   // }
                        //   name="onCheckingBroadCast"
                        //   size="small"
                        //   // disabled={
                        //   //   !_.isEmpty(intervalBroadcastSetting) && isSendBroadcast
                        //   //     ? isSendBroadcast
                        //   //     : dataConfig.deliveryBroadcast.disabled
                        //   // }
                        //   onChange={() => {}}
                        //   label={getTranslateMessage(
                        //     TRANSLATE_KEY._,
                        //     'Enable Broadcast',
                        //   )}
                        // />
                      }
                    />
                  </RadioGroup>
                </StyleCheckboxWrapper>
                {((!_.isEmpty(intervalBroadcastSetting) && isSendBroadcast) ||
                  dataConfig.deliveryBroadcast.isBroadcast) && (
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 12,
                      pointerEvents: 'none',
                    }}
                  >
                    <LabelText>
                      {getTranslateMessage(TRANSLATE_KEY._, 'From')}
                    </LabelText>
                    <WrapperFixWidth width="80px">
                      <UINumberStyled
                        name="times"
                        value={dataConfig.deliveryBroadcast.from}
                        min={
                          !_.isEmpty(intervalBroadcastSetting) &&
                          typeof fromNumber === 'number'
                            ? fromNumber
                            : 0
                        }
                        max={
                          !_.isEmpty(intervalBroadcastSetting) &&
                          typeof toNumber === 'number'
                            ? toNumber
                            : MAX_TO_SEND_BROADCAST
                        }
                        defaultValue={dataConfig.deliveryBroadcast.from}
                        defaultEmptyValue={
                          !_.isEmpty(intervalBroadcastSetting) &&
                          typeof fromNumber === 'number'
                            ? fromNumber
                            : 1
                        }
                        width={80}
                      />
                    </WrapperFixWidth>
                    <LabelText>
                      {getTranslateMessage(TRANSLATE_KEY._, 'to')}
                    </LabelText>
                    <WrapperFixWidth width="80px">
                      <UINumberStyled
                        name="times"
                        value={dataConfig.deliveryBroadcast.to}
                        min={Math.max(
                          !_.isEmpty(intervalBroadcastSetting) &&
                            typeof fromNumber === 'number'
                            ? fromNumber
                            : 1,
                          dataConfig.deliveryBroadcast.from,
                        )}
                        max={
                          !_.isEmpty(intervalBroadcastSetting) &&
                          typeof toNumber === 'number'
                            ? toNumber
                            : MAX_TO_SEND_BROADCAST
                        }
                        defaultValue={dataConfig.deliveryBroadcast.to}
                        defaultEmptyValue={
                          !_.isEmpty(intervalBroadcastSetting) &&
                          typeof toNumber === 'number'
                            ? toNumber
                            : 1000
                        }
                        width={80}
                      />
                    </WrapperFixWidth>
                    <LabelText>
                      {getTranslateMessage(
                        TRANSLATE_KEY._,
                        'person(s) per once',
                      )}
                    </LabelText>
                  </div>
                )}
              </WrapperDisable>
            </Grid>
          </Grid>
        )}
        <Grid container>
          <Grid item xs={3} style={styleLeftContent}>
            <Title>{MAP_TITLE.titlDeliveredRate}</Title>
          </Grid>
          <Grid item xs={9} style={{ paddingTop: '0.5rem' }}>
            {dataConfig.deliveryRate.type === 'normal' ? (
              <WrapperLabelOption style={{ marginLeft: '4px' }}>
                <WrapperDisable>
                  <MarkerCircle />
                  {MAP_TITLE.possible}
                </WrapperDisable>
              </WrapperLabelOption>
            ) : (
              <>
                <WrapperLabelOption style={{ marginLeft: '4px' }}>
                  {/* {MAP_TRANSLATE.labelLimit} */}
                  <MarkerCircle />
                  {MAP_TITLE.limitSentRate}
                </WrapperLabelOption>
                <span>{dataConfig.deliveryRate.limit}</span>
                <WrapperTitleItem style={{ marginLeft: '16px' }}>
                  {MAP_TITLE.timePerson}
                </WrapperTitleItem>
              </>
            )}
          </Grid>
        </Grid>
      </>
    );
  };

  return (
    <StyleContentLoading>
      <Loading isLoading={isLoading} />
      <Grid container>
        <Grid item xs={12} className="p-all-4 p-top-8">
          <BlockTitle> {MAP_TITLE.titlGeneralInfomation}</BlockTitle>
        </Grid>
        <Grid style={{ marginTop: '5px' }} item xs={12} className="p-x-4">
          {dataConfig.infoFields.map(
            each =>
              each !== 'destinationCatalog' && (
                <InputComponent
                  {...dataConfig[each]}
                  onChange={() => {}}
                  key={each}
                  classes={classes}
                  isViewMode
                  isJourneyTemplateMode
                  styleLeftContent={styleLeftContent}
                  styleRightContent={styleRightContent}
                />
              ),
          )}
          {dataConfig.extraInfoFields.map(each => (
            <InputComponent
              {...dataConfig[each]}
              onChange={() => {}}
              key={each}
              classes={classes}
              isViewMode
              isJourneyTemplateMode
              styleLeftContent={styleLeftContent}
              styleRightContent={styleRightContent}
            />
          ))}
          {/* {dataConfig.destinationCatalog.options.length === 0 && (
            <FormHelperText error>{MAP_TITLE.notiNotCreateDest}</FormHelperText>
          )} */}
        </Grid>
      </Grid>
      {dataConfig.configFields.length > 0 && (
        // <Box className="row width-100 m-x-0 m-y-1">
        <Grid container>
          <Grid item xs={12} className="p-all-4">
            <BlockTitle> {MAP_TITLE.titlConfigFields}</BlockTitle>
          </Grid>
          {/* <Loading isLoading={isLoadingConfigFields} /> */}
          {dataConfig.configFields.map(each => (
            <Grid item xs={12} className="p-x-4">
              <InputComponent
                {...dataConfig[each]}
                onChange={() => {}}
                key={each}
                classes={classes}
                channelId={channelId}
                isViewMode
                isJourneyTemplateMode
                styleLeftContent={styleLeftContent}
                styleRightContent={styleRightContent}
              />
            </Grid>
          ))}
        </Grid>
        // </Box>
      )}
      <Grid container>
        <Grid item xs={12} className="p-all-4">
          <BlockTitle>
            {' '}
            {isNewUI
              ? MAP_TITLE.titleAdvancedSetting
              : MAP_TITLE.titlGeneralSetting}
          </BlockTitle>
        </Grid>
        <Grid
          style={{ marginTop: '5px' }}
          container
          item
          xs={12}
          className="p-x-4"
        >
          <Grid item xs={3} style={{ ...styleLeftContent, paddingTop: 0 }}>
            <Title>
              {isNewUI
                ? MAP_TITLE.titlGeneralSetting
                : MAP_TITLE.titlFrequencyCapping}
            </Title>
          </Grid>
          <Grid item xs={9}>
            <UIFrequencyCapping
              isNoPadding
              channelId={channelId}
              isShowLabel={false}
              onChange={() => {}}
              initData={onMemoInitdata}
              componentId={onMemoComponenId}
              isViewMode
              // disabled
            />
          </Grid>
          {renderDeliveredFields(channelId)}
        </Grid>
        {/* <Grid container item xs={8} className="p-all-4"></Grid> */}
      </Grid>
      {isValidateActiveNode && (
        <Grid container>
          <Grid item xs={12}>
            <div className="preview-destination">
              <Destination
                moduleConfig={{ key: '' }}
                updateFreshNodesKey={1}
                activeNode={activeNode}
                initData={destinationData}
                isViewMode
                isJourneyTemplateMode
                onChange={onChangeData}
              />
            </div>
          </Grid>
        </Grid>
      )}
    </StyleContentLoading>
  );
};

const mapStateToProps = (state, ownedProps) => {
  const { moduleConfig, mode } = ownedProps;
  // use case use template tại template tatic thì không dùng các state trong redux, mà dùng trực tiếp settings nhận được
  if (mode === 'use') return {};
  const { key } = moduleConfig;
  const stateByKey = state.get(key);
  const nodes = get(stateByKey, 'configure.main.nodes');
  return {
    nodes,
  };
};

export default connect(
  mapStateToProps,
  null,
)(DestinationPreview);
