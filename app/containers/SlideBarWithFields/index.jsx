/* eslint-disable no-param-reassign */
/* eslint-disable indent */
// Libraries
import React, { Fragment, useCallback, useEffect, useMemo } from 'react';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { useImmer } from 'use-immer';

// Components
import { SlideBar, UploadImage } from '@antscorp/antsomi-ui';
import { FormHelperText, Grid } from '@material-ui/core';
import { UITextField, UIWrapperDisable } from '@xlab-team/ui-components';
import UISelect from 'components/form/UISelectCondition';
import ErrorBoundary from 'components/common/ErrorBoundary';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import InputPreview from '../../components/Atoms/InputPreview';

// Styled
import {
  ContainerSlideBar,
  WrapperCenterFlexEnd,
  Title,
  PromptText,
} from './styled';

// Hooks
import useDebounce from '../../hooks/useDebounce';
import { useDeepCompareEffect } from '../../hooks';

// Utils
import { getTypeRender, initialState, serializedDataOut } from './utils';
import { getCurrentUserId, getToken } from '../../utils/web/cookie';
import { generateKey, isProduction } from '../../utils/common';
import { addMessageToQueue } from '../../utils/web/queue';

// Locales
import { getTranslateMessage } from '../Translate/util';
import TRANSLATE_KEY from '../../messages/constant';

// Constants
import { slideActionType } from '@antscorp/antsomi-ui/es/components/atoms/SlideBar/constants';
import {
  DEFAULT_ACTION_KEYS,
  DEFAULT_ACTION_TYPES,
} from '../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/constants';
import { CATALOG_CODE } from '../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';
const PATH = 'app/containers/SlideBarWithFields/index.jsx';

export const SlideBarWithFields = props => {
  const {
    initValue,
    isViewMode,
    name,
    limit,
    errors,
    fields,
    isBlastCampaign,
    classes,
    disabled,
    catalogCode,
    isRecommendation,
    isAppendBOContentSource,
    appendPersonalizeType,
    onChange,
  } = props;

  const [state, setState] = useImmer(initialState());
  const { map, list, activeId } = state;
  const debounceActive = useDebounce(activeId, 400);
  const debounceMap = useDebounce(map, 400);
  const debounceList = useDebounce(list, 400);

  const activeIndex = useMemo(
    () => list.findIndex(item => item.value === activeId),
    [activeId, list],
  );
  const allowDisplay = useMemo(
    () => _.get(map, `${activeId}.defaultAction.value`, ''),
    [activeId, map],
  );

  const errorMessage = useCallback(
    inputName =>
      errors && errors[activeIndex] && errors[activeIndex][inputName],
    [errors, activeIndex],
  );

  const errorsSlideBar = useMemo(() => {
    if (!_.isArray(errors)) return [];

    return errors.map((error, idx) => {
      if (_.isEmpty(error)) return null;

      return !_.isEmpty(list[idx]) ? list[idx].value : null;
    });
  }, [errors, list]);

  const handleChangeInputs = ({
    activeId: activeIdInput,
    inputName,
    value,
  }) => {
    try {
      if (inputName) {
        setState(draft => {
          draft.map = {
            ...(draft.map || {}),
            [activeIdInput]: {
              ...(draft.map[activeIdInput] || {}),
              [inputName]: value,
            },
          };
        });

        if (inputName === 'defaultAction') {
          const { value: valueInner = '' } = value || {};

          setState(draft => {
            draft.map = {
              ...(draft.map || {}),
              [activeIdInput]: _.omit(
                draft.map[activeIdInput] || {},
                valueInner === DEFAULT_ACTION_KEYS.URL
                  ? DEFAULT_ACTION_KEYS.DEEP_LINK
                  : DEFAULT_ACTION_KEYS.URL,
              ),
            };
          });
        }
      }
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'handleChangeInputs',
        data: {
          err,
        },
      });
    }
  };

  const callbackSlideBar = (type = '', data = {}) => {
    try {
      switch (type) {
        case slideActionType.ADD: {
          const newId = generateKey();
          setState(draft => {
            draft.list.push({
              label: newId,
              value: newId,
            });
            draft.map = {
              ...draft.map,
              [newId]: {
                defaultAction: DEFAULT_ACTION_TYPES[DEFAULT_ACTION_KEYS.URL],
              },
            };
          });
          break;
        }
        case slideActionType.ACTIVE: {
          // Need to remove focus
          // because "TinymceEditor" component do not trigger blur to change when click active new slide
          if (document.activeElement) {
            document.activeElement.blur();
          }

          setState(draft => {
            draft.activeId = data.value;
          });
          break;
        }
        case slideActionType.DELETE: {
          const { value = '' } = data;
          const excludedList = _.differenceWith(
            _.cloneDeep(list),
            [data],
            _.isEqual,
          );
          const excludeMap = _.omit(_.cloneDeep(map), [value]);

          setState(draft => {
            draft.activeId = (excludedList[0] || {}).value;
            draft.map = excludeMap;
            draft.list = excludedList;
          });
          break;
        }
        case slideActionType.DUPLICATE: {
          const { value = '' } = data;
          const newId = generateKey();
          const newMap = _.get(map, value, {});

          setState(draft => {
            draft.activeId = newId;
            draft.list.push({
              label: newId,
              value: newId,
            });
            draft.map = {
              ...(draft.map || {}),
              [newId]: newMap,
            };
          });
          break;
        }
        default: {
          break;
        }
      }
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'callbackSlideBar',
        data: {
          err,
          type,
          data,
        },
      });
    }
  };

  // NOTE: Callback data out
  useDeepCompareEffect(() => {
    if (typeof onChange === 'function') {
      const dataOut = serializedDataOut({
        list: debounceList,
        map: debounceMap,
      });

      onChange(dataOut);
    }
  }, [debounceList, debounceMap]);

  useDeepCompareEffect(() => {
    const [listInit, mapInit] = [[], {}];

    if (_.isArray(initValue) && !state.isInitDone) {
      initValue.forEach(init => {
        const newId = generateKey();
        listInit.push({ label: newId, value: newId });
        mapInit[newId] = init;
      });

      setState(draft => {
        draft.activeId = (listInit[0] || {}).value || '';
        draft.map = mapInit;
        draft.list = listInit;
        draft.isInitDone = true;
      });
    }
  }, [initValue, state.isInitDone]);

  useEffect(
    () => () => {
      setState(draft => {
        draft.isInitDone = false;
      });
    },
    [initValue],
  );

  const getComponentInput = ({
    typeRender = '',
    inputName = '',
    inputValue = {},
    item = {},
  }) => {
    switch (typeRender) {
      case 'imageUrl': {
        // Constants
        const userId = getCurrentUserId();
        const token = getToken();
        let componentEl = null;
        const inputPersonalId = `IMAGE_URL_SLIDE_BAR-${inputName}`;
        const isAntsomiAppPush = catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH;
        const maxSize = isAntsomiAppPush ? 25 / 1024 : undefined;

        if (isRecommendation) {
          let isForceHideBtnPersonal = false;
          const inputWrapperEle = document.getElementById(inputPersonalId); // Id config from API
          const limitTagLength = 1;

          if (inputWrapperEle) {
            const numericCountTag = inputWrapperEle.querySelectorAll(
              '.insert-word',
            );
            const currentTagLength = numericCountTag.length;

            if (limitTagLength && +limitTagLength <= currentTagLength) {
              isForceHideBtnPersonal = true;
            }
          }

          componentEl = (
            <UIWrapperDisable disabled={disabled}>
              <div style={{ position: 'relative', minHeight: 35 }}>
                <TinymceEditor
                  isForceHideBtnPersonalization={isForceHideBtnPersonal}
                  typeComponent="input"
                  inputPersonalId={inputPersonalId}
                  isAppendBOContentSource={isAppendBOContentSource}
                  appendPersonalizeType={appendPersonalizeType}
                  placeholder={item.placeholder || ''}
                  onChange={valueOut => {
                    handleChangeInputs({
                      activeId: debounceActive,
                      inputName,
                      value: valueOut,
                    });
                  }}
                  errors={[]}
                  onChangeOthers={() => {}}
                  initData={inputValue}
                  enableShortLink={false}
                  width="100%"
                  isBlastCampaign={isBlastCampaign}
                />
              </div>
            </UIWrapperDisable>
          );
        } else {
          componentEl = (
            <UploadImage
              isInputMode
              domainMedia={
                isProduction() ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX
              }
              slug="api/v1"
              width="100%"
              paramConfigs={{
                token,
                userId,
                accountId: userId,
              }}
              selectedImage={{
                url: inputValue,
              }}
              maxSize={maxSize}
              placeholder={item.placeholder || 'Upload or input URL'}
              onChangeImage={image => {
                handleChangeInputs({
                  activeId: debounceActive,
                  inputName,
                  value: (image && image.url) || '',
                });
              }}
              onRemoveImage={() => {
                handleChangeInputs({
                  activeId: debounceActive,
                  inputName,
                  value: '',
                });
              }}
            />
          );
        }

        return (
          <Fragment>
            {componentEl}
            {!isRecommendation && (
              <PromptText>
                {getTranslateMessage(
                  TRANSLATE_KEY._,
                  `Use image size of around 25 KB. Recommended aspect ratio (width:height): Android: 2.9:1 with buttons, 2.8:1 without buttons and iOS: 2:1`,
                )}
              </PromptText>
            )}
          </Fragment>
        );
      }
      case 'singleLineAddPersonalize': {
        return (
          <UIWrapperDisable disabled={disabled}>
            <div style={{ position: 'relative', minHeight: 35 }}>
              <TinymceEditor
                typeComponent="input"
                placeholder={item.placeholder || ''}
                onChange={valueOut => {
                  handleChangeInputs({
                    activeId: debounceActive,
                    inputName,
                    value: valueOut,
                  });
                }}
                isAppendBOContentSource={isAppendBOContentSource}
                appendPersonalizeType={appendPersonalizeType}
                errors={[]}
                onChangeOthers={() => {}}
                initData={inputValue}
                enableShortLink
                width="100%"
                isBlastCampaign={isBlastCampaign}
              />
            </div>
          </UIWrapperDisable>
        );
      }
      case 'selectDropdown': {
        return (
          <UISelect
            onlyParent
            use="tree"
            isSearchable
            options={item.options || []}
            value={inputValue}
            onChange={valueOut =>
              handleChangeInputs({
                activeId: debounceActive,
                inputName,
                value: valueOut,
              })
            }
            placeholder={getTranslateMessage(
              TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
              'Select an item',
            )}
            fullWidthPopover
            disabled={disabled}
            labelWidth="100%"
          />
        );
      }
      default: {
        return (
          <UITextField
            value={inputValue}
            onChange={valueOut =>
              handleChangeInputs({
                activeId: debounceActive,
                inputName,
                value: valueOut,
              })
            }
            placeholder={item.placeholder || ''}
            textFieldProps={{
              disabled,
              size: 'small',
              multiline: false,
              rowsMax: 1,
              style: {
                width: '100%',
              },
            }}
          />
        );
      }
    }
  };

  const renderComponentEl = () => {
    if (_.isEmpty(fields)) return null;
    const { basicInputs = [], inputs = {} } = fields;
    let content = null;

    if (_.isArray(basicInputs)) {
      content = basicInputs.map(input => {
        const item = inputs[input];
        const inputValue = _.get(map, `${debounceActive}.${input}`, '');

        const {
          name: inputName = '',
          inputType = '',
          inputFormat = '',
          canImportFromMediaLibrary = false,
          canAddPersonalization = false,
          isFollowing = false,
          displayFollowings = [],
        } = item;
        const typeRender = getTypeRender({
          inputType,
          inputFormat,
          canImportFromMediaLibrary,
          canAddPersonalization,
        });
        let componentInput = null;

        if (typeRender) {
          componentInput = getComponentInput({
            item,
            typeRender,
            inputValue,
            inputName,
          });
        }

        if (isFollowing && !displayFollowings.includes(allowDisplay))
          return null;

        return (
          <Grid
            key={`${debounceActive}-${input}`}
            container
            className={`${classes.item}`}
          >
            <Grid
              item
              sm="auto"
              className={classNames(classes.title, classes.styledChannel)}
              style={{
                justifyContent: 'flex-start',
                minWidth: '200px',
                maxWidth: '200px',
              }}
            >
              <WrapperCenterFlexEnd>
                <Title
                  className={classes.spacingTitle}
                  style={
                    isBlastCampaign
                      ? {
                          whiteSpace: 'break-spaces',
                          wordBreak: 'initial',
                          textAlign: 'right',
                        }
                      : {}
                  }
                >
                  {item.label}{' '}
                  {!isViewMode && (
                    <span style={{ color: '#f44336' }}>
                      {item.isRequired && '*'}
                    </span>
                  )}
                </Title>
              </WrapperCenterFlexEnd>
            </Grid>
            <Grid
              item
              sm="auto"
              className={classes.content}
              style={{
                flex: 1,
                minWidth: 'calc(100% - 200px)',
                maxWidth: 'calc(100% - 200px)',
              }}
            >
              <InputPreview
                value={_.isObject(inputValue) ? inputValue.value : inputValue}
                type="input"
                className={`${classes.fontSize}, ${classes.content}`}
                isViewMode={isViewMode}
              >
                {componentInput}
                <FormHelperText
                  id="component-helper-text"
                  error={!!errorMessage(inputName)}
                >
                  {errorMessage(inputName)}
                </FormHelperText>
              </InputPreview>
            </Grid>
          </Grid>
        );
      });
    }

    return content;
  };

  return (
    <ErrorBoundary path={PATH}>
      <ContainerSlideBar key={name}>
        <SlideBar
          limit={limit}
          prefix="Slide"
          activeId={activeId}
          callback={callbackSlideBar}
          options={list}
          errors={errorsSlideBar}
          isShowLabelSequentially
          isViewMode={isViewMode}
          disabled={disabled}
        />
        {renderComponentEl()}
      </ContainerSlideBar>
    </ErrorBoundary>
  );
};

SlideBarWithFields.propTypes = {
  initValue: PropTypes.arrayOf(PropTypes.objectOf(PropTypes.string)),
  name: PropTypes.string,
  disabled: PropTypes.bool,
  errors: PropTypes.arrayOf(PropTypes.shape(PropTypes.object)),
  classes: PropTypes.object,
  fields: PropTypes.arrayOf(
    PropTypes.shape({
      basicInputs: PropTypes.arrayOf(PropTypes.string),
      inputs: PropTypes.objectOf(PropTypes.any),
    }),
  ),
  limit: PropTypes.shape({
    min: PropTypes.number,
    max: PropTypes.number,
  }),
  isRecommendation: PropTypes.bool,
  isAppendBOContentSource: PropTypes.bool,
  appendPersonalizeType: PropTypes.arrayOf(PropTypes.object),
  isBlastCampaign: PropTypes.bool,
  catalogCode: PropTypes.string,
  isViewMode: PropTypes.bool,
  onChange: PropTypes.func,
};
SlideBarWithFields.defaultProps = {
  initValue: [],
  isBlastCampaign: false,
  classes: {},
  disabled: false,
  name: '',
  fields: {
    basicInputs: [],
    inputs: {},
  },
  limit: {},
  catalogCode: '',
  isRecommendation: false,
  isAppendBOContentSource: false,
  appendPersonalizeType: [],
  errors: [],
  isViewMode: false,
  onChange: () => {},
};
