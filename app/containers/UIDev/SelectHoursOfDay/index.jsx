/* eslint-disable react/prop-types */
/* eslint-disable indent */
/* eslint-disable no-shadow */
/* eslint-disable operator-assignment */
/* eslint-disable no-param-reassign */
// Libraries
import { SelectableGroup } from 'react-selectable-fast';
import React, { memo, useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import produce from 'immer';

// Hooks
import { useDeepCompareEffect } from 'hooks/useDeepCompareEffect';

// Components
import EveryDay from './Every';

// Constants
import { mapValueToFe, getDataEveryDay } from './contants';

// Utils
import { checkFullSelectDayOfWeek } from './utils';

const DATA_EVERY = [...getDataEveryDay()];

function SelectDayOfWeek(props) {
  const selectableGroupRef = useRef(null);

  const [dataWeek, setDataWeek] = useState(() => {
    const data = mapValueToFe(props.initData);
    return _.isEmpty(props.initData) ? DATA_EVERY : data;
  });
  useDeepCompareEffect(() => {
    if (props.initData && props.initData.length > 0) {
      const data = mapValueToFe(props.initData);

      setDataWeek(data);
    }
  }, [props.initData]);
  const { isViewMode } = props;

  // Render Data Every Day
  const [dataEveryDay, setDataEveryDay] = useState(DATA_EVERY);

  useEffect(() => {
    const dataWeekTmp = [];
    dataWeek.forEach(each => {
      if (each.isSelected === true) {
        dataWeekTmp.push(Number(each.x));
      }
    });

    const arrFilterEveryday = checkFullSelectDayOfWeek(dataWeek);
    setDataEveryDay(prev => {
      const tempDataEveryday = prev.map(everydayItem => ({
        ...everydayItem,
        isSelected:
          arrFilterEveryday[everydayItem.x] &&
          !arrFilterEveryday[everydayItem.x].length,
      }));

      return tempDataEveryday;
    });

    // console.log('ITEM_SELECT', dataWeekTmp);
    props.callback('ITEM_SELECT', dataWeekTmp);
  }, [dataWeek]);

  const handleSelectEveryDayItem = itemEvery => {
    const temp = produce(dataEveryDay, draft => {
      const indexTemp = draft.findIndex(itemTemp => itemTemp.x === itemEvery.x);
      draft[indexTemp] = {
        ...itemEvery,
        isSelected: itemEvery.isSelected !== true,
      };
    });
    setDataEveryDay(temp);
    setDataWeek(temp);
  };

  useEffect(() => {
    if (
      selectableGroupRef.current &&
      !_.isEmpty(props.initData) &&
      _.isArray(props.initData)
    ) {
      const { registry, selectedItems } = selectableGroupRef.current;
      Array.from(registry).forEach(item => {
        const itemX = _.get(item, 'props.item.x');
        const itemSelectedIdx = props.initData.findIndex(
          selectItem => +selectItem === +itemX,
        );

        if (itemSelectedIdx !== -1) {
          _.set(item, 'state.isSelected', true);
          selectedItems.add(item);
        }
      });
    }
  }, [props.initData]);

  const handleSelectEveryDayItemFinish = selectedItems => {
    const newData = dataEveryDay.map(item => {
      const { x } = item;
      const selectedIdx = selectedItems.findIndex(selectItem => {
        const itemX = _.get(selectItem, 'props.item.x');
        return +itemX === +x;
      });

      return {
        x,
        isSelected: selectedIdx !== -1,
      };
    });
    setDataEveryDay(newData);
    setDataWeek(newData);
  };

  return (
    <SelectableGroup
      ref={selectableGroupRef}
      enableDeselect
      mixedDeselect
      tolerance={0}
      globalMouse={false}
      allowClickWithoutSelected
      selectedItems={props.initData}
      onSelectionFinish={handleSelectEveryDayItemFinish}
    >
      <EveryDay
        isViewMode={isViewMode}
        data={dataEveryDay}
        type="every"
        onClick={item => handleSelectEveryDayItem(item)}
      />
    </SelectableGroup>
  );
}

export default memo(SelectDayOfWeek);
