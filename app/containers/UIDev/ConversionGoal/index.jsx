/* eslint-disable array-callback-return */
/* eslint-disable no-case-declarations */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import { useImmer } from 'use-immer';
import PropTypes from 'prop-types';
import Grid from '@material-ui/core/Grid';
import UISelectWithAPI from 'components/form/UISelectWithAPI';
import {
  UIWrapperDisable as WrapperDisable,
  UIIconButton as IconButton,
  UIButton,
} from '@xlab-team/ui-components';
import FormControl from '@material-ui/core/FormControl';
import {
  ContainerNodeContent,
  // useStyles,
  Title,
  WrapperOption,
  WrapperStyle,

  // WrapperCenter,
  // WrapperGrid,
} from './styled';
import { MAP_TITLE, validateConversion } from './utils';
import { safeParse } from '../../../utils/common';
import TRANSLATE_KEY from '../../../messages/constant';
import { getTranslateMessage } from '../../Translate/util';
import { useStyles } from '../../../components/common/UISchedulerTrigger/styled';
import RadioGroup from '../../../components/Molecules/RadioGroup';
import { TextButton } from '../../../components/common/UIPerformEvent/HeaderCondition/styled';
import { StyledUIButtonWrapper } from '../../../components/common/UIPerformEventV2/styled';
import useUpdateEffect from '../../../hooks/useUpdateEffect';
import { useDispatch } from 'react-redux';
import { update, updateValue } from '../../../redux/actions';
const initState = () => ({
  conversion: {
    journeyGoal: 'none',

    goal: [
      {
        title: 'First goal',
        value: null,
        error: [],
      },
    ],
  },
});

// const onInput = data => {
//   const dataFrequencyCapping = safeParse(data, {});
//   if (Object.keys(dataFrequencyCapping).length === 0) {
//     return {
//       cappingSel: 'unlimited',
//       value: 1,
//       timeUnit: PERIOD_MAP.daily,
//     };
//   }

//   const { timeUnit, value } = dataFrequencyCapping;

//   return {
//     cappingSel: 'limited',
//     value,
//     timeUnit: PERIOD_MAP[timeUnit.value] || PERIOD_MAP.daily,
//   };
// };

const ConversionGoal = props => {
  const [state, setState] = useImmer(initState());
  // Hooks
  const dispatch = useDispatch();
  const { isBlastCampaign, activeNode, prefixKey, componentId } = props;
  const isWebChannel = Number(props.channelId) === 2;
  const isLifeTime = props.channelId && !isWebChannel;
  useEffect(() => {
    // const input = onInput(props.initData);
    if (Object.keys(safeParse(props.initData, {})).length === 0) {
      setState(() => initState());
    } else {
      setState(draft => {
        draft.conversion = props.initData;
      });
    }
  }, []);
  useEffect(() => {
    props.onChange(state.conversion);
  }, [state.conversion]);
  useUpdateEffect(() => {
    const { indexErrors, dataValidateConversion } = validateConversion(
      state.conversion,
    );
    if (
      dataValidateConversion === false &&
      state.conversion.journeyGoal === 'specific'
    ) {
      indexErrors.forEach(each => {
        setState(draft => {
          draft.conversion.goal[each].error = ['This field is empty'];
        });
      });
    } else {
      state.conversion.goal.forEach((each, index) => {
        setState(draft => {
          draft.conversion.goal[index].error = [];
        });
      });
    }
  }, [props.validateKey]);
  const classes = useStyles();
  const callback = (type, data) => {
    switch (type) {
      case 'UPDATE_ERRORS':
        const indices = [];
        state.conversion.goal.forEach((item, index) => {
          if (item.value === data) {
            indices.push(index);
          }
        });
        if (indices.length > 0) {
          indices.map(item => {
            setState(draft => {
              draft.conversion.goal[item].error = [
                MAP_TITLE.conversionInactive,
              ];
            });
          });
          if (!isBlastCampaign) {
            dispatch(
              updateValue(`${prefixKey}@@STORY_ERRORS@@`, {
                errors: { [componentId]: true },
                passValidateKey: true,
              }),
            );
          }
        }

        break;

      default:
        break;
    }
  };
  const addGoal = () => {
    setState(draft => {
      draft.conversion.goal.push({
        title: 'Second goal',
        value: null,
        error: [],
      });
    });
  };
  const deleteGoal = index => {
    const newData = [...state.conversion.goal];
    newData.splice(index, 1);
    setState(draft => {
      draft.conversion.goal = newData;
    });
  };
  const onChange = e => {
    setState(draft => {
      draft.conversion.journeyGoal = e.target.value;
      draft.conversion.goal = [
        {
          title: 'First goal',
          value: null,
          error: [],
        },
      ];
    });
  };
  const onChangeConversion = (value, index) => {
    setState(draft => {
      draft.conversion.goal[index].value = value;
      draft.conversion.goal[index].error = [];
    });
  };
  return (
    <ContainerNodeContent
      className={`${classes.root} ${classes.noPaddingBottom} ${
        classes.marginBottom10
      }`}
      style={{ marginTop: '15px' }}
    >
      <Grid container className={props.isNoPadding ? '' : classes.paddingX}>
        {props.isShowLabel && (
          <Grid
            item
            sm={2}
            style={{ marginTop: '5px', ...props.style }}
            className={`${classes.customView} titl-general-setting`}
          >
            {MAP_TITLE.journeyGoals}:
          </Grid>
        )}
        <Grid container item sm={props.isCollapsed ? 9 : 8}>
          <WrapperDisable disabled={props.disabled}>
            <FormControl component="fieldset">
              <RadioGroup
                row
                label={MAP_TITLE.infoStorySetting}
                value={state.conversion.journeyGoal}
                onChange={onChange}
                name="capping1"
                isViewMode={props.isViewMode}
                options={[
                  {
                    value: 'specific',
                    disabled: props.disabled,
                    label: getTranslateMessage(
                      TRANSLATE_KEY._,
                      'Specific conversions to measure',
                    ),
                  },
                  {
                    value: 'none',
                    disabled: props.disabled,
                    label: getTranslateMessage(TRANSLATE_KEY._, 'None'),
                  },
                ]}
              />
              {state.conversion.journeyGoal === 'specific' &&
                state.conversion.goal.map((each, index) => (
                  <WrapperOption>
                    <Title>{each.title}</Title>
                    <WrapperStyle>
                      <UISelectWithAPI
                        isViewMode={props.isViewMode}
                        placeholder="Select an item"
                        errors={each.error || []}
                        isFooter={false}
                        // placeholderTranslateCode={
                        //   TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM
                        // }
                        // {...props}
                        // options={props.options}
                        initData={each.value}
                        // disabled={props.disabled}
                        index={index}
                        onChange={onChangeConversion}
                        keyAPI="conversion"
                        callback={callback}
                      />
                    </WrapperStyle>
                    {index > 0 && !props.isViewMode && (
                      <div
                        style={{
                          position: 'absolute',
                          right: '-20px',
                        }}
                      >
                        <IconButton
                          onClick={() => deleteGoal(index)}
                          iconName="close"
                          size="20px"
                        />
                      </div>
                    )}
                  </WrapperOption>
                ))}

              {/* <WrapperOption>
                <Title>Second goal</Title>
                <WrapperStyle>
                  <UISelectWithAPI
                    placeholder="Select an item"
                    errors={[]}
                    // placeholderTranslateCode={
                    //   TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM
                    // }
                    // {...props}
                    // options={props.options}
                    // initData={id || sourceId}
                    // disabled={props.disabled}
                    // onChange={onChangeSource}
                    isGetFullValue
                    keyAPI="promotionSource"
                  />
                </WrapperStyle>
                <IconButton iconName="close" size="20px" />
              </WrapperOption> */}
              {state.conversion.goal.length < 2 &&
                state.conversion.journeyGoal === 'specific' &&
                !props.isViewMode && (
                  <StyledUIButtonWrapper style={{ marginTop: '15px' }}>
                    <UIButton
                      theme="outline"
                      fs="12px"
                      style={{
                        border: 'none',
                        background: 'none',
                        padding: '0',
                        color: '#005Eb8',
                      }}
                      reverse
                      iconName="add"
                      onClick={addGoal}
                    >
                      <TextButton>Second Goal</TextButton>
                    </UIButton>
                  </StyledUIButtonWrapper>
                )}
            </FormControl>
          </WrapperDisable>
        </Grid>
      </Grid>
    </ContainerNodeContent>
  );
};
ConversionGoal.propTypes = {
  onChange: PropTypes.func,
  initData: PropTypes.object,
  isShowLabel: PropTypes.bool,
  isNoPadding: PropTypes.bool,
  isBlastCampaign: PropTypes.bool,
};
ConversionGoal.defaultProps = {
  onChange: () => {},
  initData: {},
  isShowLabel: true,
  isNoPadding: false,
  isBlastCampaign: false,
};

export default ConversionGoal;
