/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
/* eslint-disable indent */
// Libraries
import React, { memo, useCallback, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { useImmer } from 'use-immer';
import {
  get,
  isEqual,
  cloneDeep,
  isEmpty,
  omit,
  remove,
  isObject,
} from 'lodash';

// Components
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import { UploadImage } from '@antscorp/antsomi-ui';
import { FormHelperText, Grid } from '@material-ui/core';
import {
  UITextField,
  UIWrapperDisable,
  UITippy,
} from '@xlab-team/ui-components';
import UISelect from 'components/form/UISelectCondition';
import IconXlab from 'components/common/UIIconXlab';
import ErrorBoundary from 'components/common/ErrorBoundary';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';

// Hooks
import { useDeepCompareEffect } from 'hooks/useDeepCompareEffect';
import useDebounce from '../../hooks/useDebounce';

// Constants
import { CATALOG_CODE } from '../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';
import { DEFAULT_ACTION_KEYS } from '../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/constants';
import { TEMPLATE_KEYS } from 'containers/UIPreview/AppPushPreview/AppPushTemplate/constants';

// Styled
import {
  WrapperCenterFlexStart,
  Wrapper,
  WrapperCenter,
  WrapperIconXLab,
  WrapperCenterFlexEnd,
  Title,
  ButtonAdd,
  Callouts,
} from './styled';
import InputPreview from '../../components/Atoms/InputPreview';

// Utils
import { addMessageToQueue } from '../../utils/web/queue';
import { getObjectPropSafely, isProduction } from '../../utils/common';
import { initialSettings } from './utils';
import { getCurrentUserId, getToken } from '../../utils/web/cookie';
// eslint-disable-next-line import/no-cycle
import { getTypeRender } from '../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.form';

// Locales
import { getTranslateMessage } from '../Translate/util';
import TRANSLATE_KEY from '../../messages/constant';

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';

const PATH = 'app/containers/MapGroupFields/index.jsx';

function MapGroupFields(props) {
  const {
    configure,
    calloutsText,
    options,
    minOptions,
    maxOptions,
    name,
    design,
    initValue,
    catalogCode,
    isViewMode,
    currentTemplateInput,
    errors,
    componentKey,
    callback,
    onChange,
  } = props;

  const [inputSettings, setInputSettings] = useImmer(initialSettings(options));
  const { basicInputs = [], inputs = {}, list = [] } = inputSettings;
  const [dataOut, setDataOut] = useImmer({});
  const dataOutDebounce = useDebounce(dataOut, 450);

  const isShowRemove = useMemo(() => list.length > parseInt(minOptions), [
    list,
    minOptions,
  ]);

  const checkShowButton = useCallback(
    index => list.length - 1 === index && list.length < parseInt(maxOptions),
    [list, maxOptions],
  );

  const handleActionGroup = (type = 'ADD_MORE', optionKey = '') => {
    try {
      const activeNodeId = getObjectPropSafely(
        () => configure.main.activeNode.nodeId,
        '',
      );
      if (typeof callback === 'function' && activeNodeId) {
        if (type === 'ADD_MORE') {
          let highestNum = 1;
          list.forEach(each => {
            if (each && each.label) {
              const num = parseInt(each.label.replace(/[^0-9]/g, ''));
              if (num >= highestNum) highestNum = num;
            }
          });
          highestNum += 1;
          const newValue = `button${highestNum}`;

          const newItem = {
            label: `Button ${highestNum}`,
            value: newValue,
          };

          setInputSettings(draft => {
            draft.list.push(newItem);
          });

          if (catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH) {
            const optionsClick = get(inputs, 'advBtnClick.options', []);
            const isRating = currentTemplateInput === TEMPLATE_KEYS.RATING;
            const label = isRating ? 'Submit' : '';

            setDataOut(draft => {
              draft[newValue] = {
                advBtnClick: optionsClick[0],
                advBtnLabel: label,
              };
            });
          }
          return;
        }

        if (type === 'REMOVE' && optionKey) {
          const newItem = cloneDeep(list);
          remove(newItem, eachItem => eachItem.value === optionKey);
          const newData = omit(dataOut, [optionKey]);

          setInputSettings(draft => {
            draft.list = newItem;
          });
          setDataOut(() => newData);
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleActionGroup',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const handleChangeDataGroup = (key, buttonName) => dataIn => {
    setDataOut(draft => {
      draft[buttonName] = {
        ...(draft[buttonName] || {}),
        [key]: dataIn,
      };
    });
  };

  useEffect(() => {
    if (!isEmpty(initValue)) {
      const valueList = Object.keys(initValue);
      const newList = [];

      valueList.forEach(each => {
        const num = each.replace(/[^0-9]/g, '');
        newList.push({
          label: `Button ${num}`,
          value: each,
        });
      });
      setInputSettings(draft => {
        draft.list = newList;
      });
      setDataOut(() => initValue);
    }

    return () => {
      setDataOut(() => ({}));
      setInputSettings(() => initialSettings(options));
    };
  }, [initValue]);

  useEffect(
    () => () => {
      setDataOut(() => {});
      setInputSettings(() => initialSettings());
    },
    [],
  );

  // Sync input configs
  useDeepCompareEffect(() => {
    const newInputs = get(options[0], 'inputs', {});

    if (!isEqual(newInputs, inputs)) {
      setInputSettings(draft => {
        draft.inputs = newInputs;
      });
    }
  }, [options[0] && options[0].inputs, inputs]);

  useEffect(
    function handleSetInputConfigAntsomiAppPush() {
      if (
        typeof callback === 'function' &&
        catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH
      ) {
        callback('SET_INPUT_CONFIGS_ANTSOMI_APP_PUSH', {
          template: currentTemplateInput,
        });
      }
    },
    [currentTemplateInput, catalogCode],
  );

  useEffect(() => {
    if (design === 'update' && typeof props.callback === 'function') {
      callback('UPDATE_OPTIONS_GROUP_DESTINATION', {
        name,
        data: initValue,
        oldOptions: options[0] || {},
      });
    }
  }, [design]);

  // To dataout
  useDeepCompareEffect(() => {
    if (!isEmpty(dataOutDebounce) && typeof onChange === 'function') {
      onChange(dataOutDebounce);
    }
  }, [dataOutDebounce, componentKey]);

  const getComponentInput = ({
    typeRender = '',
    inputName = '',
    buttonName = '',
    valueBinding = '',
    item = {},
  }) => {
    switch (typeRender) {
      case 'imageUrl': {
        // Constants
        const userId = getCurrentUserId();
        const token = getToken();

        return (
          <UploadImage
            isInputMode
            domainMedia={
              isProduction() ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX
            }
            slug="api/v1"
            width="100%"
            paramConfigs={{
              token,
              userId,
              accountId: userId,
            }}
            selectedImage={{
              url: valueBinding,
            }}
            placeholder={item.placeholder || 'Upload or input URL'}
            onChangeImage={image => {
              handleChangeDataGroup(inputName, buttonName)(
                (image && image.url) || '',
              );
            }}
            onRemoveImage={() => {
              handleChangeDataGroup(inputName, buttonName)('');
            }}
          />
        );
      }
      case 'singleLineAddPersonalize': {
        return (
          <UIWrapperDisable disabled={props.disabled}>
            <TinymceEditor
              typeComponent="input"
              onChange={valueOut =>
                handleChangeDataGroup(inputName, buttonName)(valueOut)
              }
              isAppendBOContentSource
              appendPersonalizeType={props.appendPersonalizeType}
              placeholder={item.placeholder || ''}
              errors={[]}
              onChangeOthers={() => {}}
              initData={valueBinding}
              enableShortLink
              width="100%"
              isBlastCampaign={props.isBlastCampaign}
            />
          </UIWrapperDisable>
        );
      }
      case 'selectDropdown': {
        return (
          <UISelect
            onlyParent
            use="tree"
            isSearchable
            options={item.options || []}
            value={valueBinding}
            onChange={valueOut =>
              handleChangeDataGroup(inputName, buttonName)(valueOut)
            }
            placeholder={getTranslateMessage(
              TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
              'Select an item',
            )}
            fullWidthPopover
            disabled={props.disabled}
            labelWidth="100%"
          />
        );
      }
      default: {
        return (
          <UITextField
            value={valueBinding}
            onChange={valueOut =>
              handleChangeDataGroup(inputName, buttonName)(valueOut)
            }
            maxLength={item.maxLength}
            placeholder={item.placeholder || ''}
            textFieldProps={{
              disabled: props.disabled,
              size: 'small',
              multiline: false,
              rowsMax: 1,
              style: {
                width: '100%',
              },
            }}
          />
        );
      }
    }
  };

  const renderDynamicFields = ({ buttonName = '' }) => {
    if (!Array.isArray(basicInputs) || basicInputs.length === 0) return null;

    return basicInputs.map(each => {
      const item = inputs[each];
      const valueBinding = getObjectPropSafely(
        () => (dataOut[buttonName] && dataOut[buttonName][each]) || '',
      );
      const {
        inputType = '',
        inputFormat = '',
        canImportFromMediaLibrary = false,
        canAddPersonalization = false,
      } = item;
      const typeRender = getTypeRender({
        inputType,
        inputFormat,
        canImportFromMediaLibrary,
        canAddPersonalization,
      });
      let componentInput = null;

      if (typeRender) {
        componentInput = getComponentInput({
          item,
          typeRender,
          buttonName,
          valueBinding,
          inputName: each,
        });
      }

      // NOTE: check hidden input field for special case is an Antsomi App Push
      if (catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH) {
        const itemBtn = getObjectPropSafely(
          () => dataOutDebounce[buttonName] || {},
          {},
        );
        let btnClickTemp = cloneDeep(itemBtn.advBtnClick);
        if (isObject(btnClickTemp)) {
          btnClickTemp = btnClickTemp.value;
        }

        if (
          Object.values(DEFAULT_ACTION_KEYS).includes(each) &&
          btnClickTemp !== each
        ) {
          return null;
        }
      }

      return (
        <Grid
          key={`${buttonName}-${each}`}
          container
          className={`${props.classes.item}`}
          style={props.style}
        >
          <Grid
            item
            sm="auto"
            className={classNames(
              props.classes.title,
              props.classes.styledChannel,
            )}
            style={{
              justifyContent: props.isTitleAlignLeft
                ? 'flex-start'
                : 'flex-end',
              minWidth: '200px',
              maxWidth: '200px',
            }}
          >
            <WrapperCenterFlexEnd>
              <Title
                className={props.classes.spacingTitle}
                style={
                  props.isBlastCampaign
                    ? {
                        whiteSpace: 'break-spaces',
                        wordBreak: 'initial',
                        textAlign: 'right',
                      }
                    : {}
                }
              >
                {item.label}{' '}
                <span style={{ color: '#f44336' }}>
                  {item.isRequired && '*'}
                </span>
              </Title>
            </WrapperCenterFlexEnd>
          </Grid>
          <Grid
            item
            sm="auto"
            className={props.classes.content}
            style={{
              flex: 1,
              minWidth: 'calc(100% - 200px)',
              maxWidth: 'calc(100% - 200px)',
            }}
          >
            <InputPreview
              value={isObject(valueBinding) ? valueBinding.value : valueBinding}
              type="input"
              className={`${props.classes.fontSize}, ${props.classes.content}`}
              isViewMode={isViewMode}
            >
              {componentInput}
              <FormHelperText
                id="component-helper-text"
                error={
                  !!getObjectPropSafely(
                    () =>
                      errors[0] &&
                      errors[0][buttonName] &&
                      errors[0][buttonName][each],
                  )
                }
              >
                {getObjectPropSafely(
                  () =>
                    errors[0] &&
                    errors[0][buttonName] &&
                    errors[0][buttonName][each],
                )}
              </FormHelperText>
            </InputPreview>
          </Grid>
        </Grid>
      );
    });
  };

  const renderContent = ({ listInfo = [] }) => {
    const content = listInfo.map((each, index) => {
      const { label = '', value: contentValue = '' } = each;

      return (
        <Grid
          // eslint-disable-next-line react/no-array-index-key
          key={`${contentValue}-${index}`}
          container
          className={`${props.classes.item} ${
            props.classes.isCustomSpace &&
            isViewMode &&
            catalogCode === 'trello'
              ? 'custom-space'
              : ''
          }`}
          style={props.style}
        >
          <Grid item sm={12}>
            <WrapperCenter style={{ height: 'auto' }}>
              <span
                style={{
                  color: 'rgba(51, 51, 51, 1)',
                  fontSize: '12px',
                  fontWeight: 'bold',
                }}
              >
                {`Button ${index + 1}`}
              </span>
              {isShowRemove && !isViewMode && (
                <WrapperIconXLab
                  onClick={() => handleActionGroup('REMOVE', contentValue)}
                  className="m-left-3 m-top-1"
                >
                  <IconXlab name="close" color="#005eb8" fontSize="18px" />
                </WrapperIconXLab>
              )}
            </WrapperCenter>
            <Wrapper>
              {renderDynamicFields({
                buttonName: contentValue,
              })}
            </Wrapper>
            {checkShowButton(index) && !isViewMode && (
              <WrapperCenterFlexStart style={{ height: 'auto' }}>
                <ButtonAdd
                  iconName="add"
                  reverse
                  height={32}
                  iconSize="12"
                  isNoBackround
                  theme="text-link"
                  onClick={() => handleActionGroup('ADD_MORE', contentValue)}
                >
                  <span>Add button</span>
                </ButtonAdd>
              </WrapperCenterFlexStart>
            )}
          </Grid>
        </Grid>
      );
    });

    return content;
  };

  return (
    <ErrorBoundary path={PATH}>
      {renderContent({ listInfo: list })}
      <Callouts isShow={!!calloutsText}>
        <UITippy content={calloutsText}>
          <InfoOutlinedIcon
            className="icon-info-rule"
            style={{ fontSize: '20px', color: '#333333' }}
          />
        </UITippy>
        <span style={{ fontSize: '12px' }}>{calloutsText}</span>
      </Callouts>
    </ErrorBoundary>
  );
}

MapGroupFields.propTypes = {
  configure: PropTypes.object,
  calloutsText: PropTypes.string,
  options: PropTypes.array,
  minOptions: PropTypes.number,
  maxOptions: PropTypes.number,
  name: PropTypes.string,
  design: PropTypes.string,
  initValue: PropTypes.any,
  catalogCode: PropTypes.string,
  isViewMode: PropTypes.bool,
  currentTemplateInput: PropTypes.string,
  errors: PropTypes.array,
  componentKey: PropTypes.string,
  callback: PropTypes.func,
  onChange: PropTypes.func,
};
MapGroupFields.defaultProps = {
  configure: {},
  calloutsText: '',
  options: [],
  minOptions: 1,
  maxOptions: 1,
  componentKey: '',
  name: '',
  design: '',
  initValue: null,
  catalogCode: '',
  isViewMode: false,
  currentTemplateInput: '',
  errors: [],
  callback: () => {},
  onChange: () => {},
};
export default memo(MapGroupFields);
