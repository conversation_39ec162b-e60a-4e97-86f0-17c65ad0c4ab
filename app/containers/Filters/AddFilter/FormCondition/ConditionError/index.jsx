/* eslint-disable react/prop-types */
import IconXlabColor from 'components/common/UIIconXlabColor';
import { Tooltip } from '@material-ui/core';
import InfoIcon from '@material-ui/icons/Info';
import React from 'react';
import {
  ARCHIVE_STATUS,
  REMOVE_STATUS,
  STATUS_ITEM_CODE,
} from '../../../../../utils/constants';
import TranslateMessage from '../../../../Translate';
import TRANSLATE_KEY from '../../../../../messages/constant';
import {
  getErrorsByStatusItemCode,
  getWarnsByStatusItemCode,
} from '../../../../../utils/web/properties';
import { FormConditionError, FormConditionWarn } from '../styles';
import { getTranslateMessage } from '../../../../Translate/util';

function ConditionError({
  statusItemCode = STATUS_ITEM_CODE.ACTIVE,
  item,
  error = 0,
  keyError = 0,
  objectLabel,
  isErrorIcon = false,
  isCheckEvent = false,
  isError = true,
}) {
  let errors = [];
  let warns = [];
  if (statusItemCode !== STATUS_ITEM_CODE.ACTIVE) {
    errors = getErrorsByStatusItemCode(
      objectLabel ||
        getTranslateMessage(TRANSLATE_KEY._ITEM_NAME_ATTRIBUTE, 'attribute'),
      item.get('property'),
    );

    warns = getWarnsByStatusItemCode(
      objectLabel ||
        getTranslateMessage(TRANSLATE_KEY._ITEM_NAME_ATTRIBUTE, 'attribute'),
      item.get('property'),
    );
  }

  if (item && item.get('property')) {
    const { status } = item.get('property');

    if (Number(status) === ARCHIVE_STATUS) {
      errors.push(
        getTranslateMessage(
          TRANSLATE_KEY.KHONG_CO,
          'This attribute is archived',
        ),
      );
    }

    if (Number(status) === REMOVE_STATUS) {
      let message = getTranslateMessage(
        TRANSLATE_KEY.KHONG_CO,
        'This attribute does not exist',
      );
      if (isCheckEvent) {
        message = getTranslateMessage(TRANSLATE_KEY._, 'Event not available');
      }
      errors.push(message);
    }
  }

  if (errors.length > 0) {
    return (
      <FormConditionError className="p-top-1">{errors[0]}</FormConditionError>
    );
  }

  if (warns.length > 0) {
    if (isErrorIcon) {
      return (
        <Tooltip title={warns[0]} placement="top-start">
          <span
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              marginRight: '10px',
            }}
          >
            <IconXlabColor name="warning" fontSize="14px" />
          </span>
        </Tooltip>
      );
    }

    return (
      <FormConditionWarn className="p-top-1">{warns[0]}</FormConditionWarn>
    );
  }

  if (keyError === error && isError) {
    if (error === 1) {
      return (
        <FormConditionError className="p-top-1">
          <TranslateMessage
            code={TRANSLATE_KEY._NOTI_FIELD_REQUIRED}
            defaultMessage="Property is required"
          />
        </FormConditionError>
      );
    }
    if (error === 3) {
      return (
        <FormConditionError className="p-top-1">
          <TranslateMessage
            code={TRANSLATE_KEY._NOTI_EMPTY_VALUE_FILTER}
            defaultMessage="Value filter empty"
          />
        </FormConditionError>
      );
    }
    // case endDate & endValue will align right
    if (error === 4) {
      return (
        <FormConditionError className="p-top-1" alignRight>
          <TranslateMessage
            code={TRANSLATE_KEY._NOTI_EMPTY_VALUE_FILTER}
            defaultMessage="Value filter empty"
          />
        </FormConditionError>
      );
    }
    if (error === 5) {
      return (
        <FormConditionError className="p-top-1" alignRight>
          <TranslateMessage
            code={TRANSLATE_KEY._NOTI_FIELD_IS_REQUIRED}
            defaultMessage="Field is required"
          />
        </FormConditionError>
      );
    }
    if (error === 6) {
      return (
        <FormConditionError className="p-top-1">
          <TranslateMessage
            code=""
            defaultMessage="This attribute is archived"
          />
        </FormConditionError>
      );
    }
    if (error === 7) {
      return (
        <FormConditionError className="p-top-1">
          <TranslateMessage
            code="_NOTI_NO_PERMISSION_TO_UPDATE_INFO"
            defaultMessage="You do not have permission to update this object"
          />
        </FormConditionError>
      );
    }
    if (error === 8) {
      return (
        <FormConditionError className="p-top-1" alignRight>
          <TranslateMessage
            code={TRANSLATE_KEY._NOTI_FIELD_IS_REQUIRED}
            defaultMessage="Field is required"
          />
        </FormConditionError>
      );
    }
    if (error === 9) {
      return (
        <FormConditionError className="p-top-1">
          <TranslateMessage
            code={TRANSLATE_KEY._}
            defaultMessage="Value must be greater than 0"
          />
          <Tooltip
            title={getTranslateMessage(
              TRANSLATE_KEY._,
              "Select the 'not exist' operator in case the event is not performed",
            )}
            placement="bottom-start"
          >
            <InfoIcon
              style={{ marginLeft: '10px', fontSize: '15px' }}
              className="icon-info"
            />
          </Tooltip>
        </FormConditionError>
      );
    }
  }
  return null;
}

export default ConditionError;
