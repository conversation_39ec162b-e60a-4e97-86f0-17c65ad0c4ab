/* eslint-disable no-else-return */
/* eslint-disable prefer-destructuring */
/* eslint-disable camelcase */
import { Map, OrderedMap } from 'immutable';
import { cloneDeep } from 'lodash';
import {
  OPERATORS,
  MAP_OPERATORS,
  OPERATORS_CODE,
  OPERATORS_OPTION,
  CODE,
} from './_UI/operators';
import { STATUS_ITEM_CODE } from '../../utils/constants';
import { generateKey, safeParse } from '../../utils/common';
import { toArrayUIWithDataType } from './utils.extends';
import { MAP_BOOLEAN_PROPERTIES } from './constants';
import { toArrayApiWithDataType } from '../../services/map';
import { MAP_SEMATIC } from '../Segment/Content/Condition/constants';
import { MAP_ATTR_TYPE } from '../modals/ModalComputedType/utils';
import { safeParseV2 } from '../../utils/web/utils';

export function buildValueConditionFromAPI(item, property) {
  const objValue = getValueConditionFromAPIV2(item, property);
  const temp = {
    value: objValue.value,
    valueEnd: objValue.valueEnd,
    timeUnit: objValue.timeUnit,
    initValue: objValue.value,
    error: 0,
  };
  if (
    item.version &&
    item.version === 'v2' &&
    (item.data_type === 'datetime' || item.compute_data_type === 'datetime')
  ) {
    // if (isCheckConditionOperatorwithAvendPicker(item.operator)) {
    //   temp.value = objValue.value[0];
    //   temp.initValue = objValue.value[0];
    // }
    const semantic = MAP_SEMATIC.list.filter(
      each => each.format === item.semantics,
    );
    temp.semantic = semantic[0];
    temp.semantics = MAP_SEMATIC.list;
  }
  return temp;
}

export function buildValueConditionFromUI(item, v2) {
  let value = item.get('value');
  let timeUnit = '';
  const operatorValue = item.get('operator') && item.get('operator').value;
  const eventAttribute = item.get('eventAttribute');

  if (isCheckConditionOperatorExits(operatorValue)) {
    if (
      item &&
      item.has('property') &&
      item.get('property').itemDataType &&
      item.get('property').itemDataType.startsWith('array_')
    ) {
      value = [];
    } else {
      value = '';
    }
  } else if (operatorValue === OPERATORS_CODE.BETWEEN) {
    // if (item.get('dataType') === 'number') {
    //   value = `${item.get('value')} AND ${item.get('valueEnd')}`;
    // } else if (item.get('dataType') === 'datetime') {
    //   const date = parseDate(item.get('value'), DATE_FORMAT_VALUE);
    //   const dateEnd = parseDate(item.get('valueEnd'), DATE_FORMAT_VALUE);
    //   value = `${date.getTime()} AND ${dateEnd.getTime()}`;
    // } else {
    //   value = `${item.get('value')} AND ${item.get('valueEnd')}`;
    // }
    if (
      (item.get('property').dataType === 'datetime' ||
        item.get('dataType') === 'datetime') &&
      v2
    ) {
      value = item.get('value');
    } else {
      value = `${item.get('value')} AND ${item.get('valueEnd')}`;
    }
  } else if (operatorValue === OPERATORS_CODE.BETWEEN_TIME_AGO) {
    value = `${item.get('value')} AND ${item.get('valueEnd')}`;
    timeUnit = safeParse(item.get('timeUnit'), 'DAY');
  } else if (
    item.get('property').dataType === 'datetime' ||
    item.get('dataType') === 'datetime'
  ) {
    if (isOperatorDateTimeAgo(operatorValue) === false) {
      // const date = parseDate(item.get('value'), DATE_FORMAT_VALUE);
      // value = `${date.getTime()}`;
      value = item.get('value');
    } else {
      value = `${value}`;
    }
    timeUnit = safeParse(item.get('timeUnit'), 'DAY');
  } else if (isOperatorMulti(operatorValue)) {
    // console.log('value', value);
    if (!Array.isArray(value)) {
      if (typeof value === 'string' || value.length > 0) {
        const tmp = value.split(',');
        value = toArrayApiWithDataType(tmp, item.get('property').dataType);
      } else {
        // value = [];
        value = [value];
      }
    } else {
      value = toArrayApiWithDataType(value, item.get('property').dataType);
    }
  } else if (item.get('property').dataType === 'boolean') {
    if (value === '' || value === undefined) {
      value = MAP_BOOLEAN_PROPERTIES.true.value;
    }
  } else if (Array.isArray(value)) {
    // console.log('----', value);
    value = toArrayApiWithDataType(value, item.get('property').dataType);
  } else if (eventAttribute && Object.keys(eventAttribute).length > 0) {
    // console.log('----', value);
    value = `${value}`;
    // value = toArrayApiWithDataType(value, item.get('property').dataType);
  } else if (item.get('property').dataType === 'number') {
    // console.log('----', value);
    value = Number(value);
    // value = toArrayApiWithDataType(value, item.get('property').dataType);
  } else {
    value = `${value}`;
  }

  if (timeUnit === '') {
    return { value };
  }
  return { value, time_unit: timeUnit };
}
export function buildValueConditionFromUISegment(
  item,
  v2,
  property,
  computeType = {},
) {
  let value = item.get('value');
  let timeUnit = '';
  const operatorValue = item.get('operator').value;
  const eventAttribute = item.get('eventAttribute');
  if (isCheckConditionOperatorExits(operatorValue)) {
    if (
      item &&
      property &&
      property.itemDataType &&
      property.itemDataType.startsWith('array_')
    ) {
      value = [];
    } else {
      value = '';
    }
  } else if (operatorValue === OPERATORS_CODE.BETWEEN) {
    // if (item.get('dataType') === 'number') {
    //   value = `${item.get('value')} AND ${item.get('valueEnd')}`;
    // } else if (item.get('dataType') === 'datetime') {
    //   const date = parseDate(item.get('value'), DATE_FORMAT_VALUE);
    //   const dateEnd = parseDate(item.get('valueEnd'), DATE_FORMAT_VALUE);
    //   value = `${date.getTime()} AND ${dateEnd.getTime()}`;
    // } else {
    //   value = `${item.get('value')} AND ${item.get('valueEnd')}`;
    // }
    if (
      (property.dataType === 'datetime' ||
        item.get('dataType') === 'datetime') &&
      v2 &&
      computeType.value !== MAP_ATTR_TYPE.unique_list_count.value
    ) {
      value = item.get('value');
    } else {
      value = `${item.get('value')} AND ${item.get('valueEnd')}`;
    }
  } else if (operatorValue === OPERATORS_CODE.BETWEEN_TIME_AGO) {
    value = `${item.get('value')} AND ${item.get('valueEnd')}`;
    timeUnit = safeParse(item.get('timeUnit'), 'DAY');
  } else if (
    property.dataType === 'datetime' ||
    item.get('dataType') === 'datetime'
  ) {
    if (isOperatorDateTimeAgo(operatorValue) === false) {
      // const date = parseDate(item.get('value'), DATE_FORMAT_VALUE);
      // value = `${date.getTime()}`;
      value = item.get('value');
    } else {
      value = `${value}`;
    }
    timeUnit = safeParse(item.get('timeUnit'), 'DAY');
  } else if (isOperatorMulti(operatorValue)) {
    // console.log('value', value);
    if (!Array.isArray(value)) {
      if (typeof value === 'string' || value.length > 0) {
        const tmp = value.split(',');
        value = toArrayApiWithDataType(tmp, property.dataType);
      } else {
        // value = [];
        value = [value];
      }
    } else {
      value = toArrayApiWithDataType(value, property.dataType);
    }
  } else if (property.dataType === 'boolean') {
    if (value === '' || value === undefined) {
      value = MAP_BOOLEAN_PROPERTIES.true.value;
    }
  } else if (Array.isArray(value)) {
    // console.log('----', value);
    value = toArrayApiWithDataType(value, property.dataType);
  } else if (eventAttribute && Object.keys(eventAttribute).length > 0) {
    // console.log('----', value);
    value = `${value}`;
    // value = toArrayApiWithDataType(value, item.get('property').dataType);
  } else if (property.dataType === 'number') {
    // console.log('----', value);
    value = Number(value);
    // value = toArrayApiWithDataType(value, item.get('property').dataType);
  } else {
    value = `${value}`;
  }

  if (timeUnit === '') {
    return { value };
  }
  return { value, time_unit: timeUnit };
}
export function getTimeUnitConditionFromUI(item) {
  return safeParse(item.get('timeUnit'), '');
}

export function validateShowValue(operator) {
  if (operator !== null) {
    const operatorValue = safeParse(operator.value, '');
    if (operatorValue === 'not_exists' || operatorValue === 'exists') {
      return false;
    }
  }
  return true;
}

export function validateItemConditionV0(
  property = null,
  operator = null,
  value = '',
  valueEnd = '',
) {
  if (property !== null && operator !== null) {
    const operatorValue = safeParse(operator.value, '');

    if (
      operatorValue === OPERATORS_CODE.NOT_EXISTS ||
      operatorValue === OPERATORS_CODE.EXISTS ||
      operatorValue === 'reset_value'
    ) {
      return true;
    }
    if (
      property.dataType !== undefined &&
      property.dataType.startsWith('array')
    ) {
      // }
      // if (property.dataType === 'array') {
      if (value === '' || (Array.isArray(value) && value.length === 0)) {
        return false;
      }
    }
    if (property.dataType === 'boolean') {
      return true;
    }

    if (value === null) {
      return false;
    }

    if (
      operatorValue === OPERATORS_CODE.BETWEEN ||
      operatorValue === OPERATORS_CODE.BETWEEN_TIME_AGO
    ) {
      if (value !== '' && valueEnd !== '' && valueEnd !== null) {
        return true;
      }
      return false;
    } else if (isOperatorMulti(operatorValue)) {
      if (value === null || value.length === 0) {
        return false;
      }
    }
    if (operatorValue !== '' && value !== '' && value !== null) {
      return true;
    }
  }
  return false;
}

export function validateItemCondition(item, computeType) {
  let property = item.get('property');
  const operator = item.get('operator');
  const value = item.get('value');
  const valueEnd = item.get('valueEnd');
  const eventAttribute = safeParse(item.get('eventAttribute'), null);
  if (
    computeType &&
    computeType.value !== 'event_counter' &&
    item.get('property') !== null
  ) {
    property = Object.keys(eventAttribute).length === 0 ? null : eventAttribute;
  }
  const out = validateItemConditionV0(property, operator, value, valueEnd);
  return out;
}

export function validateRulesCondition(conditions, version) {
  const res = {
    total: 0,
    miss: 0,
    // status: false,
    errors: [],
  };
  if (conditions.has(version)) {
    conditions.get(version).forEach((condition, groupKey) => {
      condition.forEach((item, itemKey) => {
        res.total += 1;
        if (!validateItemCondition(item)) {
          res.miss += 1;
          let error = 3;

          const property = item.get('property');
          const operator = item.get('operator');

          if (operator === null || operator === undefined) {
            error = 2;
          }
          if (property === null || property === undefined) {
            error = 1;
          }
          res.errors.push({ groupKey, itemKey, error });
        }
      });
    });
  }
  // res.status = res.miss < res.total && res.total > 0;
  return res;
}

export function bindValidateResultToCondition(conditions, errors, version) {
  if (conditions.has(version)) {
    const listErr = [];
    conditions.get(version).forEach((groups, groupKey) => {
      groups.forEach((item, itemKey) => {
        if (item.get('error') > 0) {
          listErr.push({ groupKey, itemKey, error: 0 });
        }
      });
    });

    listErr.forEach(item => {
      // eslint-disable-next-line no-param-reassign
      conditions = conditions.setIn(
        [version, item.groupKey, item.itemKey, 'error'],
        item.error,
      );
    });

    errors.forEach(item => {
      // eslint-disable-next-line no-param-reassign
      conditions = conditions.setIn(
        [version, item.groupKey, item.itemKey, 'error'],
        item.error,
      );
    });
  }
  return conditions;
}

function getValueConditionFromAPIV2(item, property) {
  // let { value } = item;
  let value = safeParseV2(item.value, '');
  let valueEnd;
  let timeUnit;

  if (item.operator === OPERATORS_CODE.BETWEEN) {
    if (typeof value === 'string') {
      const splitValue = value.split(' AND ');
      if (property.dataType === 'number') {
        value = splitValue[0];
        valueEnd = splitValue[1];
      } else if (property.dataType === 'datetime') {
        // value = formatDate(
        //   new Date(parseInt(splitValue[0])),
        //   DATE_FORMAT_VALUE,
        // );
        // valueEnd = formatDate(
        //   new Date(parseInt(splitValue[1])),
        //   DATE_FORMAT_VALUE,
        // );
        value = splitValue[0];
        valueEnd = splitValue[1];
      } else {
        value = splitValue[0];
        valueEnd = splitValue[1];
      }
    }
  } else if (item.operator === OPERATORS_CODE.BETWEEN_TIME_AGO) {
    if (typeof value === 'string') {
      const splitValue = value.split(' AND ');
      if (splitValue.length === 2) {
        value = splitValue[0];
        valueEnd = splitValue[1];
        timeUnit = item.time_unit;
      }
    }
  } else if (property.dataType === 'datetime') {
    if (isCheckValueCalendar(item.operator)) {
      // value = formatDate(new Date(parseInt(value)), DATE_FORMAT_VALUE);
      value = item.value;
    } else {
      timeUnit = item.time_unit;
    }
  } else if (isOperatorMulti(item.operator)) {
    if (!Array.isArray(value)) {
      if (typeof value === 'string' && value !== '') {
        const arr = value.split(',');
        // console.log('----', value, arr);
        value = toArrayUIWithDataType(arr, property.dataType);
        // return arr;
      }
    } else {
      value = toArrayUIWithDataType(value, property.dataType);
    }
    // value = Number(item.get('value'));
  }
  // console.log('----', { value, valueEnd, timeUnit });

  return { value, valueEnd, timeUnit };
}

export function isCheckValueCalendar(operatorValue) {
  if (
    operatorValue === OPERATORS_CODE.NOT_EXISTS ||
    operatorValue === OPERATORS_CODE.EXISTS ||
    isOperatorDateTimeAgo(operatorValue)
  ) {
    return false;
  }
  return true;
}

export function isCheckConditionBoolean(operatorValue, dataType) {
  if (
    (operatorValue === OPERATORS_CODE.EQUALS ||
      operatorValue === OPERATORS_CODE.NOT_EQUALS ||
      operatorValue === 'change_value') &&
    dataType === 'boolean'
  ) {
    return true;
  }

  return false;
}

export function isCheckConditionOperatorExits(operatorValue) {
  if (
    operatorValue === OPERATORS_CODE.EXISTS ||
    operatorValue === OPERATORS_CODE.NOT_EXISTS
  ) {
    return true;
  }

  return false;
}

export function isCheckConditionMultiCheckbox(dataType) {
  // return dataType === 'array';
  // return dataType.startsWith('array') === 'array';
  return dataType.startsWith('array');
}

export function isCheckConditionBetween(operatorValue) {
  if (operatorValue === OPERATORS_CODE.BETWEEN) {
    return true;
  }
  return false;
}

export function isCheckPropertyHasAutoSuggestion(property = {}) {
  if (property === null || property === undefined) {
    return false;
  }
  const configSuggestion = safeParse(property.configSuggestion, null);

  return configSuggestion !== null;
}

export function isCheckConditionAutoSuggestion(
  operatorValue,
  property = {},
  operators = { list: [], set: new Set([]) },
  dataType = 'string',
) {
  // console.log('isCheckConditionAutoSuggestion', operatorValue, property, dataType);
  // array_number for segment_ids
  // if (
  //   property === null ||
  //   property === undefined ||
  //   !['string', 'text', 'array_number'].includes(dataType)
  // ) {
  //   return false;
  // }
  if (
    property === null ||
    property === undefined
    // !['string', 'text', 'array_number'].includes(dataType)
  ) {
    return false;
  }
  const { autoSuggestion, itemTypeName } = property;

  if ((!operators.set || !operators.set.has) && Array.isArray(operators.list)) {
    operators.set = new Set(operators.list.map(item => item.value));
  }

  // check with operatorValue for use case edit with condition matches and not not matches
  if (isOperatorMulti(operatorValue) && !operators.set.has(operatorValue)) {
    return true;
  }
  const validOperator = isOperatorSuggestion(operatorValue);
  // if (validOperator) {
  //   return true;
  // }
  return (
    validOperator &&
    (autoSuggestion === 1 ||
      (autoSuggestion === 0 && dataType === 'string' && itemTypeName))
  );
  // return autoSuggestion === 1;
  // return false;
}

export function isOperatorSuggestion(operatorValue = '') {
  if (
    operatorValue === OPERATORS_CODE.MATCHES ||
    operatorValue === OPERATORS_CODE.NOT_MATCHES ||
    operatorValue === OPERATORS_CODE.EQUALS ||
    operatorValue === OPERATORS_CODE.NOT_EQUALS ||
    operatorValue === OPERATORS_CODE.CONTAINS ||
    operatorValue === OPERATORS_CODE.DOESNT_CONTAIN
  ) {
    return true;
  }

  return false;
}

export function isPropertyDislayFormatNumber(item) {
  if (
    item.get('property') !== null &&
    item.get('property') !== undefined &&
    item.get('property').displayFormat !== null
  ) {
    const displayFormat = safeParse(item.get('property').displayFormat, {});
    const { type } = displayFormat;

    if (type === 'NUMBER' || type === 'PERCENTAGE' || type === 'CURRENCY') {
      return true;
    }
  }
  return false;
}
export function isPropertyDislayFormatNumberwithProperty(
  property,
  computeType = {},
) {
  if (
    property !== null &&
    property !== undefined &&
    property.displayFormat !== null
  ) {
    const displayFormat = safeParse(property.displayFormat, {});
    const { type } = displayFormat;
    if (
      type === 'NUMBER' ||
      type === 'PERCENTAGE' ||
      type === 'CURRENCY' ||
      computeType.value === 'unique_list_count'
    ) {
      return true;
    }
  }
  return false;
}
export function isPropertyDislayFormatNumberWithProperty(property) {
  if (
    property !== null &&
    property !== undefined &&
    property.displayFormat !== null
  ) {
    const displayFormat = safeParse(property.displayFormat, {});
    const { type } = displayFormat;

    if (type === 'NUMBER' || type === 'PERCENTAGE' || type === 'CURRENCY') {
      return true;
    }
  }
  return false;
}

export function isOperatorObjectIdHasSuggestion(operatorValue = '', item) {
  if (
    operatorValue === OPERATORS_CODE.MATCHES ||
    operatorValue === OPERATORS_CODE.NOT_MATCHES
    //  ||
    // operatorValue === OPERATORS_CODE.EQUALS ||
    // operatorValue === OPERATORS_CODE.NOT_EQUALS
  ) {
    return true;
  }

  const itemDataType = item.get('itemDataType');
  if (itemDataType === 'string') {
    if (
      operatorValue === OPERATORS_CODE.EQUALS ||
      operatorValue === OPERATORS_CODE.NOT_EQUALS
    ) {
      return true;
    }
  }

  return false;
}

export function isOperatorDateTimeAgo(operatorValue = '') {
  if (
    operatorValue === OPERATORS_CODE.EQUAL_TIME_AGO ||
    operatorValue === OPERATORS_CODE.NOT_EQUAL_TIME_AGO ||
    operatorValue === OPERATORS_CODE.BEFORE_TIME_AGO ||
    operatorValue === OPERATORS_CODE.AFTER_TIME_AGO ||
    operatorValue === OPERATORS_CODE.BETWEEN_TIME_AGO
  ) {
    return true;
  }

  return false;
}

export function isOperatorMulti(operatorValue = '') {
  if (
    operatorValue === OPERATORS_CODE.MATCHES ||
    operatorValue === OPERATORS_CODE.NOT_MATCHES ||
    operatorValue === OPERATORS_CODE.DOESNT_INCLUDE ||
    operatorValue === OPERATORS_CODE.INCLUDES
  ) {
    return true;
  }

  return false;
}

export function isCheckHiddenValue(operatorValue) {
  if (
    operatorValue !== '' &&
    (operatorValue === OPERATORS_CODE.NOT_EXISTS ||
      operatorValue === OPERATORS_CODE.EXISTS)
  ) {
    return true;
  }
  return false;
}

export function getOperatorByDataType(dataType) {
  return OPERATORS[dataType];
}

export function getOperatorByProperty(property = {}, semantic = {}) {
  // if label_ids thì phải return về opeartor của label
  const { itemDataType, autoSuggestion, itemTypeName } = property;
  const filterOperators = safeParse(property.operators, {});
  const dataType = itemDataType || property.dataType;
  // get hard operators
  if (Object.keys(filterOperators).length > 0) {
    return filterOperators;
  }

  // kha nang khi ma autoSuggestion = 1 vơi datatype ko ho tro.
  if (isCheckConditionBoolean(filterOperators.value, dataType)) {
    return safeParse(OPERATORS[dataType], { list: [], set: new Set([]) });
  }
  if (isCheckConditionBetween(filterOperators.value)) {
    return safeParse(OPERATORS[dataType], { list: [], set: new Set([]) });
  }

  if (dataType === 'datetime') {
    return safeParse(
      Object.keys(semantic).length > 0
        ? OPERATORS[semantic.dataType]
        : OPERATORS[dataType],
      { list: [], set: new Set([]) },
    );
  }

  // if (autoSuggestion === 1 && !dataType.startsWith('array')) {
  // if (autoSuggestion === 1 && ['string', 'text'].includes(dataType)) {
  //   return OPERATORS.suggestion;
  // }
  const operators = safeParse(cloneDeep(OPERATORS[dataType]), {
    list: [],
    set: new Set([]),
  });
  const isDataTypeArray = dataType?.startsWith('array');
  // console.log('operators', operators, operators.set.has(CODE.NOT_MATCHES));
  if (
    autoSuggestion === 1 ||
    (autoSuggestion === 0 && dataType === 'string' && itemTypeName)
  ) {
    if (!operators.set.has(CODE.NOT_MATCHES)) {
      operators.list.unshift(OPERATORS_OPTION.NOT_MATCHES);
      operators.set.add(CODE.NOT_MATCHES);
    }

    if (!operators.set.has(CODE.MATCHES)) {
      operators.list.unshift(OPERATORS_OPTION.MATCHES);
      operators.set.add(CODE.MATCHES);
    }
  } else if (!isDataTypeArray && autoSuggestion === 0) {
    if (operators.set.has(CODE.NOT_MATCHES)) {
      operators.list = operators.list.filter(
        operator => operator.value !== OPERATORS_OPTION.NOT_MATCHES.value,
      );
      operators.set.delete(CODE.NOT_MATCHES);
    }

    if (operators.set.has(CODE.MATCHES)) {
      operators.list = operators.list.filter(
        operator => operator.value !== OPERATORS_OPTION.MATCHES.value,
      );
      operators.set.delete(CODE.MATCHES);
    }
  }
  return operators;
  // return safeParse(OPERATORS[dataType], { list: [], set: new Set([]) });
}

export function getInitOperator(operators = [], initValue = null) {
  if (operators === null || operators.length === 0) {
    return null;
  }

  if (initValue !== null) {
    return initValue;
  }

  return operators.list[0];
}

export function getFirstOperator(operators = []) {
  if (operators === null || operators.length === 0) {
    return null;
  }

  return operators.list[0];
}
export function getFirstSemantic(mapSemantic = {}) {
  if (mapSemantic === null) {
    return null;
  }

  return mapSemantic.date;
}
export function getOperatorByValue(key) {
  return MAP_OPERATORS[key];
}

export const initItem = initProperty => {
  const key = generateKey();
  return OrderedMap({
    [key]: initItemValue(initProperty),
  });
};
export const initItemValue = (property, value, operator, dataType) => {
  const operators = getOperatorByProperty(property);
  return Map({
    value,
    property,
    operator,
    operators,
    dataType,
  });
};

export function toConditionAPI(conditions = OrderedMap({})) {
  // console.log('conditions', conditions);
  const rules = { OR: [] };
  const rule = { AND: [] };
  conditions.forEach(item => {
    if (validateItemCondition(item)) {
      const property = item.get('property');
      const objValue = buildValueConditionFromUI(item);
      const statusItemCode = safeParse(
        item.get('statusItemCode'),
        STATUS_ITEM_CODE.ACTIVE,
      );
      let tempt = {};
      if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
        // console.log('property', property);
        tempt = {
          type: property.type,
          column: property.value,
          data_type: property.itemDataType,
          operator: item.get('operator').value,
          extendValue: item.get('extendValue'),
          ...objValue,
        };
        if (Object.keys(tempt).length > 0) {
          rule.AND.push(tempt);
        }
      } else {
        tempt = item.get('backup');

        if (tempt !== null && tempt !== undefined) {
          tempt = { ...tempt, ...objValue };
          rule.AND.push(tempt);
        }
      }
    }
  });
  rules.OR.push(rule);

  return rules;
}

export function toConditionAPIWithDefaultRule(rules, defaultRuleAND) {
  const filters = [...defaultRuleAND];
  const ruleOR = safeParse(rules.OR, []);

  if (ruleOR.length > 0) {
    const ruleAND = safeParse(ruleOR[0].AND, []);
    ruleAND.forEach(item => {
      filters.push(item);
    });
  }

  const output = { OR: [{ AND: filters }] };

  return output;
}

export function toConditionAPIFilterExport(conditions = OrderedMap({}), data) {
  const rules = { OR: [] };
  const rule = {
    AND: [
      {
        column: 'pool_id',
        data_type: 'number',
        operator: 'equals',
        value: data.objectId,
      },
    ],
  };
  conditions.forEach(item => {
    if (validateItemCondition(item)) {
      const property = item.get('property');
      const objValue = buildValueConditionFromUI(item);

      const statusItemCode = safeParse(
        item.get('statusItemCode'),
        STATUS_ITEM_CODE.ACTIVE,
      );
      let tempt = {};
      if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
        // console.log('property', property);
        tempt = {
          type: property.type,
          column: property.value,
          data_type: property.itemDataType,
          operator: item.get('operator').value,
          ...objValue,
        };
        if (Object.keys(tempt).length > 0) {
          rule.AND.push(tempt);
        }
      } else {
        tempt = item.get('backup');

        if (tempt !== null && tempt !== undefined) {
          tempt = { ...tempt, ...objValue };
          rule.AND.push(tempt);
        }
      }
    }
  });
  rules.OR.push(rule);

  return rules;
}
export function toConditionUI(
  objRules,
  mapItem = {},
  mapInfo = {},
  itemTypeId = '-1003',
) {
  // console.log('toConditionUI', objRules, mapItem, mapInfo);
  let conditions = OrderedMap({});
  const rules = [];
  const ruleOR = safeParse(objRules.OR, []);
  ruleOR.forEach(itemOr => {
    const ruleAND = safeParse(itemOr.AND, []);
    if (ruleAND.length > 0) {
      rules.push(ruleAND);
    }
  });

  if (rules.length === 0) {
    return conditions;
  }

  rules.forEach(rule => {
    // let condition = OrderedMap({});
    rule.forEach(item => {
      const property = mapItem[`${item.column}`];
      if (property !== undefined) {
        const objValue = buildValueConditionFromAPI(item, property);
        const tempt = OrderedMap({
          ...objValue,
          statusItemCode: property.statusItemCode,
          property,
          operator: getOperatorByValue(`${property.dataType}-${item.operator}`),
          operators: getOperatorByProperty(property),
          dataType: property.dataType,
          extendValue: item.extendValue,
          backup: item,
        });

        conditions = conditions.set(generateKey(), tempt);
      } else {
        // statusItemCode
        const propertyInfo = mapInfo[`${item.column}-${itemTypeId}`];

        if (propertyInfo !== undefined) {
          propertyInfo.dataType = safeParse(
            propertyInfo.dataType,
            item.data_type,
          );
          const objValue = buildValueConditionFromAPI(item, propertyInfo);
          const tempt = OrderedMap({
            ...objValue,
            statusItemCode: propertyInfo.statusItemCode,
            property: propertyInfo,
            operator: getOperatorByValue(
              `${propertyInfo.dataType}-${item.operator}`,
            ),
            operators: getOperatorByProperty(propertyInfo),
            dataType: propertyInfo.dataType,
            backup: item,
          });
          conditions = conditions.set(generateKey(), tempt);
        }
      }
    });
  });
  return conditions;
}

export function getPropertyLookup(objRules, itemTypeId) {
  // console.log('objRules', objRules);
  const result = [];
  const rules = [];
  const ruleOR = safeParse(objRules.OR, []);
  ruleOR.forEach(itemOr => {
    const ruleAND = safeParse(itemOr.AND, []);
    if (ruleAND.length > 0) {
      rules.push(ruleAND);
    }
  });

  if (rules.length === 0) {
    return result;
  }

  rules.forEach(rule => {
    // let condition = OrderedMap({});
    rule.forEach(item => {
      result.push({
        itemTypeId,
        itemPropertyName: item.column,
      });
    });
  });
  return result;
}

export function getErrorNumber(property, operator) {
  let error = 3;

  if (operator === null || operator === undefined) {
    error = 2;
  }
  if (property === null || property === undefined) {
    error = 1;
  }
  return error;
}

export function getErrorNumberV2(item) {
  let error = 3;

  if (item.get('property') === null || item.get('property') === undefined) {
    error = 1;
    return error;
  }
  if (item.get('operator') === null || item.get('operator') === undefined) {
    error = 2;
    return error;
  }
  if (
    item.get('value') === null ||
    item.get('value') === undefined ||
    item.get('value') === ''
  ) {
    error = 3;
    return error;
  }
  if (
    item.get('valueEnd') === null ||
    item.get('valueEnd') === undefined ||
    item.get('valueEnd') === ''
  ) {
    error = 4;
    return error;
  }
  return error;
}
export function isCheckConditionOperatorwithAvendPicker(operatorValue) {
  if (
    operatorValue === OPERATORS_CODE.EQUALS ||
    operatorValue === OPERATORS_CODE.NOT_EQUALS ||
    operatorValue === OPERATORS_CODE.BEFORE_DATE ||
    operatorValue === OPERATORS_CODE.AFTER_DATE
  ) {
    return true;
  }

  return false;
}
