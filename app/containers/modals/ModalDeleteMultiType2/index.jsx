/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
import React, { useEffect, useState } from 'react';
import { PropTypes } from 'prop-types';
import { connect } from 'react-redux';
import parse from 'html-react-parser';

import {
  UIModal,
  UIModalHeader,
  UIModalBody,
  UIModalFooter,
  UIButton as Button,
  UILoading as Loading,
} from '@xlab-team/ui-components';

import styled from 'styled-components';
import { addNotification } from '../../../redux/actions';
import { getTranslateMessage } from '../../Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { getErrorMessageV2Translate } from '../../../utils/web/message';
import { ModalStyle } from './styled';
// import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../utils/web/permission';

const MAP_TITLE = {
  actCancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
  actDelete: getTranslateMessage(TRANSLATE_KEY._ACT_DELETE, 'Delete'),
  warnDeleteItem: (module, all, number_not_delete, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS, 'Warnings', {
      module,
      all,
      number_not_delete,
      number_delete,
    }),
  warnDeleteAll: (module, number_delete) =>
    getTranslateMessage(TRANSLATE_KEY._WARN_DELETE_ITEMS_ALL, 'Warnings', {
      module,
      number_delete,
    }),
  warnNotDelete: module =>
    getTranslateMessage(TRANSLATE_KEY._WARN_NOT_DELETE_ITEMS, 'Warnings', {
      module,
    }),
};
const getTitleWarning = (
  moduleName,
  totalEntries,
  totalAccepted,
  totalDenied,
) => {
  if (totalAccepted === 0) {
    return MAP_TITLE.warnNotDelete(moduleName);
  }
  if (totalDenied === 0) {
    return MAP_TITLE.warnDeleteAll(moduleName, totalEntries);
  }
  return MAP_TITLE.warnDeleteItem(
    moduleName,
    totalEntries,
    totalDenied,
    totalAccepted,
  );
};

const NOTI = {
  fail: res => ({
    id: 'clone-name-error',
    ...getErrorMessageV2Translate(res.codeMessage),
    timeout: 4000,
    timestamp: 1554173349265,
    type: 'danger',
  }),
  success: () => ({
    id: 'clone-name-success',
    message: `Save!`,
    translateCode: TRANSLATE_KEY._NOTIFICATION_SUCCESS,
    timeout: 4000,
    timestamp: 1554173349265,
    type: 'success',
  }),
  serviceNotFound: () => ({
    id: 'object-server-error',
    message: 'Service not found',
    timeout: 4000,
    timestamp: 1554173349265,
    type: 'danger',
  }),
};

export const ButtonStyle = styled(Button)`
  margin: 0rem 0.25rem;
`;
export const ContainerCustomize = styled.div`
  overflow-y: hidden;
`;
export const WrapperListItem = styled.div`
  display: flex;
  position: relative;
  flex-direction: column;
  height: 2.5rem;
  overflow-y: ${({ overflowY }) => overflowY};
`;

const initState = {
  total: [],
  accepted: [],
  denied: [],
  isDisabledDelete: false,
  isLoading: false,
  isDoing: false,
  titleWarnDelete: '',
  listTitleAccepted: [],
};

function ModalDeleteMulti(props) {
  const [state, setState] = useState(initState);
  const {
    label,
    isOpenModal,
    isUseAPICheck,
    activeRows,
    ObjectServicesFn,
    mapParamsFn,
    listTitleAcceptedFn,
    // mapParamsCheckUsedFn,
    // criteriaFn,
    isSelectedAll,
    totalRecord,
    rules,
  } = props;

  // console.log('ModalDeleteMulti', props);
  useEffect(() => {
    if (isOpenModal && isUseAPICheck) {
      setState({ ...state, isLoading: true });
      const params = mapParamsFn(
        { ...props, activeRows, rules, isSelectedAll, totalRecord },
        state,
      );
      params.data.isCheckUsedStatus = 1;
      ObjectServicesFn(params).then(res => {
        const { totalEntries, totalAccepted, totalDenied } = res.data[0];
        setState({ ...state, isLoading: false });
        if (res.code === 200) {
          const newState = { ...initState };
          newState.titleWarnDelete = (props.getTitleWarning || getTitleWarning)(
            props.moduleName,
            totalEntries,
            totalAccepted,
            totalDenied,
          );
          newState.isDisabledDelete = totalAccepted === 0;
          const listAccepted =
            listTitleAcceptedFn({ ...props, response: res.data[0] }) || [];
          newState.listTitleAccepted = listAccepted;
          setState({ ...state, ...newState });
        } else {
          const notification = NOTI.fail(res);
          props.addNotification(notification);
        }
      });
    } else if (isOpenModal && !isUseAPICheck) {
      const newState = { ...initState };
      newState.titleWarnDelete = (props.getTitleWarning || getTitleWarning)(
        props.moduleName,
        activeRows.size,
        activeRows.size,
        0,
      );
      const listAccepted = listTitleAcceptedFn({ ...props }) || [];
      newState.listTitleAccepted = listAccepted;
      setState({ ...state, ...newState });
    }
  }, [isOpenModal]);
  const onCancel = () => {
    setState(initState);
    props.setOpenModal(false);
  };

  const onApply = () => {
    if (!ObjectServicesFn) {
      const notification = NOTI.serviceNotFound();
      props.addNotification(notification);
    } else {
      setState({ ...state, isDoing: true });
      const params = mapParamsFn(
        { ...props, activeRows, rules, isSelectedAll, totalRecord },
        state,
      );
      // console.log('params', params);
      ObjectServicesFn(params).then(res => {
        setState({ ...state, isDoing: false });
        if (res.code === 200) {
          const notification = NOTI.success();
          props.addNotification(notification);
          props.setOpenModal(false);
          props.fetchData();
          props.callback('DELETE_SUCCESS', params);
        } else {
          const notification = NOTI.fail(res);
          props.addNotification(notification);
        }
      });
    }
  };

  return (
    <ModalStyle
      isOpen={props.isOpenModal}
      toggle={props.setOpenModal}
      isDoing={state.isDoing}
    >
      <UIModalHeader>{label}</UIModalHeader>
      <UIModalBody>
        <ContainerCustomize>
          <Loading isLoading={state.isLoading} />
          {parse(state.titleWarnDelete)}
        </ContainerCustomize>
        <WrapperListItem
          overflowY={state.listTitleAccepted.length > 2 ? 'auto' : 'hidden'}
        >
          {state.listTitleAccepted.map((each, index) => (
            <div key={each.value}>
              {`${index + 1}. `}
              {each.label}
            </div>
          ))}
        </WrapperListItem>
      </UIModalBody>
      <UIModalFooter>
        <ButtonStyle
          theme="outline"
          onClick={onCancel}
          disabled={state.isDoing}
        >
          {MAP_TITLE.actCancel}
        </ButtonStyle>
        <ButtonStyle
          color="secondary"
          onClick={onApply}
          disabled={state.isDisabledDelete || state.isDoing}
        >
          {MAP_TITLE.actDelete}
        </ButtonStyle>
      </UIModalFooter>
    </ModalStyle>
  );
}

function mapDispatchToProps(dispatch) {
  return {
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

ModalDeleteMulti.propTypes = {
  isOpenModal: PropTypes.bool,
  label: PropTypes.string,
  ObjectServicesFn: PropTypes.any,
  addNotification: PropTypes.func,
  activeRows: PropTypes.object,
  setOpenModal: PropTypes.func,
  mapParamsFn: PropTypes.func,
  moduleName: PropTypes.string,
  listTitleAcceptedFn: PropTypes.func,
};
ModalDeleteMulti.defaultProps = {
  isOpenModal: false,
  label: 'Label',
  ObjectServicesFn: () => {},
  activeRows: [],
  setOpenModal: () => {},
  moduleName: '',
  listTitleAcceptedFn: () => {},
  callback: () => {},
};
export default connect(
  null,
  mapDispatchToProps,
)(ModalDeleteMulti);
