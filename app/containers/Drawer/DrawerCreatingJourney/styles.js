/* eslint-disable indent */
import styled, { css, keyframes } from 'styled-components';

// Keyframe animation
const slideIn = keyframes`
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
`;

export const WrapperDrawer = styled.div`
  width: ${props => props.width};
  height: calc(100% - 65px);
`;

export const WrapperContent = styled.div`
  display: flex;
  height: 100%;
  flex-direction: ${props =>
    props.isDisplayCategoryLayout ? 'column' : 'row'};

  .antsomi-spin-container .template-listing {
    overflow-y: unset;
  }
`;

export const LeftSidePaneContent = styled.div`
  display: flex;
  flex-direction: row;
  height: fit-content;

  .wrapper-menu-create {
    padding: 0 10px 10px;
  }
`;

export const MainContent = styled.div`
  display: flex;
  flex: 1;
  border-left: 1px solid #e9e9e9;
  position: relative;
  /* height: 100vh; */
`;

export const StyledLabel = styled.div`
  font-size: 20px;
  padding: 15px;
  animation: ${slideIn} 0.5s forwards;
`;

export const TemplateCreateContainer = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  top: 60px;

  .ui-tabs {
    top: -40px;
    left: 0;
    width: 100%;
    padding: 0 !important;
  }

  .wrapper-media-template {
    border: none;
  }
`;

export const WrapperZaloTemplate = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 15px;

  ${({ isZaloZNS }) =>
    isZaloZNS &&
    css`
      height: 100%;
    `}

  .template-container {
    padding-top: ${props => (props.isZaloZNS ? '15px' : '0')};
  }
`;

export const WrapperAppPush = styled.div`
  padding: 15px;
  width: 100%;
  .label {
    font-size: 20px;
    font-weight: bold;
  }
  .info-app-noti {
    color: #595959;
    padding: 5px 0 0;
  }
`;

export const DrawerHeader = styled.div`
  width: 100%;

  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 54px;
  gap: 100px;
  .right-content {
    display: flex;
    align-items: center;
    gap: 15px;

    .header-info-drawer {
      display: flex;
      flex-direction: column;
      gap: 5px;
      padding: 4.5px 0;

      > div {
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: end;
      }

      p {
        margin: 0;
      }

      button {
        white-space: nowrap;
      }
    }
  }
`;

export const WrapperContentCreate = styled.div`
  height: calc(100% - 50px);
  overflow: auto;
`;
