/* eslint-disable react/prop-types */
/* eslint-disable indent */
// Libraries
import React, { useEffect, memo, useCallback, useMemo, useState } from 'react';
import _ from 'lodash';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

// Locales
import { getTranslateMessage } from '../../../../Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { MAP_TRANSLATE } from '../../constants.translate';

// Services
import DestinationServices from 'services/Destination';

// MODULE_CONFIGS
import { MODULE_CONFIG } from 'modules/Dashboard/MarketingHub/Destination/Create/config';

// Selectors
import {
  makeSelectCreateDesDataConfig,
  makeSelectCreateDesMain,
} from 'modules/Dashboard/MarketingHub/Destination/Create/selectors';
import { makeSelectChannelIntegration } from '../../selectors';
import {
  makeSelectIsOwnerDestDetail,
  makeSelectIsHasPermissionDestDetail,
} from 'modules/Dashboard/MarketingHub/Destination/Detail/selectors';

// Actions
import { updateValue, getListDone } from 'redux/actions';

// Components
import FormControlLabel from '@material-ui/core/FormControlLabel';
import { Grid } from '@material-ui/core';
import { Alert, Checkbox } from '@antscorp/antsomi-ui';
import {
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import UISelect from 'components/form/UISelectCondition';
import Radio from '@material-ui/core/Radio';
import UIFrequencyCapping from 'components/common/UIFrequencyCappingV2';
import FormHelperText from '@material-ui/core/FormHelperText';
import RadioGroup from '@material-ui/core/RadioGroup';
import {
  useStyles,
  StyleContentLoading,
  LabelText,
} from 'modules/Dashboard/MarketingHub/Destination/Create/Content/styles';
import {
  WrapperFixWidth,
  WrapperLabelOption,
  WrapperTitleItem,
} from 'containers/Segment/Content/SegmentMember/styled';
import { UINumberStyled } from 'modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/styles';
import {
  BlockLeft,
  BlockRight,
  BlockTitle,
  Heading,
  Title,
  WrapperContent,
  WrapperSendMultiple,
} from './styled';
import ErrorBoundary from 'components/common/ErrorBoundary';

// Utils
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { generateKey, getObjectPropSafely, safeParseInt } from 'utils/common';
import {
  CHANNEL_FILE_TRANSFER,
  MAX_TO_SEND_BROADCAST,
  MINUTE_OPTIONS,
  TYPE_DELIVERY_INTERVAL,
  mapLabelDeliveryInterval,
  checkIsHideField,
} from 'modules/Dashboard/MarketingHub/Destination/Create/utils';
import { checkHideInfoFields } from '../../utils';
import { CATALOG_WITH_RESPONSE } from '../../constants';
import { CircleInfoIcon } from '@antscorp/antsomi-ui/es/components/icons';

const CHANNEL_ID_TYPE = {
  WEB_PUSH: 3,
  APP_PUSH: 4,
};
export const CATALOG_CODE_CHANNEL_LIMIT = {
  [CHANNEL_ID_TYPE.WEB_PUSH]: 'antsomi_web_push',
  [CHANNEL_ID_TYPE.APP_PUSH]: 'antsomi_app_push',
};

const PATH =
  'app/containers/Drawer/DrawerIntegration/components/Content/index.jsx';

const MAP_TITLE = {
  notiAlertEdit: getTranslateMessage(
    TRANSLATE_KEY._,
    'Editing the settings will affect the other features which is using this destination',
  ),
  notiNotCreateDest: getTranslateMessage(
    TRANSLATE_KEY._NOTI_NOT_CREATE_DESTINATION,
    'Do not exist any catalog, to create a destination required have must a catalog',
  ),
  titlGeneralInfomation: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_INFORMATION,
    'General Information',
  ),
  titlConfigFields: getTranslateMessage(
    TRANSLATE_KEY._TITL_CONFIGURE_FIELD,
    'Configure fields',
  ),
  titlGeneralSetting: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_SETTING,
    'General Setting',
  ),
  titlFrequencyCapping: getTranslateMessage(
    TRANSLATE_KEY._TITL_FREQUENCY_CAPPING,
    'Frequency Capping',
  ),
  titlDeliveredRate: getTranslateMessage(
    TRANSLATE_KEY._TITLE_DES_DELIVERED_RATE,
    'Delivered rate',
  ),
  titlDeliveredInterval: getTranslateMessage(
    TRANSLATE_KEY._DES_DELIVERY_INTERVAL,
    'Delivery Interval Setting',
  ),
  possible: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_AS_FAST_AS_POSSIBLE,
    'As fast as possible',
  ),
  limitSentRate: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_LIMIT_SEND_RATE,
    'Limit send rate',
  ),
  timePerson: getTranslateMessage(
    TRANSLATE_KEY._OPT_DES_TIME_PER_SECOND,
    'time(s) per second',
  ),
};

const InputComponent = memo(props => (
  <Grid
    container
    className={props.classes.item}
    style={props.isHideField ? { padding: 0 } : {}}
  >
    {props.componentEl(props)}
  </Grid>
));

function Content(props) {
  const classes = useStyles();

  // Selectors
  const { main, dataConfig } = props;
  const { isLoading, isInitDone, design, channelId, isValidate } = main;

  console.log(dataConfig);

  // States
  const [isValidBroadCast, setIsValidBroadCast] = useState(true);

  const onMemoInitdata = useMemo(() => dataConfig.frequencyCapping.value, [
    isInitDone,
  ]);

  const onMemoComponenId = useMemo(() => generateKey(), [isInitDone]);

  const onChangeData = useCallback(
    name => (value, extraValue) => {
      const isAntsomiWebPush = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 3 &&
          value.catalogCode === 'antsomi_web_push',
      );
      const isAntsomiEmail = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 1 &&
          value.catalogCode === 'antsomi_email',
      );

      const isAntsomiAppPush = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 4 &&
          value.catalogCode === 'antsomi_app_push',
      );

      const isZaloOA = getObjectPropSafely(
        () =>
          name === 'destinationCatalog' &&
          +channelId === 10 &&
          value.catalogCode === 'zalo_official_account',
      );

      const isNewUI =
        isAntsomiWebPush || isAntsomiEmail || isAntsomiAppPush || isZaloOA;

      if (isNewUI) {
        props.goToCreateV2({ channelId });
      } else if (value !== dataConfig[name].value) {
        props.onChangeDataConfig({ name, value, extraValue });
      }
    },
    [dataConfig],
  );

  const catalogCodeSelected = useMemo(
    () => _.get(props.main, 'activeDestCatalog.catalogCode'),
    [props.main && props.main.activeDestCatalog],
  );
  const methodList = useMemo(() => _.get(dataConfig, 'method.options', []), [
    dataConfig?.method?.options,
  ]);
  const isHideMethod = useMemo(
    () =>
      catalogCodeSelected === 'infobip_whatsapp_template' ||
      methodList?.length === 1,
    [catalogCodeSelected, methodList],
  );

  const methodSelected = useMemo(() => _.get(dataConfig, 'method.value'), [
    dataConfig?.method?.value,
  ]);

  const isHideGeneralSettings = useMemo(
    () =>
      ['sms_fpt', 'sms_fpt_new'].includes(catalogCodeSelected) &&
      methodSelected?.value === 'sendCSKH',
    [catalogCodeSelected, methodSelected],
  );

  const canSendBroadcast = useMemo(
    () =>
      getObjectPropSafely(
        () => props.main.activeDestCatalog.catalogInput.canSendBroadcast,
        false,
      ),
    [props.main && props.main.activeDestCatalog],
  );
  const canSendMultiple = useMemo(
    () =>
      getObjectPropSafely(
        () => props.main.activeDestCatalog.catalogInput.canSendMultiple,
        false,
      ),
    [props.main && props.main.activeDestCatalog],
  );

  const intervalBroadcastSetting = useMemo(
    () =>
      getObjectPropSafely(
        () =>
          props.main.activeDestCatalog.catalogSetting.intervalBroadcastSetting,
        {},
      ),
    [props.main && props.main.activeDestCatalog],
  );
  const { isSendBroadcast, fromNumber, toNumber } =
    intervalBroadcastSetting || {};

  const isForceHideBroadCast = useMemo(
    () => _.get(dataConfig, 'method.value.forceHide', false),
    [dataConfig && dataConfig.method],
  );

  useEffect(() => {
    if (!_.isEmpty(intervalBroadcastSetting) && design === 'create') {
      props.onChangeDataConfigForce({
        name: 'deliveryBroadcast',
        value: {
          from: fromNumber,
          to: toNumber,
          isBroadcast: isSendBroadcast,
        },
      });
    }
  }, [intervalBroadcastSetting]);

  useEffect(() => {
    if (canSendBroadcast && design === 'create') {
      onChangeData('deliveryBroadcast')(canSendBroadcast);
    }
  }, [canSendBroadcast]);
  useEffect(() => {
    // init send multiple to stored
    if (canSendMultiple && design === 'create') {
      onChangeData('deliveryBroadcast')('onCheckingMultiple', false);
    }
  }, [canSendMultiple]);

  useEffect(() => {
    if (
      design === 'create' &&
      ['sms_fpt', 'sms_fpt_new'].includes(catalogCodeSelected)
    ) {
      const currentMethod = _.get(dataConfig, 'method.value', '');

      if (_.isEmpty(currentMethod)) {
        const initMethod = _.get(dataConfig, 'method.mapOptions.sendOTP');

        if (initMethod) {
          props.onChangeDataConfig({ name: 'method', value: initMethod });
        }
      }
    }
  }, [dataConfig?.method, design, catalogCodeSelected]);

  const handleValidateDestinationCount = async catalogId => {
    try {
      const res = await DestinationServices.data.getCountDestination({
        catalogId,
      });

      const newOptions = main.cacheOptionsDataDes.filter(
        option =>
          option.catalogCode !==
          CATALOG_CODE_CHANNEL_LIMIT[safeParseInt(channelId)],
      );
      if (res.code === 200 && safeParseInt(res.data.destinationCount) > 0) {
        props.onChangeDesCataLog(newOptions);
      }
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'handleValidateDestinationCount',
        data: err.stack,
      });
      console.warn('err', err);
    }
  };

  useEffect(() => {
    if (
      design === 'create' &&
      Object.values(CHANNEL_ID_TYPE).includes(safeParseInt(channelId))
    ) {
      const catalogTemp = dataConfig.destinationCatalog.options.find(
        option =>
          option.catalogCode ===
          CATALOG_CODE_CHANNEL_LIMIT[safeParseInt(channelId)],
      );
      const catalogId = getObjectPropSafely(() => catalogTemp.catalogId, '');

      if (catalogId) {
        handleValidateDestinationCount(catalogId);
      }
    }
  }, [dataConfig.destinationCatalog.options]);

  const renderDeliveredFields = channelIdDes => {
    if (+channelIdDes === CHANNEL_FILE_TRANSFER) {
      const { value = '' } = getObjectPropSafely(
        () => dataConfig.deliveryInterval.mapOptions,
        {},
      );
      const valueTemp = getObjectPropSafely(
        () => dataConfig.deliveryInterval.value,
        1,
      );
      const label = mapLabelDeliveryInterval[value] || '';
      const mapValueMinute =
        MINUTE_OPTIONS.find(minuteOption => minuteOption.value === valueTemp) ||
        MINUTE_OPTIONS[0];

      return (
        <Grid container>
          <BlockLeft
            item
            xs={3}
            style={{ display: 'flex', alignItems: 'center' }}
          >
            <Title>{MAP_TITLE.titlDeliveredInterval}</Title>
          </BlockLeft>
          <BlockRight item xs={9}>
            <WrapperDisable disabled={dataConfig.deliveryInterval.disabled}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <WrapperFixWidth width="250px">
                  <UISelect
                    onlyParent
                    use="tree"
                    isSearchable={false}
                    options={dataConfig.deliveryInterval.options}
                    value={dataConfig.deliveryInterval.mapOptions}
                    onChange={onChangeData('deliveryInterval')}
                    placeholder={getTranslateMessage(
                      TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                      'Select an item',
                    )}
                    fullWidthPopover
                    disabled={false}
                  />
                </WrapperFixWidth>
                {value !== TYPE_DELIVERY_INTERVAL.AFTER_COMPLETED && (
                  <>
                    <LabelText>
                      {getTranslateMessage(TRANSLATE_KEY._EVERY, 'every')}
                    </LabelText>
                    <WrapperFixWidth width="100px">
                      {value === TYPE_DELIVERY_INTERVAL.MINUTE ? (
                        <UISelect
                          onlyParent
                          use="tree"
                          isSearchable={false}
                          options={MINUTE_OPTIONS}
                          value={mapValueMinute}
                          onChange={onChangeData('deliveryInterval')}
                          placeholder={getTranslateMessage(
                            TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                            'Select an item',
                          )}
                          fullWidthPopover
                          disabled={false}
                        />
                      ) : (
                        <UINumberStyled
                          name="times"
                          onChange={onChangeData('deliveryInterval')}
                          value={valueTemp}
                          min={1}
                          // max={100}
                          defaultValue={1}
                          width={100}
                        />
                      )}
                    </WrapperFixWidth>
                    <LabelText>{label}</LabelText>
                  </>
                )}
              </div>
            </WrapperDisable>
          </BlockRight>
        </Grid>
      );
    }

    return (
      <>
        {canSendBroadcast && !isForceHideBroadCast && (
          <Grid container style={{ marginBottom: 15 }}>
            <BlockLeft
              xs={3}
              style={{
                display: 'flex',
                alignItems: 'flex-start',
              }}
            >
              <Title>{MAP_TITLE.titlDeliveredInterval}</Title>
            </BlockLeft>
            <BlockRight item xs={9}>
              <WrapperDisable disabled={dataConfig.deliveryBroadcast.disabled}>
                <Checkbox
                  name="onCheckingBroadCast"
                  checked={dataConfig.deliveryBroadcast.isBroadcast}
                  disabled={dataConfig.deliveryBroadcast.disabled}
                  onChange={event => {
                    if (event.target) {
                      const {
                        checked = false,
                        name: checkName = '',
                      } = event.target;

                      onChangeData('deliveryBroadcast')(checkName, checked);
                    }
                  }}
                >
                  {getTranslateMessage(TRANSLATE_KEY._, 'Enable Broadcast')}
                </Checkbox>
                <WrapperSendMultiple
                  isShow={
                    canSendMultiple &&
                    canSendBroadcast &&
                    dataConfig.deliveryBroadcast.isBroadcast
                  }
                >
                  <Checkbox
                    name="onCheckingMultiple"
                    checked={dataConfig.deliveryBroadcast?.isSendMultiple}
                    disabled={dataConfig.deliveryBroadcast.disabled}
                    onChange={event => {
                      if (event.target) {
                        const {
                          checked = false,
                          name: checkName = '',
                        } = event.target;

                        onChangeData('deliveryBroadcast')(checkName, checked);
                      }
                    }}
                  >
                    {getTranslateMessage(
                      TRANSLATE_KEY._,
                      'Send multiple messages',
                    )}
                  </Checkbox>
                </WrapperSendMultiple>
                {dataConfig?.deliveryBroadcast?.isBroadcast && (
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 12,
                      marginTop: 8,
                    }}
                  >
                    <LabelText>
                      {getTranslateMessage(TRANSLATE_KEY._, 'From')}
                    </LabelText>
                    <WrapperFixWidth width="80px">
                      <UINumberStyled
                        name="times"
                        onChange={value => {
                          setIsValidBroadCast(
                            getObjectPropSafely(
                              () => value <= dataConfig.deliveryBroadcast.to,
                            ),
                          );

                          onChangeData('deliveryBroadcast')({
                            from: value,
                            isFrom: true,
                          });
                        }}
                        value={dataConfig.deliveryBroadcast.from}
                        min={
                          !_.isEmpty(intervalBroadcastSetting) &&
                          typeof fromNumber === 'number'
                            ? fromNumber
                            : 0
                        }
                        max={
                          !_.isEmpty(intervalBroadcastSetting) &&
                          typeof toNumber === 'number'
                            ? toNumber
                            : MAX_TO_SEND_BROADCAST
                        }
                        defaultValue={dataConfig.deliveryBroadcast.from}
                        defaultEmptyValue={
                          !_.isEmpty(intervalBroadcastSetting) &&
                          typeof fromNumber === 'number'
                            ? fromNumber
                            : 1
                        }
                        width={80}
                      />
                    </WrapperFixWidth>
                    <LabelText>
                      {getTranslateMessage(TRANSLATE_KEY._, 'to')}
                    </LabelText>
                    <WrapperFixWidth width="80px">
                      <UINumberStyled
                        name="times"
                        onChange={value => {
                          setIsValidBroadCast(
                            getObjectPropSafely(
                              () => value >= dataConfig.deliveryBroadcast.from,
                            ),
                          );

                          onChangeData('deliveryBroadcast')({
                            to: value,
                            isFrom: false,
                          });
                        }}
                        value={dataConfig.deliveryBroadcast.to}
                        min={Math.max(
                          !_.isEmpty(intervalBroadcastSetting) &&
                            typeof fromNumber === 'number'
                            ? fromNumber
                            : 1,
                          dataConfig.deliveryBroadcast.from,
                        )}
                        max={
                          !_.isEmpty(intervalBroadcastSetting) &&
                          typeof toNumber === 'number'
                            ? toNumber
                            : MAX_TO_SEND_BROADCAST
                        }
                        defaultValue={dataConfig.deliveryBroadcast.to}
                        defaultEmptyValue={
                          !_.isEmpty(intervalBroadcastSetting) &&
                          typeof toNumber === 'number'
                            ? toNumber
                            : 1000
                        }
                        width={80}
                      />
                    </WrapperFixWidth>
                    <LabelText>
                      {getTranslateMessage(
                        TRANSLATE_KEY._,
                        'person(s) per once',
                      )}
                    </LabelText>
                  </div>
                )}
                {!isValidBroadCast && dataConfig.deliveryBroadcast.isBroadcast && (
                  <div
                    style={{
                      marginTop: 6,
                      fontSize: '11px',
                      color: '#f44336',
                    }}
                  >
                    From must be less than or equal to To
                  </div>
                )}
              </WrapperDisable>
            </BlockRight>
          </Grid>
        )}
        <Grid container>
          <BlockLeft item xs={3}>
            <Title>{MAP_TITLE.titlDeliveredRate}</Title>
          </BlockLeft>
          <BlockRight item xs={9}>
            <WrapperDisable disabled={dataConfig.deliveryRate.disabled}>
              <RadioGroup
                key={catalogCodeSelected}
                aria-label="segmentMember"
                name="segmentMember"
                defaultValue={dataConfig.deliveryRate.type}
                value={dataConfig.deliveryRate.type}
                onChange={onChangeData('deliveryRate')}
                style={{ gap: '5px', marginLeft: '10px' }}
              >
                <FormControlLabel
                  value="normal"
                  control={
                    <Radio
                      color="primary"
                      style={{ width: '20px', height: '20px' }}
                    />
                  }
                  label={
                    <WrapperLabelOption style={{ marginLeft: '4px' }}>
                      <WrapperDisable>{MAP_TITLE.possible}</WrapperDisable>
                    </WrapperLabelOption>
                  }
                  className={classes.customSizeInput}
                />
                <div className="d-flex align-items-center">
                  <FormControlLabel
                    value="limit"
                    control={
                      <Radio
                        color="primary"
                        style={{ width: '20px', height: '20px' }}
                      />
                    }
                    label={
                      <WrapperLabelOption style={{ marginLeft: '4px' }}>
                        {MAP_TITLE.limitSentRate}
                      </WrapperLabelOption>
                    }
                    className={classes.customSizeInput}
                  />
                  <WrapperFixWidth>
                    <UINumberStyled
                      name="times"
                      onChange={onChangeData('deliveryRate')}
                      value={dataConfig.deliveryRate.limit}
                      min={1}
                      // max={100}
                      defaultValue={1}
                      width={60}
                    />
                  </WrapperFixWidth>

                  <WrapperTitleItem style={{ marginLeft: '16px' }}>
                    {MAP_TITLE.timePerson}
                  </WrapperTitleItem>
                </div>
                {isValidate && (
                  <div style={{ color: '#EF3340', fontSize: '12px' }}>
                    Please input limit send rate values
                  </div>
                )}
              </RadioGroup>
            </WrapperDisable>
          </BlockRight>
        </Grid>
      </>
    );
  };

  const renderContentSettings = () => (
    <StyleContentLoading>
      <Loading isLoading={isLoading} />

      {design === 'update' && (
        <>
          <Alert
            showIcon
            type="warning"
            variant="outline"
            message={MAP_TRANSLATE.notiAlertEdit}
            style={{ marginBottom: 15, width: '100%' }}
          />

          {CATALOG_WITH_RESPONSE.includes(catalogCodeSelected) && (
            <Alert
              showIcon
              closable
              type="info"
              variant="outline"
              message={MAP_TRANSLATE.notiInfoRetry}
              style={{ marginBottom: 15, width: '100%' }}
              icon={<CircleInfoIcon size={24} />}
            />
          )}
        </>
      )}
      <Grid container>
        <Heading item xs={12}>
          <BlockTitle style={{ marginTop: 0 }}>
            {MAP_TITLE.titlGeneralInfomation}
          </BlockTitle>
        </Heading>
        <Grid item xs={12}>
          {dataConfig.infoFields.map(
            each =>
              !checkHideInfoFields(each) && (
                <InputComponent
                  {...dataConfig[each]}
                  onChange={onChangeData}
                  key={each}
                  classes={classes}
                  isOwner={props.isOwner}
                  isHasPermissionEdit={props.isHasPermissionEdit}
                />
              ),
          )}
          {dataConfig.extraInfoFields.map(each => {
            if (each === 'method' && isHideMethod) return null;

            return (
              <InputComponent
                {...dataConfig[each]}
                onChange={onChangeData}
                key={each}
                classes={classes}
                isOwner={props.isOwner}
                isHasPermissionEdit={props.isHasPermissionEdit}
              />
            );
          })}
          {dataConfig.destinationCatalog.options.length === 0 && (
            <FormHelperText error>{MAP_TITLE.notiNotCreateDest}</FormHelperText>
          )}
        </Grid>
      </Grid>
      {dataConfig.configFields.length > 0 && (
        <Grid container>
          <Heading item xs={12}>
            <BlockTitle>{MAP_TITLE.titlConfigFields}</BlockTitle>
          </Heading>
          <Grid item xs={12}>
            {dataConfig.configFields.map(each => (
              <InputComponent
                {...dataConfig[each]}
                onChange={onChangeData}
                key={each}
                isHideField={checkIsHideField({
                  channelId,
                  extraEmailBtn: +channelId === 1 ? main.extraEmailBtn : [],
                  name: each,
                })}
                extraEmailBtn={main.extraEmailBtn}
                onChangeExtraEmailBtn={props.onChangeExtraEmailBtn}
                classes={classes}
                channelId={channelId}
                isOwner={props.isOwner}
                isHasPermissionEdit={props.isHasPermissionEdit}
              />
            ))}
          </Grid>
        </Grid>
      )}
      {!isHideGeneralSettings && (
        <Grid container>
          <Heading item xs={12}>
            <BlockTitle> {MAP_TITLE.titlGeneralSetting}</BlockTitle>
          </Heading>
          <Grid container item xs={12}>
            <BlockLeft item xs={3}>
              <Title>{MAP_TITLE.titlFrequencyCapping}</Title>
            </BlockLeft>
            <BlockRight item xs={9}>
              <UIFrequencyCapping
                key={catalogCodeSelected}
                isNoPadding
                channelId={channelId}
                isShowLabel={false}
                onChange={onChangeData('frequencyCapping')}
                initData={onMemoInitdata}
                componentId={onMemoComponenId}
                disabled={dataConfig.frequencyCapping.disabled}
              />
            </BlockRight>
          </Grid>
          <Grid container>{renderDeliveredFields(channelId)}</Grid>
        </Grid>
      )}
    </StyleContentLoading>
  );

  return (
    <ErrorBoundary path={PATH}>
      <WrapperContent>{renderContentSettings()}</WrapperContent>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  workspace: makeSelectChannelIntegration(),
  dataConfig: makeSelectCreateDesDataConfig(),
  main: makeSelectCreateDesMain(),
  isOwner: makeSelectIsOwnerDestDetail(),
  isHasPermissionEdit: makeSelectIsHasPermissionDestDetail(),
});

function mapDispatchToProps(dispatch, _ownProps) {
  return {
    onChangeExtraEmailBtn: data => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@UPDATE_EXTRA_EMAIL_BTN@@`, data),
      );
    },
    onChangeDataConfig: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATA_CONFIG@@`, value)),
    onChangeDataConfigForce: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATA_CONFIG_FORCE@@`, value)),
    onChangeDesCataLog: value =>
      dispatch(getListDone(`${MODULE_CONFIG.key}@@DEST_CATALOG@@`, value)),
    onChangeInitCataLog: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@INIT_CATALOG@@`, value)),
    goToCreateV2: data =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@GO_TO_CREATE_V2`, data)),
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Content);
