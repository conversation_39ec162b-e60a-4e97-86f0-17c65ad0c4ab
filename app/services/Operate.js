/* eslint-disable no-param-reassign */
/* eslint-disable arrow-body-style */
/* eslint-disable prefer-destructuring */
import produce from 'immer';
import { pick, find, difference, isEmpty, map } from 'lodash';
import { ENDPOINT } from '../../config/common';
import { callApiWithAuthV2 } from '../utils/request';
import { getEntriesWithTotalV2, getEntryV2 } from './utils';

export const withAccessInfo = async ({
  data = [],
  objectType,
  objectIdKey,
}) => {
  const { data: accessInfos } = await API.permission.getInfo({
    body: {
      objects: data.map(item => ({
        objectType,
        objectId: item[objectIdKey],
      })),
    },
  });

  const result = data.map(item => {
    const accessInfo = find(accessInfos, { objectId: item[objectIdKey] });

    return { ...item, accessInfo };
  });

  return result;
};

export const ensureDataInfos = async ({
  objects = [],
  objectType,
  objectIdKey,
  requiredByIds = [],
}) => {
  const lostObjectIds = difference(
    requiredByIds,
    map(objects, obj => obj[objectIdKey]),
  );

  if (isEmpty(lostObjectIds)) return objects;

  const [{ data: accessInfos }, { data: baseInfos }] = await Promise.all([
    API.permission.getInfo({
      body: {
        objects: lostObjectIds.map(objectId => ({
          objectType,
          objectId,
        })),
      },
    }),
    API.getObjectInfo({
      body: { objectIds: lostObjectIds, objectType },
    }),
  ]);

  const lostObjects = lostObjectIds.map(id => {
    const accessInfo = find(accessInfos, { objectId: id });
    const baseInfo = find(baseInfos, { id });

    return { [objectIdKey]: id, ...baseInfo, accessInfo };
  });

  return objects.concat(lostObjects);
};

const API = {
  permission: {
    getInfo: async ({ body }) => {
      const URL = `${ENDPOINT.toolkitV2}/operate/check-permission`;
      const res = await callApiWithAuthV2({
        endpoint: URL,
        method: 'POST',
        body,
      });

      const serializeRes = getEntriesWithTotalV2(res, []);

      return produce(serializeRes, draft => {
        serializeRes.data.forEach((i, index) => {
          const properties = [
            'objectId',
            'objectType',
            'objectCode',
            'isEdit',
            'isView',
            'isExist',
            'permissions',
          ];

          draft.data[index] = pick(i, properties);
        });
      });
    },
  },
  requestAccess: async (params = {}) => {
    const { body } = params;

    const URL = `${ENDPOINT.toolkitV2}/operate/request-access`;

    const res = await callApiWithAuthV2({
      endpoint: URL,
      method: 'POST',
      body,
    });

    return getEntryV2(res);
  },
  getObjectInfo: async (params = {}) => {
    const { body = {} } = params;

    const URL = `${ENDPOINT.toolkitV2}/operate/get-object-info`;

    const res = await callApiWithAuthV2({
      endpoint: URL,
      method: 'POST',
      body,
    });

    return getEntriesWithTotalV2(res);
  },
};

export default API;
