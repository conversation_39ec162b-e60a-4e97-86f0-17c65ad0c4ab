/* eslint-disable arrow-body-style */
/* eslint-disable prefer-destructuring */
import { callApiWithAuth, callApiWithMockup } from 'utils/request';
// import { safeParse } from 'utils/common';
import { ENDPOINT, URL_HISTORY } from '../../config/common';
import { callApiWithAuthV2 } from '../utils/request';
import { getEntryV2, getEntriesWithTotalV2, getEntriesV2 } from './utils';

// const URL_HISTORY = 'http://localhost:8008/history';

const API = {
  data: {
    getList: params => {
      const url = `${ENDPOINT.destinationV2_1}/destination/`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListDestToolkit: params => {
      const url = `${ENDPOINT.toolkitV2}/selector/channel/destination`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListRelationShip: params => {
      const url = `${
        ENDPOINT.destinationV2_1
      }/destination/listing-relationship`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getDetail: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/${params.objectId}`,
        'GET',
        null,
      ).then(res => {
        return getEntryV2(res, null);
      });
    },
    getDetailV2: params => {
      const url = `${ENDPOINT.historyDestination}/detail/${params.objectId}`;

      return callApiWithAuthV2({
        endpoint: url,
        method: 'GET',
        domain: URL_HISTORY,
      }).then(res => {
        return getEntryV2(res, null);
      });
    },
    updateStatus: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/${
          params.objectId
        }/status?status=${params.status}&catalogId=${params.catalogId}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    updateMultiStatus: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/${
          params.objectId
        }/status?status=${params.status}&catalogId=${params.catalogId}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    updateStatusPopup: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/update-status`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    create: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/create`,
        'POST',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    update: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/${params.objectId}`,
        'PUT',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    rename: params => {
      const destinationId = params.destinationId;

      const url = `api/v2/destination/rename/${destinationId}`;

      return callApiWithAuthV2({
        endpoint: url,
        method: 'PUT',
        body: params.body,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesV2(res, []);
      });
    },
    delete: params => {
      const url = `${ENDPOINT.toolkitV2}/action/DESTINATIONS/-1012/remove`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getCountDestination: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/catalog/${params.catalogId}/count`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, {});
      });
    },
    verifyNotification: () => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/verify-dest-for-smart-inbox`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  verifies: {
    line: params => {
      return callApiWithAuth(
        `${
          ENDPOINT.destinationV2_1
        }/destination-line/verify-token-for-line-app/ping`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    reachedLimitRichMenu: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination-line/rich-menu/check-limit`,
        'POST',
        params.body,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  info: {
    getListChannels: () => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/channels`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListTemplate: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/template/list?channelId=${
          params.channelId
        }&catalogCode=${params.catalogCode}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListDestCatalog: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/catalog/channel/${params.objectId}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getDestCatalogDetail: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/catalog/${params.objectId}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListTemplateTemp: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/template/list?channelId=${
          params.channelId
        }&catalogCode=${params.catalogCode}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListThirdPartyCampaigns: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/catalog/third-party-catalog/channel/${
          params.channelId
        }`,
        'POST',
        {},
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListZNS: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/template-3rd/${
          params.destinationId
        }/all?catalogCode=${params.catalogCode}&page=1&limit=100`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getDetailFormZNS: params => {
      return callApiWithAuth(
        `${ENDPOINT.destinationV2_1}/destination/template-3rd/${
          params.destinationId
        }/info?templateId=${params.templateId}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
  columns: {
    getListGroupAttrs: () => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/destinations`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
  deliveryLog: {
    getList: params => {
      const url = `${ENDPOINT.destiantionV2}/destination-histories/${
        params.data.destination_id
      }`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        // others: {
        //   _owner_id: params._owner_id,
        // },
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getChart: params => {
      const url = `${ENDPOINT.destiantionV2}/destination-histories/chart`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        // others: {
        //   _owner_id: params._owner_id,
        // },
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  history: {
    create: params => {
      const url = `${ENDPOINT.historyDestination}`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'POST',
        body: params.data,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntriesV2(res, null);
      });
    },
    update: params => {
      const url = `${ENDPOINT.historyDestination}/${params.objectId}`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'PUT',
        body: params.data,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntryV2(res, null);
      });
    },
    getIdentityStatus: params => {
      const url = `${ENDPOINT.historyDestination}/identity-status/${
        params.emailIdentity
      }`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'GET',
        domain: URL_HISTORY,
      }).then(res => {
        return getEntryV2(res, null);
      });
    },
    checkEmailIdentity: params => {
      const url = `${ENDPOINT.historyDestination}/check-email-identity/${
        params.emailIdentity
      }`;
      return callApiWithAuthV2({
        endpoint: url,
        method: 'GET',
        domain: URL_HISTORY,
      }).then(res => {
        return getEntryV2(res, null);
      });
    },
  },
};

export default API;
