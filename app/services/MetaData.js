/* eslint-disable no-else-return */
/* eslint-disable arrow-body-style */
/* eslint-disable prefer-destructuring */
import { callApiWithAuth, callApiMediaTemplateWithAuth } from 'utils/request';
// import { safeParse } from 'utils/common';
import { ENDPOINT } from '../../config/common';
import { getEntryV2, getEntriesWithTotalV2, getEntriesV2 } from './utils';
import { getDataLookupIds } from './map';
import { safeParse } from '../utils/common';

const API = {
  segment: {
    product: {
      getList: params => {
        let url = `${ENDPOINT.products}/segment`;
        if (params !== undefined) {
          url = `${ENDPOINT.products}/segment?${params}`;
        }
        return callApiWithAuth(url, 'GET', null).then(res => {
          return getEntriesV2(res, []);
        });
      },
    },
  },
  eventTracking: {
    getList: (params = { isUniqueEvent: true, status: -1 }) =>
      callApiWithAuth(
        `${ENDPOINT.toolkit}/selector/full-event-tracking?isUniqueEvent=${
          params.isUniqueEvent
        }&status=${params.status}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, {});
      }),
  },
  dataSource: {
    getList: () => {
      const url = `${ENDPOINT.datasource}/sources?sort=utime&sd=desc`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkit}/info/dsource/${
        params.insightPropertyIds
      }`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getDataLookupIds(res, 'dataSource');
      });
    },
  },
  eventSchema: {
    getList: params => {
      const url = `${ENDPOINT.datasource}/schemas/events/${params}`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getListById: params => {
      let url = `${ENDPOINT.datasource}/schemas/events/${
        params.insightPropertyId
      }`;
      if (safeParse(params.itemTypeId, null) !== null) {
        url = `${ENDPOINT.datasource}/schemas/events/${
          params.insightPropertyId
        }?itemTypeId=${params.itemTypeId}`;
      }

      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkit}/info/dsource/event/${
        params.insightPropertyId
      }`;
      return callApiWithAuth(url, 'POST', params.dataPost).then(res => {
        return getDataLookupIds(res, 'eventSchema', params.label);
      });
    },
  },
  eventProperty: {
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkit}/info/properties/${params.inputUrl}`;
      return callApiWithAuth(url, 'POST', params.dataPost).then(res => {
        return getDataLookupIds(res, 'eventProperty');
      });
    },
  },
  itemProperty: {},
  customerProperty: {
    getList: () => {
      const url = `${ENDPOINT.properties}/customer/group/v2?isSetting=true`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getSelector: params => {
      const url = `${ENDPOINT.toolkit}/selector/customer-properties${
        params.inputUrl
      }`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
    lookupByCodes: params => {
      const url = `${ENDPOINT.toolkit}/info/properties/customer`;
      return callApiWithAuth(url, 'POST', params.dataPost).then(res => {
        return getDataLookupIds(res, 'customerProperty');
      });
    },
  },
  mappingProperty: {
    getList: () => {
      const url = `${ENDPOINT.properties}/setting/customer/mapping-property`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getDetail: () => {
      const url = `${ENDPOINT.properties}/setting/customer/mapping-property`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntryV2(res, {});
      });
    },
    update: params => {
      const url = `${ENDPOINT.properties}/setting/customer/mapping-property`;
      return callApiWithAuth(url, 'PUT', params.data).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  destination: {
    getById: params =>
      callApiWithAuth(
        `${ENDPOINT.destination}/destination/${params.objectId}`,
        'GET',
        null,
      ).then(res => getEntryV2(res, {})),
    getList: params => {
      let url = `${ENDPOINT.destination}/destination`;
      if (params !== undefined) {
        if (params.catalogIds) {
          url += `?catalogIds=${params.catalogIds}`;
        }
      }
      return callApiWithAuth(url, 'GET', null).then(res =>
        getEntriesV2(res, []),
      );
    },

    getListCatalog: params => {
      let url = `${ENDPOINT.destination}/catalog`;
      if (params !== undefined) {
        if (params.includes) {
          url += `?includes=${params.includes}`;
        }
      }
      return callApiWithAuth(url, 'GET', null).then(res =>
        getEntriesV2(res, []),
      );
    },
  },
  item: {
    properties: {
      getList: params => {
        let url = `${ENDPOINT.toolkit}/selector/attributes/${params.objectId}${
          params.inputUrl
        }`;
        if (params !== undefined) {
          if (params.excludes) {
            url += `&excludes=${params.excludes}`;
          }
        }
        return callApiWithAuth(url, 'GET', null).then(res => {
          return getEntriesV2(res, []);
        });
      },
      getByGroupIds: (params, others = {}) => {
        let url = `${
          ENDPOINT.toolkit
        }/properties/groups?groupIds=item_attributes`;
        Object.keys(params).forEach(key => {
          url += `&${key}=${params[key]}`;
        });
        // if (params !== undefined) {
        //   if (params.excludes) {
        //     url += `&excludes=${params.excludes}`;
        //   }
        // }
        return callApiWithAuth(url, 'GET', null, others).then(res => {
          const data = getEntriesV2(res, []);
          // need move itemTypeName to child
          data.data.forEach(group => {
            group.properties.forEach(groupNext => {
              // eslint-disable-next-line no-param-reassign
              groupNext.itemTypeName = group.itemTypeName;
              if (groupNext.properties) {
                groupNext.properties.forEach(item => {
                  // eslint-disable-next-line no-param-reassign
                  item.itemTypeName = group.itemTypeName;
                });
              }
            });
          });
          return data;
          // return getEntriesV2(res, []);
        });
      },
      lookupByIds: params => {
        const url = `${ENDPOINT.datasource}/events/items/info/${
          params.inputUrl
        }`;
        return callApiWithAuth(url, 'POST', params.dataPost).then(res => {
          return getDataLookupIds(res, 'itemAttribute');
        });
      },
      lookupByIdsV2: params => {
        const url = `${ENDPOINT.toolkit}/info/items/attributes`;
        return callApiWithAuth(url, 'POST', params.dataPost).then(res => {
          return getDataLookupIds(res, 'itemAttribute');
        });
      },
    },
  },
  productTemplate: {
    getList: params => {
      return callApiMediaTemplateWithAuth(
        `${ENDPOINT.templateMedia}/product-templates?${params}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
  },
  channel: {
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/channels?objectType=${
          params.objectType
        }`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
};

export default API;
