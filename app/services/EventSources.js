/* eslint-disable prefer-destructuring */
/* eslint-disable arrow-body-style */
import {
  callApiWithAuth,
  // callApiWithMockupOut
  callApiWithAuthV2,
} from '../utils/request';
// import { safeParse } from 'utils/common';
import { ENDPOINT, URL_HISTORY } from '../../config/common';
import { getEntryV2, getEntriesWithTotalV2, getEntriesV2 } from './utils';

const API = {
  data: {
    getListing: params => {
      const url = `${ENDPOINT.datasourceV2}/sources/data-sources/listing`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getList: params => {
      const url = `${ENDPOINT.toolkitV2}/selector/object/INSIGHT_PROPERTY`;
      return callApiWithAuth(url, 'POST', params.body).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getDetail: params => {
      return callApiWithAuth(
        `${ENDPOINT.datasourceV2}/sources/detail/${params.insightPropertyId}`,
        'GET',
        null,
      ).then(res => {
        return getEntryV2(res, {});
      });
    },
    create: params => {
      return callApiWithAuth(
        `${ENDPOINT.datasourceV2}/sources/create`,
        'POST',
        params,
      ).then(res => {
        return getEntryV2(res, {});
      });
    },
    update: params => {
      return callApiWithAuth(
        `${ENDPOINT.datasourceV2}/sources/update/${params.insightPropertyId}`,
        'PUT',
        params.body,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    assign: params => {
      return callApiWithAuth(
        `${ENDPOINT.datasourceV2}/sources/assign/${params.insightPropertyId}`,
        'PUT',
        params.body,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    checkExistSourceName: params => {
      return callApiWithAuth(
        `${ENDPOINT.datasourceV2}/sources/validate`,
        'POST',
        params,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    updateTimeToLine: params => {
      return callApiWithAuth(
        `${ENDPOINT.datasourceV2}/sources/events/update-time`,
        'POST',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    updateSettings: async params => {
      const { data } = params;

      const res = await callApiWithAuthV2({
        // domain: 'http://localhost:8004/hub',
        method: 'POST',
        endpoint: `${ENDPOINT.datasourceV2}/sources/events/update-settings`,
        body: data,
      });

      return getEntryV2(res);
    },
    getSourceByCodes: params => {
      const url = `${ENDPOINT.datasourceV2}/sources/get-by-codes`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getByEvents: async params => {
      const { eventConfig = [], columns = [] } = params;

      const endpoint = `${ENDPOINT.datasourceV2
        }/sources/list-data-source-by-events`;

      const res = await callApiWithAuthV2({
        method: 'POST',
        endpoint,
        body: {
          columns: [
            'insight_property_name',
            'insight_property_id',
            'insight_property_type',
            'status',
            'event_action_id',
            'event_category_id',
            ...columns,
          ],
          eventConfig: eventConfig.map(e => ({
            ea: e.eventActionId,
            ec: e.eventCategoryId,
          })),
        },
      });

      return getEntriesV2(res, []);
    },
    updateName: params => {
      return callApiWithAuthV2({
        endpoint: `api/v1/data-sources/event-sources/rename/${params.insightPropertyId
          }`,
        method: 'PUT',
        body: params.data,
        domain: URL_HISTORY,
      }).then(res => {
        return getEntryV2(res, 0);
      });
    },
  },
  event: {
    data: {
      getList: async params => {
        const { data } = params;

        const res = await callApiWithAuthV2({
          // domain: 'http://localhost:8004/hub',
          method: 'POST',
          endpoint: `${ENDPOINT.datasourceV2}/sources/events/listing`,
          body: data,
        });

        return getEntriesWithTotalV2(res, []);
      },
      updateStatus: params => {
        return callApiWithAuth(
          `${ENDPOINT.datasourceV2}/sources/events/update`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesV2(res, null);
        });
      },
      delete: params => {
        const url = `${ENDPOINT.datasourceV2}/sources/events/remove`;
        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, []);
        });
      },
      getByEventTrackingName: async params => {
        const res = await callApiWithAuthV2({
          endpoint: `${ENDPOINT.datasourceV2
            }/sources/events/get-by-event-tracking-name`,
          method: 'POST',
          body: params.data,
        });

        return getEntriesV2(res, []);
      },
    },
    columns: {
      getListGroupAttrs: objectType => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/info-properties/${objectType}`,
          'GET',
          null,
        ).then(res => {
          return getEntriesV2(res, null);
        });
      },
    },
    chart: {
      getList: params => {
        const url = `${ENDPOINT.datasourceV2}/sources/events/chart`;
        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, []);
        });
      },
    },
    info: {
      getListAllEvents: params => {
        const url = `${ENDPOINT.toolkitV2}/selector/object/${params.objectType
          }`;
        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, []);
        });
      },
    },
  },
  eventAttribute: {
    data: {
      getList: params => {
        const url = `${ENDPOINT.datasourceV2}/sources/attributes/listing`;
        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      updateStatus: params => {
        return callApiWithAuth(
          `${ENDPOINT.datasourceV2}/sources/attributes/update`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesV2(res, null);
        });
      },
      delete: params => {
        const url = `${ENDPOINT.datasourceV2}/sources/attributes/remove`;
        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, []);
        });
      },
    },
    columns: {
      getListGroupAttrs: objectType => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/info-properties/${objectType}`,
          'GET',
          null,
        ).then(res => {
          return getEntriesV2(res, null);
        });
      },
    },
    chart: {
      getList: params => {
        const url = `${ENDPOINT.datasourceV2}/sources/events/chart`;
        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, []);
        });
      },
    },
  },
  eventLogs: {
    chart: {
      getList: params => {
        const url = `${ENDPOINT.datasourceV2}/sources/event-logs/chart`;

        return callApiWithAuth(url, 'POST', params.data).then(res => {
          return getEntriesV2(res, []);
        });
      },
    },
    columns: {
      getListGroupAttrs: params => {
        const url = `${ENDPOINT.toolkitV2}/info-properties/EVENT_LOGS`;

        return callApiWithAuthV2({
          endpoint: url,
          method: 'GET',
          params,
        }).then(res => {
          return getEntriesV2(res, []);
        });
      },
    },
    data: {
      getList: params => {
        const url = `${ENDPOINT.datasourceV2}/sources/event-logs/listing`;

        return callApiWithAuthV2({
          endpoint: url,
          method: 'POST',
          body: params.data,
        }).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      detailRawLog: ({ logId, logTimestamp }) => {
        const url = `${ENDPOINT.datasourceV2
          }/sources/event-logs/detail/${logId}/${logTimestamp}`;

        return callApiWithAuthV2({
          endpoint: url,
          method: 'GET',
        }).then(res => getEntriesV2(res, {}));
      },
    },
  },
  template: {
    getList: () => {
      const url = `${ENDPOINT.datasourceV2}/sources/templates`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getListEventByTemplateId: params => {
      const url = `${ENDPOINT.datasourceV2}/sources/templates/${params.templateId
        }`;
      return callApiWithAuth(url, 'GET', params.data).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getListEvent: async params => {
      const url = `${ENDPOINT.toolkitV2}/selector/object/ES_EVENT`;

      const res = await callApiWithAuthV2({
        // domain: 'http://localhost:8004/hub',
        endpoint: url,
        method: 'POST',
        body: params.data,
      });

      return getEntriesV2(res, []);
    },
    getEventDetail: params => {
      const url = `${ENDPOINT.datasourceV2}/sources/templates/event/${params.eventCategoryId
        }/${params.eventActionId}`;
      return callApiWithAuth(url, 'GET', params.data).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getListEventByInsightPropertyId: async params => {
      const url = `${ENDPOINT.datasourceV2}/sources/events/${params.insightPropertyId
        }`;

      const res = await callApiWithAuthV2({
        // domain: 'http://localhost:8004/hub',
        endpoint: url,
        method: 'GET',
      });

      return getEntriesV2(res, []);
    },
  },
  // attributes: {
  //   getList: params => {
  //     const url = `${ENDPOINT.datasourceV2}/event-tracking-attributes/${
  //       params.eventCategoryId
  //     }/${params.eventActionId}/listing`;
  //     return callApiWithAuth(url, 'POST', params.data).then(res => {
  //       return getEntriesWithTotalV2(res, []);
  //     });
  //   },
  // },
  info: {},
  columns: {
    getListGroupAttrs: objectType => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${objectType}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
  },
};

export default API;
