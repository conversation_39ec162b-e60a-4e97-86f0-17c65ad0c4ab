/* eslint-disable arrow-body-style */
/* eslint-disable prefer-destructuring */
import { callApiWithAuth } from 'utils/request';
import { ENDPOINT, URL_HISTORY } from '../../config/common';
import {
  getEntryV2,
  getEntriesWithTotalV2,
  getEntriesV2,
  getData,
} from './utils';

const API = {
  promotionSource: {
    data: {
      getList: () => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/connector/listing`,
          'GET',
        ).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      getListById: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/connector/${params.connectorId}`,
          'GET',
        ).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      getListTable: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion-source/listing`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      updateStatus: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion-source/update-status`,
          'PUT',
          params.data,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
      removePool: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion/pools/remove-pools`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
      removeCode: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion-code/${
            params.poolId
          }/remove-codes`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
      checkRemovePool: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion/pools/check-remove-pools`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
      checkRemoveCode: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion-code/${
            params.poolId
          }/check-remove-codes`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
      detail: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion-source/${params.sourceId}`,
          'GET',
          params.data,
        ).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      create: params => {
        // return params;
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion-source/create`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
      updateSouce: params => {
        // return params;
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion-source/update/${
            params.sourceId
          }`,
          'PUT',
          params.data,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
      authen: params => {
        // return params;
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion-source/authenticate`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
      shopifyListPriceRule: params => {
        return callApiWithAuth(
          `${
            ENDPOINT.thirdParty
          }/shopify/list-price-rule?integration_source_id=${params.sourceId}`,
          'GET',
          params.data,
          {},
          URL_HISTORY,
        ).then(res => {
          return getData(res, { price_rules: [] });
        });
      },
    },
  },
  promotionPool: {
    data: {
      getList: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion/pools/listing`,
          'POST',
          params.data,
        ).then(res => {
          return getEntriesWithTotalV2(res, []);
        });
      },
      updateStatus: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion/pools/update-status`,
          'PUT',
          params.data,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
      getByIds: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion/pools/get-by-ids`,
          'POST',
          params,
        ).then(res => {
          return getEntriesV2(res, []);
        });
      },
    },

    columns: {
      getListGroupAttrs: objectType => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/info-properties/${objectType}`,
          'GET',
          null,
        ).then(res => {
          return getEntriesV2(res, null);
        });
      },
    },
  },
  setting: {
    uploadFile: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion-code/upload`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    create: params => {
      // return params;
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion/pools/create`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    updateById: params => {
      // return params;
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion/pools/update/${params.poolId}`,
        'PUT',
        params.data,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    getByIds: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion/pools/get-by-ids`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    updateFileById: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion-code/${
          params.poolId
        }/add-codes`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
    updateNameById: params => {
      // return params;
      return callApiWithAuth(
        `${ENDPOINT.apiV1}/promotion-pools/rename/${params.poolId}`,
        'PUT',
        params.data,
        { responseError: true },
        URL_HISTORY,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  promotionCode: {
    detail: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion-code/${params.poolId}/${
          params.promotionCode
        }/detail`,
        'GET',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getList: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion-code/${params.poolId}/listing`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListProcess: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion/pools/${
          params.poolId
        }/history`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListProcessAll: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion/pools/integration-history`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    getListGroupAttrs: () => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/PROMOTION_POOL_CODE`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListGroupAttrsProcess: () => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/PROMOTION_PROCESS_HISTORY`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    getListGroupAttrsPromotionCode: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion-code/group/${
          params.itemTypeId
        }`,
        'GET',
        params.data,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    moveTo: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion-code/${
          params.sourcePoolId
        }/move-codes/${params.targetPoolId}`,
        'POST',
        params.data,
      ).then(res => {
        return getEntryV2(res, 0);
      });
    },
    selectorPool: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/selector/object`,
        'POST',
        params.data,
      ).then(res => {
        const data = getEntriesV2(res, 0);
        return data;
      });
    },
    checkMoveCode: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion-code/${
          params.sourcePoolId
        }/check-move-codes`,
        'POST',
        params.data,
      ).then(res => {
        const data = getEntriesV2(res, 0);
        return data;
      });
    },
    forceSync: params => {
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/promotion/pools/${
          params.poolId
        }/force-sync`,
        'GET',
      ).then(res => {
        const data = getEntriesV2(res, 0);
        return data;
      });
    },
    generateCode: params => {
      let url = `${ENDPOINT.promotionCenterV2}/promotion/pools/${
        params.poolId
      }/force-sync?type=${params.type}`;

      if (params.sourceId) {
        url = `${ENDPOINT.promotionCenterV2}/promotion/pools/${
          params.poolId
        }/force-sync?type=${params.type}&sourceId=${params.sourceId}`;
      }

      return callApiWithAuth(url, 'GET').then(res => {
        const data = getEntriesV2(res, 0);
        return data;
      });
    },
  },
  connector: {
    getConnectThirdParty: params => {
      // return params;
      return callApiWithAuth(
        `${ENDPOINT.promotionCenterV2}/connector/connect-third-party?type=${
          params.type
        }`,
        'POST',
        params.data,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
};

export default API;
