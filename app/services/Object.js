/* eslint-disable arrow-body-style */
/* eslint-disable prefer-destructuring */
import { callApiWithAuth, callApiMediaTemplateWithAuth } from 'utils/request';
// import { safeParse } from 'utils/common';
import { ENDPOINT, URL_SHORT_LINK } from '../../config/common';
import {
  getEntriesV2,
  getCodeV2,
  getEntryV2,
  getEntriesWithTotalV2,
} from './utils';
import { generateKey, safeParse } from '../utils/common';
import { getParamsAPISuggestionMultiLang } from './map';
import { getLabelCodeStatus } from './Abstract.data';
import { callApiWithoutHeaders } from '../utils/request';
import { pick } from 'lodash';

function buildQueryParams(page, limit, ids, search) {
  let url = `page=${page}&limit=${limit}&search=${search}`;
  if (ids !== '') {
    url = `${url}&ids=${ids}`;
  }
  return url;
}
const idNamePropertyCode = ['id', 'customer_id', 'segment_ids', 'campaign_id'];
function serializeData(config = {}, data, keyValue, keyLabel, total) {
  const output = {
    list: [],
    map: {},
    total,
  };
  const styleItem = idNamePropertyCode.includes(config.propertyCode);
  const isCodeStatus = config.propertyCode === 'code_status';
  data.forEach(item => {
    if (item[keyValue] !== undefined && item[keyLabel]) {
      let label = `${item[keyLabel]}`;
      const name = `${item[keyValue]}`;
      if (config.isPk == 1 && config.objectType === 'SEGMENT_COMP_HISTORIES') {
        label = `${item[keyValue]}`;
      } else if (
        config.isPk == 1 &&
        config.objectType === 'SPECIFIC_SEGMENT_COMP_HISTORIES'
      ) {
        label = `${item[keyValue]}`;
      } else if (
        config.isPk == 1 &&
        config.propertyCode !== 'user_id' &&
        config.propertyCode !== 'event_id' &&
        config.propertyCode !== 'segment_ids'
      ) {
        // label = `${item[keyValue]} - ${item[keyLabel]}`;
        label = `${item[keyLabel]}`;
        // console.log('label', label);
      }

      const tempt = {
        name: name || `${item[keyValue]}`,
        ...item,
        value: `${item[keyValue]}`,
        label: label || `${item[keyLabel]}`,
        title: label || `${item[keyLabel]}`,
        key: `${item[keyValue]}`,
      };
      if (config.dataType === 'datetime') {
        tempt.name = `${item[keyLabel]}`;
        tempt.id = item[keyValue];
      }
      // gen id cho matches any ko có id
      if (!tempt.id) {
        tempt.id = generateKey();
      }
      if (styleItem) {
        tempt.subTitle = tempt.value;
        tempt.title = tempt.label;
      }
      if (isCodeStatus) {
        tempt.title = getLabelCodeStatus(Number(name));
      }
      if (tempt.status) {
        let labelNoti = config.objectLabel || '';
        if (config.itemTypeId !== undefined) {
          labelNoti = +config.itemTypeId === 1 ? 'collection' : 'segment';
        }
        let tooltip = '';

        switch (tempt.status) {
          case 4:
            tooltip = `This ${labelNoti} is archived`;
            tempt.error = tooltip;

            break;
          case 3:
            tooltip = `This ${labelNoti} does not exist`;
            tempt.error = tooltip;
            break;
          default:
            break;
        }
      }
      output.list.push(tempt);
      output.map[tempt.value] = tempt;
      // output.mapBackup[tempt.value] = tempt;
      // output.mapBackup[`${tempt.value}`] = tempt; // dùng cho case không phải là string
    } else if (config.objectType === 'DESTINATION_HISTORY') {
      const label = `${item.errorMessage}`;
      const tempt = {
        name: `${item.errorMessage}`,
        ...item,
        value: `${item.errorMessage}`,
        label: label || `${item.errorMessage}`,
        id: `${item.errorMessage}`,
        title: label || `${item.errorMessage}`,
        key: `${item.errorMessage}`,
      };
      if (styleItem) {
        tempt.subTitle = tempt.value;
        tempt.title = tempt.label;
      }
      output.list.push(tempt);
      output.map[tempt.value] = tempt;
    }
  });
  return output;
}

const API = {
  sourceGetAll: undefined,
  eventItem: {
    getListV3: params => {
      const url = `${ENDPOINT.toolkit}/suggestion/items`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        return serializeData(params.data, data.data, 'name', 'name');
      });
    },
  },
  objectId: {
    getList: params => {
      // const url = `${ENDPOINT.toolkit}/suggestion`;
      const url = `${ENDPOINT.toolkitV2}/suggestion/items`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        return serializeData(params.data, data.data, 'id', 'name');
      });
    },
  },
  suggestionDateTime: {
    getList: params => {
      // const url = `${ENDPOINT.toolkit}/suggestion`;
      const url = `${ENDPOINT.toolkitV2}/suggestion/semantic-date`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        if (params.data.dataType === 'datetime' && params.data.semantic) {
          return serializeData(params.data, data.data, 'value', 'label');
        }
        return serializeData(params.data, data.data, 'name', 'name');
      });
    },
  },
  suggestion: {
    getList: params => {
      // const url = `${ENDPOINT.toolkit}/suggestion`;
      const url = `${ENDPOINT.toolkitV2}/suggestion/items`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        const total = data.meta?.total;

        if (safeParse(params.data.isPk, 0) === 1) {
          return serializeData(params.data, data.data, 'id', 'name', total);
        }
        if (params.data.propertyCode === 'code_status') {
          return serializeData(params.data, data.data, 'id', 'id', total);
        }
        if (params.data.dataType === 'datetime' && params.data.semantic) {
          return serializeData(params.data, data.data, 'value', 'label', total);
        }
        return serializeData(params.data, data.data, 'name', 'name', total);
      });
    },

    getListBO: params => {
      // console.log('params', params);
      const url = `${ENDPOINT.toolkitV2}/suggestion/bo/object`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        if (safeParse(params.data.isPk, 0) === 1) {
          return serializeData(params.data, data.data, 'id', 'name');
        }
        return serializeData(params.data, data.data, 'name', 'name');
      });
    },
    // lookupByIds: params => {
    //   const url = `${ENDPOINT.toolkit}/info/suggestion`;
    //   return callApiWithAuth(url, 'POST', params.data).then(res => {
    //     const data = getEntriesWithTotalV2(res, []);
    //     return serializeData(params.data, data.data, 'id', 'name');
    //   });
    // },
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkitV2}/suggestion/items`;
      params.data.lookupValues = params.data.ids;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        if (safeParse((params.data.config || {}).isPk, 0) === 1) {
          return serializeData(params.data, data.data, 'id', 'name');
        }
        return serializeData(params.data, data.data, 'name', 'name');
        // return serializeData(params.data, data.data, 'id', 'name');
      });
    },
    getLimit: params => {
      const url = `${ENDPOINT.toolkit}/suggestion/get-limit?&moduleNames=${
        params.moduleNames
      }`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    translate: params => {
      const url = `${ENDPOINT.toolkit}/suggestion/translate`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesV2(res, 0);
      });
    },
  },
  suggestionV1: {
    getList: params => {
      const url = `${ENDPOINT.toolkit}/suggestion`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        if (safeParse(params.data.isPk, 0) === 1) {
          return serializeData(params.data, data.data, 'id', 'name');
        }
        return serializeData(params.data, data.data, 'name', 'name');
      });
    },
    getListBO: params => {
      // console.log('params', params);
      const url = `${ENDPOINT.toolkitV2}/suggestion/bo/object`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        if (safeParse(params.data.isPk, 0) === 1) {
          return serializeData(params.data, data.data, 'id', 'name');
        }
        return serializeData(params.data, data.data, 'name', 'name');
      });
    },
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkit}/info/suggestion`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        return serializeData(params.data, data.data, 'id', 'name');
      });
    },
    getLimit: params => {
      const url = `${ENDPOINT.toolkit}/suggestion/get-limit?&moduleNames=${
        params.moduleNames
      }`;
      return callApiWithAuth(url, 'GET', null).then(res => {
        return getEntriesWithTotalV2(res, []);
      });
    },
    translate: params => {
      const url = `${ENDPOINT.toolkit}/suggestion/translate`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        return getEntriesV2(res, 0);
      });
    },
  },
  suggestionHistoriesJourney: {
    getList: params => {
      const url = `${ENDPOINT.toolkitV2}/suggestion/journey-histories`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const resData = getEntriesWithTotalV2(res, []);
        if (safeParse(params.data.isPk, 0) === 1) {
          return serializeData(
            params.data,
            resData.data,
            'id',
            'name',
            resData.totalRecord,
          );
        }
        // SỬ DỤNG CHO TRƯỜNG HỢP SEARCH FILTER SUGGESTION THEO CODE
        // USING: THEM propertyCode === 'suggestion_with_code' ở configSuggestion trong util.map
        if (
          safeParse(params.data.propertyCode) === 'suggestion_with_process_id'
        ) {
          return serializeData(
            params.data,
            resData.data,
            'id',
            'id',
            resData.totalRecord,
          );
        }
        return serializeData(
          params.data,
          resData.data,
          'name',
          'name',
          resData.totalRecord,
        );
      });
    },
  },
  suggestionMultilang: {
    getList: params => {
      const url = `${ENDPOINT.toolkitV2}/suggestion/objects`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const resData = getEntriesWithTotalV2(res, []);
        if (safeParse(params.data.isPk, 0) === 1) {
          return serializeData(
            params.data,
            resData.data,
            'id',
            'name',
            resData.totalRecord,
          );
        }
        // SỬ DỤNG CHO TRƯỜNG HỢP SEARCH FILTER SUGGESTION THEO CODE
        // USING: THEM propertyCode === 'suggestion_with_code' ở configSuggestion trong util.map
        if (safeParse(params.data.propertyCode) === 'suggestion_with_code') {
          return serializeData(
            params.data,
            resData.data,
            'code',
            'code',
            resData.totalRecord,
          );
        }
        if (
          safeParse(params.data.propertyCode) === 'suggestion_with_connector_id'
        ) {
          return serializeData(
            params.data,
            resData.data,
            'connectorId',
            'connectorName',
            resData.totalRecord,
          );
        }
        if (
          safeParse(params.data.propertyCode) === 'suggestion_with_pool_id' ||
          safeParse(params.data.propertyCode) ===
            'suggestion_with_process_id' ||
          safeParse(params.data.propertyCode) === 'suggestion_with_id' ||
          safeParse(params.data.propertyCode) === 'suggestion_with_source_id'
        ) {
          return serializeData(
            params.data,
            resData.data,
            'id',
            'id',
            resData.totalRecord,
          );
        }

        if (safeParse(params.data.propertyCode) === 'event_property_name') {
          return serializeData(
            params.data,
            resData.data,
            'id',
            'name',
            resData.totalRecord,
          );
        }

        return serializeData(
          params.data,
          resData.data,
          'name',
          'name',
          resData.totalRecord,
        );
      });
    },
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkitV2}/info/metadata`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        return serializeData(params.data, data.data, 'id', 'name');
      });
    },
  },
  suggestionVendorManagement: {
    getList: params => {
      const { search = '', decryptFields = [] } = params.data;
      const columnName = decryptFields.join('');
      const data = {
        filters: {
          OR: [
            {
              AND: [
                {
                  type: 1,
                  column: columnName,
                  data_type: 'string',
                  operator: 'contains',
                  value: search,
                },
              ],
            },
          ],
        },
        properties: [...decryptFields, 'account_id'],
        sd: 'asc',
        search: '',
        sort: 'ctime',
        limit: 25,
        page: 1,
      };
      const endpoint = `${
        ENDPOINT.apiPerformance
      }/vendor-account?source=cdp&type=2`;
      return callApiWithoutHeaders({
        endpoint,
        method: 'POST',
        body: data,
        domain: URL_SHORT_LINK,
      }).then(res => {
        const resData = getEntriesWithTotalV2(res, []);
        return serializeData(
          params.data,
          resData.data,
          columnName,
          columnName,
          resData.totalRecord,
        );
      });
    },
  },
  suggestionShortenerManagement: {
    getList: params => {
      const { search = '', decryptFields = [] } = params.data;
      const columnName = decryptFields.join('');
      const data = {
        filters: {
          OR: [
            {
              AND: [
                {
                  type: 1,
                  column: columnName,
                  data_type: 'string',
                  operator: 'contains',
                  value: search,
                },
              ],
            },
          ],
        },
        properties: [...decryptFields, 'link_shortener_id', 'vendor'],
        sd: 'asc',
        search: '',
        sort: 'ctime',
        limit: 25,
        page: 1,
      };
      const endpoint = `${
        ENDPOINT.apiPerformance
      }/link-shortener?source=cdp&type=2`;
      return callApiWithoutHeaders({
        endpoint,
        method: 'POST',
        body: data,
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      }).then(res => {
        const resData = getEntriesWithTotalV2(res, []);
        return serializeData(
          params.data,
          resData.data,
          columnName,
          columnName,
          resData.totalRecord,
        );
      });
    },
  },
  suggestionLinkManagement: {
    getList: params => {
      const { search = '', decryptFields = [], accountId } = params.data;
      const columnName = decryptFields.join('');
      const data = {
        filters: {
          OR: [
            {
              AND: [
                {
                  type: 1,
                  column: columnName,
                  data_type: 'string',
                  operator: 'contains',
                  value: search,
                },
                {
                  type: 1,
                  column: 'account_id',
                  data_type: 'number',
                  operator: 'matches',
                  value: [+accountId],
                },
              ],
            },
          ],
        },
        properties: [...decryptFields, 'account_id'],
        sd: 'asc',
        search: '',
        sort: 'ctime',
        limit: 25,
        page: 1,
      };
      const endpoint = `${ENDPOINT.performanceLink}?source=cdp&type=3`;
      return callApiWithoutHeaders({
        endpoint,
        method: 'POST',
        body: data,
        domain: URL_SHORT_LINK,
        isAddAccountId: true,
      }).then(res => {
        const resData = getEntriesWithTotalV2(res, []);
        return serializeData(
          params.data,
          resData.data,
          columnName,
          columnName,
          resData.totalRecord,
        );
      });
    },
  },
  suggestionMultilangJourneyHistories: {
    getList: params => {
      const url = `${ENDPOINT.toolkitV2}/suggestion/journey-histories`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const resData = getEntriesWithTotalV2(res, []);
        return serializeData(
          params.data,
          resData.data,
          'id',
          'id',
          resData.totalRecord,
        );
      });
    },
  },
  suggestionMultilangExport: {
    getList: params => {
      const url = `${ENDPOINT.toolkitV2}/suggestion/export-histories`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const resData = getEntriesWithTotalV2(res, []);
        if (safeParse(params.data.isPk, 0) === 1) {
          return serializeData(
            params.data,
            resData.data,
            'id',
            'name',
            resData.totalRecord,
          );
        }
        // SỬ DỤNG CHO TRƯỜNG HỢP SEARCH FILTER SUGGESTION THEO CODE
        // USING: THEM propertyCode === 'suggestion_with_code' ở configSuggestion trong util.map
        if (safeParse(params.data.propertyCode) === 'suggestion_with_code') {
          return serializeData(
            params.data,
            resData.data,
            'code',
            'code',
            resData.totalRecord,
          );
        }
        if (safeParse(params.data.propertyCode) === 'suggestion_with_pool_id') {
          return serializeData(
            params.data,
            resData.data,
            'id',
            'id',
            resData.totalRecord,
          );
        }
        if (
          safeParse(params.data.propertyCode) === 'suggestion_with_connector_id'
        ) {
          return serializeData(
            params.data,
            resData.data,
            'connectorId',
            'connectorName',
            resData.totalRecord,
          );
        }
        return serializeData(
          params.data,
          resData.data,
          'name',
          'name',
          resData.totalRecord,
        );
      });
    },
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkitV2}/info/metadata`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const data = getEntriesWithTotalV2(res, []);
        return serializeData(params.data, data.data, 'id', 'name');
      });
    },
  },
  suggestionEvent: {
    getList: params => {
      const url = `${ENDPOINT.toolkitV2}/suggestion/events`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const resData = getEntriesWithTotalV2(res, []);
        if (safeParse(params.data.isPk, 0) === 1) {
          return serializeData(
            params.data,
            resData.data,
            'property_value',
            'property_value',
            resData.totalRecord,
          );
        }

        return serializeData(
          params.data,
          resData.data,
          'property_value',
          'property_value',
          resData.totalRecord,
        );
      });
    },
  },
  suggestionLocationItems: {
    getList: params => {
      const url = `${ENDPOINT.toolkitV2}/suggestion/location-items`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const resData = getEntriesWithTotalV2(res, []);
        return serializeData(
          params.data,
          resData.data,
          'countryName',
          'countryName',
          resData.totalRecord,
        );
      });
    },
    lookupByIds: params => {
      const url = `${ENDPOINT.toolkitV2}/suggestion/location-items-search`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        const resData = getEntriesWithTotalV2(res, []);
        return serializeData(
          params.data,
          resData.data,
          'countryId',
          'countryName',
          resData.totalRecord,
        );
      });
    },
  },
  columns: {
    get: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkit}/columns/${params.objectType}/${params.objectId}`,
        'GET',
      ).then(res => getEntryV2(res, {}));
    },
    update: params => {
      // body: {reportNewName: 'new name'}
      return callApiWithAuth(
        `${ENDPOINT.toolkit}/columns/${params.objectType}/${params.objectId}/${
          params.columnId
        }`,
        'POST',
        params.data,
      ).then(res => getCodeV2(res, 0));
    },
    getListDetail: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkitV2}/info-properties/${params.objectName}`,
        'GET',
        null,
      ).then(res => {
        return getEntriesV2(res, null);
      });
    },
    // updateV2: params => {
    //   // 21/06/2020
    //   return callApiWithAuth(
    //     `${ENDPOINT.toolkit}/columns/${params.objectType}/${params.objectId}`,
    //     'POST',
    //     params.data,
    //   ).then(res => getCodeV2(res, 0));
    // },
  },
  filters: {
    get: params => {
      return callApiWithAuth(
        `${ENDPOINT.toolkit}/filters/${params.objectType}/${params.objectId}`,
        'GET',
      ).then(res => getEntryV2(res, {}));
    },
    update: params => {
      // body: {reportNewName: 'new name'}
      return callApiWithAuth(
        `${ENDPOINT.toolkit}/filters/${params.objectType}/${params.objectId}/${
          params.filterId
        }`,
        'POST',
        params.data,
      ).then(res => getCodeV2(res, 0));
    },
  },

  // audienceSegment: {
  //   delete: objectIds => {
  //     return callApiWithAuth(
  //       `${ENDPOINT.userExplorer}/audience-segment/${objectIds}`,
  //       'DELETE',
  //     ).then(res => getCodeV2(res, 0));
  //   },
  //   clone: params => {
  //     // body: {reportNewName: 'new name'}
  //     return callApiWithAuth(
  //       `${ENDPOINT.userExplorer}/audience-segment/${params.objectId}`,
  //       'POST',
  //       params.data,
  //     ).then(res => getEntryV2(res, 0));
  //   },
  // },
  audienceSegment: {
    delete: objectIds => {
      return callApiWithAuth(
        `${ENDPOINT.itemSegment}/segment/${objectIds}`,
        'DELETE',
      ).then(res => getCodeV2(res, 0));
    },
    clone: params => {
      // body: {reportNewName: 'new name'}
      return callApiWithAuth(
        `${ENDPOINT.itemSegment}/segment/${params.objectId}`,
        'POST',
        params.data,
      ).then(res => getEntryV2(res, 0));
    },
  },
  workflow: {
    delete: objectIds => {
      return callApiWithAuth(
        `${ENDPOINT.automation}/workflows/${objectIds}`,
        'DELETE',
      ).then(res => getCodeV2(res, 0));
    },
    clone: params => {
      // body: {reportNewName: 'new name'}
      return callApiWithAuth(
        `${ENDPOINT.automation}/workflows/${params.objectId}`,
        'POST',
        params.data,
      ).then(res => getEntryV2(res, 0));
    },
  },
  contentBlock: {
    delete: objectIds => {
      return callApiMediaTemplateWithAuth(
        `${ENDPOINT.templateMedia}/content-blocks/${objectIds}`,
        'DELETE',
        null,
      ).then(res => {
        return getCodeV2(res, []);
      });
    },
  },
  userComputedProperty: {
    delete: objectIds => {
      return callApiWithAuth(
        `${ENDPOINT.userExplorer}/computed-properties/${objectIds}`,
        'DELETE',
      ).then(res => getCodeV2(res, 0));
    },
    clone: params => {
      // body: {reportNewName: 'new name'}
      return callApiWithAuth(
        `${ENDPOINT.userExplorer}/computed-properties/${params.objectId}`,
        'POST',
        params.data,
      ).then(res => getEntryV2(res, 0));
    },
  },

  campaign: {
    delete: objectIds => {
      return callApiMediaTemplateWithAuth(
        `${ENDPOINT.templateMedia}/campaign/${objectIds}`,
        'DELETE',
        null,
      ).then(res => getEntriesV2(res, []));
    },
  },
  destination: {
    delete: objectIds => {
      return callApiWithAuth(
        `${ENDPOINT.destination}/destination/${objectIds}`,
        'DELETE',
      ).then(res => getCodeV2(res, 0));
    },
  },
  mediaLibrary: {
    delete: objectIds => {
      return callApiMediaTemplateWithAuth(
        `${ENDPOINT.templateMedia}/media/${objectIds}`,
        'DELETE',
      ).then(res => getCodeV2(res, 0));
    },
  },
  productSegment: {
    delete: objectIds =>
      callApiWithAuth(
        `${ENDPOINT.products}/segment/${objectIds}`,
        'DELETE',
      ).then(res => getCodeV2(res, 0)),
    clone: params =>
      callApiWithAuth(
        `${ENDPOINT.products}/segment/${params.objectId}`,
        'POST',
        params.data,
      ).then(res => getEntryV2(res, 0)),
  },
  productTemplate: {
    delete: objectIds =>
      callApiMediaTemplateWithAuth(
        `${ENDPOINT.templateMedia}/product-templates/${objectIds}`,
        'DELETE',
      ).then(res => getCodeV2(res, 0)),
    clone: params => {
      const dataPost = {
        name: params.data.newName,
        productTemplateId: params.objectId,
      };
      return callApiMediaTemplateWithAuth(
        `${ENDPOINT.templateMedia}/product-templates/clone`,
        'POST',
        dataPost,
      ).then(res => getEntryV2(res, 0));
    },
  },
  studioReport: {
    delete: objectIds =>
      callApiWithAuth(`${ENDPOINT.studio}/reports/${objectIds}`, 'DELETE').then(
        res => getCodeV2(res, 0),
      ),
  },
  studioReportGroup: {
    delete: objectIds =>
      callApiWithAuth(
        `${ENDPOINT.studio}/report-groups/${objectIds}`,
        'DELETE',
      ).then(res => getCodeV2(res, 0)),
  },
  studioDataSource: {
    delete: objectIds => {
      return callApiWithAuth(
        `${ENDPOINT.studio}/datasources/${objectIds}`,
        'DELETE',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
  studioDataConnector: {
    delete: objectIds => {
      return callApiWithAuth(
        `${ENDPOINT.studio}/connectors/${objectIds}`,
        'DELETE',
        null,
      ).then(res => {
        return getEntriesV2(res, []);
      });
    },
  },
};

export default API;
