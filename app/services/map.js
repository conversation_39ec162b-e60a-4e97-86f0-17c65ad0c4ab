/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-expressions */
import { isNumeric, safeParse, safeParseInt } from '../utils/common';
import { getEntriesV2 } from './utils';
import {
  buildPropertiesWithFilter,
  getNameTypeItemAttributes,
  getStatusItemCode,
} from '../utils/web/properties';
import { safeParseDisplayFormat } from '../utils/web/portalSetting';
import { getConfigAutoSuggestion } from '../utils/web/map';
import { getDisplayMultilang } from '../utils/web/translate';
import { isEmpty, uniq } from 'lodash';

export function getDataLookupIds(
  res,
  type = '',
  label,
  isUsingJourneyTemplate,
) {
  let data = {
    map: {},
    list: [],
  };
  const resData = getEntriesV2(res, []);
  if (resData.data.length > 0) {
    if (type === 'dataSource') {
      data = mapItemDataSource(resData.data, 'lookup');
    } else if (type === 'eventSchema') {
      data = mapItemEventSchema(resData.data, 'lookup', label);
    } else if (type === 'eventProperty') {
      data = mapItemEventPropertyLookup(
        safeParse(resData.data[0], {}),
        isUsingJourneyTemplate,
      );
    } else if (type === 'itemAttribute') {
      data = mapItemAttributes(resData.data, 'lookup');
    } else if (type === '') {
      resData.data.forEach(item => {
        const tempt = {
          value: `${item.insightPropertyId}`,
          label: item.insightPropertyName,
          status: item.status,
        };
        data.map[tempt.value] = tempt;
        data.list.push(tempt);
      });
    }
  }
  return data;
}

function mapToDataType(isPk, dataType) {
  // return object_id thi mat id ngoai man hinh sort_by
  // if (parseInt(isPk) === 1) {
  //   return 'object_id';
  // }
  return dataType;
}

export function autoMapDataWithLookup(data, mapLookup = {}) {
  if (Object.keys(mapLookup.map).length < 1) {
    return data;
  }
  const dataTempt = data;
  Object.keys(mapLookup.map).forEach(key => {
    const obj = mapLookup.map[key];

    if (dataTempt.map[key] === undefined) {
      dataTempt.map[key] = obj;
      dataTempt.list.unshift(obj);
    }
  });

  return dataTempt;
}

export function mapItemDataSource(items, use = 'info') {
  const data = {
    map: {},
    list: [],
  };
  items.forEach(item => {
    // if (parseInt(item.status) === 1) {
    // console.log(item);
    const tempt = {
      id: item.insightPropertyId,
      name: item.insightPropertyName,
      display: item.insightPropertyName,
      domain: item.domain,
      dataType: item.insightPropertyType,
      value: `${item.insightPropertyId}`,
      label: item.insightPropertyName,
      status: item.status,
      statusItemCode: getStatusItemCode(use, parseInt(item.status)),
    };
    data.map[tempt.value] = tempt;
    data.list.push(tempt);
    // }
  });

  return data;
}
export function mapAccountMember(items, use = 'info') {
  const data = {
    map: {},
    list: [],
  };
  items.forEach(item => {
    // if (parseInt(item.status) === 1) {
    // console.log(item);
    const tempt = {
      id: item.userId,
      name: item.userName,
      display: item.userName,
      domain: item.domain,
      dataType: 1,
      value: `${item.userId}`,
      label: item.userName,
      status: item.status,
      statusItemCode: getStatusItemCode(use, parseInt(item.status)),
    };
    data.map[tempt.value] = tempt;
    data.list.push(tempt);
    // }
  });

  return data;
}
export function mapAccountPortal(items, use = 'info') {
  const data = {
    map: {},
    list: [],
  };

  !isEmpty(items) &&
    items?.forEach(item => {
      const tempt = {
        id: item.user_id,
        name: item.full_name,
        display: item.full_name,
        domain: item.domain,
        dataType: 1,
        value: `${item.user_id}`,
        label: item.full_name,
        status: item.status,
        statusItemCode: getStatusItemCode(use, parseInt(item.status)),
      };
      data.map[tempt.value] = tempt;
      data.list.push(tempt);
    });

  return data;
}
export function mapItemEventSchema(
  items,
  use = 'info',
  label = 'translateLabel',
) {
  const data = {
    map: {},
    list: [],
  };
  items.forEach(item => {
    if (item.eventTrackingName.trim() !== '') {
      const tempt = {
        id: `${item.eventCategoryId}-${item.eventActionId}`,
        insightPropertyId: item.insightPropertyId,
        eventCategoryId: item.eventCategoryId,
        eventActionId: item.eventActionId,
        name: item.eventTrackingName,
        // status: item.status,
        value: `${item.eventCategoryId}-${item.eventActionId}`,
        // label: item.eventTrackingName,
        label: safeParse(item[label], item.eventTrackingName),
        isSort: safeParseInt(item.isSort, 0),
        isFilter: safeParseInt(item.isFilter, 0),
        statusItemCode: getStatusItemCode(use, parseInt(item.status)),
        available: item.status === 3,
      };
      data.map[tempt.value] = tempt;
      data.list.push(tempt);
    }
  });
  return data;
}

export function mapItemEventProperty(items, use = 'info', params = {}) {
  const data = {
    map: {},
    list: [],
  };
  items.forEach(item => {
    const tempt = {
      name: item.eventPropertyName,
      display: item.eventPropertyDisplay,
      // dataType: mapToDataType(item.isPk, item.dataType),
      dataType: item.dataType,
      itemDataType: item.dataType,
      itemTypeId: safeParse(item.itemTypeId, 0),
      itemTypeName: safeParse(item.itemTypeName, null),
      value: `${item.eventPropertyName}-${safeParse(item.itemTypeId, 0)}`,
      // propertySyntax: `${item.eventPropertySyntax}`,
      propertySyntax: `${item.eventPropertySyntax}`,
      // label: item.eventPropertyDisplay,
      label: safeParse(item.translateLabel, item.eventPropertyDisplay),
      isSort: safeParseInt(item.isSort, 0),
      // isFilter: safeParseInt(item.isFilter, 1),
      isFilter: 1,
      type: 'event',
      eventCategoryId: item.eventCategoryId,
      eventActionId: item.eventActionId,
      // propertyName: `${item.eventPropertySyntax}`,
      propertyName: safeParse(
        item.eventPropertySyntax,
        item.itemPropertyDisplay,
      ),
      eventPropertyName: item.eventPropertyName,
      statusItemCode: getStatusItemCode(use, parseInt(item.status), 1),
      displayFormat: safeParseDisplayFormat(
        safeParse(item.displayFormat, null),
        { dataType: item.dataType },
      ),
      sources: [...new Set(safeParse(item.insightPropertyIds, []))],

      isEncrypt: safeParseIsEncrypt(
        item.itemTypeId,
        item.itemTypeName,
        safeParseInt(item.isEncrypt, 0),
      ),
      autoSuggestion: safeParseInt(item.autoSuggestion, 0),
      computeType: item.computeType,
    };

    if (item.items) {
      const options = [];
      item.items.forEach(obj => {
        const temptChild = {
          name: obj.itemPropertyName,
          display: obj.itemPropertyDisplay,
          dataType: mapToDataType(obj.isPk, obj.dataType),
          itemDataType: obj.dataType,
          itemTypeId: safeParse(obj.itemTypeId, 0),
          itemTypeName: safeParse(obj.itemTypeName),
          value: `${obj.itemPropertyName}-${safeParse(obj.itemTypeId, 0)}`,
          propertySyntax: `${obj.eventPropertySyntax}-${obj.itemPropertyName}`,
          encryptCode: `${obj.itemPropertyName}`,
          // propertySyntax: `${obj.itemPropertyName}`,
          // label: obj.itemPropertyDisplay,
          label: safeParse(obj.translateLabel, obj.itemPropertyDisplay),
          isSort: safeParseInt(obj.isSort, 0),
          isFilter: safeParseInt(obj.isFilter, 0),
          type: 'item',
          attrType: obj.type,
          eventCategoryId: item.eventCategoryId,
          eventActionId: item.eventActionId,
          // propertyName: `${obj.itemPropertyName}`,
          propertyName: safeParse(
            obj.itemPropertyName,
            obj.itemPropertyDisplay,
          ),
          statusItemCode: getStatusItemCode(
            use,
            parseInt(obj.status),
            safeParseInt(obj.isFilter, 0),
          ),

          autoSuggestion: safeParseInt(obj.autoSuggestion, 0),
          configSuggestion: getConfigAutoSuggestion(obj),
          displayFormat: safeParseDisplayFormat(
            safeParse(obj.displayFormat, null),
            { dataType: obj.dataType },
          ),
          isEncrypt: safeParseIsEncrypt(
            obj.itemTypeId,
            obj.itemPropertyName,
            safeParseInt(obj.isEncrypt, 0),
          ),
          sources: [...new Set(safeParse(tempt.sources, []))],
          computeType: obj.computeType,
        };
        if (obj.itemPropertyName === 'segment_ids') {
          // temptChild.configSuggestion = {
          //   feServices: 'suggestionMultilang',
          //   feKey: `${obj.itemTypeId}-${obj.itemTypeName}-${
          //     obj.itemPropertyName
          //   }`,
          //   itemTypeId: obj.itemTypeId,
          //   objectType: 'BO_SEGMENTS',
          //   isPk: 1,
          //   propertyCode: `${obj.itemPropertyName}`,
          //   objectName: obj.itemPropertyName,
          //   filters: {
          //     OR: [
          //       {
          //         AND: [
          //           {
          //             type: 1,
          //             column: 'item_type_id',
          //             data_type: 'number',
          //             operator: 'equals',
          //             value: `${obj.itemTypeId}`,
          //           },
          //         ],
          //       },
          //     ],
          //   },
          // };
        }

        data.map[temptChild.value] = temptChild;
        options.push(temptChild);
      });
      tempt.options = options;
    }
    data.map[tempt.value] = tempt;
    data.list.push(tempt);
  });
  return data;
}

export function mapItemEventPropertyForStory(
  items,
  use = 'info',
  params = {},
  isPersonalization = false,
) {
  // console.log('mapItemEventPropertyForStory', items);
  // Thay đổi ở  propertySyntax, chỉ lấy eventPropertySyntax

  const conFilter = safeParse(params.isFilter, -1);
  const data = {
    map: {},
    list: [],
    mapBracketedCode: {},
  };

  items.forEach(item => {
    const tempt = {
      // bracketed_code: `${item.eventPropertyName}-${safeParse(
      //   item.itemTypeId,
      //   0,
      // )}`, // Dùng cho personalization
      bracketed_code: `${item.eventPropertyName}`, // Dùng cho personalization
      name: item.eventPropertyName,
      display: item.eventPropertyDisplay,
      // dataType: mapToDataType(item.isPk, item.dataType),
      dataType: item.dataType,
      itemDataType: item.dataType,
      itemTypeId: safeParse(item.itemTypeId, 0),
      itemTypeName: safeParse(item.itemTypeName, null),
      value: `${item.eventPropertyName}-${safeParse(item.itemTypeId, 0)}`,
      // propertySyntax: `${item.eventPropertySyntax}`,
      propertySyntax: `${item.eventPropertySyntax}`,
      // label: item.eventPropertyDisplay,
      label: safeParse(item.translateLabel, item.eventPropertyDisplay),
      isSort: safeParseInt(item.isSort, 0),
      // isFilter: safeParseInt(item.isFilter, 1),
      isFilter: 1,
      type: 'event',
      eventCategoryId: item.eventCategoryId,
      eventActionId: item.eventActionId,
      // propertyName: `${item.eventPropertySyntax}`,
      propertyName: safeParse(
        item.eventPropertySyntax,
        item.itemPropertyDisplay,
      ),
      eventPropertyName: item.eventPropertyName,
      statusItemCode: getStatusItemCode(use, parseInt(item.status), 1),
      displayFormat: safeParseDisplayFormat(
        safeParse(item.displayFormat, null),
        { dataType: item.dataType },
      ),
      isEncrypt: safeParseIsEncrypt(
        item.itemTypeId,
        item.itemTypeName,
        safeParseInt(item.isEncrypt, 0),
      ),
      sources: [...new Set(safeParse(item.insightPropertyIds, []))],
      autoSuggestion: safeParseInt(item.autoSuggestion, 0),
      status: safeParseInt(item.status, 0),
    };

    if (item.items) {
      // Thay đổi ở  propertySyntax, chỉ lấy eventPropertySyntax
      const options = [];
      item.items.forEach(obj => {
        const temptChild = {
          // bracketed_code: `${obj.itemPropertyName}-${safeParse(
          //   obj.itemTypeId,
          //   0,
          // )}`, // Dùng cho personalization
          bracketed_code: `${obj.itemTypeName}.${obj.itemPropertyName}`, // Dùng cho personalization
          name: obj.itemPropertyName,
          display: obj.itemPropertyDisplay,
          dataType: mapToDataType(obj.isPk, obj.dataType),
          itemDataType: obj.dataType,
          itemTypeId: safeParse(obj.itemTypeId, 0),
          itemTypeName: safeParse(obj.itemTypeName),
          value: `${obj.itemPropertyName}-${safeParse(obj.itemTypeId, 0)}`,
          encryptCode: `${obj.itemPropertyName}`,
          // propertySyntax: `${obj.eventPropertySyntax}-${obj.itemPropertyName}`,
          propertySyntax: `${obj.eventPropertySyntax}`,
          // label: obj.itemPropertyDisplay,
          label: safeParse(obj.translateLabel, obj.itemPropertyDisplay),
          isSort: safeParseInt(obj.isSort, 0),
          isFilter: safeParseInt(obj.isFilter, 0),
          type: 'item',
          attrType: obj.type,
          eventCategoryId: item.eventCategoryId,
          eventActionId: item.eventActionId,
          // propertyName: `${obj.itemPropertyName}`,
          propertyName: safeParse(
            obj.itemPropertyName,
            obj.itemPropertyDisplay,
          ),
          statusItemCode: getStatusItemCode(
            use,
            parseInt(obj.status),
            safeParseInt(obj.isFilter, 0),
          ),

          autoSuggestion: safeParseInt(obj.autoSuggestion, 0),
          // autoSuggestion: 1,

          configSuggestion: getConfigAutoSuggestion(obj),
          displayFormat: safeParseDisplayFormat(
            safeParse(obj.displayFormat, null),
            { dataType: obj.dataType },
          ),
          // isEncrypt: safeParse(obj.isEncrypt, 0),
          isEncrypt: safeParseIsEncrypt(
            obj.itemTypeId,
            obj.itemPropertyName,
            safeParseInt(obj.isEncrypt, 0),
          ),
          sources: [...new Set(safeParse(tempt.sources, []))],
          status: obj.status,
        };
        // console.log('object', obj);
        if (obj.itemPropertyName === 'segment_ids') {
          // temptChild.configSuggestion = {
          //   feServices: 'suggestionMultilang',
          //   feKey: `${obj.itemTypeId}-${obj.itemTypeName}-${
          //     obj.itemPropertyName
          //   }`,
          //   itemTypeId: obj.itemTypeId,
          //   objectType: 'BO_SEGMENTS',
          //   isPk: 1,
          //   propertyCode: `${obj.itemPropertyName}`,
          //   objectName: obj.itemPropertyName,
          //   filters: {
          //     OR: [
          //       {
          //         AND: [
          //           {
          //             type: 1,
          //             column: 'item_type_id',
          //             data_type: 'number',
          //             operator: 'equals',
          //             value: `${obj.itemTypeId}`,
          //           },
          //         ],
          //       },
          //     ],
          //   },
          // };
        }
        data.map[temptChild.value] = temptChild;
        data.mapBracketedCode[temptChild.bracketed_code] = temptChild;

        options.push(temptChild);
        // data.list.push(temptChild);
      });
      tempt.options = options;
    } else {
      data.map[tempt.value] = tempt;
      data.mapBracketedCode[tempt.bracketed_code] = tempt;
    }
    if (isPersonalization) {
      data.map[tempt.value] = tempt;
      data.mapBracketedCode[tempt.bracketed_code] = tempt;
    }
    data.list.push(tempt);
  });

  return data;
}
export function mapItemBOPropertyForStory(items) {
  // console.log('mapItemEventPropertyForStory', items);
  // Thay đổi ở  propertySyntax, chỉ lấy eventPropertySyntax

  const data = {
    map: {},
    list: [],
    mapBracketedCode: {},
  };

  items.forEach(item => {
    const tempt = {
      // bracketed_code: `${item.eventPropertyName}-${safeParse(
      //   item.itemTypeId,
      //   0,
      // )}`, // Dùng cho personalization
      autoSuggestion: safeParseInt(item.auto_suggestion, 0),
      bracketed_code: `${item.item_property_name}`, // Dùng cho personalization
      name: item.item_property_name,
      display: item.item_property_display,
      // dataType: mapToDataType(item.isPk, item.dataType),
      dataType: item.data_type,
      itemDataType: item.data_type,
      itemTypeId: safeParse(item.item_type_id, 0),
      itemTypeName: safeParse(item.itemTypeName, null),
      value: `${item.item_property_name}-${safeParse(item.item_type_id, 0)}`,
      // propertySyntax: `${item.eventPropertySyntax}`,
      propertySyntax: `${item.eventPropertySyntax}`,
      // label: item.eventPropertyDisplay,
      label: safeParse(item.translateLabel, item.item_property_display),
      isSort: safeParseInt(item.isSort, 0),
      // isFilter: safeParseInt(item.isFilter, 1),
      isFilter: 1,
      // type: 'event',
      eventCategoryId: item.eventCategoryId,
      eventActionId: item.eventActionId,
      status: item.status,
      itemPropertyName: item.item_property_name,
      // propertyName: safeParse(
      //   item.eventPropertySyntax,
      //   item.itemPropertyDisplay,
      // ),
      // eventPropertyName: item.eventPropertyName,
      // statusItemCode: getStatusItemCode(use, parseInt(item.status), 1),
      // displayFormat: safeParseDisplayFormat(
      //   safeParse(item.displayFormat, null),
      //   { dataType: item.dataType },
      // ),
      // isEncrypt: safeParseIsEncrypt(
      //   item.itemTypeId,
      //   item.itemTypeName,
      //   safeParseInt(item.isEncrypt, 0),
      // ),
      // sources: safeParse(item.insightPropertyIds, []),
    };

    if (item.items) {
      // Thay đổi ở  propertySyntax, chỉ lấy eventPropertySyntax
      const options = [];
      item.items.forEach(obj => {
        const temptChild = {
          // bracketed_code: `${obj.itemPropertyName}-${safeParse(
          //   obj.itemTypeId,
          //   0,
          // )}`, // Dùng cho personalization
          autoSuggestion: safeParseInt(item.auto_suggestion, 0),
          bracketed_code: `${item.item_property_name}`, // Dùng cho personalization
          name: item.item_property_name,
          display: item.item_property_display,
          // dataType: mapToDataType(item.isPk, item.dataType),
          dataType: item.data_type,
          itemDataType: item.data_type,
          itemTypeId: safeParse(item.item_type_id, 0),
          itemTypeName: safeParse(item.itemTypeName, null),
          value: `${item.eventPropertyName}-${safeParse(item.item_type_id, 0)}`,
          // propertySyntax: `${item.eventPropertySyntax}`,
          propertySyntax: `${item.eventPropertySyntax}`,
          // label: item.eventPropertyDisplay,
          label: safeParse(item.translateLabel, item.item_property_display),
          isSort: safeParseInt(item.isSort, 0),
          // isFilter: safeParseInt(item.isFilter, 1),
          isFilter: 1,
          // type: 'event',
          eventCategoryId: item.eventCategoryId,
          eventActionId: item.eventActionId,
          itemPropertyName: item.item_property_name,
        };
        data.map[temptChild.value] = temptChild;
        data.mapBracketedCode[temptChild.bracketed_code] = temptChild;

        options.push(temptChild);
        // data.list.push(temptChild);
      });
      tempt.options = options;
    } else {
      data.map[tempt.value] = tempt;
      data.mapBracketedCode[tempt.bracketed_code] = tempt;
    }
    data.list.push(tempt);
  });

  return data;
}

export function mapItemEventPropertyLookup(items, isUsingJourneyTemplate) {
  const data = {
    map: {},
    list: [],
  };

  const eventProperties = safeParse(items.eventProperties, []);
  const itemProperties = safeParse(items.itemProperties, []);
  eventProperties.forEach(item => {
    const tempt = {
      name: item.eventPropertyName,
      value: `${item.eventPropertyName}-${safeParse(item.itemTypeId, 0)}`,
      label: isUsingJourneyTemplate
        ? item.eventPropertyDisplay
        : safeParse(item.translateLabel, item.eventPropertyName),
      eventTrackingCode: item.eventTrackingName,
      isSort: safeParseInt(item.isSort, 0),
      isFilter: safeParseInt(item.isFilter, 0),
      dataType: mapToDataType(item.isPk, item.dataType),
      itemDataType: item.dataType,
      itemTypeId: safeParse(item.itemTypeId, 0),
      statusItemCode: getStatusItemCode('lookup', +item.status),
      type: safeParse(item.type, null),
      status: safeParse(item.status, +item.status),
    };
    data.map[tempt.value] = tempt;
    data.list.push(tempt);
  });

  itemProperties.forEach(item => {
    const tempt = {
      name: item.itemPropertyName,
      value: `${item.itemPropertyName}-${safeParse(item.itemTypeId, 0)}`,
      // label: item.itemPropertyDisplay,
      label: safeParse(item.translateLabel, item.itemPropertyDisplay),
      isSort: safeParseInt(item.isSort, 0),
      isFilter: safeParseInt(item.isFilter, 0),
      dataType: mapToDataType(item.isPk, item.dataType),
      itemDataType: item.dataType,
      itemTypeId: safeParse(item.itemTypeId, 0),
      statusItemCode: getStatusItemCode('lookup', +item.status),
      type: safeParse(item.type, null),
      status: safeParse(item.status, +item.status),
    };
    data.map[tempt.value] = tempt;
    data.list.push(tempt);
  });

  return data;
}

/**
 *
 Cho URL: http://sandbox-app.cdp.asia/hub/toolkit/v1.1/selector/attributes/1?excludeDataType=object&portalId=554926187
 Use for condition product segment
 */
export function mapItemAttributes(items, use = 'info') {
  const data = {
    map: {},
    mapByName: {},
    list: [],
  };
  items.forEach(item => {
    const tempt = {
      // id: safeParseInt(item.itemPropertyId, 1),
      name: item.itemPropertyName,
      display: item.itemPropertyDisplay,
      // dataType: item.dataType,
      dataType: mapToDataType(item.isPk, item.dataType),
      itemDataType: item.dataType,
      itemTypeId: item.itemTypeId,
      itemTypeName: item.itemTypeName,
      conditionType: getNameTypeItemAttributes(safeParseInt(item.type)),
      value: `${item.itemPropertyName}-${safeParse(item.itemTypeId, 0)}`,
      encryptCode: `${item.itemPropertyName}`,
      label: safeParse(item.translateLabel, item.itemPropertyDisplay),
      translateLabel: getDisplayMultilang(
        safeParse(item.propertyDisplayMultilang, {}),
      ),
      isSort: safeParseInt(item.isSort, 0),
      isFilter: safeParseInt(item.isFilter, 0),
      statusItemCode: getStatusItemCode(use, parseInt(item.status)),
      autoSuggestion: safeParseInt(item.autoSuggestion, 0),
      configSuggestion: getConfigAutoSuggestion(item),
      isEncrypt: safeParseIsEncrypt(
        item.itemTypeId,
        item.itemPropertyName,
        safeParseInt(item.isEncrypt, 0),
      ),
      displayFormat: safeParseDisplayFormat(
        safeParse(item.displayFormat, null),
        { dataType: item.dataType },
      ),
      status: safeParse(item.status, null),
      type: safeParse(+item.type, null),
    };
    data.map[tempt.value] = tempt;
    data.list.push(tempt);
  });
  return data;
}

/**
 * use cho luồng mới -> move customer /user vào item
 * @param {}} items
 */
export const mapGroupItemAttributeUniqueWithItemTypeId = (
  groups,
  parentGroupId = null,
  params = { showIsRequired: false },
) => {
  const data = {
    groups: [],
    mapGroups: {},
    filterGroups: [],
    map: {},
    // initProperty: null,
  };

  groups.forEach(group => {
    const { properties, segmentId, ...otherDataGroup } = group;
    const temptGroup = {
      ...otherDataGroup,
      groupId: group.groupId,
      groupName: safeParse(group.translateLabel, group.groupName),
      translateCode: group.translateCode,
      value: group.groupId,
      label: safeParse(group.translateLabel, group.groupName),
      preDefined: safeParseInt(group.preDefined, 0),
      options: [],
    };
    group.properties.forEach(item => {
      const tempt = convertItemAttributesUniqueWithItemTypeId(
        item,
        group,
        parentGroupId,
        params,
        segmentId,
      );
      data.map[`${tempt.item.value}`] = tempt.item;
      temptGroup.options.push(tempt.item);
      if (Object.keys(tempt.map).length > 0) {
        data.map = { ...data.map, ...tempt.map };
      }
    });

    data.groups.push(temptGroup);
    data.mapGroups[temptGroup.groupId] = temptGroup;
  });
  data.filterGroups = buildPropertiesWithFilter(data.groups);
  // data.initProperty = getInitProperty(data.groups);
  return data;
};

export const mapGroupItemAttributeUniqueWithBusinessObject = (
  groups,
  parentGroupId = null,
) => {
  const data = {
    groups: [],
    mapGroups: {},
    filterGroups: [],
    map: {},
    // initProperty: null,
  };
  // console.log('mapGroupItemAttribute', groups, parentGroupId);
  groups.forEach(group => {
    const { properties, ...otherDataGroup } = group;
    const temptGroup = {
      ...otherDataGroup,
      groupId: group.groupId,
      groupName: safeParse(group.translateLabel, group.groupName),
      value: group.groupId,
      label: safeParse(group.translateLabel, group.groupName),
      translateCode: group.translateCode,
      preDefined: safeParseInt(group.preDefined, 0),
      options: [],
    };
    group.properties.forEach(item => {
      const tempt = convertItemAttributesUniqueWithBussinessObject(
        item,
        group,
        parentGroupId,
      );
      data.map[`${tempt.item.value}`] = tempt.item;
      temptGroup.options.push(tempt.item);
      if (Object.keys(tempt.map).length > 0) {
        data.map = { ...data.map, ...tempt.map };
      }
    });

    data.groups.push(temptGroup);
    data.mapGroups[temptGroup.groupId] = temptGroup;
  });
  data.filterGroups = buildPropertiesWithFilter(data.groups);
  // data.initProperty = getInitProperty(data.groups);

  return data;
};

/**
 * use cho luồng mới -> move customer /user vào item
 * @param {}} items
 */
export const mapGroupItemAttribute = (groups, parentGroupId = null) => {
  const listitemTypeId = [-1003, -1007];
  const data = {
    groups: [],
    mapGroups: {},
    filterGroups: [],
    map: {},
    // initProperty: null,
  };
  // console.log('mapGroupItemAttribute', groups, parentGroupId);
  groups.forEach(group => {
    const { properties, ...otherDataGroup } = group;
    const temptGroup = {
      ...otherDataGroup,
      groupId: group.groupId,
      groupName: safeParse(group.translateLabel, group.groupName),
      value: group.groupId,
      label: safeParse(group.translateLabel, group.groupName),
      translateCode: group.translateCode,
      preDefined: safeParseInt(group.preDefined, 0),
      options: [],
    };
    properties.forEach(item => {
      // hide properties segment_ids with Customer,Visitor
      if (
        listitemTypeId.includes(Number(item.itemTypeId)) &&
        item.itemPropertyName === 'segment_ids'
      ) {
        return;
      }
      const tempt = convertItemAttributes(item, group, parentGroupId);
      data.map[`${tempt.value}`] = tempt;
      temptGroup.options.push(tempt);
    });

    data.groups.push(temptGroup);
    data.mapGroups[temptGroup.groupId] = temptGroup;
  });
  data.filterGroups = buildPropertiesWithFilter(data.groups);
  // data.initProperty = getInitProperty(data.groups);
  return data;
};
export const mapGroupItemAttributeWithItemTypeIdHaveDefaultColumn = (
  groups,
  parentGroupId = null,
) => {
  const data = {
    groups: [],
    mapGroups: {},
    filterGroups: [],
    map: {},
    requires: [],
    defaults: [],
    // initProperty: null,
  };
  // console.log('mapGroupItemAttribute', groups, parentGroupId);
  groups.forEach(group => {
    const { properties, ...otherDataGroup } = group;
    const temptGroup = {
      ...otherDataGroup,
      groupId: group.groupId,
      groupName: safeParse(group.translateLabel, group.groupName),
      value: group.groupId,
      label: safeParse(group.translateLabel, group.groupName),
      translateCode: group.translateCode,
      preDefined: safeParseInt(group.preDefined, 0),
      options: [],
    };
    group.properties.forEach(item => {
      if (item.isIdentity === 1) {
        data.defaults.push(item.itemPropertyName);
      }
      const tempt = convertItemAttributes(item, group, parentGroupId);
      data.map[`${tempt.value}`] = tempt;
      temptGroup.options.push(tempt);
    });

    data.groups.push(temptGroup);
    data.mapGroups[temptGroup.groupId] = temptGroup;
  });
  data.filterGroups = buildPropertiesWithFilter(data.groups);
  // data.initProperty = getInitProperty(data.groups);
  return data;
};

export const mapGroupItemAttributeWithByItemTypeId = groupsItemTypeIds => {
  const data = {
    mapByItemTypeId: {},
    map: {},
  };

  groupsItemTypeIds.forEach(groups => {
    const tmp = mapGroupItemAttribute(groups.properties, groups.groupId);

    data.mapByItemTypeId[groups.groupId] = tmp;
    data.map = { ...data.map, ...tmp.map };
  });
  return data;
};

export const mapGroupItemAttributeForeCastWithItemTypeId = groupsItemTypeIds => {
  // console.log('groupsItemTypeIds', groupsItemTypeIds);
  const data = {
    mapByItemTypeId: {},
    map: {},
  };

  groupsItemTypeIds.forEach(groups => {
    const tmp = mapGroupItemAttributeWithItemTypeIdHaveDefaultColumn(
      groups.properties,
      groups.groupId,
    );

    data.mapByItemTypeId[groups.groupId] = tmp;
    data.map = { ...data.map, ...tmp.map };
  });
  return data;
};

/**
 *
 * @param {*} itemTypeId
 * @param {*} isEncrypt
 * return 1 : bị khóa và có quyền mở khóa
 * return 2: bị khóa và không có quyền mở khóa
 */
export function safeParseIsEncrypt(itemTypeId, propertyCode, isEncrypt) {
  // console.log('safeParseIsEncrypt', { itemTypeId, propertyCode, isEncrypt });
  if (isEncrypt === 0) {
    return isEncrypt;
  }
  const arrayCheck =
    // eslint-disable-next-line no-undef
    APP_CACHE_PARAMS.decryptPermission.encryptFields.itemAttributes[
    itemTypeId
    ] || [];
  if (!arrayCheck.includes(propertyCode)) {
    return 2;
  }
  return isEncrypt;
}

export const convertItemAttributes = (item, group, parentGroupId = null) => {
  // console.log('group', group);
  const tempt = {
    // isSort: 1,
    propertyId: item.itemPropertyName,
    propertyCode: item.itemPropertyName,
    propertyName: safeParse(item.translateLabel, item.itemPropertyDisplay),
    translateCode: group.translateCode,
    itemTypeId: item.itemTypeId,
    itemTypeName: item.itemTypeName,
    dataType: mapToDataType(item.isPk, item.dataType),
    itemDataType: item.dataType,
    isOutputProductId: item.isOutputProductId,
    type: item.type,
    groupCode: group.value,
    groupId: group.groupId,
    value: item.itemPropertyName,
    label: safeParse(item.translateLabel, item.itemPropertyDisplay),
    isSort: safeParseInt(item.isSort, 0),
    // translateCode: group.translateCode,
    isFilter: safeParseInt(item.isFilter, 0),
    isEncrypt: safeParseIsEncrypt(
      item.itemTypeId,
      item.itemPropertyName,
      safeParseInt(item.isEncrypt, 0),
    ),
    // statusItemCode !== status cua attributes
    // statusItemCode: getStatusItemCode('info', parseInt(item.status)),
    autoSuggestion: safeParseInt(item.autoSuggestion, 0),
    configSuggestion: getConfigAutoSuggestion(item),
    displayFormat: safeParseDisplayFormat(safeParse(item.displayFormat, null), {
      dataType: item.dataType,
    }),
  };

  if (item.itemPropertyName === 'user_id') {
    tempt.autoSuggestion = 1;
  }

  if (item.itemPropertyName === 'segment_ids') {
    // tempt.configSuggestion = {
    //   feServices: 'suggestionMultilang',
    //   feKey: `1-${item.itemTypeId}-${item.itemTypeName}-${
    //     item.itemPropertyName
    //   }`,
    //   itemTypeId: item.itemTypeId,
    //   objectType: 'BO_SEGMENTS',
    //   isPk: 1,
    //   propertyCode: `${item.itemPropertyName}`,
    //   objectName: item.itemPropertyName,
    //   filters: {
    //     OR: [
    //       {
    //         AND: [
    //           {
    //             type: 1,
    //             column: 'item_type_id',
    //             data_type: 'number',
    //             operator: 'equals',
    //             value: `${item.itemTypeId}`,
    //           },
    //         ],
    //       },
    //     ],
    //   },
    // };
    tempt.uiType = 'lookup-info-multi-operator';
    tempt.config = {
      objectName: item.itemPropertyName,
      objectType: 'BO_SEGMENTS',
    };
  } else if (group.groupId === -1003 || parseInt(parentGroupId) === -1003) {
    tempt.itemConditionType = 'computed_property';
  } else if (group.groupId === -1007 || parseInt(parentGroupId) === -1007) {
    tempt.itemConditionType = 'computed_property';
  }
  return tempt;
};

function convertObjectMetadata(parentKey, objectMedata = {}, params) {
  // let objectMedata = safeParse(objectMedata, {});
  // console.log('objectMedata', parentKey, objectMedata);
  if (objectMedata === null || Object.keys(objectMedata).length === 0) {
    return { list: [], map: {} };
  }
  const res = { list: [], map: {} };
  // eslint-disable-next-line camelcase
  const { option_metadata = [] } = objectMedata;
  if (Array.isArray(option_metadata)) {
    option_metadata.forEach(item => {
      const tmp = {
        propertyId: item.key_name,
        propertyCode: item.key_name,
        value: `${parentKey}@@${item.key_name}`,
        label: `${item.key_display}${params.showIsRequired && item.is_required === 1 ? '*' : ''
          }`,
        dataType: item.data_type,
        itemDataType: item.data_type,
        isRequired: safeParseInt(item.is_required, 0),
      };
      res.list.push(tmp);
      res.map[tmp.value] = tmp;
    });
  }
  return res;
}

export const convertItemAttributesUniqueWithItemTypeId = (
  item,
  group,
  parentGroupId = null,
  params = { showIsRequired: false },
  segmentId = null,
) => {
  const objectMetadata = convertObjectMetadata(
    `${item.itemPropertyName}-${item.itemTypeId}`,
    item.objectMetadata,
    params,
  );
  const tempt = {
    // isSort: 1,
    ...item,
    propertyId: item.itemPropertyName,
    propertyCode: item.itemPropertyName,
    propertyName: safeParse(item.translateLabel, item.itemPropertyDisplay),
    itemTypeId: item.itemTypeId,
    itemTypeName: item.itemTypeName,
    dataType: mapToDataType(item.isPk, item.dataType),
    itemDataType: item.dataType,
    isOutputProductId: item.isOutputProductId,
    type: item.type,
    groupCode: group.value,
    groupId: group.groupId,
    // value: item.itemPropertyName,
    value: `${item.itemPropertyName}-${item.itemTypeId}`,
    encryptCode: `${item.itemPropertyName}`,
    label: `${safeParse(item.translateLabel, item.itemPropertyDisplay)}${params.showIsRequired && item.isRequired === 1 ? '*' : ''
      }`,
    isSort: safeParseInt(item.isSort, 0),
    isFilter: safeParseInt(item.isFilter, 0),
    isRequired: safeParseInt(item.isRequired, 0),
    statusItemCode: getStatusItemCode('info', parseInt(item.status)),
    autoSuggestion: safeParseInt(item.autoSuggestion, 0),
    objectMetadata: objectMetadata.list,
    isEncrypt: safeParseIsEncrypt(
      item.itemTypeId,
      item.itemPropertyName,
      safeParseInt(item.isEncrypt, 0),
    ),
    configSuggestion: getConfigAutoSuggestion(item, segmentId),
    displayFormat: safeParseDisplayFormat(safeParse(item.displayFormat, null), {
      dataType: item.dataType,
    }),
  };

  if (item.itemPropertyName === 'user_id') {
    tempt.autoSuggestion = 1;
  }

  if (item.itemPropertyName === 'segment_ids') {
    // tempt.configSuggestion = {
    //   feServices: 'suggestionMultilang',
    //   feKey: `1-${item.itemTypeId}-${item.itemTypeName}-${
    //     item.itemPropertyName
    //   }`,
    //   itemTypeId: item.itemTypeId,
    //   objectType: 'BO_SEGMENTS',
    //   isPk: 1,
    //   propertyCode: `${item.itemPropertyName}`,
    //   filters: {
    //     OR: [
    //       {
    //         AND: [
    //           {
    //             type: 1,
    //             column: 'item_type_id',
    //             data_type: 'number',
    //             operator: 'equals',
    //             value: `${item.itemTypeId}`,
    //           },
    //         ],
    //       },
    //     ],
    //   },
    // };
    tempt.uiType = 'lookup-info-multi-operator';
    tempt.config = {
      objectName: item.itemPropertyName,
    };
  } else if (group.groupId === -1003 || parseInt(parentGroupId) === -1003) {
    tempt.itemConditionType = 'computed_property';
  } else if (group.groupId === -1007 || parseInt(parentGroupId) === -1007) {
    tempt.itemConditionType = 'computed_property';
  }
  return { item: tempt, map: objectMetadata.map };
};

export const mapItemEventTracking = data => {
  const temp = {
    list: [],
    map: {},
  };

  const validateData = safeParse(data, []);

  if (validateData.length === 0) {
    return temp;
  }

  validateData.forEach(event => {
    const key = `${event.eventCategoryId}@@${event.eventActionId}`;

    const tempEvent = {
      key,
      eventActionId: event.eventActionId,
      eventCategoryId: event.eventCategoryId,
      insightPropertyId: event.insightPropertyId,
      eventNameDisplay: event.eventNameDisplay,
      eventNameMultilang: event.eventNameMultilang,
      eventTrackingName: event.eventTrackingName,
      translateDescription: event.translateDescription,
      translateLabel: event.translateLabel,
      label: safeParse(event.translateLabel, event.eventNameDisplay),
      eventTrackingCode: event.eventTrackingCode,
      iconUrl: event.iconUrl,
      // value: `${event.eventCategoryId}@@${event.eventActionId}`,
      value: key,
    };
    temp.list.push(tempEvent);
    temp.map[key] = tempEvent;
  });

  return temp;
};

export const mapChannel = (data = [], allChannel) => {
  const temp = {
    list: [],
    map: {},
  };

  data.forEach(item => {
    const key = item.channelId;
    const tmp = {
      key,
      value: key,
      code: item.channelCode,
      label: safeParse(item.translateLabel, item.channelName),
      logoUrl: item.logoUrl,
    };
    temp.list.push(tmp);
    temp.map[key] = tmp;
  });

  return temp;
};

export const convertItemAttributesUniqueWithBussinessObject = (
  item,
  group,
  parentGroupId = null,
  params = { showIsRequired: false },
) => {
  const objectMetadata = convertObjectMetadata(
    `${item.itemPropertyName}-${item.itemTypeId}`,
    item.objectMetadata,
    params,
  );
  const tempt = {
    // isSort: 1,
    ...item,
    propertyId: item.itemPropertyName,
    propertyCode: item.itemPropertyName,
    propertyName: safeParse(item.translateLabel, item.itemPropertyDisplay),
    itemTypeId: item.itemTypeId,
    itemTypeName: item.itemTypeName,
    dataType: mapToDataType(item.isPk, item.dataType),
    itemDataType: item.dataType,
    isOutputProductId: item.isOutputProductId,
    type: item.type,
    groupCode: group.value,
    groupId: group.groupId,
    value: item.itemPropertyName,
    label: `${safeParse(item.translateLabel, item.itemPropertyDisplay)}${params.showIsRequired && item.isRequired === 1 ? '*' : ''
      }`,
    isSort: safeParseInt(item.isSort, 0),
    isFilter: safeParseInt(item.isFilter, 0),
    isRequired: safeParseInt(item.isRequired, 0),
    statusItemCode: getStatusItemCode('info', parseInt(item.status)),
    autoSuggestion: safeParseInt(item.autoSuggestion, 0),
    objectMetadata: objectMetadata.list,
    isEncrypt: safeParseIsEncrypt(
      item.itemTypeId,
      item.itemPropertyName,
      safeParseInt(item.isEncrypt, 0),
    ),
    configSuggestion: getConfigAutoSuggestion(item),
    displayFormat: safeParseDisplayFormat(safeParse(item.displayFormat, null), {
      dataType: item.dataType,
    }),
  };

  if (item.itemPropertyName === 'user_id') {
    tempt.autoSuggestion = 1;
  }

  if (item.itemPropertyName === 'segment_ids') {
    // tempt.configSuggestion = {
    //   feServices: 'suggestionMultilang',
    //   feKey: `1-${item.itemTypeId}-${item.itemTypeName}-${
    //     item.itemPropertyName
    //   }`,
    //   itemTypeId: item.itemTypeId,
    //   objectType: 'BO_SEGMENTS',
    //   isPk: 1,
    //   propertyCode: `${item.itemPropertyName}`,
    //   objectName: item.itemPropertyName, // required
    //   filters: {
    //     OR: [
    //       {
    //         AND: [
    //           {
    //             type: 1,
    //             column: 'item_type_id',
    //             data_type: 'number',
    //             operator: 'equals',
    //             value: `${item.itemTypeId}`,
    //           },
    //         ],
    //       },
    //     ],
    //   },
    // };
    tempt.uiType = 'lookup-info-multi-operator';
    tempt.config = {
      objectName: item.itemPropertyName,
    };
  } else if (group.groupId === -1003 || parseInt(parentGroupId) === -1003) {
    tempt.itemConditionType = 'computed_property';
  } else if (group.groupId === -1007 || parseInt(parentGroupId) === -1007) {
    tempt.itemConditionType = 'computed_property';
  }
  return { item: tempt, map: objectMetadata.map };
};

// Lookup Multilang

const TEMPLATE_PARAMS_LOOKUP = {
  segment_ids: ({ ids = [], config = {} }) => {
    // console.log('TEMPLATE_PARAMS_LOOKUP', config, config.objectType);
    // tranfrom data here
    const { objectType } = config;
    const value = toArrayApiWithDataType(ids, 'number');

    return {
      objectType,
      filters: {
        OR: [
          {
            AND: [
              {
                column: 'segment_id',
                data_type: 'number',
                operator: 'matches',
                value,
              },
            ],
          },
        ],
      },
    };
  },
  label_ids: ({ ids = [], config = {} }) => {
    // console.log('TEMPLATE_PARAMS_LOOKUP', config, config.objectType);
    // tranfrom data here
    const { objectType } = config;
    const value = toArrayApiWithDataType(ids, 'number');

    return {
      objectType,
      filters: {
        OR: [
          {
            AND: [
              {
                column: 'label_id',
                data_type: 'number',
                operator: 'matches',
                value,
              },
            ],
          },
        ],
      },
    };
  },
  segment_display: ({ ids = [], config = {} }) => {
    const { objectType, propertyCode } = config;
    const value = toArrayApiWithDataType(ids, 'array_string');
    console.log({ ids, config, value, propertyCode });
    return {
      objectType,
      filters: {
        OR: [
          {
            AND: [
              {
                // column: propertyCode,
                column: 'segment_display',
                data_type: 'string',
                operator: 'matches',
                value,
              },
            ],
          },
        ],
      },
    };
  },
  pool_id: ({ ids = [], config = {} }) => {
    // console.log('TEMPLATE_PARAMS_LOOKUP', config, config.objectType);
    // tranfrom data here
    const { objectType } = config;
    const value = toArrayApiWithDataType(ids, 'number');

    return {
      objectType,
      filters: {
        OR: [
          {
            AND: [
              {
                column: 'pool_id',
                data_type: 'number',
                operator: 'matches',
                value,
              },
            ],
          },
        ],
      },
    };
  },
  default: () => ({}),
};
export const getParamsAPILookup = data => {
  const { objectName = '', encryptCode = '' } = data;
  return (TEMPLATE_PARAMS_LOOKUP[objectName] ||
    TEMPLATE_PARAMS_LOOKUP[encryptCode] ||
    TEMPLATE_PARAMS_LOOKUP.default)(data);
};

// // Suggestion Multilang

// const TEMPLATE_PARAMS_SUGGESTION = {
//   field_name: ({ objectType, search = '', model_id }) => {
//     // tranfrom data here
//     return {
//       objectType,
//       limit: 20,
//       page: 1,
//       sort: 'asc',
//       search,
//       filters: {
//         OR: [
//           {
//             AND: [
//               {
//                 column: 'model_id',
//                 data_type: 'number',
//                 operator: 'equals',
//                 value: parseInt(model_id),
//               },
//             ],
//           },
//         ],
//       },
//     };
//   },
//   default: () => ({}),
// };

// export const getParamsAPISuggestionMultiLang = data => {
//   const { objectName = '' } = data;
//   return (TEMPLATE_PARAMS_SUGGESTION[objectName] ||
//     TEMPLATE_PARAMS_SUGGESTION.default)(data);
// };

export function toArrayApiWithDataType(arr, dataType = 'string') {
  if (dataType === 'number' || dataType === 'array_number') {
    const tmp = [];
    arr.forEach(item => {
      if (isNumeric(item)) {
        tmp.push(+item);
      }
    });
    return tmp;
  }
  if (dataType === 'datetime' || dataType === 'array_datetime') {
    const tmp = [];
    arr.forEach(item => {
      if (isNumeric(item)) {
        tmp.push(+item);
      } else {
        tmp.push(item); // https://trello.com/c/tPO620LY
      }
    });
    return tmp;
  }
  // for case label_ids data API return number but array is string
  if (dataType === 'array_string') {
    const tmp = [];
    arr.forEach(item => {
      if (isNumeric(item)) {
        tmp.push(`${item}`);
      } else {
        tmp.push(item);
      }
    });
    return tmp;
  }
  return arr;
}
