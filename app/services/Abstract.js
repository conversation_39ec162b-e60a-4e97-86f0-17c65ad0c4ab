/* eslint-disable no-else-return */
/* eslint-disable indent */
/* eslint-disable react/no-danger */
/* eslint-disable arrow-body-style */
import React from 'react';
import { pick } from 'lodash';
import {
  callApiWithAuth,
  callApiWithAuthV2,
  callApiWithoutHeaders,
} from 'utils/request';
import ObjectServies from 'services/Object';
import PMService from './PredictiveModel';
import { safeParse } from 'utils/common';
import { ENDPOINT, URL_IAM, URL_SHORT_LINK } from '../../config/common';
import { DATA_INFO } from './Abstract.data';
import { getEntriesV2, getMessageByCode } from './utils';
import { getParamsAPILookup } from './map';
import LinkManagementService from './LinkManagement';
// import { mappingCustomerProperty } from '../utils/web/properties';
const dataBackup = {
  source_names: {},
  // c_user_id: {},
  story_id: {},
  destination_id: {},
  status: {},
  item_type_id: {},
  process_status: {},
  channel_id: {},
  catalog_id: {},
  variant_id: {},
  allocated_to_journey: {},
  state: {},
};

const err = {
  code: 500,
  codeMessage: 'INTERNAL_SERVER_ERROR',
  message: getMessageByCode('INTERNAL_SERVER_ERROR'),
  data: [],
};

const isExistedDataBackup = (data, objectName) => {
  if (data[objectName]) {
    if (Object.keys(data[objectName]).length === 0) {
      return false;
    } else if (!data[objectName].data) {
      return false;
    } else if (data[objectName].data && data[objectName].data.length === 0) {
      return false;
    }
    return true;
  }
  return false;
};

const API = {
  lookupInfo: {
    get: params => {
      // body: {reportNewName: 'new name'}
      const { objectName = '' } = params;

      if (isExistedDataBackup(dataBackup, objectName)) {
        return new Promise((resolve, reject) => {
          resolve(dataBackup[objectName]);
          reject(Error(err));
        });
      }

      // if (DATA_INFO[objectName] !== undefined) return DATA_INFO[objectName];
      if (DATA_INFO[objectName] !== undefined) {
        return API.lookupInfo.callApi.getDataHard(params);
      }

      if (API.lookupInfo.callApi[objectName]) {
        return API.lookupInfo.callApi[objectName](params);
      }
      return API.lookupInfo.callApi.__DEFAULT__(params);
    },
    callApi: {
      story_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/stories/`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          dataBackup[params.objectName] = data;
          return data;
        });
      },
      variant_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object/`,
          'POST',
          {
            objectType: 'VARIANTS',
            filters: {
              OR: [
                {
                  AND: [],
                },
              ],
            },
            limit: null,
            page: null,
            search: '',
            isSnakeCase: 0,
          },
        ).then(res => {
          const data = getEntriesV2(res, 0);
          dataBackup[params.objectName] = data;
          return data;
        });
      },
      source_names: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkit}/selector/data-source`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          dataBackup[params.objectName] = data;
          return data;
        });
      },
      connector_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.promotionCenterV2}/promotion-source/selector`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          dataBackup[params.objectName] = data;
          return data;
        });
      },
      c_user_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/users/listing?userIds=${safeParse(
            params.data,
            [],
          ).join(',')}`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      suggestion_bo_name: params => {
        return callApiWithAuth(`${ENDPOINT.items}/items`, 'GET', null).then(
          res => {
            const data = getEntriesV2(res, 0);
            // dataBackup[params.objectName] = data;
            return data;
          },
        );
      },
      group_attribute: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object/BO_ATTRIBUTE_GROUP`,
          'POST',
          params.config.mapDataToAPI,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      user_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/users/listing?userIds=${safeParse(
            params.data,
            [],
          ).join(',')}`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },

      predictive_model_id: async params => {
        const { data } = params;
        const { search } = data;
        const endpoint = `${ENDPOINT.toolkitV2}/suggestion/objects`;

        const res = await callApiWithAuthV2({
          endpoint,
          body: {
            objectType: 'AUDIENCE_PREDICTIVE_MODELS',
            sort: 'asc',
            search,
          },
          method: 'POST',
        });

        return getEntriesV2(res, []);
      },
      account: params => {
        // console.log('params', params);
        // return callApiWithAuth(
        //   `${ENDPOINT.toolkitV2}/selector/users/listing?userIds=${safeParse(
        //     params.data,
        //     [],
        //   ).join(',')}`,
        //   'GET',
        //   null,
        // ).then(res => {
        //   const data = getEntriesV2(res, 0);
        //   // dataBackup[params.objectName] = data;
        //   return data;
        // });
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/users/listing`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      account_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/users/listing?userIds=${safeParse(
            params.data,
            [],
          ).join(',')}`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      zone_id: (params = { config: { filters: {} } }) => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object/ZONES`,
          'POST',
          { filters: params.config.filters },
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      segment_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object/BO_SEGMENTS`,
          'POST',
          { filters: params.config.filters },
        ).then(res => {
          const data = getEntriesV2(res, 0);

          return data;
        });
      },
      model_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object/ANALYTIC_MODEL`,
          'POST',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          return data;
        });
      },
      u_user_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/users/listing?userIds=${safeParse(
            params.data,
            [],
          ).join(',')}`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      encrypt_field: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/encrypt/attributes`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          return data;
        });
      },
      // segment_ids: params => {
      //   const { data: ids, config = {}, objectName } = params;
      //   const data = getParamsAPILookup({ objectName, ids, config });
      //   const url = `${ENDPOINT.toolkitV2}/info/metadata`;
      //   return callApiWithAuth(url, 'POST', data).then(res =>
      //     getEntriesV2(res, []),
      //   );
      // },
      // label_ids: params => {
      //   const { data: ids, config = {}, objectName } = params;
      //   const data = getParamsAPILookup({ objectName, ids, config });
      //   const url = `${ENDPOINT.toolkitV2}/info/metadata`;
      //   return callApiWithAuth(url, 'POST', data).then(res =>
      //     getEntriesV2(res, []),
      //   );
      // },
      __DEFAULT__: params => {
        console.warn('NOT DEFINE API Lookup, using default API lookup');
        const { data: ids, config = {}, objectName } = params;
        const data = getParamsAPILookup({ objectName, ids, config });
        const url = `${ENDPOINT.toolkitV2}/info/metadata`;
        return callApiWithAuth(url, 'POST', data).then(res =>
          getEntriesV2(res, []),
        );
      },
      getDataHard: params => {
        const res = {
          data: {
            code: 200,
            codeMessage: 'SUCCESS',
            data: { entries: DATA_INFO[params.objectName] },
          },
        };

        // console.log(res);

        const data = getEntriesV2(res, 0);
        dataBackup[params.objectName] = data;

        return new Promise((resolve, reject) => {
          resolve(data);
          reject(Error(err));
        });
      },
      channel_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/channels`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          dataBackup[params.objectName] = data;
          return data;
        });
      },
      channel_id_with_objectType: params => {
        // console.log('params', params);
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/channels?objectType=${
            params.config.objectType
          }`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;

          return data;
        });
      },
      compute_id: params => {
        // console.log('params', params);
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object`,
          'POST',
          params.config.selectorFilter,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      export_id: params => {
        // console.log('params', params);
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/export-histories`,
          'POST',
          params.config.selectorFilter,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      catalog_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.destinationV2_1}/catalog/channel/${params.config
            .channelId || 'all'}`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      campaign_id: params => {
        // console.log('params', params);
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/campaigns?&type=2&storyId=${
            params.config.objectId
          }`,
          'GET',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          dataBackup[params.objectName] = data;
          return data;
        });
      },
      campaign_id_suggestion: params => {
        // console.log(params);
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object`,
          'POST',
          params.config.suggestion,
        ).then(res => {
          return getEntriesV2(res, 0);
        });
      },
      stories_id_suggestion: params => {
        // console.log(params);
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object`,
          'POST',
          params.config.suggestion,
        ).then(res => {
          return getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
        });
      },
      variant_id_suggestion: params => {
        // console.log(params);
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object`,
          'POST',
          params.config.suggestion,
        ).then(res => {
          return getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
        });
      },
      destination_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object/DESTINATIONS`,
          'POST',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      allocated_to_journey: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object/STORIES`,
          'POST',
          null,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      import_id: params => {
        // console.log('params import', params);
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object`,
          'POST',
          params.config.selectorFilter,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          // dataBackup[params.objectName] = data;
          return data;
        });
      },
      // pool_id: params => {
      //   console.log('params import', params);
      //   return callApiWithAuth(
      //     `${ENDPOINT.toolkitV2}/selector/object`,
      //     'POST',
      //     params.config.selectorFilter,
      //   ).then(res => {
      //     const data = getEntriesV2(res, 0);
      //     // dataBackup[params.objectName] = data;
      //     return data;
      //   });
      // },
      pool_name: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object`,
          'POST',
          params.config.selectorFilter,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          return data;
        });
      },
      event_category_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object/EVENT_DIMENSIONS`,
          'POST',
          (params.config || {}).data,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          return data;
        });
      },
      event_action_id: params => {
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object/EVENT_DIMENSIONS`,
          'POST',
          (params.config || {}).data,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          return data;
        });
      },
      treat_as: params => {
        // console.log('params', params);
        return callApiWithAuth(
          `${ENDPOINT.toolkitV2}/selector/object/EVENT_DIMENSIONS`,
          'POST',
          (params.config || {}).data,
        ).then(res => {
          const data = getEntriesV2(res, 0);
          return data;
        });
      },
      group_name: params => {
        // console.log('params', params);
        const url = `${ENDPOINT.toolkitV2}/suggestion/objects`;
        return callApiWithAuth(url, 'POST', (params.config || {}).data).then(
          res => {
            const resTmp = getEntriesV2(res, []);

            const list = [];
            const map = {};
            resTmp.data.forEach(item => {
              const itemSearch = {
                ...item,
                label: item.name,
                value: item.name,
              };
              list.push(itemSearch);
              map[item.name] = itemSearch;
            });
            const data = { ...resTmp, data: { list, map } };
            return data;
          },
        );
      },
      vendor: params => {
        const endpoint = `${ENDPOINT.apiPerformance}/vendor?source=cdp`;
        const data = {
          filters: {
            OR: [
              {
                AND: [
                  {
                    column: 'vendor_name',
                    data_type: 'string',
                    operator: 'contains',
                    value: '',
                  },
                ],
              },
            ],
          },
          properties: ['vendor_id', 'vendor_name'],
          limit: 12,
          page: 1,
          sort: 'ctime',
          sd: 'desc',
          search: '',
        };
        return callApiWithoutHeaders({
          endpoint,
          method: 'POST',
          body: data,
          domain: URL_SHORT_LINK,
          isAddAccountId: true,
        }).then(res => {
          const data = getEntriesV2(res, 0);
          return data;
        });
      },
      status_vendor: params => {
        const endpoint = `api/vendor-account?type=get-status`;
        return callApiWithoutHeaders({
          endpoint,
          method: 'GET',
          domain: URL_SHORT_LINK,
          isAddAccountId: true,
        }).then(res => {
          return res.data;
        });
      },
      status_shortener: () => {
        const endpoint = `api/link-shortener?type=get-status`;
        return callApiWithoutHeaders({
          endpoint,
          method: 'GET',
          domain: URL_SHORT_LINK,
          isAddAccountId: true,
        }).then(res => {
          return res.data;
        });
      },
      verify_status: () => {
        const endpoint = `api/link-shortener?type=get-verify-status`;
        return callApiWithoutHeaders({
          endpoint,
          method: 'GET',
          domain: URL_SHORT_LINK,
          isAddAccountId: true,
        }).then(res => {
          return res.data;
        });
      },
    },
  },

  tableSuggestion: {
    suggestion: params => {
      return ObjectServies.suggestion.getList(params);
    },
    suggestionHistoriesJourney: params => {
      return ObjectServies.suggestionHistoriesJourney.getList(params);
    },
    suggestionV1: params => {
      return ObjectServies.suggestionV1.getList(params);
    },
    suggestionMultilang: params => {
      return ObjectServies.suggestionMultilang.getList(params);
    },
    suggestionMultilangJourneyHistories: params => {
      return ObjectServies.suggestionMultilangJourneyHistories.getList(params);
    },
    suggestionMultilangExport: params => {
      return ObjectServies.suggestionMultilangExport.getList(params);
    },
    suggestionVendorManagement: params => {
      return ObjectServies.suggestionVendorManagement.getList(params);
    },
    suggestionShortenerManagement: params => {
      return ObjectServies.suggestionShortenerManagement.getList(params);
    },
    suggestionLinkManagement: params => {
      return ObjectServies.suggestionLinkManagement.getList(params);
    },
    eventLogRawData: params => {
      const { data } = params;

      const { configSuggestion, search } = data;

      // if (search.length === 0) return Promise.resolve({ list: [] });
      const body = {
        ...pick(configSuggestion, [
          'columns',
          'durations',
          'eventTrackingName',
          'insightPropertyId',
        ]),
        limit: 10,
        filters: {
          OR: [
            {
              AND: search.length
                ? [
                    {
                      type: 1,
                      column: 'raw_data',
                      data_type: 'string',
                      operator: 'contains',
                      value: search,
                    },
                  ]
                : [],
            },
          ],
        },
      };

      const endpoint = `${ENDPOINT.datasourceV2}/sources/event-logs/listing`;

      return callApiWithAuthV2({
        endpoint,
        method: 'POST',
        body,
      }).then(res => {
        const resTmp = getEntriesV2(res, []);

        const list = [];
        const regex = new RegExp(`(${search})`, 'g');

        resTmp.data.forEach(item => {
          const hightlightSearchHtml = `${item.raw_data}`.replace(
            regex,
            '<span style="color: red">$1</span>',
          );

          const itemSearch = {
            ...item,
            label: (
              <div
                style={{ wordBreak: 'break-all' }}
                dangerouslySetInnerHTML={{
                  __html: hightlightSearchHtml,
                }}
              />
            ),
            value: item.log_id,
          };
          list.push(itemSearch);
        });
        return { list };
      });
    },
    businessObjectV2: params => {
      const { search } = params.data;
      const tmp = {
        dataPost: {
          objectType: 'BUSINESS_OBJECT',
          limit: 20,
          page: 1,
          sort: 'asc',
          search,
          filters: {
            OR: [
              {
                AND: [
                  {
                    column: 'type',
                    data_type: 'number',
                    operator: 'equals',
                    value: 2,
                  },
                ],
              },
            ],
          },
        },
      };
      const url = `${ENDPOINT.toolkitV2}/suggestion/objects`;
      return callApiWithAuth(url, 'POST', tmp.dataPost).then(res => {
        const resTmp = getEntriesV2(res, []);
        const list = [];
        resTmp.data.forEach(item => {
          const itemSearch = {
            ...item,
            label: item.name,
            value: item.id,
          };
          list.push(itemSearch);
        });
        return { list };
      });
    },
    segmentMember: params => {
      const {
        search,
        itemTypeId,
        apiColumnDefault,
        objectId,
        searchConfig: { page, limit, sd, mapToEntryFE, filterBase },
      } = params.data;
      const tmp = {
        dataPost: {
          itemTypeId,
          page,
          limit,
          sort: 'last_updated',
          sd: safeParse(sd, 'desc'),
          properties: apiColumnDefault,
          filters: filterBase(search),
          filterSegments: {
            OR: [
              {
                AND: [
                  {
                    column: 'segment_id',
                    value: objectId,
                  },
                ],
              },
            ],
          },
        },
      };
      const url = `${ENDPOINT.explorerV2}/search/${itemTypeId}`;

      return callApiWithAuth(url, 'POST', tmp.dataPost).then(res => {
        const resTmp = getEntriesV2(res, []);
        const list = [];
        resTmp.data.forEach(item => {
          const itemSearch = mapToEntryFE(item);
          list.push(itemSearch);
        });
        return { list };
      });
    },
    userId: params => {
      const { search } = params.data;
      const tmp = {
        dataPost: {
          limit: 10,
          page: 1,
          search,
        },
      };
      const url = `${ENDPOINT.toolkitV2}/suggestion/users`;
      return callApiWithAuth(url, 'POST', tmp.dataPost).then(res => {
        const resTmp = getEntriesV2(res, []);
        const list = [];
        resTmp.data.forEach(item => {
          const itemSearch = {
            ...item,
            label: item.name,
            value: item.id,
          };
          list.push(itemSearch);
        });
        return { list };
      });
    },
    encryptedFields: params => {
      const { search } = params.data;
      const tmp = {
        dataPost: {
          limit: 10,
          page: 1,
          search,
        },
      };
      const url = `${ENDPOINT.toolkitV2}/suggestion/bo/encrypted-fields`;
      return callApiWithAuth(url, 'POST', tmp.dataPost).then(res => {
        const resTmp = getEntriesV2(res, []);
        const list = [];
        resTmp.data.forEach(item => {
          const itemSearch = {
            ...item,
            label: item.translateLabel,
            value: `${item.itemPropertyName}_${item.itemTypeId}`,
          };
          list.push(itemSearch);
        });
        return { list };
      });
    },
    businessObject: params => {
      const { search } = params.data;
      const tmp = {
        dataPost: {
          limit: 10,
          page: 1,
          search,
        },
      };
      const url = `${ENDPOINT.toolkitV2}/suggestion/bo/encrypted-fields`;
      return callApiWithAuth(url, 'POST', tmp.dataPost).then(res => {
        const resTmp = getEntriesV2(res, []);
        console.log('resTmp: ', resTmp);
        const list = [];
        resTmp.data.forEach(item => {
          const itemSearch = {
            ...item,
            label: item.translateLabel,
            value: `${item.itemPropertyName}_${item.itemTypeId}`,
          };
          list.push(itemSearch);
        });
        return { list };
      });
    },
    // attrsSuggestion: params => {
    //   // const { search } = params.data;
    //   // console.log({params});
    //   // const tmp = {
    //   //   dataPost: {
    //   //     limit: 10,
    //   //     page: 1,
    //   //     search,
    //   //   },
    //   // };
    //   // const url = `${ENDPOINT.toolkit}/suggestion`;
    //   const url = `${ENDPOINT.toolkitV2}/suggestion/objects`;
    //   return callApiWithAuth(url, 'POST', params.data).then(res => {
    //     const resTmp = getEntriesWithTotalV2(res);
    //     const list = [];
    //     resTmp.data.forEach(item => {
    //       const itemSearch = {
    //         ...item,
    //         label: item.name,
    //         value: item.id,
    //       };
    //       list.push(itemSearch);
    //     });
    //     return { list, total: resTmp.meta.total };
    //   });
    // },
    dataTable: params => {
      // const { search } = params.data;
      // console.log({params});
      // const tmp = {
      //   dataPost: {
      //     limit: 10,
      //     page: 1,
      //     search,
      //   },
      // };
      // const url = `${ENDPOINT.toolkit}/suggestion`;
      const url = `${ENDPOINT.toolkitV2}/suggestion/items`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        // const resTmp = getEntriesV2(res, []);
        const resTmp = getEntriesV2(res);
        const list = [];
        resTmp.data.forEach(item => {
          const itemSearch = {
            ...item,
            label: item.name,
            value: item.id,
          };
          list.push(itemSearch);
        });
        return { list };
      });
    },
    promotionCode: params => {
      const url = `${ENDPOINT.toolkitV2}/suggestion/pool-info`;
      return callApiWithAuth(url, 'POST', params.data).then(res => {
        // const resTmp = getEntriesV2(res, []);
        const resTmp = getEntriesV2(res);
        const list = [];
        resTmp.data.forEach(item => {
          const itemSearch = {
            ...item,
            label: item.name,
            value: item.id,
          };
          list.push(itemSearch);
        });
        return { list };
      });
    },
  },
  deletePortal: {
    delete: params => {
      const endpoint = `api/network/index/${
        params.portalId
      }?type=delete-portal&password=${params.password}&_account_id=${
        params.userId
      }`;
      return callApiWithoutHeaders({
        endpoint,
        method: 'DELETE',
        domain: URL_IAM,
        // isAddAccountId: true,
      }).then(res => {
        return res.data;
      });
    },
  },
};

export default API;
