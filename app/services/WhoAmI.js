import { callApiWhoAmI, callApiLogout } from '../utils/request';
import { getData } from './utils';
import {
  getToken,
  getPortalId,
  getCurrentAccessUserId,
} from '../utils/web/cookie';
import { addMessageToQueue } from '../utils/web/queue';
// import { callApi } from '../utils/request';

const API = {
  network: {
    info: () => {
      const userId = getCurrentAccessUserId();
      const url = `api/network/info?_pid=&_state=app.permission.role&app_id=${getPortalId()}&_token=${getToken()}&_user_id=${userId}&_account_id=${userId}`;
      return callApiWhoAmI(url, 'GET', null).then(res => {
        const output = getData(res, {});

        // push notification if column get error
        if (output.code !== 200 || Object.keys(output.data).length === 0) {
          addMessageToQueue({
            path: 'app/services/WhoAmI.js',
            func: 'get network info',
            data: { code: res.code, url, data: res.data },
          });
        }
        return output;
      });
    },
  },
  user: {
    info: () => {
      const url = `api/3rd/info?type=user-info&userId=${getCurrentAccessUserId()}&networkId=${getPortalId()}`;
      return callApiWhoAmI(url, 'GET', null).then(res => {
        const output = getData(res, {});
        // push notification if column get error
        if (output.code !== 200 || Object.keys(output.data).length === 0) {
          addMessageToQueue({
            path: 'app/services/WhoAmI.js',
            func: 'get network info',
            data: { code: res.code, url, data: res.data },
          });
        }
        return output;
      });
    },
  },

  account: {
    authenticate: data => {
      const url = `api/account/authenticate`;
      return callApiWhoAmI(url, 'POST', data).then(res => {
        const output = getData(res, {});

        // push notification if column get error
        if (output.code !== 200 || Object.keys(output.data).length === 0) {
          addMessageToQueue({
            path: 'app/services/WhoAmI.js',
            func: 'get network info',
            data: { code: res.code, url, data: res.data },
          });
        }
        return output;
      });
    },
    logout: data => {
      const url = `ogs/logout`;
      return callApiLogout(url, 'GET', data).then(res => {
        const output = getData(res, {});

        return output;
      });
    },
  },
};

export default API;
