import { ENDPOINT, URL_HISTORY } from '../../config/common';
import { callApiWithAuthV2 } from '../utils/request';
import { getEntriesV2 } from './utils';

const API = {
  getIngredients: async args => {
    const { objectConfig } = args;

    const res = await callApiWithAuthV2({
      body: { objectConfig },
      domain: URL_HISTORY,
      endpoint: `${ENDPOINT.journeyTemplate}/get-ingredients`,
      method: 'POST',
    });

    return getEntriesV2(res, []);
  },

  validateIngredients: async args => {
    const { objectConfig } = args;

    const res = await callApiWithAuthV2({
      body: { objectConfig },
      domain: URL_HISTORY,
      endpoint: `${ENDPOINT.journeyTemplate}/validate-ingredients`,
      method: 'POST',
    });

    return getEntriesV2(res, []);
  },
};

export default API;
