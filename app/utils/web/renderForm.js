/* eslint-disable import/no-cycle */
/* eslint-disable react/prop-types */
/* eslint-disable prefer-const */
/* eslint-disable dot-notation */
/* eslint-disable indent */
/* eslint-disable no-useless-escape */
import TextareaAutosize from '@material-ui/core/TextareaAutosize';
import {
  UICheckbox,
  UINumber,
  UIPassword,
  UITextField,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import classnames from 'classnames';
import PairKeyValue from 'components/common/UIPairKeyValue';
import UISelect from 'components/form/UISelectCondition';
import UISelectWithAPI from 'components/form/UISelectWithAPI';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import React from 'react';
import styled from 'styled-components';
// import UIModalInsert from 'components/common/UIInsertMedia';
import {
  Checkbox as CheckboxAntsomiUI,
  DatePicker,
  EditableName,
  Flex,
  IconSelection,
  Switch,
  UploadImage,
} from '@antscorp/antsomi-ui';
import Icon from '@antscorp/icons/main';
import { FormControlLabel, Link, Radio, RadioGroup } from '@material-ui/core';
import FormHelperText from '@material-ui/core/FormHelperText';
import Grid from '@material-ui/core/Grid';
import ToggleButton from 'components/Atoms/ToggleButton/index';
import {
  buildDisableHours,
  getCurrentDate,
  // FORMAT_DATE_CALENDAR,
  getTimeRangeCalendar,
} from 'components/Organisms/CalendarTable/utils';
import ComputationSchedule from 'components/Organisms/ComputationSchedule';
import { defaultComputationSchedule } from 'components/Organisms/ComputationSchedule/constant';
import { validateComputeScheduleInHour } from 'components/Organisms/ComputationSchedule/utils';
import NotificationSetup from 'components/Organisms/NotificationSetup';
import { getNotificationDefault } from 'components/Organisms/NotificationSetup/constant';
import CalendarSelection from 'components/Templates/CalendarSelection';
import ChipSelect from 'components/common/ChipSelect';
import ComputeSchedule from 'components/common/ComputeSchedule';
import ConditionAttributes from 'components/common/ConditionAttributes';
import DataEvents from 'components/common/DataEvents';
import DataSources from 'components/common/DataSources';
import ModalConfigFormat from 'components/common/Modal/ModalConfigFormat';
import ModalFieldFormat from 'components/common/Modal/ModalFieldFormat';
import InputGetScript from 'components/common/UICopyScript';
import DropZone from 'components/common/UIDropZone';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import UITextColorPicker from 'components/common/UIFiledColorPicker';
import Formular from 'containers/UIDev/UIFormulaInputDemo';
import EditorFormular from 'containers/UIDev/UIFormular/Editor';
import ModalCalendarTable from 'containers/modals/ModalCalendarTable/FormModalCalendarTable';
import { isObject } from 'lodash';
import moment from 'moment';
import AceEditor from 'react-ace';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import InputPreview from '../../components/Atoms/InputPreview';
import Checkbox from '../../components/Molecules/CheckBox';
import { initDateValue } from '../../components/Templates/CalendarSelection/utils';
import AceEditorView from '../../components/common/AceEditor';
import InputLanguage from '../../components/common/InputLanguage';
import { getDefaultVal } from '../../components/common/InputLanguage/utils';
import { StyleSpan } from '../../components/common/UIAddFieldV2/styles';
import { UINumberStyled } from '../../components/common/UIFrequencyCapping/styled';
import ModalModifyColumn from '../../containers/ModifyColumn/CustomizeColumnObject/index';
import {
  DAYSOFMILISECOND,
  initTimeRangeDefaultV2,
} from '../../containers/Segment/Content/Condition/utils';
import { WrapperContent } from '../../containers/Segment/Content/styles';
import { getTranslateMessage } from '../../containers/Translate/util';
import TRANSLATE_KEY from '../../messages/constant';
import ChooseReport from '../../modules/Dashboard/Dashboard/Main/components/ChooseReport';
import TemplatePreview from '../../modules/Dashboard/Dashboard/Main/components/SelectTemplate/TemplatePreview';
import { Span } from '../../modules/Dashboard/MarketingHub/Promotion/Create/_UI/FieldRectriction/styles';
import { DisplayTimeZone } from '../../modules/Dashboard/Profile/Segment/Create/styles';
import { getLabelPortalTimeZone } from '../../modules/Dashboard/utils';
import { MAX_LNAME } from '../common';
import { STATUS_ITEM_CODE } from '../constants';
import { getCurrentUserId, getToken } from './cookie';
import {
  getErrorsByStatusItemCode,
  getUntitledName,
  getWarnsByStatusItemCode,
} from './properties';
import {
  MAX_LCODE,
  checkDomainIsValid,
  getObjectPropSafely,
  isProduction,
  safeParse,
  validateDomain,
  validateDomainDNS,
  validateEmail,
  validateWithSpaceAndSpecialChar,
} from './utils';
const { AdvancedRangePicker } = DatePicker;
const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';
const currentDate = new Date();

export const MAP_TRANSLATE = {
  nameEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EMPTY_NAME,
    `Name can't be empty`,
  ),
  nameEmptyV2: getTranslateMessage(
    TRANSLATE_KEY._NAME_RULE_EMPTY,
    `Name can't be empty`,
  ),
  invalidField: getTranslateMessage(
    TRANSLATE_KEY._NAME_RULE_INVALID_CHARACTER,
    `Invalid name! Name must start with a letter a-Z or underscore`,
  ),
  fieldIsRequired: getTranslateMessage(
    TRANSLATE_KEY._NOTI_FIELD_IS_REQUIRED,
    `Field is required`,
  ),
  notiPrefixPresvered: getTranslateMessage(
    TRANSLATE_KEY._NOTI_PREFIX_PRESVERED,
    'Codes start with the prefix aud_, sgmt_ or number_ are preserved for the system, please use another code.',
  ),
  notiObjectName: getTranslateMessage(
    TRANSLATE_KEY._,
    'Do not have the same name with other objects in the same portal.',
  ),
  spaceError: getTranslateMessage(
    TRANSLATE_KEY._NOTI_INVALID_NAME,
    'Invalid name',
  ),
  selectAnItem: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
    'Select an item',
  ),
  cannotEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EMPTY_NAME,
    `This field can't be empty`,
  ),
  domainError: getTranslateMessage(
    TRANSLATE_KEY._NOTI_INVALID_URL,
    'Invalid URL',
  ),
  notiEndFixPresvered: getTranslateMessage(
    TRANSLATE_KEY._NOTI_ENDFIX_PRESVERED,
    'Codes end with the endfix *_id are preserved for the system, please use another code.',
  ),
  notFormularCantEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_FORMULA_CANT_EMPTY,
    `Formular can't be emty.`,
  ),

  // Name
  invalidNameMaxLength: getTranslateMessage(
    TRANSLATE_KEY._NAME_RULE_INVALID_MAX_LENGTH,
    'Invalid name! Name contains no more than 255 characters',
  ),
  invalidRuleName: getTranslateMessage(
    TRANSLATE_KEY._NAME_RULE_INVALID_CHARACTER,
    'Invalid name! Name must start with a letter a-Z or underscore',
  ),
  invalidNameEmpty: getTranslateMessage(
    TRANSLATE_KEY._NAME_RULE_EMPTY,
    'Invalid name! Name can’t be empty',
  ),

  // Code
  invalidRuleCode: getTranslateMessage(
    TRANSLATE_KEY._CODE_RULE_INVALID_CHARACTER,
    'Invalid code! Only lowercase letter, numbers and underscore are accepted',
  ),
  invalidStartCode: getTranslateMessage(
    TRANSLATE_KEY._CODE_RULE_INVALID_START_CHARACTER,
    'Invalid code! Code must start with lowercase letters a-z or underscore',
  ),
  invalidCodeMaxLength: getTranslateMessage(
    TRANSLATE_KEY._CODE_RULE_INVALID_MAX_LENGTH,
    'Invalid code! Code contains no more than 50 characters',
  ),
  invalidCodeEmpty: getTranslateMessage(
    TRANSLATE_KEY._CODE_RULE_EMPTY,
    'Invalid code! Code can’t be empty',
  ),
  invalidRuleFormat: getTranslateMessage(
    TRANSLATE_KEY._CODE_RULE_INVALID_FORMAT,
    'Codes with format aud_*, number_* and sgmt_* are reserved for system.',
  ),
  invalidRuleFormatEvt: getTranslateMessage(
    TRANSLATE_KEY._CODE_RULE_INVALID_FORMAT_EVT,
    'Codes with format *_id , aud_*, number_* and sgmt_* are reserved for system.',
  ),
  invalidComputationSchedule: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Please complete computation schedule setting',
  ),
  invalidLimitSchedule: getTranslateMessage(
    TRANSLATE_KEY._004,
    'In this hour, the limit on the number of attributes to be built has been reached.',
  ),
  // domain
  invalidDNSDomain: getTranslateMessage(
    TRANSLATE_KEY._NOTI_INVALID_DOMAIN_1,
    'Please enter a valid domain, including a top-level domain (e.g., .com, .net, .vn)',
  ),
  onlyLetters: getTranslateMessage(
    TRANSLATE_KEY._NOTI_INVALID_DOMAIN_2,
    'Only letters (a-z), numbers (0-9), hyphens (-), and dots (.) are allowed',
  ),
  specialCharacter: getTranslateMessage(
    TRANSLATE_KEY._NOTI_INVALID_COOKIE_1,
    'The Cookie Domain must not end with a special character',
  ),
  enteredDomain: getTranslateMessage(
    TRANSLATE_KEY._NOTI_INVALID_COOKIE_2,
    'The Cookie Domain must be a part of the entered domain',
  ),
};
const AceEditorStyle = styled(AceEditor)`
  height: 240px !important;
  width: 580px !important;
  border: solid 1px #e5e5e5;
  overflow: auto !important;
  span {
    color: #000000 !important
    ;
  }
`;
export const IconWrraper = styled.div`
  background-color: #ffffff;
  left: 285px;
  top: 10px;
  position: absolute;
  justify-content: center;
  align-items: center;
  display: flex;
  height: 36px;
  width: 36px;
  border: 1px solid #b8cfe6;
  border-radius: 10px;
  z-index: 1;
  visibility: hidden;
  cursor: pointer;
  .pencil {
    font-size: 24px;
    color: #005eb8;
  }
`;
export const MAP_VALIDATE = {
  link: () => {
    return { errors: [], isValidate: true };
  },
  iconSelection: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  singleLineText: ({ value, isRequired, maxLength, name }) => {
    let [isValidate, errors] = [true, []];
    if (typeof value === 'undefined' || value === null) {
      isValidate = false;
    } else if (value === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (value.length > 0 && value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.spaceError];
    } else if (maxLength !== null && value.length > maxLength) {
      isValidate = false;
      errors = [`Invalid: maximum ${maxLength} characters`];
    }
    return { errors, isValidate };
  },
  singleLineTextSetting: ({ value, isRequired, maxLength }) => {
    let valueTmp;
    if (value) {
      valueTmp = value;
    } else {
      valueTmp = '';
    }
    let [isValidate, errors] = [true, []];
    if (valueTmp === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (valueTmp.length > 0 && valueTmp.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.spaceError];
    } else if (maxLength !== null && valueTmp.length > maxLength) {
      isValidate = false;
      errors = [`Invalid: maximum ${maxLength} characters`];
    }
    return { errors, isValidate };
  },
  imageUrl: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  toggle: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  editor: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (typeof value === 'string' && value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      !Object.prototype.hasOwnProperty.call(value, 'html') &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      Object.prototype.hasOwnProperty.call(value, 'html') &&
      value.html.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  htmlEditor: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (typeof value === 'string' && value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      !Object.prototype.hasOwnProperty.call(value, 'html') &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      Object.prototype.hasOwnProperty.call(value, 'html') &&
      value.html.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  multiLineText: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  selectDropdown: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  selectDropdownAPI: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];

    if ((value === undefined || value === null || value === '') && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  email: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
    } else {
      isValidate = validateEmail(value);
      errors = isValidate ? [] : ['Invalid email'];
    }
    return { errors, isValidate };
  },
  domain: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    let valueTmp;
    if (value) {
      valueTmp = value;
    } else {
      valueTmp = '';
    }
    if (valueTmp.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.cannotEmpty];
    } else {
      const { error, isValidates } = checkDomainIsValid(valueTmp);
      errors = [MAP_TRANSLATE[error]];
      isValidate = isValidates;
    }
    return { errors, isValidate };
  },
  cookieDomain: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    let valueTmp;
    if (value) {
      valueTmp = value;
    } else {
      valueTmp = '';
    }
    if (valueTmp.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.enteredDomain];
    }
    return { errors, isValidate };
  },
  password: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
    }
    return { errors, isValidate };
  },
  keyvalue: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else {
      isValidate = Object.keys(value).every(
        each => each.trim() !== '' && value[each].trim() !== '',
      );
      errors = isValidate ? [] : [`None input can't be blank`];
    }

    return { errors, isValidate };
  },
  checkbox: () => ({ errors: [], isValidate: true }),
  radioGroup: () => ({ errors: [], isValidate: true }),
  default: () => ({ errors: [], isValidate: true }),
  fieldFormat: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (isRequired && _isEmpty(value.type)) {
      isValidate = false;
    }
    return { errors, isValidate };
  },
  number: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (Number.isNaN(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  multiLangInput: ({ value, isRequired, maxLength }) => {
    let [isValidate, errors] = [true, []];
    const defaultValue = getDefaultVal(value);
    if (
      typeof value === 'undefined' ||
      value === null ||
      typeof defaultValue === 'undefined' ||
      defaultValue === null
    ) {
      isValidate = false;
    } else if (defaultValue === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      defaultValue.length > 0 &&
      defaultValue.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.spaceError];
    } else if (maxLength !== null && defaultValue.length > maxLength) {
      isValidate = false;
      errors = [`Invalid: maximum ${maxLength} characters`];
    }
    return { errors, isValidate };
  },
  objectName: ({ value, isRequired, maxLength }) => {
    let [isValidate, errors] = [true, []];
    const defaultValue = getDefaultVal(value);
    const regexHasBannedPrefix = new RegExp(
      // /^(\d|object|attribute|event|group|source)|((_id)$)/,
      /^(\d|object|attribute|event|group|source)/,
    );
    const valueLowerCase = defaultValue.toLowerCase();
    if (
      typeof value === 'undefined' ||
      value === null ||
      typeof defaultValue === 'undefined' ||
      defaultValue === null
    ) {
      isValidate = false;
    } else if (defaultValue === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      defaultValue.length > 0 &&
      defaultValue.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.spaceError];
    } else if (maxLength !== null && defaultValue.length > maxLength) {
      isValidate = false;
      errors = [`Invalid: maximum ${maxLength} characters`];
    } else if (regexHasBannedPrefix.test(valueLowerCase.toString())) {
      isValidate = false;
      errors = [MAP_TRANSLATE.notiObjectName];
    }
    return { errors, isValidate };
  },
  multiLangInputNewInputRule: ({ value, isRequired, maxLength }) => {
    let [isValidate, errors] = [true, []];
    const regex = new RegExp(
      /[A-Za-z_\[\]ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ]/,
      'gmi',
    );
    // Trường hợp sau ký tự đằng sau ký tự đầu tiên
    // [A-Za-z0-9,'-.!?():;\\"-\[\]@_+=ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ ]
    const defaultValue = getDefaultVal(value);
    const firstChar = safeParse(defaultValue, '')[0];
    if (
      typeof value === 'undefined' ||
      value === null ||
      typeof defaultValue === 'undefined' ||
      defaultValue === null
    ) {
      isValidate = false;
    } else if (defaultValue === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidNameEmpty];
    } else if (
      defaultValue.length > 0 &&
      defaultValue.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidRuleName];
    } else if (maxLength !== null && defaultValue.length > maxLength) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidNameMaxLength];
    } else if (!regex.test(firstChar.toString())) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidRuleName];
    }
    return { errors, isValidate };
  },
  singleLineAddPersonalize: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  // attributeCode: ({ value, isRequired, maxLength, isCheckObjectId }) => {
  //   let [isValidate, errors] = [true, []];
  //   if (value.trim() === '' && isRequired) {
  //     isValidate = false;
  //     errors = [MAP_TRANSLATE.nameEmpty];
  //   } else if (!validateAttributePrefix(value)) {
  //     isValidate = false;
  //     errors = [MAP_TRANSLATE.notiPrefixPresvered];
  //   } else if (!validateWithSpaceAndSpecialChar(value)) {
  //     isValidate = false;
  //     errors = ['No special characters and blank spaces at item code'];
  //   } else if (maxLength !== null && value.length > maxLength) {
  //     isValidate = false;
  //     errors = [`Invalid: maximum ${maxLength} characters`];
  //   } else if (isCheckObjectId && validateObjectPrefixWithId(value)) {
  //     // Check objectId with '_id'
  //     isValidate = false;
  //     errors = [MAP_TRANSLATE.notiEndFixPresvered];
  //   }
  //   return { errors, isValidate };
  // },
  attributeCode: ({ value, isRequired, maxLength, isCheckObjectId }) => {
    const regex = new RegExp(/^[a-z_][a-z0-9_]*/, 'gmi');
    // const regexFirstChar = new RegExp(/[a-z_]/);
    const regexAllowLowerChar = new RegExp(/^[a-z0-9_\-]+$/);
    const regexHasBannedPrefix = new RegExp(/^(\d|aud_|sgmt_)/);
    // const firstChar = safeParse(value, '')[0];

    let [isValidate, errors] = [true, []];

    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidCodeEmpty];
    } else if (!validateWithSpaceAndSpecialChar(value)) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidRuleCode];
    } else if (maxLength !== null && value.length > maxLength) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidCodeMaxLength];
    } else if (!regexAllowLowerChar.test(value)) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidStartCode];
    } else if (!regex.test(value.toString())) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidRuleCode];
      // } else if (isCheckObjectId && regexHasBannedPrefix.test(value.toString())) {
      //   isValidate = false;
      //   errors = [MAP_TRANSLATE.invalidRuleFormat];
      // }
    } else if (regexHasBannedPrefix.test(value.toString())) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidRuleFormat];
    }
    return { errors, isValidate };
  },

  // using for event internal name need validate prefiex *_id, *_sgmt_, *_aud_
  eventAttributeCode: ({
    value,
    isRequired,
    maxLength,
    ignoreValidatePrefix = false,
  }) => {
    const regex = new RegExp(/^[a-z_][a-z0-9_]*/, 'gmi');
    const regexFirstChar = new RegExp(/[a-z_]/);
    const regexHasBannedPrefix = new RegExp(/^(\d|aud_|sgmt_)|((_id)$)/);
    const firstChar = safeParse(value, '')[0];

    let [isValidate, errors] = [true, []];

    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidCodeEmpty];
    } else if (!validateWithSpaceAndSpecialChar(value)) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidRuleCode];
    } else if (maxLength !== null && value.length > maxLength) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidCodeMaxLength];
    } else if (!regexFirstChar.test(firstChar.toString())) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidStartCode];
    } else if (!regex.test(value.toString())) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidRuleCode];
    } else if (
      !ignoreValidatePrefix &&
      regexHasBannedPrefix.test(value.toString())
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidRuleFormatEvt];
    }
    return { errors, isValidate };
  },
  // code: ({ value, isRequired, maxLength }) => {
  //   let [isValidate, errors] = [true, []];
  //   if (value.trim() === '' && isRequired) {
  //     isValidate = false;
  //     errors = [MAP_TRANSLATE.nameEmpty];
  //   } else if (!validateWithSpaceAndSpecialChar(value)) {
  //     isValidate = false;
  //     errors = ['No special characters and blank spaces at item code'];
  //   } else if (maxLength !== null && value.length > maxLength) {
  //     isValidate = false;
  //     errors = [`Invalid: maximum ${maxLength} characters`];
  //   }
  //   return { errors, isValidate };
  // },

  code: ({ value, isRequired, maxLength }) => {
    const regex = new RegExp(/^[a-z_][a-z0-9_]*/, 'gmi');
    const regexFirstChar = new RegExp(/[a-z_]/);
    // const regexHasBannedPrefix = new RegExp(/^(\d|aud_|sgmt_)|((_id)$)/);
    const firstChar = safeParse(value, '')[0];

    let [isValidate, errors] = [true, []];

    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidCodeEmpty];
    } else if (!validateWithSpaceAndSpecialChar(value)) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidRuleCode];
    } else if (maxLength !== null && value.length > maxLength) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidCodeMaxLength];
    } else if (!regexFirstChar.test(firstChar.toString())) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidStartCode];
    } else if (!regex.test(value.toString())) {
      isValidate = false;
      errors = [MAP_TRANSLATE.invalidRuleCode];
    }
    return { errors, isValidate };
  },

  // codeV2: ({ value, isRequired, maxLength }) => {
  //   const regex = new RegExp(/^[a-z_][a-z0-9_]*/, 'gmi');
  //   const regexFirstChar = new RegExp(/[a-z_]/);
  //   const regexHasBannedPrefix = new RegExp(/^(\d|aud_|sgmt_)|((_id)$)/);
  //   const firstChar = safeParse(value, '')[0];

  //   let [isValidate, errors] = [true, []];

  //   if (value.trim() === '' && isRequired) {
  //     isValidate = false;
  //     errors = [MAP_TRANSLATE.invalidCodeEmpty];
  //   } else if (!validateWithSpaceAndSpecialChar(value)) {
  //     isValidate = false;
  //     errors = [MAP_TRANSLATE.invalidRuleCode];
  //   } else if (maxLength !== null && value.length > maxLength) {
  //     isValidate = false;
  //     errors = [MAP_TRANSLATE.invalidCodeMaxLength];
  //   } else if (!regexFirstChar.test(firstChar.toString())) {
  //     isValidate = false;
  //     errors = [MAP_TRANSLATE.invalidStartCode];
  //   } else if (!regex.test(value.toString())) {
  //     isValidate = false;
  //     errors = [MAP_TRANSLATE.invalidRuleCode];
  //   } else if (regexHasBannedPrefix.test(value.toString())) {
  //     isValidate = false;
  //     errors = [MAP_TRANSLATE.invalidRuleFormat];
  //   }
  //   return { errors, isValidate };
  // },
  dropZone: ({ linkImage, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (linkImage.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  source: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (!Array.isArray(value)) {
      isValidate = false;
      errors = [];
    } else if (isRequired && value.length === 0) {
      isValidate = false;
      errors = [MAP_TRANSLATE.cannotEmpty];
    }
    return { errors, isValidate };
  },
  formula: ({ value = '', mapFields = {} }) => {
    const validateContent = value || '';
    let [isValidate, errors] = [true, []];
    if (validateContent.length === 0) {
      isValidate = false;
      errors = [MAP_TRANSLATE.notFormularCantEmpty];
    } else {
      const regular = /{{.*?}}/gm;
      const subStringsMatch = validateContent.match(regular);

      if (subStringsMatch && subStringsMatch.length > 0) {
        subStringsMatch.forEach(ele => {
          if (!mapFields[ele.replace(/{|}/gim, '')]) {
            isValidate = false;
            errors = [`Attribute code ${ele} not found.`];
          }
        });
      }
    }

    return { errors, isValidate };
  },
  displayFormat: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (isRequired && _isEmpty(value.type)) {
      isValidate = false;
    }
    return { errors, isValidate };
  },
  computationSchedule: (
    { value, isRequired, design },
    dataConfig,
    activeRow = {},
  ) => {
    // use errors format of segment + AM
    let [isValidate, errors] = [true, {}];

    const cacheComputeSchedule = activeRow.compute_schedule;
    if (
      (design === 'create' && value.type === 'dynamic') ||
      (design === 'update' &&
        cacheComputeSchedule &&
        value.type === 'dynamic' &&
        !_isEqual(cacheComputeSchedule, value))
    ) {
      const modalCalendarTable = getObjectPropSafely(
        () => dataConfig['modalCalendarTable'].value,
        {},
      );
      const {
        dataEventsGroupByDate,
        timeRange,
        limitHour,
      } = modalCalendarTable;

      // rebuild disableHours for current settings
      const disableTimes = buildDisableHours({
        computeSchedule: value,
        dataEventsGroupByDate,
        timeRange,
        limitHour,
      });

      const scheduleValidate = validateComputeScheduleInHour({
        computeSchedule: value,
        disableTimes,
        timeRange,
      });

      if (!scheduleValidate) {
        isValidate = false;
        errors.limit_schedule = [MAP_TRANSLATE.invalidLimitSchedule];
      }
    }

    return { errors, isValidate };
  },
};

const styleError = {
  height: '1.5rem',
  minWidth: '1.75rem',
};

const styleEventDropDownAPI = { height: '45px' };

export const Template1 = React.forwardRef((props, ref) => {
  return (
    <>
      {props.isHiddenLabel || (
        <Grid
          item
          sm={props.isJourneyTemplateMode ? 2 : props.xsTitle || 3}
          style={{ ...(props.styleLeftContent || {}) }}
        >
          <Title
            textAlign={props.textAlign}
            className={props.classNameTitle}
            ref={ref}
            style={{ color: props.color || '', ...props.styleTitle }}
            isViewMode={props.isViewMode}
          >
            {props.starFirst && (
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` * `}
              </span>
            )}
            {props.label}
            {!props.starFirst && (
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && ` *`}
              </span>
            )}
          </Title>
          {!!props.errors[0] && <div style={styleError} />}
        </Grid>
      )}
      <Grid
        item
        sm={props.girdItemSize || 9}
        style={{
          width: props.rightContentWidth || 'auto',
          position: 'relative',
          ...(props.styleRightContent || {}),
        }}
      >
        {props.children}
      </Grid>
    </>
  );
});

export const Template2 = props => {
  // flex START
  return (
    <>
      {props.isHiddenLabel || (
        <Grid item sm={3} style={{ ...(props.styleLeftContent || {}) }}>
          <WrapperCenterFlexStart>
            <Title
              className={props.classNameTitle}
              isViewMode={props.isViewMode}
            >
              {props.label}
              {props.isRequired && `*`}
            </Title>
            {!!props.errors[0] && <div style={styleError} />}
          </WrapperCenterFlexStart>
        </Grid>
      )}
      <Grid item sm={props.girdItemSize || 9}>
        {props.children}
      </Grid>
    </>
  );
};

export const Template3 = props => {
  // flex START
  return (
    <Grid
      container
      xs={12}
      style={{
        ...(props.styleBlock || {}),
      }}
    >
      <Grid item sm={props.xsTitle || 2}>
        {props.isShowLabel && (
          <Title isViewMode={props.isViewMode}>
            {props.label}
            {props.isRequired && `*`}
          </Title>
        )}
      </Grid>
      <Grid item sm={props.isGridContentRight || 9}>
        {props.children}
      </Grid>
    </Grid>
  );
};

export const Template4 = props => {
  // flex START
  return (
    <>
      <Grid style={{ ...(props.styleLeftContent || {}) }} item sm={12}>
        <WrapperCenterFlexStart>
          <Title className={props.classNameTitle}>
            {props.label}
            {props.isRequired &&
              (props.isRedStar ? (
                <span style={{ color: 'red' }}> *</span>
              ) : (
                `*`
              ))}
          </Title>
          {!!props.errors[0]}
        </WrapperCenterFlexStart>
      </Grid>
      <Grid style={{ ...(props.styleRightContent || {}) }} item sm={12}>
        {props.children}
      </Grid>
    </>
  );
};

export const Template5 = props => {
  // flex END
  return (
    <>
      <Grid item>{props.children}</Grid>
      {props.isHiddenLabel || (
        <Grid item sm={9}>
          <WrapperCenterFlexEnd>
            <Title className={props.classNameTitle}>
              {props.label}
              {props.isRequired && `*`}
            </Title>
            {!!props.errors[0] && <div style={styleError} />}
          </WrapperCenterFlexEnd>
        </Grid>
      )}
    </>
  );
};

export const Template6 = props => {
  // Nested components with labels
  return (
    <>
      <Grid item sm={props.xsTitle || 3} />
      <Grid
        item
        sm={props.girdItemSize || 9}
        style={{ width: props.rightContentWidth || 'auto' }}
      >
        <Grid item sm={9}>
          <WrapperFlexGapRow gap={40}>
            {props.isHiddenLabel || (
              <Grid item sm={3}>
                <Title>
                  {props.description}
                  <span style={{ color: '#ff0000' }}>
                    {props.isRequired && ` *`}
                  </span>
                </Title>
              </Grid>
            )}
            <Grid item sm={6}>
              {props.children}
            </Grid>
          </WrapperFlexGapRow>
        </Grid>
        <Grid item sm={3} />
      </Grid>
    </>
  );
};
export const Template7 = props => {
  // flex START
  return (
    <Grid container xs={12}>
      <Grid item sm={9}>
        {props.children}
      </Grid>
    </Grid>
  );
};
export const MAP_INPUT_TYPE = {
  link: props => {
    const style = {
      display: 'block',
      lineHeight: '26px',
      fontSize: '12px',
      width: 'fit-content',
    };

    return (
      <Template1 {...props}>
        <Link style={style} href={props.value} target="_blank" underline="none">
          {props.linkText || 'Link'}
        </Link>

        {props.helperText}
      </Template1>
    );
  },
  singleLineText: props => {
    return (
      <>
        {props.useTemplate === 'lineBreaks' ? (
          <Template4 {...props}>
            <InputPreview
              type="input"
              isViewMode={props.isViewMode}
              value={props.value}
              inputWrapperStyle={props?.inputWrapperStyle}
            >
              <UITextField
                componentKey={props.componentKey}
                id={props.name}
                value={props.value}
                onChange={props.onChange(props.name)}
                placeholder={props.placeholder}
                firstText={props.errors[0]}
                textFieldProps={{
                  disabled: props.disabled,
                  size: 'small',
                  multiline: false,
                  rowsMax: 1,
                  className: props.singleTextWidth ? 'width-430' : 'width-100',
                  error: !!props.errors[0],
                }}
              />

              {props.helperText}
            </InputPreview>
          </Template4>
        ) : (
          <Template1 {...props}>
            <InputPreview
              type="input"
              isViewMode={props.isViewMode}
              value={props.value}
              errors={props.errors}
              name={props.name}
              inputWrapperStyle={props?.inputWrapperStyle}
            >
              <UITextField
                componentKey={props.componentKey}
                id={props.name}
                value={props.value}
                placeholder={props.placeholder}
                onChange={props.onChange(props.name)}
                firstText={props.errors[0]}
                textFieldProps={{
                  disabled: props.disabled,
                  size: 'small',
                  multiline: false,
                  rowsMax: 1,
                  className: props.singleTextWidth ? 'width-430' : 'width-100',
                  width: props.singleTextWidth,
                  error: !!props.errors[0],
                  onKeyDown: props.onKeyDown,
                  onMouseDown: props.onMouseDown,
                  id: props.name,
                }}
              />
              {/* "." in filed cookie doamin source */}
              {props.name === 'cookieDomain' && (
                <span
                  style={{
                    left: '2px',
                    position: 'absolute',
                    top: '5px',
                  }}
                >
                  .
                </span>
              )}
              {props.helperText}
            </InputPreview>
          </Template1>
        )}
      </>
    );
  },
  singleLineTextWithPrefixAndSuffix: props => {
    return (
      <Grid item sm={props.grid || 12}>
        <Flex align="center" gap={10}>
          <Span
            style={{
              width: 'fit-content',
            }}
          >
            {props.prefix}
          </Span>
          {props.isViewMode ? (
            <span style={{ fontSize: 12, color: '#000000', margin: '0px 2px' }}>
              {props.value}
            </span>
          ) : (
            <UINumberStyled
              width={props.width || 50}
              key={props.componentKey}
              id={props.name}
              value={props.value}
              onChange={val => {
                props.onChange(props.name)(val);
              }}
              onBlur={e => {
                props.onChangeBlur(props.name)(e.target.value);
              }}
              min={props.min}
              max={props.max}
            />
          )
          /* <InputNumber
                            style={{
                              width: '50px',
                              margin: '0px 10px 0px 10px',
                            }}
                            key={props.componentKey}
                            id={props.name}
                            value={props.value}
                            onChange={val => {
                              props.onChange(props.name)(val);
                            }}
                            onBlur={e => {
                              props.onChangeBlur(props.name)(e.target.value);
                            }}
                            min={props.min}
                            max={props.max}
                            type="number"
                            inputProps={{
                              min: props.min,
                              max: props.max,
                              // step: '1',
                              // pattern: '[0-9]*',
                            }} // inputProps={{
                            //   min: 0,
                            //   max: 31,
                            // }}
                            // textFieldProps={{
                            //   disabled: props.disabled,
                            //   size: 'small',
                            //   multiline: false,
                            //   rowsMax: 1,
                            //   className: 'width-100',
                            //   error: !!props.errors[0],
                            // }}
                          /> */
          }
          <Span
            style={{
              width: 'fit-content',
            }}
          >
            {props.suffix}
          </Span>
        </Flex>
      </Grid>
    );
  },
  password: props => {
    return (
      <Template1 {...props}>
        <UIPassword
          componentKey={props.componentKey}
          disabled
          id={props.name}
          value={props.value}
          onChange={props.onChange(props.name)}
          firstText={props.errors[0]}
          textFieldProps={{
            disabled: props.disabled,
            type: 'password',
            size: 'small',
            multiline: false,
            rowsMax: 1,
            className: 'width-100',
            error: !!props.errors[0],
          }}
        />
      </Template1>
    );
  },
  imageUrl: props => {
    // Constants
    const userId = getCurrentUserId();
    const token = getToken();

    return (
      <Template1 {...props}>
        <UploadImage
          isInputMode
          domainMedia={
            isProduction() ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX
          }
          slug="api/v1"
          paramConfigs={{
            token,
            userId,
            accountId: userId,
          }}
          width="100%"
          selectedImage={{
            url: props.value,
          }}
          onChangeImage={image => {
            props.onChange(props.name)((image && image.url) || '');
          }}
          onRemoveImage={() => {
            props.onChange(props.name)('');
          }}
        />
        {/* <UIModalInsert
          componentKey={props.componentKey}
          value={props.value}
          initData={props.initValue}
          onChange={props.onChange(props.name)}
          errors={props.errors}
        /> */}
      </Template1>
    );
  },
  selectDropdown: props => {
    const { isShow = true } = props;
    if (!isShow) {
      return null;
    }
    return (
      <>
        {props.useTemplate === 'lineBreaks' ? (
          <Template4 {...props}>
            <InputPreview
              isViewMode={props.isViewMode}
              type="input"
              value={
                typeof props.value !== 'object'
                  ? props.value
                  : props.value && props.value.label
              }
              inputWrapperStyle={props.inputWrapperStyle}
            >
              <UISelect
                onlyParent
                use="tree"
                isSearchable
                placeholder={MAP_TRANSLATE.selectAnItem}
                placeholderTranslateCode={TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM}
                fullWidthPopover
                {...props}
                label=""
                options={props.options}
                value={props.value}
                disabled={props.disabled}
                onChange={props.onChange(props.name)}
                selectWidth={props.selectWidth}
              />
            </InputPreview>
            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
            >
              {props.errors}
            </FormHelperText>
          </Template4>
        ) : (
          <Template1 {...props}>
            <InputPreview
              isViewMode={props.isViewMode}
              type="input"
              value={
                typeof props.value !== 'object'
                  ? props.value
                  : props.value && props.value.label
              }
              inputWrapperStyle={props.inputWrapperStyle}
            >
              <UISelect
                onlyParent
                use="tree"
                isSearchable
                placeholder={MAP_TRANSLATE.selectAnItem}
                placeholderTranslateCode={TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM}
                fullWidthPopover
                {...props}
                options={props.options}
                value={props.value}
                label=""
                disabled={props.disabled}
                onChange={props.onChange(props.name)}
                selectWidth={props.selectWidth}
                {...(props.name === 'layout' ? { labelWidth: '50%' } : {})}
              />
            </InputPreview>
            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
            >
              {props.errors}
            </FormHelperText>
          </Template1>
        )}
      </>
    );
  },
  inputFormular: props => {
    return (
      <>
        {props.useTemplate === 'lineBreaks' ? (
          <Template4 {...props}>
            <Formular
              onlyParent
              use="tree"
              isSearchable
              placeholder={MAP_TRANSLATE.selectAnItem}
              placeholderTranslateCode={TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM}
              fullWidthPopover
              {...props}
              options={props.options}
              value={props.value}
              disabled={props.disabled}
              onChange={props.onChange(props.name)}
              customWidth={props.customWidth}
            />
            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
            >
              {props.errors}
            </FormHelperText>
          </Template4>
        ) : (
          <Template1 {...props}>
            <Formular
              onlyParent
              use="tree"
              isSearchable
              placeholder={MAP_TRANSLATE.selectAnItem}
              placeholderTranslateCode={TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM}
              fullWidthPopover
              {...props}
              options={props.options}
              value={props.value}
              disabled={props.disabled}
              onChange={props.onChange(props.name)}
            />
            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
            >
              {props.errors}
            </FormHelperText>
          </Template1>
        )}
      </>
    );
  },
  selectDropdownCustomWidthGrid: props => {
    return (
      <>
        <Grid item xs={props.xsTitle || 3} className="customWidth--grid">
          <TitleCustomStyle>{props.label}</TitleCustomStyle>
        </Grid>

        <Grid item xs={props.xsContent || 8}>
          <UISelect
            onlyParent
            // label={props.label}
            isViewMode={props.isViewMode}
            use="tree"
            isSearchable
            placeholder={MAP_TRANSLATE.selectAnItem}
            placeholderTranslateCode={TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM}
            fullWidthPopover
            {...props}
            options={props.options}
            value={props.value}
            disabled={props.disabled}
            onChange={props.onChange(props.name)}
          />
          <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
            {props.errors}
          </FormHelperText>
        </Grid>
      </>
    );
  },
  drawerChooseReport: props => {
    const handleToggleChooseReport = () => {
      props.callback('ON_OPEN_CHOOSE_REPORT');
    };
    const isHasValue = Object.keys(props.value || {}).length !== 0;
    return (
      <>
        <Grid
          item
          xs={props.xsTitle || 3}
          style={{ ...(props.styleLeftContent || {}) }}
        >
          <TitleCustomStyle
            style={{ fontSize: 12, color: '#000', paddingTop: 'unset' }}
          >
            {props.label}
          </TitleCustomStyle>
        </Grid>

        <Grid item xs={props.xsContent || 9}>
          {isHasValue ? (
            <>
              <TemplatePreview
                handleToggleChooseReport={handleToggleChooseReport}
                template={props.value}
                readOnly
              />

              {/* <UIButton
                style={{ margin: '12.5px 0', marginBottom: 0 }}
                fs="12px"
                reverse
                iconName="edit"
                theme="text-link"
                onClick={handleToggleChooseReport}
              >
                Change report
              </UIButton> */}
            </>
          ) : (
            // <UIButton theme="outline" onClick={handleToggleChooseReport}>
            //   Choose Reports
            // </UIButton>
            <ChooseReport onClick={handleToggleChooseReport} />
          )}
          <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
            {props.errors}
          </FormHelperText>
        </Grid>
      </>
    );
  },
  radioGroup: props => {
    const {
      fontSize = '14px',
      sizeIcon = 'small',
      isInline = false,
      isViewMode = false,
    } = props;
    return (
      <>
        {props.useTemplate === 'lineBreaks' ? (
          <Template4 {...props}>
            <RadioGroup
              row
              aria-label="gender"
              name="row-radio-buttons-group"
              value={props.value}
              {...props.radioGroupProps}
            >
              {props.options.map(each => {
                return (
                  <FormControlLabel
                    value={each.value}
                    control={
                      <Radio
                        color="primary"
                        checked={props.value === each.value}
                        disabled={
                          each.disabled || props.disabled || isViewMode || false
                        }
                        size="small"
                        disableRipple
                        disableFocusRipple
                        disableTouchRipple
                        {...props.radioProps}
                      />
                    }
                    label={
                      <span style={{ fontSize: '14px', ...props.labelStyle }}>
                        {each.label}
                      </span>
                    }
                    onChange={e => props.onChange(props.name)(e.target.value)}
                  />
                );
              })}
            </RadioGroup>
            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
            >
              {props.errors}
            </FormHelperText>
          </Template4>
        ) : (
          <>
            <Template1 {...props}>
              <RadioGroup
                aria-label={props.label}
                name="radio-buttons-group"
                value={props.value}
                onChange={e => props.onChange(props.name)(e.target.value)}
                row={isInline}
                defaultValue={props?.value}
                {...props.radioGroupProps}
              >
                {props.options.map(each => (
                  <FormControlLabel
                    value={each.value}
                    control={
                      <Radio
                        color="primary"
                        disabled={
                          each.disabled || props.disabled || isViewMode || false
                        }
                        size={sizeIcon}
                        {...props.radioProps}
                      />
                    }
                    label={
                      <span style={{ fontSize, ...(props.labelStyle || {}) }}>
                        {each.label}
                      </span>
                    }
                  />
                ))}
              </RadioGroup>
              <FormHelperText
                id="component-helper-text"
                error={!!props.errors[0]}
              >
                {props.errors}
              </FormHelperText>
            </Template1>
          </>
        )}
      </>
    );
  },
  attribute: props => {
    const newValue = props.value;
    newValue.label = newValue.eventPropertyDisplay
      ? newValue.eventPropertyDisplay
      : newValue.label;
    return (
      <Template1 {...props}>
        <UISelect
          onlyParent
          use="tree"
          isSearchable
          placeholder={MAP_TRANSLATE.selectAnItem}
          placeholderTranslateCode={TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM}
          fullWidthPopover
          {...props}
          label=""
          options={props.options}
          value={newValue}
          disabled={props.disabled}
          onChange={props.onChange(props.name)}
        />
        {props.errors[0] ? (
          <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
            {props.errors}
          </FormHelperText>
        ) : (
          <AttributeError item={props.value} />
        )}
      </Template1>
    );
  },
  selectDropdownAPI: props => {
    return (
      <Template1 {...props}>
        <InputPreview
          isViewMode={
            props.isViewMode &&
            (props.isJourneyTemplateMode ? props.mode === 'use' : true)
          }
          type="input"
          value={
            typeof props.value !== 'object'
              ? props.value
              : props.value && props.value.lable
          }
        >
          <div
            style={{
              ...(props.isJourneyTemplateMode ? { paddingTop: 8 } : {}),
              ...styleEventDropDownAPI,
            }}
          >
            <UISelectWithAPI
              placeholder={MAP_TRANSLATE.selectAnItem}
              placeholderTranslateCode={TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM}
              {...props}
              options={props.options}
              value={props.value}
              initData={props.initValue}
              disabled={props.disabled}
              onChange={props.onChange(props.name)}
            />
          </div>
        </InputPreview>
      </Template1>
    );
  },
  multiLineText: props => {
    return (
      <Template2 {...props}>
        <WrapperDisable disabled={props.disabled}>
          <TextareaAutosize
            componentKey={props.componentKey}
            aria-label="minimum height"
            rowsMin={3}
            placeholder=""
            value={props.value}
            onChange={evt => props.onChange(props.name)(evt.target.value)}
            style={{ width: '100%' }}
          />
        </WrapperDisable>

        <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
          {props.errors}
        </FormHelperText>
      </Template2>
    );
  },
  editor: props => {
    return (
      <Template2 {...props}>
        <WrapperDisable disabled={props.disabled}>
          <TinymceEditor
            {...props}
            initData={props.initValue}
            onChange={props.onChange(props.name)}
            typeComponent="editor"
            // showPopupEditorHTML={false}
            showPopupEditorHTML
            showPersonalization
          />
        </WrapperDisable>
        <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
          {props.errors}
        </FormHelperText>
      </Template2>
    );
  },
  keyvalue: props => {
    return (
      <Template2 {...props}>
        <WrapperDisable disabled={props.disabled}>
          <PairKeyValue
            {...props}
            global={{ ...props }}
            errors={props.errors}
            isRequired={props.isRequired}
            initData={props.initValue}
            onChange={value => props.onChange(props.name)(value)}
          />
        </WrapperDisable>
      </Template2>
    );
  },
  checkbox: props => {
    const { girdLabel = 3, girdContent = 9, girdItemSize, xsTitle } = props;
    return (
      <>
        <Grid
          item
          sm={xsTitle || girdLabel}
          style={{ ...(props.styleLeftContent || {}) }}
        />
        <Grid item sm={girdItemSize || girdContent}>
          <WrapperDisable disabled={props.disabled && !props.isViewMode}>
            <CheckboxAntsomiUI
              color="primary"
              size="small"
              style={{ padding: '3px', margin: '-3px' }}
              name={props.name}
              disabled={props.disabled}
              checked={!!props.value}
              onChange={value => {
                props.onChange(props.name)(value.target.checked);
              }}
            >
              {props.label}
            </CheckboxAntsomiUI>
            {/* <UICheckbox
              componentKey={props.componentKey}
              name={props.name}
              checked={!!props.value}
              onChange={value => {
                console.log('value', value);
                props.onChange(props.name)(value);
              }}
              label={props.label}
              disabled={props.isViewMode}
            >
            </UICheckbox> */}
          </WrapperDisable>
        </Grid>
      </>
    );
  },
  checkboxCustomLabelTwoLevel: props => {
    return (
      props.isShow && (
        <>
          <Grid item sm={props.xsTitle || 3}>
            <Title className={props.classNameTitle}>
              {props.labelRoot}
              {props.isRequired && `*`}
            </Title>
          </Grid>
          <Grid item sm={props.girdItemSize || 9}>
            <WrapperDisable disabled={props.disabled}>
              <CheckboxAntsomiUI
                componentKey={props.componentKey}
                name={props.name}
                checked={!!props.value}
                onChange={value => {
                  props.onChange(props.name)(value.target.checked);
                }}
                disabled={props.isViewMode}
              >
                {props.label}
              </CheckboxAntsomiUI>
              {/* <span>{props.label}</span> */}
            </WrapperDisable>
          </Grid>
        </>
      )
    );
  },
  checkboxGird: props => {
    if (props.isViewMode) {
      return (
        <Grid
          item
          sm={props.grid}
          style={{ display: 'flex', alignItems: 'center', marginRight: '10px' }}
        >
          <CirclePoint />
        </Grid>
      );
    }

    return (
      <Grid item sm={props.grid}>
        {/* <WrapperDisable disabled={props.disabled}> */}
        {props.label ? (
          <FormControlLabel
            label={props.label}
            control={
              <Checkbox
                color="primary"
                size="small"
                style={{ padding: '3px', margin: '-3px' }}
                name={props.name}
                disabled={props.disabled}
                checked={!!props.value}
                onChange={value =>
                  props.onChange(props.name)(value.target.checked)
                }
              />
            }
          />
        ) : (
          <Checkbox
            color="primary"
            size="small"
            style={{ padding: '3px', margin: '-3px' }}
            name={props.name}
            disabled={props.disabled}
            checked={!!props.value}
            onChange={value => props.onChange(props.name)(value.target.checked)}
          />
        )}

        {/* <UICheckbox
            componentKey={props.componentKey}
            name={props.name}
            checked={!!props.value}
            onChange={value => props.onChange(props.name)(value)}
            label={props.label}
          >
          </UICheckbox> */}
        {/* </WrapperDisable> */}
      </Grid>
    );
  },
  checkboxType2: props => {
    return (
      <>
        {props.useTemplate === 'lineBreaks' ? (
          // <Template4 {...props}>
          <WrapperDisable disabled={props.disabled}>
            <div className="d-flex align-items-center">
              <Checkbox
                componentKey={props.componentKey}
                name={props.name}
                checked={!!props.value}
                onChange={e => props.onChange(props.name)(e.target.checked)}
                margin="0 5px 0 0"
                // label={props.description}
              />
              <span
                style={{
                  fontSize: props.fontSize || '14px',
                  fontWeight: '400',
                }}
              >
                {props.description}
              </span>
            </div>
          </WrapperDisable>
        ) : (
          // </Template4>
          <Template1 {...props}>
            <WrapperDisable disabled={props.disabled}>
              <div className="d-flex align-items-center">
                <Checkbox
                  componentKey={props.componentKey}
                  name={props.name}
                  checked={!!props.value}
                  onChange={e => props.onChange(props.name)(e.target.checked)}
                  margin="0 5px 0 0"
                  // label={props.description}
                />
              </div>
              <span
                style={{
                  fontSize: props.fontSize || '14px',
                  fontWeight: '400',
                }}
              >
                {props.description}
              </span>
            </WrapperDisable>
          </Template1>
        )}
      </>
    );
  },

  checkboxType3: props => {
    return (
      <>
        {props.useTemplate === 'lineBreaks' ? (
          // <Template4 {...props}>
          <WrapperDisable disabled={props.disabled}>
            <UICheckbox
              componentKey={props.componentKey}
              name={props.name}
              checked={!!props.value}
              onChange={value => props.onChange(props.name)(value)}
            >
              <span style={{ fontSize: '14px', fontWeight: '400' }}>
                {/* {props.value === true ? 'Enable' : ''} */}
                Enable
              </span>
            </UICheckbox>
          </WrapperDisable>
        ) : (
          // </Template4>
          <Template1 {...props}>
            <WrapperDisable disabled={props.disabled}>
              <UICheckbox
                componentKey={props.componentKey}
                name={props.name}
                checked={!!props.value}
                onChange={value => props.onChange(props.name)(value)}
                // label={props.description}
              >
                <span style={{ fontSize: '14px', fontWeight: '400' }}>
                  Enable
                </span>
              </UICheckbox>
            </WrapperDisable>
          </Template1>
        )}
      </>
    );
  },

  htmlEditor: props => {
    return (
      <Template2 {...props}>
        <WrapperDisable disabled={props.disabled}>
          <TinymceEditor
            {...props}
            initData={props.initValue}
            onChange={props.onChange(props.name)}
            typeComponent="htmlEditor"
            showPersonalization
            showPopupEditorHTML
          />
        </WrapperDisable>
        <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
          {props.errors}
        </FormHelperText>
      </Template2>
    );
  },
  multiLangInput: props => {
    const { initValue = {} } = props;
    return (
      <Template1 {...props}>
        <InputLanguage
          componentKey={props.componentKey}
          disabled={props.disabled}
          initData={isObject(initValue) ? initValue : JSON.parse(initValue)}
          value={props.value}
          onChange={props.onChange(props.name)}
          placeholder={props.placeholder}
          errors={props.errors}
          isViewMode={props.isViewMode}
          inputWrapperStyle={props?.inputWrapperStyle}
          wrapperType={props.wrapperType}
        />
      </Template1>
    );
  },
  subTextField: props => {
    return (
      props.isShow && (
        <Template6 {...props}>
          <UITextField
            componentKey={props.componentKey}
            id={props.name}
            value={props.value}
            onChange={props.onChange(props.name)}
            firstText={props.errors[0]}
            textFieldProps={{
              disabled: props.disabled || props.isReadonly,
              size: 'small',
              multiline: false,
              rowsMax: 1,
              className: 'width-100',
              width: props.singleTextWidth,
              error: !!props.errors[0],
            }}
            // inputProps={{
            //   readOnly: props.isReadonly,
            // }}
          />
        </Template6>
      )
    );
  },
  subSelect: props => {
    const optionValue = props.value.value || props.value;
    const optionLabel = props.options.find(item => item.value === optionValue)
      .label;
    return (
      props.isShow && (
        <Template6 {...props}>
          <InputPreview
            isViewMode={props.isViewMode}
            type="input"
            value={
              typeof props.value !== 'object'
                ? props.value
                : props.value.value && props.value.lable
            }
          >
            <UISelect
              onlyParent
              use="tree"
              isSearchable
              placeholder={MAP_TRANSLATE.selectAnItem}
              placeholderTranslateCode={TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM}
              fullWidthPopover
              {...props}
              options={props.options}
              value={{
                label: optionLabel,
                value: optionValue,
              }}
              label=""
              disabled={props.disabled}
              onChange={props.onChange(props.name)}
              selectWidth={props.selectWidth}
              {...(props.name === 'layout' ? { labelWidth: '50%' } : {})}
            />
          </InputPreview>
          <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
            {props.errors}
          </FormHelperText>
        </Template6>
      )
    );
  },
  number: props => {
    return (
      <Template2 {...props}>
        <WrapperDisable disabled={props.disabled}>
          <InputPreview
            isViewMode={props.isViewMode}
            value={props.value}
            type="input"
          >
            <UINumber
              componentKey={props.componentKey}
              type="number"
              onChange={props.onChange(props.name)}
              value={props.value}
              min={props.minLength}
              max={props.maxLength}
              width={props.width || '4rem'}
            />
          </InputPreview>
        </WrapperDisable>
        <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
          {props.errors}
        </FormHelperText>
      </Template2>
    );
  },
  singleLineAddPersonalize: props => {
    return (
      <Template1 {...props}>
        <WrapperDisable disabled={props.disabled}>
          <TinymceEditor
            {...props}
            typeComponent="input"
            onChange={props.onChange(props.name)}
            initData={props.initValue}
          />
        </WrapperDisable>
      </Template1>
    );
  },
  dropZone: props => {
    return (
      <Template1 {...props}>
        <WrapperDisable disabled={props.disabled && !props.isViewMode}>
          {props.options && props.options.length > 0 ? (
            <RadioGroup row aria-label="gender" name="row-radio-buttons-group">
              {props.options.map(each => {
                if (props.isViewMode) {
                  if (props.valueType === each.value) {
                    return (
                      <WrapperFlex>
                        <CirclePoint />
                        <span
                          style={{
                            fontSize: '12px',
                          }}
                        >
                          {each.label}
                        </span>
                      </WrapperFlex>
                    );
                  }

                  return null;
                }

                return (
                  <FormControlLabel
                    value={each.value}
                    control={
                      <Radio
                        color="primary"
                        checked={props.valueType === each.value}
                        disabled={props.disabled || false}
                      />
                    }
                    label={
                      <span style={{ fontSize: '12px' }}>{each.label}</span>
                    }
                    onChange={e =>
                      props.onChange(props.nameRadio)(e.target.value)
                    }
                  />
                );
              })}
              {props.valueType === 'system' ? (
                <StyledIconSelection
                  icon={props.value}
                  onChange={e => props.onChange(props.name)(e)}
                  onRemove={() => props.onChange(props.name)(null)}
                  isViewMode={props.isViewMode}
                />
              ) : (
                <DropZone {...props} callback={props.onChange(props.name)} />
              )}
            </RadioGroup>
          ) : (
            <DropZone {...props} callback={props.onChange(props.name)} />
          )}
        </WrapperDisable>
      </Template1>
    );
  },
  copyArea: props => {
    return (
      <Template1 {...props}>
        <InputGetScript body={props.value} componentKey={props.componentKey} />
      </Template1>
    );
  },
  source: props => {
    return (
      <Template1 {...props}>
        <DataSources
          {...props}
          initData={props.value}
          disabled={props.disabled}
          onChange={props.onChange(props.name)}
        />
        <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
          {props.errors}
        </FormHelperText>
      </Template1>
    );
  },
  event: props => {
    return (
      <Template1 {...props}>
        <DataEvents
          {...props}
          initData={props.initValue}
          disabled={props.disabled}
          onChange={props.onChange(props.name)}
        />
        <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
          {props.errors}
        </FormHelperText>
      </Template1>
    );
  },
  fieldFormat: props => {
    return (
      <Template1 {...props}>
        <ModalFieldFormat
          {...props}
          onChange={props.onChange(props.name)}
          initData={props.initValue}
          isViewMode={props?.isViewMode}
        />
      </Template1>
    );
  },
  displayFormat: props => {
    return (
      <Template1 {...props}>
        <ModalConfigFormat
          {...props}
          onChange={props.onChange(props.name)}
          initData={props.initValue}
        />
      </Template1>
    );
  },
  condition: props => {
    return (
      <ConditionAttributes
        {...props}
        rules={props.initValue}
        mapAttrs={props.mapOptions}
        onChange={props.onChange(props.name)}
      />
    );
  },

  formula: props => {
    const { isShow = true, girdLabel = 2, girdContent = 10 } = props;
    return (
      isShow && (
        <>
          <Grid item sm={girdLabel} style={{ ...props.styleLeftContent }}>
            <WrapperCenterFlexStart
              style={{ marginRight: props.isViewMode && '30px' }}
            >
              <Title
              // style={{
              //   marginTop: '-15px',
              // }}
              >
                {props.label}
              </Title>
            </WrapperCenterFlexStart>
          </Grid>
          <Grid
            item
            sm={girdContent}
            style={{
              ...props.styleRightContent,
              paddingBottom: props.errors.length > 0 ? '60px' : '0px',
            }}
          >
            <WrapperDisable disabled={!props.isViewMode && props.disabled}>
              <DndProvider backend={HTML5Backend}>
                <EditorFormular
                  {...props}
                  callback={(type, data) => props.onChange(props.name)(data)}
                />
              </DndProvider>
            </WrapperDisable>

            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
            >
              {props.errors}
            </FormHelperText>
          </Grid>
        </>
      )
    );
  },
  timeRange: props => {
    const { titleRef, ...rests } = props;
    return (
      <>
        {props.useTemplate === 'lineBreaks' ? (
          <Template4 {...props}>
            <WrapperDisable disabled={props.disabled}>
              <CalendarSelection
                {...props}
                initData={props.initValue}
                callback={(type, data) =>
                  props.onChange(props.name)({
                    dataCallback: props.property,
                    value: data,
                  })
                }
              />
            </WrapperDisable>
          </Template4>
        ) : (
          <Template1
            {...rests}
            classNameTitle={classnames('m-top-2', {
              'm-bottom-5': !props.isViewMode,
            })}
            ref={titleRef}
          >
            <WrapperDisable disabled={props.disabled}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginTop: '11px',
                  flexWrap: 'wrap',
                  justifyContent: props.rightContentWidth ? 'center' : 'unset',
                }}
              >
                <CalendarSelection
                  {...props}
                  initData={props.initValue}
                  maxTotalWidth={
                    props.rightContentWidth ? props.rightContentWidth - 26 : 0
                  }
                  callback={(type, data) => props.onChange(props.name)(data)}
                />
                <DisplayTimeZone>{getLabelPortalTimeZone()}</DisplayTimeZone>
              </div>
            </WrapperDisable>
          </Template1>
        )}
      </>
    );
  },
  timeRangeV2: props => {
    const { titleRef, ...rests } = props;
    return (
      <>
        <Template1
          {...rests}
          classNameTitle="m-top-2 m-bottom-5"
          ref={titleRef}
          girdItemSize={10}
        >
          <WrapperDisable disabled={props.disabled}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                marginTop: '17px',
                flexWrap: 'wrap',
                justifyContent: props.rightContentWidth ? 'center' : 'unset',
              }}
            >
              <AdvancedRangePicker
                timeRange={props.value}
                onChange={props.onChange(props.name)}
                inputStyle={{
                  width: '180px',
                }}
                isViewMode={props.isViewMode}
              />
              {/* <CalendarSelection
                {...props}
                initData={props.initValue}
                maxTotalWidth={
                  props.rightContentWidth ? props.rightContentWidth - 26 : 0
                }
                callback={(type, data) => props.onChange(props.name)(data)}
              />
              <DisplayTimeZone>{getLabelPortalTimeZone()}</DisplayTimeZone> */}
            </div>
          </WrapperDisable>
        </Template1>
        {props.eventValue && props.eventValue.ttl && (
          <Template1
            {...rests}
            classNameTitle="m-top-2 m-bottom-5"
            ref={titleRef}
            label=""
            girdItemSize={10}
          >
            <WrapperContent style={{ marginTop: '10px' }}>
              <Icon
                type="icon-ants-info-outline"
                style={{
                  color: '#FFBF60',
                  fontSize: '14px',
                  marginRight: '10px',
                }}
              />
              <StyleSpan>
                {`Data for this condition is only taken within TTL range (${moment(
                  currentDate.getTime() -
                    (props.eventValue.ttl - 1) * DAYSOFMILISECOND,
                ).format('ll')} - ${moment().format('ll')})`}
              </StyleSpan>
            </WrapperContent>
          </Template1>
        )}
      </>
    );
  },

  toggle: props => {
    return (
      props.isShow && (
        <Template1
          {...props}
          styleRightContent={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <ToggleButton
            componentKey={props.componentKey}
            isToggle={props.value}
            handleClick={() => props.onChange(props.name)(!props.value)}
            handleChange={() => {}}
            name={props.name}
            disabled={props.isViewMode || props.disabled}
            style={props.style}
            className={`toggle-button-element ${props.value ? 'active' : ''}`}
            isViewMode={props.isViewMode}
            isJourneyTemplate={props.isJourneyTemplateMode}
          />
          {props.description && (
            <span className="m-left-2">{props.description}</span>
          )}
        </Template1>
      )
    );
  },
  switch: props => {
    return (
      props.isShow && (
        <Template1 {...props}>
          <Switch
            componentKey={props.componentKey}
            checked={props.value}
            onClick={() => props.onChange(props.name)(!props.value)}
            name={props.name}
            disabled={props.isViewMode || props.disabled}
            style={props.style}
            className={`toggle-button-element ${props.value ? 'active' : ''}`}
            // isViewMode={props.isViewMode}
            // isJourneyTemplate={props.isJourneyTemplateMode}
          />
          {props.description && (
            <span className="m-left-2">{props.description}</span>
          )}
        </Template1>
      )
    );
  },
  toggleReverse: props => {
    return (
      <Template5 {...props}>
        <ToggleButton
          componentKey={props.componentKey}
          isToggle={props.value}
          handleClick={() => props.onChange(props.name)(!props.value)}
          handleChange={() => {}}
          name={props.name}
          className="toggleButton--style"
        />
        {props.description && (
          <span className="m-left-2">{props.description}</span>
        )}
      </Template5>
    );
  },
  modifyColumn: props => {
    return (
      props.isShow && (
        <Template1 {...props}>
          <ModalModifyColumn
            {...props}
            columns={props.value || props.initValue.lastModifyColumn}
            defaultSortColumns={[]}
            isOpenModal={false}
            groups={props.initValue.modifyColumn.groups}
            description={props.description}
            isShowUICheckboxWithInput={false}
            handleConfirm={props.onChange(props.name)}
            // callback={(type, data) => props.onChange(props.name)(data)}
          />
        </Template1>
      )
    );
  },
  computeSchedule: props => {
    return (
      <Template1 {...props}>
        <ComputeSchedule {...props} onChange={props.onChange(props.name)} />
      </Template1>
    );
  },

  computationSchedule: props => {
    return (
      <WrapperDisable disabled={props.disabled}>
        <div className="full-width">
          <ComputationSchedule
            design={props.design}
            value={props.value}
            onChange={props.onChange(props.name)}
            repeatByOptions={props.options}
            callback={props.callback}
            errors={props.errors}
            isViewMode={props.isViewMode}
          />
        </div>
        {/* {props.errors[0] ? <TextError>{props.errors[0]}</TextError> : null} */}
      </WrapperDisable>
    );
  },
  modalCalendarTable: props => {
    return (
      <ModalCalendarTable
        {...props}
        value={props.value}
        onChange={props.onChange}
      />
    );
  },

  chipSelect: props => {
    return (
      <>
        <Grid item sm={3} />
        <Grid item sm={9}>
          <Title>
            {props.isRequired && (
              <span style={{ color: '#ff0000' }}>
                {props.isRequired && `* `}
              </span>
            )}
            {props.label}
          </Title>
          <WrapperDisable disabled={props.disabled}>
            <ChipSelect
              value={props.value}
              onChange={props.onChange(props.name)}
              options={props.options}
              label="Add attribute"
            />
          </WrapperDisable>
        </Grid>
      </>
    );
  },

  color: props => {
    return (
      <>
        {props.useTemplate === 'lineBreaks' ? (
          <Template4 {...props}>
            <UITextColorPicker
              componentKey={props.componentKey}
              id={props.name}
              value={props.value}
              onChange={props.onChange(props.name)}
              errors={props.errors[0]}
            />
          </Template4>
        ) : (
          <Template1 {...props}>
            <UITextColorPicker
              componentKey={props.componentKey}
              id={props.name}
              value={props.value}
              onChange={props.onChange(props.name)}
              errors={props.errors[0]}
            />
          </Template1>
        )}
      </>
    );
  },
  notificationSetup: props => {
    return (
      <NotificationSetup
        value={props.value}
        onChange={props.onChange(props.name)}
        ownerId={props.ownerId}
        isViewMode={props.isViewMode}
      />
    );
  },
  iconSelection: props => {
    const { value, disabled } = props;

    return (
      <Template1 {...props}>
        <WrapperDisable disabled={disabled}>
          <IconSelection
            icon={value}
            iconTypes={['font-awesome', 'cus']}
            style={{ maxWidth: '400px' }}
            onChange={props.onChange(props.name)}
            disabled
          />
        </WrapperDisable>
      </Template1>
    );
  },
  singleLineHeading: props => {
    return (
      <Template7 isShowLabel={false} {...props}>
        <div style={{ maxWidth: '450px', minWidth: '250px' }}>
          {/* <InputAutoResize
            inputProps={{ style: { fontSize: 20, color: '#000000' } }}
            value={props?.value?.[props?.value?.DEFAULT_LANG] || ''}
            onChange={newValue => {
              props.onChange(props.name)({
                DEFAULT_LANG: 'EN',
                EN: newValue,
                VI: newValue,
                JA: newValue,
              });
            }}
          />
          <ErrorText>{props.errors[0]}</ErrorText> */}
          <EditableName
            className="edit_name"
            value={props?.value?.[props?.value?.DEFAULT_LANG] || ''}
            error={props.errors[0]}
            onBlur={() => {}}
            onChange={newValue => {
              props.onChange(props.name)({
                DEFAULT_LANG: 'EN',
                EN: newValue,
                VI: newValue,
                JA: newValue,
              });
            }}
            required={props.isRequired}
          />
        </div>
      </Template7>
    );
  },
  aceEditor: props => {
    return (
      <Template1 {...props}>
        {/* <DndProvider backend={HTML5Backend}>
          <EditorFormular {...props} callback={() => { }} />
        </DndProvider> */}
        <AceEditorView {...props} />
      </Template1>
    );
  },
};

// console.log(Object.keys(MAP_INPUT_TYPE).sort());

export const MAP_INPUT_MODAL_TYPE = {
  singleLineText: props => {
    return (
      <Grid item sm={12}>
        <Title>
          {props.label}
          {props.isRequired && `*`}
        </Title>
        <UITextField
          componentKey={props.componentKey}
          id={props.name}
          value={props.value}
          onChange={props.onChange(props.name)}
          firstText={props.errors[0]}
          textFieldProps={{
            disabled: props.disabled,
            size: 'small',
            multiline: false,
            rowsMax: 1,
            className: 'width-100',
            // id: 'standard-basic',
            error: !!props.errors[0],
          }}
        />
      </Grid>
    );
  },
  multiLangInput: props => {
    return (
      <Grid item sm={12}>
        <Grid container>
          <Grid item sm={3}>
            <Title>
              {props.label}
              <span style={{ color: 'red', marginLeft: '5px' }}>
                {props.isRequired && `*`}
              </span>
            </Title>
          </Grid>
          <Grid item sm={9}>
            <InputLanguage
              componentKey={props.componentKey}
              disabled={props.disabled}
              initData={props.initValue}
              onChange={props.onChange(props.name)}
              errors={props.errors}
              placeholder={props.placeholder}
            />
          </Grid>
        </Grid>
      </Grid>
    );
  },
};

export const Title = styled.div`
  /* padding-top: 0.4rem; */
  color: #000000;
  width: 100%;
  font-size: 12px;
  line-height: 30px;
  text-align: ${props => (props.textAlign ? 'end' : 'none')};
  margin-right: ${props => (props.textAlign ? '30px' : '0px')};

  /* margin-right: 0.5rem; */
`;

export const StyledTitle = styled(Title)`
  padding-top: 0;
  color: #000000;
`;

export const TitleCustomStyle = styled.div`
  padding-top: 0.4rem;
  width: 100%;
  display: flex;
  align-items: center;
  /* margin-right: 0.5rem; */
`;

export const WrapperCenter = styled.div`
  display: flex;
  /* flex-direction: column; */
  justify-content: space-between;
  align-items: center;
  height: 100%;
`;
export const WrapperCenterFlexEnd = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  height: 100%;
`;

export const WrapperCenterFlexStart = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
  gap: ${props => props.gap && `${props.gap}px`};
`;

export const WrapperCenterFlexStartRow = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
`;

export const WrapperFlexGapRow = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: ${props => (props.gap && `${props.gap}px`) || '20px'};
  padding-top: 0.4rem;
`;

export const CountryFlagWrapper = styled.div`
  width: 16px;
`;

export const CirclePoint = styled.div`
  clip-path: circle(50%);
  width: 4px;
  height: 4px;
  background-color: #aaaaaa;
`;

export const WrapperFlex = styled(WrapperFlexGapRow)`
  padding-top: 0.7rem !important;
  margin-bottom: 10px;
`;

export const StyledIconSelection = styled(IconSelection)`
  .antsomi-btn {
    display: ${props => (props.isViewMode ? 'none !important' : 'flex')};
  }
`;
export const initInputElement = (
  name,
  value,
  maxLength,
  isRequired,
  label,
  errors,
  validate,
) => ({
  name: name || '',
  value: value || '',
  maxLength: maxLength || null,
  isRequired,
  label: label || '',
  errors: errors || [],
  isValidate: !isRequired,
  validate: validate || (() => ({ errors: [], isValidate: false })),
  initValue: value || null,
});

export const initElementExplicit = ({
  name,
  value,
  maxLength,
  isRequired,
  label,
  errors,
  validate,
  useTemplate,
  initValue,
}) => ({
  name: name || '',
  value: value || '',
  maxLength: maxLength || null,
  isRequired,
  label: label || '',
  errors: errors || [],
  isValidate: false,
  validate: validate || (() => ({ errors: [], isValidate: false })),
  initValue: initValue || value || null,
  useTemplate: useTemplate || 'default',
});

export const mapBluePrint = (dataIn = [], bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};

export const MAP_INPUT_TEMPLATE = {
  // template by name ==================
  link: {
    ...initElementExplicit({
      name: 'link',
      value: '',
      isRequired: false,
      label: 'From',
      validate: MAP_VALIDATE.link,
    }),
    componentEl: MAP_INPUT_TYPE.link,
  },
  code: {
    ...initElementExplicit({
      name: 'code',
      value: '',
      maxLength: MAX_LCODE,
      isRequired: true,
      label: 'Code',
      validate: MAP_VALIDATE.code,
    }),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  attributeCode: {
    ...initElementExplicit({
      name: 'code',
      value: '',
      maxLength: MAX_LCODE,
      isRequired: true,
      label: 'Code',
      validate: MAP_VALIDATE.attributeCode,
    }),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  displayFormat: {
    ...initElementExplicit({
      name: 'displayFormat',
      value: {},
      isRequired: true,
      label: 'Display format',
      validate: MAP_VALIDATE.displayFormat,
    }),
    componentEl: MAP_INPUT_TYPE.displayFormat,
    dataType: '',
    disableGroupingAndDecimal: true,
    usePortal: ['group', 'decimal'],
  },
  timeRange: {
    ...initElementExplicit({
      name: 'timeRange',
      value: initDateValue(),
      label: 'Time range',
      validate: MAP_VALIDATE.default,
    }),
    componentEl: MAP_INPUT_TYPE.timeRange,
    isHideNextPrev: true,
  },
  timeRangeV2: {
    ...initElementExplicit({
      name: 'timeRange',
      value: initTimeRangeDefaultV2(),
      label: 'Time range',
      validate: MAP_VALIDATE.default,
    }),
    componentEl: MAP_INPUT_TYPE.timeRangeV2,
    isHideNextPrev: true,
  },
  formula: {
    ...initElementExplicit({
      name: 'formula',
      value: '',
      isRequired: true,
      label: 'Formula',
      validate: MAP_VALIDATE.formula,
    }),
    componentEl: MAP_INPUT_TYPE.formula,
    mapFields: {},
    use: '',
  },
  event: {
    ...initElementExplicit({
      name: 'source',
      value: [],
      maxLength: MAX_LNAME,
      isRequired: true,
      label: 'Source',
      validate: MAP_VALIDATE.selectDropdown,
    }),
    componentEl: MAP_INPUT_TYPE.event,
    options: [],
    mapOptions: {},
    defaultDataSource: { list: [], map: {} },
    extraDeps: 0,
    useDefaultListSource: true,
    isComfirmDelete: false,
  },
  source: {
    ...initElementExplicit({
      name: 'source',
      value: [],
      maxLength: MAX_LNAME,
      isRequired: true,
      label: 'Source',
      validate: MAP_VALIDATE.selectDropdown,
    }),
    componentEl: MAP_INPUT_TYPE.source,
    options: [],
    mapOptions: {},
    defaultDataSource: { list: [], map: {} },
    extraDeps: 0,
    useDefaultListSource: true,
    isComfirmDelete: false,
  },
  condition: {
    ...initElementExplicit({
      name: 'condition',
      value: {},
      isRequired: false,
      label: 'Condition',
      validate: MAP_VALIDATE.default,
    }),
    componentEl: MAP_INPUT_TYPE.condition,
    options: [],
    mapOptions: {},
  },
  // computeSchedule: {
  //   ...initElementExplicit({
  //     name: 'computeSchedule',
  //     value: {},
  //     isRequired: true,
  //     label: 'Compute schedule',
  //     validate: MAP_VALIDATE.default,
  //   }),
  //   componentEl: MAP_INPUT_TYPE.computeSchedule,
  // },

  // template by type ==================
  multiLangInput: {
    ...initElementExplicit({
      name: 'multiLangInput',
      value: {},
      maxLength: MAX_LNAME,
      isRequired: false,
      label: 'Multilang',
      validate: MAP_VALIDATE.multiLangInput,
    }),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  subTextField: {
    ...initElementExplicit({
      name: 'subTextField',
      value: {},
      maxLength: MAX_LNAME,
      isRequired: false,
      isReadonly: true,
      label: 'subTextField',
      // validate: MAP_VALIDATE.singleLineText,
    }),
    componentEl: MAP_INPUT_TYPE.subTextField,
  },
  subSelect: {
    ...initElementExplicit({
      name: 'subSelect',
      value: {},
      maxLength: MAX_LNAME,
      isRequired: true,
      label: 'subSelect',
      validate: MAP_VALIDATE.selectDropdown,
    }),
    componentEl: MAP_INPUT_TYPE.subSelect,
  },
  objectName: {
    ...initElementExplicit({
      name: 'multiLangInput',
      value: {},
      maxLength: MAX_LNAME,
      isRequired: false,
      label: 'Multilang',
      validate: MAP_VALIDATE.objectName,
    }),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  multiLangInputNewInputRule: {
    ...initElementExplicit({
      name: 'multiLangInputNewInputRule',
      value: {},
      maxLength: MAX_LNAME,
      isRequired: false,
      label: 'Multilang',
      validate: MAP_VALIDATE.multiLangInputNewInputRule,
    }),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  singleLineText: {
    ...initElementExplicit({
      name: 'singleLineText',
      value: '',
      maxLength: MAX_LNAME,
      isRequired: false,
      label: 'Text',
      validate: MAP_VALIDATE.singleLineText,
    }),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  // codeV2: {
  //   ...initElementExplicit({
  //     // name: 'codeV2',
  //     name: 'code',
  //     value: '',
  //     maxLength: MAX_LCODE,
  //     isRequired: true,
  //     label: 'Code',
  //     validate: MAP_VALIDATE.codeV2,
  //   }),
  //   componentEl: MAP_INPUT_TYPE.singleLineText,
  // },
  selectDropdown: {
    ...initElementExplicit({
      name: 'selectDropdown',
      value: {},
      isRequired: true,
      label: 'Select Dropdown',
      validate: MAP_VALIDATE.selectDropdown,
    }),
    // onlyParent: false,
    componentEl: MAP_INPUT_TYPE.selectDropdown,
    options: [],
    mapOptions: {},
    isShow: true,
  },
  selectDropdownCustomWidthGrid: {
    ...initElementExplicit({
      name: 'selectDropdown',
      value: {},
      isRequired: true,
      label: 'Select Dropdown',
      validate: MAP_VALIDATE.selectDropdown,
    }),
    // onlyParent: false,
    componentEl: MAP_INPUT_TYPE.selectDropdownCustomWidthGrid,
    options: [],
    mapOptions: {},
  },
  drawerChooseReport: {
    ...initElementExplicit({
      name: 'selectDropdown',
      value: {},
      isRequired: true,
      label: 'Select Dropdown',
      validate: MAP_VALIDATE.selectDropdown,
    }),
    // onlyParent: false,
    componentEl: MAP_INPUT_TYPE.drawerChooseReport,
    options: [],
    mapOptions: {},
  },
  radioGroup: {
    ...initElementExplicit({
      name: 'radioGroup',
      value: '',
      isRequired: true,
      label: 'Select Radio',
      validate: MAP_VALIDATE.radioGroup,
    }),
    // onlyParent: false,
    componentEl: MAP_INPUT_TYPE.radioGroup,
    options: [],
    mapOptions: {},
    isValidate: true,
  },
  attribute: {
    ...initElementExplicit({
      name: 'attribute',
      value: {},
      isRequired: true,
      label: 'Select Dropdown',
      validate: MAP_VALIDATE.selectDropdown,
    }),
    componentEl: MAP_INPUT_TYPE.attribute,
    options: [],
    mapOptions: {},
  },
  fieldFormat: {
    ...initElementExplicit({
      name: 'fieldFormat',
      value: {},
      maxLength: null,
      isRequired: true,
      label: 'Field format',
      errors: null,
      validate: MAP_VALIDATE.fieldFormat,
    }),
    componentEl: MAP_INPUT_TYPE.fieldFormat,
    dataType: '',
    disableGroupingAndDecimal: true,
    usePortal: ['group', 'decimal'],
    mapDisabledOptions: {},
  },
  checkbox: {
    ...initElementExplicit({
      name: 'checkbox',
      label: 'Checkbox',
      validate: MAP_VALIDATE.checkbox,
    }),
    componentEl: MAP_INPUT_TYPE.checkbox,
    isValidate: true,
  },
  checkboxCustomLabelTwoLevel: {
    ...initElementExplicit({
      name: 'checkbox',
      label: 'Checkbox',
      validate: MAP_VALIDATE.checkbox,
    }),
    componentEl: MAP_INPUT_TYPE.checkboxCustomLabelTwoLevel,
    isValidate: true,
    isShow: true,
  },
  checkboxWithGirdCustom: {
    ...initElementExplicit({
      name: 'checkbox',
      label: 'Checkbox',
      validate: MAP_VALIDATE.checkbox,
    }),
    componentEl: MAP_INPUT_TYPE.checkboxGird,
    isValidate: true,
  },
  checkboxType2: {
    ...initElementExplicit({
      name: 'checkboxType2',
      label: 'checkboxType2',
      validate: MAP_VALIDATE.checkbox,
    }),
    componentEl: MAP_INPUT_TYPE.checkboxType2,
    isValidate: true,
  },
  checkboxType3: {
    ...initElementExplicit({
      name: 'checkboxType3',
      label: 'checkboxType3',
      validate: MAP_VALIDATE.checkbox,
    }),
    componentEl: MAP_INPUT_TYPE.checkboxType3,
    isValidate: true,
  },
  toggle: {
    ...initElementExplicit({
      name: 'toggle',
      label: 'Toggle',
      // validate: MAP_VALIDATE.toggle,
    }),
    componentEl: MAP_INPUT_TYPE.toggle,
    isShow: true,
  },
  toggleReverse: {
    ...initElementExplicit({
      name: 'toggle',
      label: 'Toggle',
      // validate: MAP_VALIDATE.toggle,
    }),
    componentEl: MAP_INPUT_TYPE.toggleReverse,
  },
  modifyColumn: {
    ...initElementExplicit({
      name: 'modifyColumn',
      label: 'ModifyColumn',
      // validate: null,
      value: null,
      isShow: false,
    }),
    componentEl: MAP_INPUT_TYPE.modifyColumn,
  },
  objectCode: {
    ...initElementExplicit({
      name: 'objectCode',
      value: '',
      maxLength: MAX_LNAME,
      isRequired: true,
      label: 'Object Code',
      validate: MAP_VALIDATE.attributeCode,
    }),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  updateSchedule: {
    ...initElementExplicit({
      name: 'updateSchedule',
      value: {
        ...defaultComputationSchedule,
        type: 'dynamic',
        isLoading: false,
      },
      isRequired: true,
      label: 'Compute schedule',
      validate: MAP_VALIDATE.computationSchedule,
    }),
    componentEl: MAP_INPUT_TYPE.computationSchedule,
  },
  modalCalendarTable: {
    ...initElementExplicit({
      name: 'modalCalendarTable',
      value: {
        isLoading: false,
        isOpen: false,
        selectedTime: '',
        dataEvents: [],
        dataEventsGroupByDate: {},
        disableTimes: [],
        limitHour: 1,
        currentDate: getCurrentDate(),
        // time range for validate schedule
        timeRange: getTimeRangeCalendar(),
      },
      isRequired: false,
      validate: MAP_VALIDATE.default,
    }),
    isValidate: true,
    componentEl: MAP_INPUT_TYPE.modalCalendarTable,
  },
  chipSetting: {
    ...initElementExplicit({
      name: 'chipSetting',
      value: [{}],
      isRequired: true,
      label: 'Chip setting',
      validate: MAP_VALIDATE.default,
    }),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  singleLineTextWithPrefixAndSuffix: {
    ...initElementExplicit({
      name: 'singleLineTextWithPrefixAndSuffix',
      value: '',
      isRequired: true,
      label: 'Single line text',
      validate: MAP_VALIDATE.default,
    }),
    componentEl: MAP_INPUT_TYPE.singleLineTextWithPrefixAndSuffix,
  },
  inputFormular: {
    ...initElementExplicit({
      name: 'code',
      value: '',
      maxLength: MAX_LCODE,
      isRequired: true,
      label: 'Formular',
      validate: MAP_VALIDATE.singleLineText,
    }),
    componentEl: MAP_INPUT_TYPE.inputFormular,
  },
  notificationSetup: {
    ...initElementExplicit({
      name: 'notificationSetup',
      value: getNotificationDefault(),
      isRequired: false,
      label: 'Notification Setup',
      validate: MAP_VALIDATE.default,
    }),
    componentEl: MAP_INPUT_TYPE.notificationSetup,
  },
  chipSelect: {
    ...initElementExplicit({
      name: 'chipSelect',
      value: [],
      isRequired: true,
      label: 'Compute schedule',
      validate: true,
    }),
    componentEl: MAP_INPUT_TYPE.chipSelect,
  },
  singleLineHeading: {
    ...initElementExplicit({
      name: 'singleLineHeading',
      value: {
        DEFAULT_LANG: 'EN',
        EN: getUntitledName('Untitled Pool'),
      },
      maxLength: MAX_LNAME,
      isRequired: false,
      label: 'Heading',
      validate: MAP_VALIDATE.multiLangInputNewInputRule,
    }),
    componentEl: MAP_INPUT_TYPE.singleLineHeading,
  },
  switch: {
    ...initElementExplicit({
      name: 'toggle',
      label: 'Toggle',
      // validate: MAP_VALIDATE.toggle,
    }),
    componentEl: MAP_INPUT_TYPE.switch,
    isShow: true,
  },
};

function AttributeError({ item }) {
  const statusItemCode = safeParse(
    item.statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );
  if (statusItemCode !== STATUS_ITEM_CODE.ACTIVE) {
    const errors = getErrorsByStatusItemCode(
      getTranslateMessage(TRANSLATE_KEY._ITEM_NAME_ATTRIBUTE, 'attribute'),
      item,
    );

    const warns = getWarnsByStatusItemCode(
      getTranslateMessage(TRANSLATE_KEY._ITEM_NAME_ATTRIBUTE, 'attribute'),
      item,
    );

    if (errors.length > 0) {
      return <div className="is--text--error p-top-1">{errors[0]}</div>;
    }
    if (warns.length > 0) {
      return <div className="is--text--warn p-top-1">{warns[0]}</div>;
    }
  }
  return null;
}

export const mapBusinessObject = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.itemTypeId,
    value: item.itemTypeId,
    label: item.translateLabel,
  }));
