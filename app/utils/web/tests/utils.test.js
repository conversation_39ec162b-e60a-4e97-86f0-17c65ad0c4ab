import { omitDeep } from '../utils';

it('omitDepp', () => {
  expect(
    omitDeep(
      {
        confirmed: true,
        domain: 'example11.com',
        cName: {
          isChecked: false,
          isValid: true,
          msg: '',
        },
        domainRoot: {
          enable: true,
          value: 'example.com',
        },
        https: {
          use: 'custom',
          primaryKey:
            'ssh-rsa ASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKD',
          certificate:
            'ASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSA',
          caRootChain: [
            {
              id: 'id_1',
              value: '',
            },
          ],
        },
      },
      ['https.caRootChain'],
    ),
  ).toEqual({
    confirmed: true,
    domain: 'example11.com',
    cName: {
      isChecked: false,
      isValid: true,
      msg: '',
    },
    domainRoot: {
      enable: true,
      value: 'example.com',
    },
    https: {
      use: 'custom',
      primaryKey:
        'ssh-rsa ASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKD',
      certificate:
        'ASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSALKDASDJKASLDJLKASJDKLAJSDKLJSA',
    },
  });
});
