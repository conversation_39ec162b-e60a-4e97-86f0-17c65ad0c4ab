/* eslint-disable camelcase */
/* eslint-disable no-plusplus */
/* eslint-disable no-lonely-if */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-param-reassign */
/* eslint-disable no-restricted-properties */
/* eslint-disable import/no-cycle */

import {
  camelCase,
  cloneDeep,
  forEach,
  isEmpty,
  set,
  snakeCase,
  unset,
} from 'lodash';
import uniqid from 'uniqid';
import { getTranslateMessage } from '../../containers/Translate/util';
// import TRANSLATE_KEY from '../../messages/constant';
import { NumberRegex, STATUS_ITEM_CODE } from '../constants';
import {
  getAppSession,
  getCurrentOwnerIds,
  getCurrentUserId,
  getPortalId,
  getSessionStorage,
} from './cookie';

/* eslint-disable no-undef */
export const safeParse = (val, defaultVal) => {
  // debugger;
  if (
    typeof val === 'undefined' ||
    val === 'undefined' ||
    val === null ||
    val.length === 0 ||
    val === 'NaN'
  ) {
    return defaultVal;
  }

  return val;
};
export const safeParseV2 = (val, defaultVal) => {
  // debugger;
  if (typeof val === 'undefined' || val === null || val.length === 0) {
    return defaultVal;
  }

  return val;
};

export const safeParseArray = (val, defaultVal = []) => {
  // debugger;
  if (Array.isArray(val)) {
    return val;
  }
  return defaultVal;
};

export function safeParseFirstEle(source, defaultVal) {
  if (Array.isArray(source) || typeof source === 'string') {
    return source.length > 0 ? source[0] : defaultVal;
  }
  return defaultVal;
}

export const safeParseInt = (val, defaultVal) => {
  let tempt = safeParse(val, 0);
  if (tempt !== 0) {
    tempt = parseInt(tempt);
    if (Number.isNaN(tempt)) {
      return defaultVal;
    }
  }
  return tempt;
};

export const random = number => {
  let text = '';
  const possible = 'abcdefghijklmnopqrstuvwxyz0123456789';

  for (let i = 0; i < number; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }

  return text;
};

export const validateParam = (val, listDefaultVal, defaultVal) => {
  if (listDefaultVal.includes(val)) {
    return val;
  }
  return defaultVal;
};

export function safeParseNumber(number, defaultVal = '--') {
  if (number === '--' || number === defaultVal) {
    return number;
  }

  if (typeof number === 'number') {
    return number;
  }
  if (isNumeric(number)) {
    return +number;
  }
  return defaultVal;
}

export function isNumeric(num) {
  // eslint-disable-next-line no-restricted-globals
  return !isNaN(num) && num !== '';
}

export function numberWithCommas(n) {
  const tempt = safeParse(n, 0);
  const parts = tempt.toString().split('.');
  return (
    parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
    (parts[1] ? `.${parts[1]}` : '')
  );
}

export function roundWithDigit(num, digit = 2) {
  return +`${Math.round(`${num}e+${digit}`)}e-${digit}`;
}

export function getStringTimeFromSecondTime(second) {
  // var second = parseInt(minisecond / 1000);
  const regex = /^([0-1]?[0-9]|2[0-4]):([0-5][0-9])(:[0-5][0-9])?$/;

  let hour = 0;
  if (second >= 60 * 60) {
    hour = second / 3600;
  }

  const s1 = second - hour * 3600;
  let minus = 0;
  if (s1 >= 60) {
    minus = parseInt(s1 / 60);
  }

  const sec = s1 - minus * 60;

  let hStr = hour;
  if (hour < 10) {
    hStr = `0${hour}`;
  }
  let mStr = minus;
  if (minus < 10) {
    mStr = `0${minus}`;
  }
  let sStr = sec;
  if (sec < 10) {
    sStr = `0${sec}`;
  }
  const r = `${hStr}:${mStr}:${sStr}`;
  const isValidTime = regex.test(r);

  return [r, isValidTime];
}

export function getAvatarLabel(customerName) {
  if (customerName === '******') {
    return '';
  }
  if (
    typeof customerName !== 'undefined' &&
    customerName !== null &&
    customerName.length > 0
  ) {
    const tempt = customerName.split(' ');

    if (tempt.length > 1) {
      return safeParse(tempt[0][0], '') + safeParse(tempt[1][0], '');
    }
    return customerName[0];
  }
  return '';
}

// eslint-disable-next-line no-useless-escape
const REGEX_EMAIL = /^[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;

function internalValidateEmail(email) {
  if (!email) return false;

  if (email.length > 256) return false;

  if (!REGEX_EMAIL.test(email)) return false;

  // Further checking of some things regex can't handle
  const [account, address] = email.split('@');
  if (account.length > 64) return false;

  const domainParts = address.split('.');
  if (domainParts.some(part => part.length > 63)) return false;

  return true;
}

export const capitalizeFirstLetters = inputStr => {
  const words = inputStr.split(' ');

  const capitalizedWords = words.map(
    word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(),
  );

  const outputStr = capitalizedWords.join(' ');

  return outputStr;
};

export function validateEmail(email) {
  // eslint-disable-next-line no-useless-escape
  // const re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  // return re.test(email);

  return internalValidateEmail(email);
}

const invalidError = 'domainError';
const domainError = 'invalidDNSDomain';
const specialCharacterError = 'onlyLetters';
const endOfDomainError = 'specialCharacter';

export const checkDomainIsValid = domainName => {
  const httpPattern = /^https?:\/\//;
  const validDomainPattern = new RegExp(`[\\w\\.-]*(${DNS.join('|')})$`);
  const specialCharacterPattern = /[\\!\s\\+\\*\\?\\%\\#\\@\\^\\$\\(\\)\\[\]\\{\\}\\|\\]+[\w\\.-]+/;
  const specialCharacterEndPattern = new RegExp(`.*\\.(${DNS.join('|')}).+$`);

  const domainPattern = new RegExp(
    `^https?://[\\w\\.-]+\\.(${DNS.join('|')})(?=(/?$|/.*$))`,
    'g',
  );

  const isMatch = domainPattern.test(domainName);
  if (isMatch) return { error: [], isValidates: true };

  let errorMessage = 'domainError';
  if (!httpPattern.test(domainName)) {
    errorMessage = invalidError;
    return { error: errorMessage, isValidates: false };
  }
  const domainOnly = domainName.replace(httpPattern, '').split('/')[0];
  if (!validDomainPattern.test(domainOnly)) {
    errorMessage = domainError;
    return { error: errorMessage, isValidates: false };
  }
  if (specialCharacterPattern.test(domainOnly)) {
    errorMessage = specialCharacterError;
    return { error: errorMessage, isValidates: false };
  }
  if (specialCharacterEndPattern.test(domainOnly)) {
    errorMessage = endOfDomainError;
    return { error: errorMessage, isValidates: false };
  }
  return { error: errorMessage, isValidates: false };
};
export function validateDomainDNS(domain) {
  // const expression = /(http:|https:)+[^\s]+[a-z]\./gm;
  const pattern = new RegExp(
    `^https?:\\/\\/[\\w\\.-]+\\.(${DNS.join('|')})(?=/?)(?![\\w\\.\\s])`,
    'g',
  );
  return pattern.test(domain);
}

export const validatePassword = password => {
  //   /^
  //   (?=.*\d)          // should contain at least one digit
  //   (?=.*[a-z])       // should contain at least one lower case
  //   (?=.*[A-Z])       // should contain at least one upper case
  //   [a-zA-Z0-9]{8,}   // should contain at least 8 from the mentioned characters
  // $/

  //   'password ===>',
  //   password.match(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,}$/),
  // );
  // const ruleLength = /^[a-zA-Z0-9].{8,}$/;
  const ruleLowerCase = /(?=.*[a-z])/;
  const ruleUpperCase = /(?=.*[A-Z])/;
  const ruleNumber = /(?=.*\d)/;

  const mapValidate = {
    ruleLength: false,
    ruleLowerCase: false,
    ruleUpperCase: false,
    ruleNumber: false,
  };
  if (password.length >= 8) {
    mapValidate.ruleLength = true;
  }
  if (ruleLowerCase.test(password)) {
    mapValidate.ruleLowerCase = true;
  }
  if (ruleUpperCase.test(password)) {
    mapValidate.ruleUpperCase = true;
  }
  if (ruleNumber.test(password)) {
    mapValidate.ruleNumber = true;
  }

  return mapValidate;
};

export function validateWithSpaceAndSpecialChar(text) {
  // eslint-disable-next-line no-useless-escape
  const re = /^[0-9a-zA-Z\_]+$/;
  return re.test(text);
}

export function validatePhoneNumber(phone) {
  let number;
  if (phone[0] === 0 || phone[0] === '0') {
    number = phone.slice(1);
  } else {
    number = phone;
  }
  if (number.length < 8) {
    return false;
  }
  const regex = /^(9[976]\d|8[987530]\d|6[987]\d|5[90]\d|42\d|3[875]\d|2[98654321]\d|9[8543210]|8[6421]|6[6543210]|5[87654321]|4[987654310]|3[9643210]|2[70]|7|1)\d{1,14}$/g;
  return regex.test(number);
}

export function validateURL(string) {
  // // eslint-disable-next-line no-var
  // const res = string.match(
  //   /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g,
  // );
  // return res !== null;
  // eslint-disable-next-line no-useless-escape
  const regexp = /^((([A-Za-z]{3,9}:(?:\/\/)?)(?:[\-;:&=\+\$,\w]+@)?[A-Za-z0-9\.\-]+|(?:www\.|[\-;:&=\+\$,\w]+@)[A-Za-z0-9\.\-]+)((?:\/[\+~%\/\.\w\-_]*)?\??(?:[\-\+=&;%@\.\w_]*)#?(?:[\.\!\/\\\w]*))?)/;
  if (regexp.test(string)) {
    return true;
  }
  return false;
}

export function validateImageURL(url = null) {
  if (url === null) return false;
  return validateURL(url);
  // if (url.includes('googleusercontent.com')) return true;
  // return url.match(/\.(jpeg|jpg|gif|png)$/) != null;
}

export function validateImageURLPrefix(url = null) {
  if (url === null) return false;
  return url.match(/\.(jpeg|jpg|gif|png)$/) != null;
}

export function convertArrayToString(arr, prefix) {
  let res = '';
  if (typeof arr !== 'undefined' && arr.length > 0) {
    arr.forEach((item, index) => {
      if (index === 0) {
        res += item;
      } else {
        res += `${prefix}${item}`;
      }
    });
  }
  return res;
}

export function updateUrl(newUrl) {
  // eslint-disable-next-line no-restricted-globals
  if (history.pushState) {
    // eslint-disable-next-line no-restricted-globals
    history.pushState({}, null, newUrl);
  }
}

export function getRedirectUrl(location) {
  let redirectTo = location.pathname;
  if (location.search) {
    redirectTo += location.search;
  }
  if (location.hash) {
    redirectTo += location.hash;
  }
  return redirectTo;
}

export function serializeArray(arr, keyFrom, keyTo, valueFrom, valueTo) {
  // return arr;
  if (Array.isArray(arr)) {
    const list = [];
    arr.forEach(item => {
      list.push({
        [keyTo]: safeParse(item[keyFrom], ''),
        [valueTo]: safeParse(item[valueFrom], ''),
      });
    });

    return list;
  }
  return [];
}

export function makeArrayToLabel(arr, prefix, property = {}) {
  let tempt = '';
  const isCodeStatus =
    property.propertyCode === 'code_status' ||
    property.itemPropertyName === 'code_status';
  arr.forEach((item, index) => {
    if (index === 0) {
      tempt += isCodeStatus ? mapLableCodeStatus[Number(item)].value : item;
    } else if (index < arr.length - 1) {
      tempt += `, ${
        isCodeStatus ? mapLableCodeStatus[Number(item)].value : item
      }`;
    } else {
      tempt += `, ${prefix} ${
        isCodeStatus ? mapLableCodeStatus[Number(item)].value : item
      }`;
    }
  });
  return tempt;
}

export function makeArrayToLabelFilter(arr) {
  return arr.map(
    (value, index, array) => value + (index < array.length - 1 ? ', ' : ''),
  );
}

export function makeArrayToLabelCustomShowError(arr, prefix) {
  let tempt = '';
  let labelTranslate = '';
  arr.forEach((item, index) => {
    labelTranslate = getTranslateMessage(
      safeParseV2(item.translateCode, {}),
      item.label,
    );
    if (index === 0) {
      if (
        item.statusItemCode &&
        item.statusItemCode !== STATUS_ITEM_CODE.ACTIVE
      ) {
        tempt += `<strong class="item-inline-deactive">${labelTranslate}</strong>`;
      } else {
        tempt += labelTranslate;
      }
    } else if (index < arr.length - 1) {
      if (
        item.statusItemCode &&
        item.statusItemCode !== STATUS_ITEM_CODE.ACTIVE
      ) {
        tempt += `, <strong class="item-inline-deactive">${labelTranslate}</strong>`;
      } else {
        tempt += `, ${labelTranslate}`;
      }
    } else {
      if (
        item.statusItemCode &&
        item.statusItemCode !== STATUS_ITEM_CODE.ACTIVE
      ) {
        tempt += ` ${prefix} <strong class="item-inline-deactive">${labelTranslate}</strong>`;
      } else {
        tempt += ` ${prefix} ${labelTranslate}`;
      }
    }
  });
  return tempt;
}

export function serializeLabelToCode(name) {
  return name
    .toLocaleLowerCase()
    .replace(/^\d/g, 'n$&')
    .replace(/ /g, '_')
    .replace(/[^\w]/g, '');
}

export function getDataObjectByAccessor(obj, path, def) {
  let tempt = '--';

  if (path.indexOf('.') < 0) {
    tempt = safeParse(obj[path], def);
  } else if (!path) {
    tempt = obj;
  } else {
    const pathObj = makePathArray(path);

    let val;
    try {
      val = pathObj.reduce((current, pathPart) => current[pathPart], obj);
    } catch (e) {
      // continue regardless of error
    }
    // return  typeof val !== 'undefined' ? val : def;
    tempt = safeParse(val, def);
  }

  if (typeof tempt === 'string' && tempt !== def) {
    if (tempt.trim().length === 0) {
      return def;
    }
  }

  return tempt;
}

function makePathArray(obj) {
  return flattenDeep(obj)
    .join('.')
    .replace(/\[/g, '.')
    .replace(/\]/g, '')
    .split('.');
}

function flattenDeep(arr, newArr = []) {
  if (!isArray(arr)) {
    newArr.push(arr);
  } else {
    for (let i = 0; i < arr.length; i += 1) {
      flattenDeep(arr[i], newArr);
    }
  }
  return newArr;
}

function isArray(a) {
  return Array.isArray(a);
}

export function updateUrlFromIframe(event) {
  const { origin } = event;
  const hostname = origin.split('://');
  if (hostname.length === 2 && PORTAL_CONFIG.URL_DMP.indexOf(hostname[1])) {
    const { data } = event;
    if (data.type === 'change-url-by-hash') {
      updateUrl(
        `${window.location.origin + window.location.pathname}#${data.hash}`,
      );
    }
  }
}

// export function generateKey() {
//   return `key-${Math.random()}`;
// }

export function generateKey() {
  // const uid = (
  //   new Date().getTime() +
  //   Math.floor(Math.random() * 10000) +
  //   1
  // ).toString(36);
  // return uid;
  // return `key-${Math.random()}`;
  return uniqid.process();
}

export const MAX_LNAME = 255;
export const MAX_LCODE = 50;

export function validateNameLength(name) {
  return name.length <= MAX_LNAME;
}

export function validateCodeLength(code) {
  return code.length <= MAX_LCODE;
}

export function isProduction() {
  const env = safeParse(PORTAL_CONFIG.ENV, 'production');
  return env === 'production';
}

export function isStaging() {
  return getLocationOrigin().includes('staging-cdp.antsomi.com');
}

export function isDevelopment() {
  const env = safeParse(PORTAL_CONFIG.ENV, 'production');
  return env === 'development';
}

export function isLocalhost() {
  return window.location.hostname === 'localhost';
}

export function isShowMenuV2() {
  // const portals = safeParse(PORTAL_CONFIG.MENU_PORTAL_V2, []);
  // const portalId = safeParse(APP_CACHE_PARAMS.api_pid, 0);

  // if (Array.isArray(portals)) {
  //   if (portals.length === 0) {
  //     return true;
  //   }
  //   return portals.includes(portalId);
  // }

  if (isDevelopment()) {
    return false;
  }

  return true;

  // return false;
}

// export function isOthreePartner() {
//   const env = safeParse(PORTAL_CONFIG.ENV, 'production');
//   return env === 'development';
// }

export function isDevelopmentAPI() {
  const env = safeParse(PORTAL_CONFIG.ENV, 'production');
  return (
    env === 'development' && PORTAL_CONFIG.URL_API === '//dev-app.cdp.asia/hub'
  );
}

export function encodeURL(query) {
  return window.encodeURIComponent(query);
}

export function decodeURL(query) {
  return window.decodeURIComponent(query);
}

export function getLocationOrigin() {
  return window.location.origin;
}

export const validateAttributePrefix = stringIn => {
  const hasBannedPrefix = /^(\d|aud_|sgmt_|_)/.test(stringIn);
  return !hasBannedPrefix;
};
export const validateObjectPrefixWithId = stringIn => {
  let isSuffixId = false;
  // const [hasObjectIdPrefix] = stringIn.toLowerCase().match(/(.{3})\s*$/gm);
  // if (hasObjectIdPrefix === '_id') {
  //   isSuffixId = true;
  // }
  if (typeof stringIn === 'string') {
    isSuffixId = stringIn.toLowerCase().endsWith('_id');
  }
  return isSuffixId;
};
export const validateEventAttributeFormat = stringIn => {
  const hasBannedPrefix = /(_id)$/.test(stringIn);
  return !hasBannedPrefix;
};

export function serializeLabelToCodeAttr(name) {
  // console.log('name', name);
  return name
    .toLocaleLowerCase()
    .replace(/ /g, '_')
    .replace(/[^\w]/g, '')
    .replace(/^(\d|aud|sgmt|_)+/g, '');
}

export function arrayMove(arr, old_index, new_index) {
  if (new_index >= arr.length) {
    let k = new_index - arr.length + 1;
    // eslint-disable-next-line no-plusplus
    while (k--) {
      arr.push(undefined);
    }
  }
  arr.splice(new_index, 0, arr.splice(old_index, 1)[0]);
  return arr; // for testing
}

export const getToggleState = toggleKey =>
  safeParse(JSON.parse(localStorage.getItem(toggleKey)), false);

export const getColumnWidthsState = key =>
  safeParse(JSON.parse(localStorage.getItem(`column-widths-${key}`)), {});

export const setColumnWidthsState = (key, value) =>
  localStorage.setItem(`column-widths-${key}`, JSON.stringify(value));

export const handleTextSearch = text => text.trim().toLowerCase(0);

export const dynamicSort = (property, type = 'asc') => {
  if (type === 'desc') {
    return (a, b) =>
      a[property].toLowerCase() > b[property.toLowerCase()]
        ? -1
        : a[property.toLowerCase()] < b[property].toLowerCase()
        ? 1
        : 0;
  }
  return (a, b) =>
    a[property].toLowerCase() > b[property].toLowerCase()
      ? 1
      : a[property].toLowerCase() < b[property].toLowerCase()
      ? -1
      : 0;
};

// detct and set param for purpose public url customer/visitor detail
export function isUrlEmbedded() {
  return APP_CACHE_PARAMS.IS_URL_EMMBEDDED;
}

export function getObjectFromQueryParams(url = '') {
  // eslint-disable-next-line func-names
  const result = url.split('&').reduce(function(res, item) {
    const parts = item.split('=');
    if (parts[1]) {
      // eslint-disable-next-line prefer-destructuring
      res[parts[0]] = parts[1];
    }
    return res;
  }, {});
  return result;
}

export function getParamHashParams() {
  try {
    const hash = window.location.hash.substr(1);

    return getObjectFromQueryParams(hash);
  } catch (err) {
    console.log(err);
  }
  return {};
}

/* ----------------------- Move utils number to utils ----------------------- */
export const convertNumberByDisplayFormat = (
  value,
  groupIn,
  decimal,
  decimalPlace,
) => {
  // console.log('value', value);
  // console.log('decimalPlace', decimalPlace);
  // console.log('decimal', decimal);
  // console.log('groupIn', groupIn);
  let group = groupIn;
  if (groupIn === 'none') {
    group = '';
  } else if (groupIn === 'space') {
    group = ' ';
  }
  if (safeParse(value, null) === null) return '';
  // Check safeParse
  // const valueTmp = safeParse(value, '');
  const indexOfDecimal = String(value).indexOf('.');

  let valueInt = '';
  let valueDec = '';
  if (indexOfDecimal >= 0) {
    valueInt = value.toString().substring(0, indexOfDecimal);
    valueDec = value.toString().substring(indexOfDecimal + 1);
  } else {
    valueInt = value;
    valueDec = '';
  }
  // convert to string for using function replace
  valueInt = String(valueInt)
    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,') // seperate group by ','
    .replace(/,/gi, `${group}`); // remove ',' with group

  valueDec = valueDec.substring(0, decimalPlace);
  valueDec = valueDec.substring(0, decimalPlace);
  valueDec = valueDec.padEnd(decimalPlace, '0');

  let out = '';
  if (valueInt !== '' && decimalPlace > 0) {
    out = `${valueInt}${decimal}${valueDec}`;
  } else {
    out = `${valueInt}`;
  }

  return out;
};
const mapLableCodeStatus = [
  { value: 'undefined' },
  { value: 'Available' },
  { value: 'Allocated' },
  { value: 'Sent' },
  { value: 'Used' },
];

// basic implement from https://deepdash.io/#filterdeep
export const filterDeep = (
  obj = [],
  predicate = () => true,
  options = {
    childrenPath: 'children',
  },
) => {
  if (!Array.isArray(obj)) return obj;

  const { childrenPath } = options;

  return obj.filter(item => {
    let isValidItem = true;

    if (typeof predicate === 'string') {
      isValidItem = !!item[predicate];
    }

    if (typeof predicate === 'function') {
      isValidItem = Object.entries({ ...item }).every(([key, value]) =>
        predicate(value, key, item, obj),
      );
    }

    if (isValidItem && item[childrenPath]) {
      item[childrenPath] = filterDeep(item[childrenPath], predicate, options);
    }

    return isValidItem;
  });
};

export function trackEvent(ec, ea, object) {
  const id = `${getCurrentUserId()}_${getCurrentOwnerIds()}_${getCurrentUserId()}`;
  const name = object.isOwner
    ? getAppSession('owner_name') === 'all'
      ? safeParse(getAppSession('user_name'), '')
      : getAppSession('owner_name')
    : safeParse(getAppSession('user_name'), '');
  const web_event_track = window.web_event;
  const web_analytic_track = window.web_analytic;
  if (web_event_track && web_analytic_track) {
    web_analytic_track.setFirstPartyUserId(id);
    web_event_track.track(ec, ea, {
      items: [],
      dims: {
        customers: {
          customer_id: getCurrentOwnerIds(),
          name,
        },
        cdp_portal: {
          id: getPortalId(),
          name: safeParse(getSessionStorage('api_name'), ''),
        },
        cdp_objects: {
          id: ea !== 'listing' ? `${object.id}_${object.type}` : object.type,
          name: object.name,
          object_type: object.type,
        },
        users: {
          id,
          name: safeParse(getAppSession('user_name'), ''),
        },
      },
      extra: {
        id_login: getCurrentUserId(),
        id_owner: getCurrentOwnerIds(),
        id_action: getCurrentUserId(),
      },
    });
  }
}

export const getObjectPropSafely = (fn, defaultValue = '') => {
  try {
    return fn();
  } catch (e) {
    return defaultValue;
  }
};

export const iterateObject = (obj, callback, opts = {}) => {
  const { path: defautPath = '' } = opts;

  const stack = [{ obj, path: defautPath }];

  while (stack.length > 0) {
    const { obj: currentObj, path } = stack.pop();

    Object.entries(currentObj).forEach(([key, value]) => {
      const currentPath = path ? `${path}.${key}` : key;

      let continueIter = true;

      if (typeof callback === 'function') {
        callback(value, currentPath, {
          setContinute: isContinute => {
            continueIter = isContinute;
          },
        });
      }

      if (continueIter && typeof value === 'object' && value !== null) {
        stack.push({ obj: value, path: currentPath });
      }
    });
  }
};

/* eslint-disable */
export function findPathObjectByProperyName(obj, property) {
  let path = [];

  function search(obj) {
    for (let key in obj) {
      if (key === property) {
        return [...path, key];
      }

      if (Array.isArray(obj[key])) {
        for (let i = 0; i < obj[key].length; i++) {
          path.push(key, i);
          const nestedPath = search(obj[key][i]);
          if (nestedPath) {
            return nestedPath;
          }
          path.pop();
          path.pop();
        }
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        path.push(key);
        const nestedPath = search(obj[key]);
        if (nestedPath) {
          return nestedPath;
        }
        path.pop();
      }
    }
    return null;
  }

  let result = search(obj);

  return result && Array.isArray(result) ? result.join('.') : undefined;
}

export function findPathByCondition(obj, condition, options = {}) {
  const { findFirst = false } = options;

  const paths = [];

  function searchObj(currentObj, currentPath) {
    if (Array.isArray(currentObj)) {
      currentObj.forEach((val, i) => {
        const newPath = [...currentPath, i];
        if (condition(val)) {
          paths.push(newPath);
          if (findFirst) {
            return;
          }
        }
        searchObj(val, newPath);
      });
    } else if (typeof currentObj === 'object') {
      Object.keys(currentObj).forEach(key => {
        const newPath = [...currentPath, key];
        if (condition(currentObj[key])) {
          paths.push(newPath);
          if (findFirst) {
            return;
          }
        }
        searchObj(currentObj[key], newPath);
      });
    }
  }

  searchObj(obj, []);

  return findFirst ? paths[0] : paths;
}

export const removeEmptyLine = str => str.replace(/^\s*$(?:\r\n?|\n)/gm, '');
/* eslint-enable */

export function shortenNumber(
  num,
  config = {
    precision: 1,
    thousandSeparator: ',',
    abbreviation: ['k', 'M', 'B', 'T', 'Q'],
  },
) {
  if (num === 0) return '0';

  const magnitude = Math.floor(Math.log10(num) / 3);

  const shortNum = num / Math.pow(10, magnitude * 3);

  let roundedNum = shortNum.toFixed(config.precision);

  if (config.thousandSeparator) {
    roundedNum = roundedNum.replace(
      /\B(?=(\d{3})+(?!\d))/g,
      config.thousandSeparator,
    );
  }

  let abbreviation = '';
  if (config.abbreviation) {
    if (magnitude < config.abbreviation.length) {
      abbreviation = config.abbreviation[magnitude - 1];
    } else {
      abbreviation = `*10^${magnitude * 3}`;
    }
  }

  if (abbreviation) {
    roundedNum += abbreviation;
  }

  return roundedNum;
}

export function updateByPaths(draft, paths) {
  forEach(paths, ({ path, data }) => {
    set(draft, path, data);
  });
  return draft;
}

export function omitDeep(obj, paths = []) {
  const temp = cloneDeep(obj);

  paths.forEach(path => {
    unset(temp, path);
  });

  return temp;
}

export function isClassComponent(component) {
  return (
    typeof component === 'function' && !!component.prototype.isReactComponent
  );
}

export function isFunctionComponent(component) {
  return (
    typeof component === 'function' &&
    String(component).includes('return React.createElement')
  );
}

export function isReactComponent(component) {
  return isClassComponent(component) || isFunctionComponent(component);
}

export const isValidDomain = string => {
  const regex = new RegExp(
    /^(?:(?:(?:[a-zA-z-]+):\/{1,3})?(?:[a-zA-Z0-9])(?:[a-zA-Z0-9\-.]){1,61}(?:\.[a-zA-Z]{2,})+|\[(?:(?:(?:[a-fA-F0-9]){1,4})(?::(?:[a-fA-F0-9]){1,4}){7}|::1|::)\]|(?:(?:[0-9]{1,3})(?:\.[0-9]{1,3}){3}))(?::[0-9]{1,5})?$/,
    'gm',
  );

  return regex.test(string);
};

// This function converts the string to lowercase, then perform the conversion
export function toLowerCaseNonAccentVietnamese(str) {
  str = str.toLowerCase();
  //     We can also use this instead of from line 11 to line 17
  //     str = str.replace(/\u00E0|\u00E1|\u1EA1|\u1EA3|\u00E3|\u00E2|\u1EA7|\u1EA5|\u1EAD|\u1EA9|\u1EAB|\u0103|\u1EB1|\u1EAF|\u1EB7|\u1EB3|\u1EB5/g, "a");
  //     str = str.replace(/\u00E8|\u00E9|\u1EB9|\u1EBB|\u1EBD|\u00EA|\u1EC1|\u1EBF|\u1EC7|\u1EC3|\u1EC5/g, "e");
  //     str = str.replace(/\u00EC|\u00ED|\u1ECB|\u1EC9|\u0129/g, "i");
  //     str = str.replace(/\u00F2|\u00F3|\u1ECD|\u1ECF|\u00F5|\u00F4|\u1ED3|\u1ED1|\u1ED9|\u1ED5|\u1ED7|\u01A1|\u1EDD|\u1EDB|\u1EE3|\u1EDF|\u1EE1/g, "o");
  //     str = str.replace(/\u00F9|\u00FA|\u1EE5|\u1EE7|\u0169|\u01B0|\u1EEB|\u1EE9|\u1EF1|\u1EED|\u1EEF/g, "u");
  //     str = str.replace(/\u1EF3|\u00FD|\u1EF5|\u1EF7|\u1EF9/g, "y");
  //     str = str.replace(/\u0111/g, "d");
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/đ/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // Huyền sắc hỏi ngã nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // Â, Ê, Ă, Ơ, Ư

  return str;
}

// This function keeps the casing unchanged for str, then perform the conversion
export function toNonAccentVietnamese(str) {
  str = str.replace(/A|Á|À|Ã|Ạ|Â|Ấ|Ầ|Ẫ|Ậ|Ă|Ắ|Ằ|Ẵ|Ặ/g, 'A');
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/E|É|È|Ẽ|Ẹ|Ê|Ế|Ề|Ễ|Ệ/, 'E');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/I|Í|Ì|Ĩ|Ị/g, 'I');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/O|Ó|Ò|Õ|Ọ|Ô|Ố|Ồ|Ỗ|Ộ|Ơ|Ớ|Ờ|Ỡ|Ợ/g, 'O');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/U|Ú|Ù|Ũ|Ụ|Ư|Ứ|Ừ|Ữ|Ự/g, 'U');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/Y|Ý|Ỳ|Ỹ|Ỵ/g, 'Y');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/Đ/g, 'D');
  str = str.replace(/đ/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // Huyền sắc hỏi ngã nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // Â, Ê, Ă, Ơ, Ư

  return str;
}

export const nthNumber = number => {
  return number > 0
    ? ['th', 'st', 'nd', 'rd'][
        (number > 3 && number < 21) || number % 10 > 3 ? 0 : number % 10
      ]
    : '';
};

export function isObject(value) {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

export const formatPlural = (num, label) => {
  if (num === 0) return '';

  return num > 1 ? `${num} ${label}s` : `1 ${label}`;
};

export const parseObjectNumeric = (object, opts = {}) => {
  const { ignoreKeys = [], keys = [], keyFormat } = opts;

  if (!isObject(object)) return object;

  let formatKeyFn;

  if (['snake', 'camel'].includes(keyFormat)) {
    formatKeyFn = keyFormat === 'camel' ? camelCase : snakeCase;
  }

  const entries = Object.entries(object).map(([key, value]) => {
    if (!isEmpty(ignoreKeys) && ignoreKeys.includes(key)) return [key, value];

    if (!isEmpty(keys) && !keys.includes(key)) return [key, value];

    let temp = value;

    if (typeof value === 'string') {
      const isNumber = NumberRegex.test(value);

      if (isNumber) {
        temp = +parseFloat(value);
      }
    }

    return [formatKeyFn ? formatKeyFn(key) : key, temp];
  });

  return Object.fromEntries(entries);
};

export const getBase64Image = img => {
  try {
    const canvas = document.createElement('canvas');
    canvas.width = img.width;
    canvas.height = img.height;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(img, 0, 0);
    return canvas.toDataURL('image/png');
  } catch (e) {
    return '';
  }
};

export const getBase64FromUrl = async url => {
  return new Promise(resolve => {
    const imgEl = document.createElement('img');
    imgEl.crossOrigin = 'anonymous';
    imgEl.src = url;
    imgEl.onload = () => {
      resolve(getBase64Image(imgEl));
    };
    imgEl.onerror = () => {
      resolve('');
    };
  });
};

/**
 * Converts a URLSearchParams object to a regular JavaScript object,
 * converting 'undefined' string values to actual undefined.
 *
 * @param {URLSearchParams} searchParams - The URLSearchParams object to convert.
 *
 * @returns {Object} The resulting object with keys and values from the URLSearchParams.
 *
 * @example
 * // Example usage of parseSearchParamsToObject
 * const searchParams = new URLSearchParams('foo=1&bar=undefined');
 * const paramsObject = parseSearchParamsToObject(searchParams);
 * console.log(paramsObject);
 * // Output: { foo: '1', bar: undefined }
 */
export const parseSearchParamsToObject = searchParams => {
  const paramsObject = {};
  const entries = searchParams.entries();

  // eslint-disable-next-line no-restricted-syntax
  for (const [key, value] of entries) {
    const newValue = value === 'undefined' ? undefined : value;
    paramsObject[key] = newValue;
  }

  return paramsObject;
};

/**
 * Checks if the given function returns a Promise.
 *
 * This function calls the provided function with the given arguments and checks if the returned value is a Promise.
 *
 * @param {Function} func - The function to check.
 * @param {...*} args - The arguments to pass to the function when calling it.
 * @returns {boolean} True if the function returns a Promise, false otherwise.
 *
 * @example
 * // Example usage:
 * const asyncFunc = async () => {};
 * const syncFunc = () => {};
 * console.log(isFunctionReturningPromise(asyncFunc)); // true
 * console.log(isFunctionReturningPromise(syncFunc));  // false
 */
export const isFunctionReturningPromise = (func, ...args) => {
  try {
    const result = func(...args);
    return result instanceof Promise;
  } catch (error) {
    // If calling the function throws an error, it's not returning a Promise
    return false;
  }
};

export function isSubarray(parent, sub) {
  let parentIndex = 0;
  let subIndex = 0;

  while (parentIndex < parent.length && subIndex < sub.length) {
    if (parent[parentIndex] === sub[subIndex]) {
      subIndex += 1;
    }

    parentIndex += 1;
  }

  return subIndex === sub.length;
}

/**
 * Extracts merge tag content sources from the input text.
 *
 * This function searches for patterns in the format `groups.<groupId>[<index>].<attributeCode>}` within the input text
 * and returns an array of objects representing the extracted components.
 *
 * @param {string} [inputText=''] - The input text containing the merge tag patterns.
 * @returns {Array<Object>} An array of objects where each object contains the following properties:
 *   - {string} groupId - The group ID extracted from the merge tag.
 *   - {number} index - The index extracted from the merge tag.
 *   - {string} attributeCode - The attribute code extracted from the merge tag.
 *
 * @example
 * // Example usage:
 * const inputText = "123123 #{groups.csg1j4dc[1].product_code} #{groups.csg1j4dc[1].category_level_1} asdf alsdkfj";
 * const result = extractMergeTagsContentSources(inputText);
 * // result will be:
 * // [
 * //   { groupId: 'csg1j4dc', index: 1, attributeCode: 'product_code' },
 * //   { groupId: 'csg1j4dc', index: 1, attributeCode: 'category_level_1' }
 * // ]
 */
export const extractMergeTagsContentSources = (inputText = '') => {
  const regex = /groups\.(\w+)\[(\d+)\]\.(\w+)\}/g;
  const matches = [];
  let match;

  // eslint-disable-next-line no-cond-assign
  while ((match = regex.exec(inputText)) !== null) {
    // eslint-disable-next-line no-unused-vars
    const [_, groupId, index, attributeCode] = match;
    matches.push({
      groupId,
      index: parseInt(index, 10), // converting index to an integer
      attributeCode,
    });
  }

  return matches;
};

/**
 * Normalizes a list of merge tag content sources into a dictionary format.
 *
 * This function takes an array of tag objects and converts it into an object containing a list of unique group IDs
 * and a map where each group ID maps to its respective attributes.
 *
 * @param {Array<Object>} [tagList=[]] - The list of tag objects, where each object contains:
 *   - {string} groupId - The group ID.
 *   - {number} index - The index.
 *   - {string} attributeCode - The attribute code.
 * @returns {Object} An object with the following structure:
 *   - {Array<string>} list - An array of unique group IDs.
 *   - {Object} map - An object where each key is a group ID and the value is another object containing attribute keys
 *                    mapped to objects with `attributeCode` and `index` properties.
 *
 * @example
 * // Example usage:
 * const tagList = [
 *   { groupId: 'csg1j4dc', index: 1, attributeCode: 'product_code' },
 *   { groupId: 'csg1j4dc', index: 1, attributeCode: 'category_level_1' }
 * ];
 * const result = normalizeMergeTagsContentSources(tagList);
 * // result will be:
 * // {
 * //   list: ['csg1j4dc'],
 * //   map: {
 * //     csg1j4dc: {
 * //       'product_code-1': { attributeCode: 'product_code', index: 1 },
 * //       'category_level_1-1': { attributeCode: 'category_level_1', index: 1 }
 * //     }
 * //   }
 * // }
 */
export const normalizeMergeTagsContentSources = (tagList = []) => {
  const normalizedData = {
    list: [],
    map: {},
  };

  tagList.forEach(tag => {
    const { groupId, index, attributeCode } = tag;
    const attributeKey = `${attributeCode}-${index}`;

    if (!normalizedData.list.includes(groupId)) {
      normalizedData.list.push(groupId);
    }

    if (!normalizedData.map[groupId]) {
      normalizedData.map[groupId] = {};
    }

    normalizedData.map[groupId][attributeKey] = {
      attributeCode,
      index,
    };
  });

  return normalizedData;
};

/**
 * Retrieves an object from the normalized merge tags data based on attribute code, index, and item key.
 *
 * This function searches the normalized data to find the object corresponding to the specified attribute code, index, and item key.
 *
 * @param {Object} params - The parameters object.
 * @param {Object} params.normalizedData - The normalized merge tags data, containing:
 *   - {Array<string>} list - An array of unique group IDs.
 *   - {Object} map - An object where each key is a group ID and the value is another object containing attribute keys
 *                    mapped to objects with `attributeCode` and `index` properties.
 * @param {string} params.attributeCode - The attribute code to search for.
 * @param {number|string} params.index - The index to search for.
 * @param {string} params.itemKey - The group ID to search for.
 * @returns {Object|null} The object corresponding to the specified attribute code and index within the item key, or null if not found.
 *
 * @example
 * // Example usage:
 * const normalizedData = {
 *   list: ['csg1j4dc'],
 *   map: {
 *     csg1j4dc: {
 *       'product_code-1': { attributeCode: 'product_code', index: 1 },
 *       'category_level_1-1': { attributeCode: 'category_level_1', index: 1 }
 *     }
 *   }
 * };
 * const result = getObjectFromNormalizedMergeTags({
 *   normalizedData,
 *   attributeCode: 'product_code',
 *   index: 1,
 *   itemKey: 'csg1j4dc'
 * });
 * // result will be:
 * // { attributeCode: 'product_code', index: 1 }
 */
export const getObjectFromNormalizedMergeTags = ({
  normalizedData = { list: [], map: {} },
  attributeCode = '',
  index = '',
  itemKey = '',
}) => {
  const attributeKey = `${attributeCode}-${index}`;

  if (normalizedData.list.includes(itemKey) && normalizedData.map[itemKey]) {
    return normalizedData.map[itemKey][attributeKey] || null;
  }

  return null;
};
const DNS = [
  'com',
  'net',
  'org',
  'info',
  'vn',
  'sg',
  'id',
  'my',
  'ph',
  'th',
  'kh',
  'la',
  'mm',
  'bn',
  'shop',
  'edu',
  'biz',
  'net',
  'info',
  'gov',
  'asia',
  'co',
  'us',
  'mobi',
  'today',
  'xyz',
  'jp',
  'store',
  'market',
  'shopping',
  'fashion',
  'boutique',
  'tokyo',
  'osaka',
  'co.th',
  'co.jp',
  'online',
  'tech',
  'co.id',
  'au',
  'one',
];
