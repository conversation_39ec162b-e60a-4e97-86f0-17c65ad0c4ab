export const RESTART_ON_REMOUNT = '@@saga-injector/restart-on-remount';
export const DAEMON = '@@saga-injector/daemon';
export const ONCE_TILL_UNMOUNT = '@@saga-injector/once-till-unmount';

export const STATUS_ITEM_CODE = {
  ARCHIVED: 'ARCHIVED',
  ACTIVE: 'ACTIVE',
  FORBIDDEN: 'FORBIDDEN',
  DISABLED: 'DISABLED',
  REMOVED: 'REMOVED',
  NO_FILTER: 'NO_FILTER',
};

export const SOURCE_TYPE = {
  WEBSITE: 1,
  APP: 2,
  SERVER: 3,
  CLOUD: 4,
  DELIVERY: 5,
};

export const COLLECTION_STATUS = {
  ENABLED: 1,
  DISABLED: 2,
};

export const DEFAULT_STATUS = {
  ENABLED: 1,
  DISABLED: 2,
  REMOVED: 3,
  ARCHIVED: 4,
};

export const ATTRIBUTE_STATUS = { ...DEFAULT_STATUS };

export const ARCHIVE_STATUS = 4;
export const REMOVE_STATUS = 3;

export const DATA_ACCESS_OBJECT = {
  DASHBOARD: 1,
  JOURNEY_OVERVIEW: 2,
  SEGMENT: 3,
  BO: 4,
  VIEW: 5,
  DESTINATION: 6,
  PROMOTION: 14,
};

export const TYPE_ATTRIBUTE = {
  DEFAULT: 1,
  CUSTOM: 2,
  COMPUTED: 3,
};

export const PROCESS_COMPUTE_STATUS = {
  COMPUTING: 1,
  DONE: 2,
  UNSUCCESS: 3,
  PAUSED: 4,
  WAITING: 5,
  READY_TO_USE: 6,
  WAITING_INITIAL: 7,
  LIVE_UPDATE: 8,
};

export const NAME_TYPE_ATTR = {
  [TYPE_ATTRIBUTE.DEFAULT]: 'default_attr',
  [TYPE_ATTRIBUTE.CUSTOM]: 'custom_attr',
  [TYPE_ATTRIBUTE.COMPUTED]: 'comp_attr',
};

export const STATUS_SOURCE_CODE = {
  NO_LONGER_ASSIGN_EVENT: 'NO_LONGER_ASSIGN_EVENT',
};

export const PORTAL_KEYS = {
  CURRENCY: 'p_currency',
  USER_LANGUAGE: 'user_language',
  PORTAL_LANGUAGE: 'p_language',
  TIME_ZONE: 'p_timezone',
  CURRENCY_FORMAT: 'p_f_currency',
  DATE_TIME_FORMAT: 'p_f_datetime',
  DATE_TIME_FORMAT_LONG: 'p_f_longdatetime',
  NUMBER_FORMAT: 'p_f_number',
  PERCENTAGE_FORMAT: 'p_f_percentage',
  VERSION: '_ver',
  AVATAR: 'avatar',
};

export const OS = {
  WINDOWS: 'Windows',
  MAC_OS: 'macOS',
  IOS: 'iOS',
  LINUX: 'Linux',
  UBUNTU: 'Ubuntu',
  ANDROID: 'Android',
};

export const BROWSER = {
  BRAVE: 'Brave',
  OPERA_MINI: 'Opera Mini',
  OPERA: 'Opera',
  FIREFOX: 'Firefox',
  COC_COC: 'Coc coc',
  VIVALDI: 'Vivaldi',
  UC_BROWSER: 'UC Browser',
  EDGE: 'Edge',
  CHROME: 'Chrome',
  SAFARI: 'Safari',
  IE: 'IE',
};

export const TIME_UNIT_TYPES = {
  DAYS_OF_WEEK: 'DAYS_OF_WEEK',
  DAYS_OF_WEEK_CUSTOM: 'DAYS_OF_WEEK_CUSTOM',
};

export const CONFIG_OBJECT_STATUS = {
  ENABLE: 1,
  DISABLE: 2,
  REMOVE: 3,
  ACTIVE: 1,
  SYS_REMOVE: 0,
};

export const SYSTEM_BO = {
  Variant: {
    itemTypeId: -1011,
  },
  Campaign: {
    itemTypeId: -1010,
  },
  Journey: {
    itemTypeId: -1009,
  },
  Customer: {
    itemTypeId: -1003,
  },
  Visitor: {
    itemTypeId: -1007,
  },
  Product: {
    itemTypeId: 1,
  },
};

export const REGEX_DETECT_TOKEN = /#\{([^}]+)\}/g;
export const REGEX_DETECT_TOKEN_SUPPORT = /\${([^}]+)\}/g;

export const NumberRegex = /^(?:-(?:[1-9](?:\d{0,2}(?:,\d{3})+|\d*))|(?:0|(?:[1-9](?:\d{0,2}(?:,\d{3})+|\d*))))(?:.\d+|)$/;

export const DRAWER_NAME_CACHE = {
  JOURNEY_DETAIL: 'journey-detail',
  DASHBOARD_REPORT: 'dashboard-report',
  DO_DETAIL: 'data-object-detail',
  COLLECTION_DETAIL: 'collection-detail',
  PROMOTION_POOL_CREATE: 'promotion-pool-create',
  PROMOTION_POOL_DETAIL: 'promotion-pool-detail',
  PROMOTION_POOL_UPLOAD: 'promotion-pool-upload',
  PROMOTION_INTEGRATION_SELECT_CONNECTOR:
    'promotion-integration-select-connector',
  PROMOTION_INTEGRATION_CONFIG_CONNECTOR:
    'promotion-integration-config-connector',
  PROMOTION_INTEGRATION_DETAIL: 'promotion-integration-detail',
  SEGMENT: 'segment-workspace',
  SHORT_LINKS_VENDOR_CREATE_SELECT: 'short-links-vendor-create-select',
  SHORT_LINKS_SHORTENER_CREATE_SELECT: 'short-links-shortener-create-select',
  SHORT_LINKS_VENDOR_CREATE_CONFIG: 'short-links-vendor-create-config',
  SHORT_LINKS_VENDOR_DETAIL: 'short-links-vendor-detail',
  SHORT_LINKS_SHORTENER_DETAIL: 'short-links-shortener-detail',
  CHANNEL_INTEGRATION: 'channel-integration-workspace',
  DRAWER_QUICK_TEST: 'drawer-quick-test',
  DRAWER_SCHEDULE_RESUME: 'drawer-schedule-resume',
  UPLOADS: 'uploads',
  UPLOADS_V2: 'uploads-v2',
  JOURNEY_CREATE: 'journey-create',
  UNSUBSCRIBED: 'unsubscribed',
};
