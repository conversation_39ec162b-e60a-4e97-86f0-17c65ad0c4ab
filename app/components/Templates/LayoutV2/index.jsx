/* eslint-disable indent */
// Libraries
import { get, merge } from 'lodash';
import React, { useCallback, useRef } from 'react';
import { Layout } from '@antscorp/antsomi-ui';
import ForecastResult from 'containers/ForecastResult';
import PropTypes from 'prop-types';
import { updateValue } from '../../../redux/actions';
import { useDispatch, useSelector } from 'react-redux';
import { getCurrentOwnerId } from '../../../utils/web/cookie';
import { makeSelectTmpOwnerId } from '../../../modules/Dashboard/selector';
import { isDetailDrawer } from '../../../modules/Dashboard/MarketingHub/Journey/utils';

export const LayoutV2 = props => {
  const { children, headerProps, ...restProps } = props;
  const dispatch = useDispatch();

  const tmpOwnerId = useSelector(makeSelectTmpOwnerId());

  const forecastResultRef = useRef(null);

  const onSelectAccount = accountId => {
    dispatch(updateValue('@@DASHBOARD_SET_OWNER_ID_FROM_DATA@@', accountId));
  };

  const defaultHeaderProps = {
    accountSelection: {
      onSelectAccount,
      // currentAccount: isDetailDrawer() && tmpOwnerId,
    },
  };

  const handleCallbackProcessingNotify = useCallback((type, data) => {
    switch (type) {
      case 'forecast_name': {
        const onSetupForecast = get(
          forecastResultRef,
          'current.onSetupForecast',
          () => {},
        );
        const rawData = get(data, 'row', {});

        if (typeof onSetupForecast === 'function') {
          onSetupForecast(rawData);
        }
        break;
      }
      default: {
        break;
      }
    }
  }, []);

  return (
    <Layout
      {...restProps}
      headerProps={merge(defaultHeaderProps, headerProps)}
      processingNotificationProps={{
        callback: handleCallbackProcessingNotify,
      }}
    >
      {children}
      <ForecastResult ref={forecastResultRef} />
    </Layout>
  );
};

LayoutV2.propTypes = {
  children: PropTypes.node,
  changeOwnerBreadcrumb: PropTypes.func,
  headerProps: PropTypes.any,
};
