/* eslint-disable prefer-destructuring */
/* eslint-disable no-lonely-if */
/* eslint-disable prefer-const */
/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable no-shadow */
/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import { useImmer } from 'use-immer';
import Grid from '@material-ui/core/Grid';
import styled from 'styled-components';
import UIDropdownAction from 'components/common/UIDropdownAction';
import {
  UIButton,
  UILoading,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import UITargetSelection from '../../common/UITargetSelection';
import {
  initCluster,
  initState,
  MAP_TRANSLATE,
  mapBackupToState,
  validateDataAudienceSegment,
  LIST_AUDIENCE_TARGET,
  toOnChangeAudience,
} from './utils';

import ModalFlowChartConfirmChangeItemTypeId from '../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/modals/ModalFlowChartConfirmChangeItemTypeId';

const AddAudiences = props => (
  <UIButton
    variant="contained"
    iconName="add"
    theme="outline"
    borderRadius="20px"
    reverse
    style={{ color: '#005eb8', height: '28px' }}
    data-test="include-btn"
    {...props}
  >
    <span className="text-button">{MAP_TRANSLATE.includeAudiences}</span>
  </UIButton>
);

const ExcludeButton = props => (
  <UIButton
    variant="contained"
    iconName="add"
    theme="outline"
    borderRadius="20px"
    reverse
    style={{ color: '#005eb8', height: '28px' }}
    data-test="exclude-btn"
    {...props}
  >
    <span className="text-button">{MAP_TRANSLATE.excludeAudiencesBtn}</span>
  </UIButton>
);
const TargetAudience = props => {
  const [state, setState] = useImmer(initState());
  const { initData = {} } = props;
  const tempInclu = state.sortFilter.indexOf('include'); // kiểm tra filter nào đang đứng đầu
  const tempExclu = state.sortFilter.indexOf('exclude');
  const LIST_AUDIENCES_TARGET =
    parseInt(props.itemTypeId) === -1003
      ? LIST_AUDIENCE_TARGET.filter(
          each => parseInt(each.itemTypeId) === parseInt(props.itemTypeId),
        )
      : LIST_AUDIENCE_TARGET;
  useEffect(() => {
    // console.log('render props.componentId', props.componentId);
    setState(() =>
      // draft.isLoading = true;
      // draft.isInit = true;
      initState(),
    );
  }, [props.componentId]);

  useEffect(() => {
    // Need init data here for init node layout
    const { isInit, backup = {}, currentData = {}, itemTypeId } = initData;
    if (state.isLoading) {
      setState(draft => {
        // moi vao man hinh can loading de init data
        draft.isLoading = false;
        if (isInit) {
          const {
            includeCluster = [],
            excludeCluster = [],
            mapCluster = {},
          } = mapBackupToState(backup, itemTypeId);
          if (includeCluster.length > 0 && excludeCluster.length === 0) {
            draft.sortFilter = ['include'];
          } else if (excludeCluster.length > 0 && includeCluster.length === 0) {
            draft.sortFilter = ['exclude'];
          } else if (includeCluster.length > 0 && excludeCluster.length > 0) {
            if (backup.sortFilter) {
              draft.sortFilter =
                backup.sortFilter.length > 0
                  ? backup.sortFilter
                  : ['include', 'exclude'];
            } else {
              draft.sortFilter = ['include', 'exclude'];
            }
          }
          draft.itemTypeId = itemTypeId;
          draft.includeCluster = includeCluster;
          draft.excludeCluster = excludeCluster;
          draft.mapCluster = mapCluster;
          draft.triggerOut += 1;
        } else {
          const {
            includeCluster = [],
            excludeCluster = [],
            mapCluster = {},
            sortFilter = [],
          } = currentData;
          if (includeCluster.length > 0 && excludeCluster.length === 0) {
            draft.sortFilter = ['include'];
          } else if (excludeCluster.length > 0 && includeCluster.length === 0) {
            draft.sortFilter = ['exclude'];
          } else if (includeCluster.length > 0 && excludeCluster.length > 0) {
            if (sortFilter) {
              draft.sortFilter =
                sortFilter.length > 0 ? sortFilter : ['include', 'exclude'];
            } else {
              draft.sortFilter = ['include', 'exclude'];
            }
          }
          draft.itemTypeId = itemTypeId;
          draft.includeCluster = includeCluster;
          draft.excludeCluster = excludeCluster;
          draft.mapCluster = mapCluster;
        }
        draft.excludeOptions = itemTypeId
          ? LIST_AUDIENCES_TARGET.filter(
              each => parseInt(each.itemTypeId) === parseInt(itemTypeId),
            )
          : LIST_AUDIENCES_TARGET;
        draft.includeOptions = itemTypeId
          ? LIST_AUDIENCES_TARGET.filter(
              each => parseInt(each.itemTypeId) === parseInt(itemTypeId),
            )
          : LIST_AUDIENCES_TARGET;
      });
    }
  }, [state.isLoading]);

  useEffect(() => {
    const data = {
      ...initData,
      isInit: false,
      itemTypeId: state.itemTypeId,
      currentData: {
        includeCluster: state.includeCluster,
        excludeCluster: state.excludeCluster,
        mapCluster: state.mapCluster,
      },
    };
    // props.onChange(data);
    const { status } = validateDataAudienceSegment(data);
    props.callback('CHANGE_IS_VALIDATE_DESTINATION', status);
  }, [state.triggerOut]);

  const onSelectAudienceTypes = value => {
    const { itemTypeId, audienceTypes } = value;
    if (
      props.hasOpenModalConfirm &&
      `${state.itemTypeId}` === '-1007' &&
      `${itemTypeId}` === '-1003'
    ) {
      setState(draft => {
        draft.isOpenModal = true;
        draft.tempItemTypeId = itemTypeId;
        draft.tempAudienceTypes = audienceTypes;
      });
    } else {
      onInclude(value);
    }
  };

  const onInclude = ({ itemTypeId, audienceTypes }) => {
    const cluster = initCluster({
      itemTypeId,
      audienceTypes,
      use: 'include',
    });
    const sortInclude = state.sortFilter;
    sortInclude.push('include');
    setState(draft => {
      draft.sortFilter = sortInclude;
      draft.itemTypeId = itemTypeId;
      draft.includeCluster.push(cluster);
      draft.mapCluster[cluster.id] = cluster;
      draft.excludeOptions = state.includeOptions.filter(
        each => parseInt(each.itemTypeId) === parseInt(itemTypeId),
      );
      draft.triggerOut += 1;
    });
    props.callback('CHANGE_ITEM_TYPE_ID', itemTypeId);
  };

  const onExclude = value => {
    // console.log(value);
    const { itemTypeId, audienceTypes } = value;
    const cluster = initCluster({
      itemTypeId,
      audienceTypes,
      use: 'exclude',
    });
    const sortExclude = state.sortFilter;
    sortExclude.push('exclude');
    setState(draft => {
      draft.sortFilter = sortExclude;
      draft.itemTypeId = itemTypeId;
      draft.excludeCluster.push(cluster);
      draft.mapCluster[cluster.id] = cluster;
      draft.includeOptions = state.excludeOptions.filter(
        each => parseInt(each.itemTypeId) === parseInt(itemTypeId),
      );
      draft.triggerOut += 1;
    });
  };

  const onChangeTargetAudience = dataIn => {
    const { id, data } = dataIn;
    let dataSelectedExclude = { ...state.dataSelectedExclude };
    let dataSelectedInclude = { ...state.dataSelectedInclude };
    let includeCluster = [...state.includeCluster];
    let excludeCluster = [...state.excludeCluster];
    let mapCluster = { ...state.mapCluster };
    const cluster = state.mapCluster[id] || {};
    setState(draft => {
      if (cluster.use === 'include') {
        draft.dataSelectedInclude = data;
        dataSelectedInclude = { ...data };
        const indexOf = state.includeCluster.findIndex(each => each.id === id);
        if (indexOf >= 0) {
          draft.includeCluster[indexOf].initData = data;
          draft.mapCluster[id].initData = data;
          Object.assign(includeCluster[indexOf].initData, data);
          Object.assign(mapCluster[id].initData, data);
        }
      } else if (cluster.use === 'exclude') {
        draft.dataSelectedExclude = data;
        dataSelectedExclude = { ...data };
        const indexOf = state.excludeCluster.findIndex(each => each.id === id);
        if (indexOf >= 0) {
          draft.excludeCluster[indexOf].initData = data;
          draft.mapCluster[id].initData = data;
          Object.assign(excludeCluster[indexOf].initData, data);
          Object.assign(mapCluster[id].initData, data);
        }
      }
      draft.triggerOut += 1;
    });
    const dataCurrent = {
      includeCluster,
      excludeCluster,
      mapCluster,
      sortFilter: state.sortFilter,
    };
    const output = toOnChangeAudience(
      data,
      state.itemTypeId,
      dataSelectedExclude,
      dataSelectedInclude,
      dataCurrent,
      state.sortFilter,
    );
    props.onChange(output);
  };
  const toggleModal = value => {
    setState(draft => {
      draft.isOpenModal = value;
    });
  };

  const callback = (type, value) => {
    switch (type) {
      case 'CONFIRM_RESET_ITEM_TYPE_ID':
        if (value === true) {
          const { tempItemTypeId, tempAudienceTypes } = state;
          onInclude({
            itemTypeId: tempItemTypeId,
            audienceTypes: tempAudienceTypes,
          });
          props.callback('RESET_TRIGGER_TARGET_AUDIENCE', tempItemTypeId);
        }
        break;
      case 'ON_REMOVE_ITEM': {
        const id = value;
        const cluster = state.mapCluster[id] || {};
        let dataSelectedExclude = { ...state.dataSelectedExclude };
        let dataSelectedInclude = { ...state.dataSelectedInclude };
        let includeCluster = [...state.includeCluster];
        let excludeCluster = [...state.excludeCluster];
        let mapCluster = { ...state.mapCluster };
        setState(draft => {
          if (cluster.use === 'include') {
            draft.sortFilter = state.sortFilter.filter(
              item => item !== 'include',
            );
            draft.dataSelectedInclude = {};
            dataSelectedInclude = {};
            const indexOf = state.includeCluster.findIndex(
              each => each.id === id,
            );
            if (indexOf >= 0) {
              draft.includeCluster.splice(indexOf, 1);
              delete draft.mapCluster[id];
              includeCluster = [];
              // Object.assign(includeCluster, []);
              Object.assign(mapCluster[id], {});
            }
            if (state.sortFilter.includes('exclude')) {
              draft.includeOptions = LIST_AUDIENCES_TARGET.filter(
                each =>
                  parseInt(each.itemTypeId) === parseInt(state.itemTypeId),
              );
            } else {
              draft.excludeOptions = LIST_AUDIENCES_TARGET;
              draft.includeOptions = LIST_AUDIENCES_TARGET;
            }
            const dataCurrent = {
              includeCluster,
              excludeCluster,
              mapCluster,
            };

            const output = toOnChangeAudience(
              state.dataSelectedExclude,
              state.itemTypeId,
              dataSelectedExclude,
              dataSelectedInclude,
              dataCurrent,
            );
            props.onChange(output);
          } else if (cluster.use === 'exclude') {
            draft.sortFilter = state.sortFilter.filter(
              item => item !== 'exclude',
            );
            draft.dataSelectedExclude = {};
            dataSelectedExclude = {};
            const indexOf = state.excludeCluster.findIndex(
              each => each.id === id,
            );
            if (indexOf >= 0) {
              draft.excludeCluster.splice(indexOf, 1);
              delete draft.mapCluster[id];
              excludeCluster = [];
              // Object.assign(excludeCluster, []);
              Object.assign(mapCluster[id], {});
            }
            if (state.sortFilter.includes('include')) {
              draft.excludeOptions = LIST_AUDIENCES_TARGET.filter(
                each =>
                  parseInt(each.itemTypeId) === parseInt(state.itemTypeId),
              );
            } else {
              draft.excludeOptions = LIST_AUDIENCES_TARGET;
              draft.includeOptions = LIST_AUDIENCES_TARGET;
            }
            const dataCurrent = {
              includeCluster,
              excludeCluster,
              mapCluster,
            };

            const output = toOnChangeAudience(
              state.dataSelectedInclude,
              state.itemTypeId,
              dataSelectedExclude,
              dataSelectedInclude,
              dataCurrent,
            );
            props.onChange(output);
          }
          draft.triggerOut += 1;
        });
        break;
      }
      default:
        break;
    }
  };
  // render Include và Exclude
  const renderIncludeExclude = (
    cluster,
    cludeOptions,
    cludeButton,
    onClude,
    labelButton,
    temp,
    audienType,
  ) => (
    <>
      <WrapperDisable disabled={props.disabled}>
        {cluster.length === 0 && cludeOptions.length > 1 ? (
          <UIDropdownAction
            PopoverAnchor={cludeButton}
            options={cludeOptions}
            callback={onClude}
          />
        ) : (
          !state.sortFilter.includes(`${audienType}`) && (
            <UIButton
              onClick={() => onClude(cludeOptions[0])}
              variant="contained"
              iconName="add"
              theme="outline"
              borderRadius="20px"
              reverse
              style={{ color: '#005eb8', height: '28px' }}
              data-test={`${audienType}-btn`}
              {...props}
            >
              <span className="text-button">{labelButton}</span>
            </UIButton>
          )
        )}
      </WrapperDisable>
      {cluster.map(item => (
        <>
          {state.sortFilter.includes(`${audienType}`) &&
            temp === 0 &&
            !props.isViewMode && (
              <div
                style={{
                  marginTop: '10px',
                  marginBottom: '25px',
                  color: '#595959',
                  fontSize: '12px',
                }}
              >
                {MAP_TRANSLATE.andAlso}
              </div>
            )}
          <div key={`${item.key}`}>
            <UITargetSelection
              key={`${item.key}`}
              id={item.id}
              titleSelect={item.titleSelect}
              titleHeader={item.titleHeader}
              // item={item}
              itemTypeId={item.itemTypeId}
              audienceTypes={item.audienceTypes}
              onChange={onChangeTargetAudience}
              initData={item.initData}
              validateKey={props.validateKey}
              componentId={props.componentId}
              callback={callback}
              isViewMode={props.isViewMode}
              use={item.use}
            />
          </div>
        </>
      ))}
    </>
  );

  // console.log(state);

  const itemAudiencesCheck = item => {
    if (item === 'include') {
      return renderIncludeExclude(
        state.includeCluster,
        state.includeOptions,
        AddAudiences,
        onSelectAudienceTypes,
        MAP_TRANSLATE.includeAudiences,
        tempExclu,
        'include',
      );
    } else {
      return renderIncludeExclude(
        state.excludeCluster,
        state.excludeOptions,
        ExcludeButton,
        onExclude,
        MAP_TRANSLATE.excludeAudiences,
        tempInclu,
        'exclude',
      );
    }
  };

  const renderAudiences = () => {
    // console.log('render', state.includeCluster, state.isLoading);
    if (state.isLoading) {
      return (
        <div className="d-flex pos-relative">
          <UILoading isLoading />
        </div>
      );
    }
    const result = [];
    if (state.sortFilter.length === 0) {
      result.push(
        <div className="conidition-type-btn-group">
          {itemAudiencesCheck('include')}
          {itemAudiencesCheck('exclude')}
        </div>,
      );
    }
    state.sortFilter.forEach((item, index) => {
      if (index === 0) {
        result.push(
          <>
            <Grid item xs={10} lg={12} data-test={`${item}-block`}>
              {itemAudiencesCheck(item)}
            </Grid>
            {!state.sortFilter.includes(
              item === 'exclude' ? 'include' : 'exclude',
            ) ? (
              !props.isViewMode && (
                <div
                  className="wrapper-center and-audiences"
                  style={{ marginTop: '0.75rem' }}
                  data-test={
                    item === 'exclude' ? 'include-block' : 'exclude-block'
                  }
                >
                  <div style={{ marginRight: '1.25rem' }}>
                    {MAP_TRANSLATE.andAudiences}
                  </div>
                  {itemAudiencesCheck(
                    item === 'exclude' ? 'include' : 'exclude',
                  )}
                </div>
              )
            ) : (
              <Grid
                item
                xs={10}
                lg={12}
                data-test={
                  item === 'exclude' ? 'include-block' : 'exclude-block'
                }
              >
                {itemAudiencesCheck(item === 'exclude' ? 'include' : 'exclude')}
              </Grid>
            )}
          </>,
        );
      }
    });
    return result;
  };
  return (
    <>
      <TargetAudienceFilterWrapper data-test="target-audience-filter">
        <Grid container>{renderAudiences()}</Grid>
        <ModalFlowChartConfirmChangeItemTypeId
          isOpen={state.isOpenModal}
          toggle={toggleModal}
          callback={callback}
        />
      </TargetAudienceFilterWrapper>
    </>
  );
};

const TargetAudienceFilterWrapper = styled.div`
  .conidition-type-btn-group button,
  .and-audiences button {
    font-size: 0.75rem;
  }

  .conidition-type-btn-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
    .text-button {
      color: #005eb8;
      font-size: 12px;
    }
  }
`;

TargetAudience.defaultProps = {
  initData: {},
  onChange: () => {},
  callback: () => {},
  isShowFullOption: true,
  disabled: false,
  hasOpenModalConfirm: false,
};

export default TargetAudience;
