import { cloneDeep, isEmpty } from 'lodash';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { safeParse } from '../../../utils/common';
import { SEGMENT_STATUS } from '../../common/constant';
import {
  MAP_OPTIONS_SELECT_SEGMENTS,
  MAP_CONDITION_TYPE_SEGMENT,
  OPTIONS_SELECT_SEGMENTS,
} from '../../common/UITargetSelection/utils';
import { validateSegment } from '../../../utils/validate';
// import { useFetchAudiences, useFetchSegment } from './useFetchData';

export const labelTargetVisitor = getTranslateMessage(
  TRANSLATE_KEY._STORY_AUDIENCE_VISITOR,
  'Target to Visitors',
);
export const labelTargetCustomer = getTranslateMessage(
  TRANSLATE_KEY._STORY_AUDIENCE_CUSTOMER,
  'Target to Customers',
);
export const MAP_TRANSLATE = {
  addAudiences: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_MORE, `Add more`),
  addMore: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_MORE, `Add more`),
  excludeAudiencesBtn: getTranslateMessage(
    TRANSLATE_KEY._TITL_EXCLUDED_AUDIENCES,
    `Exclude Audiences`,
  ),
  andAlso: getTranslateMessage(
    TRANSLATE_KEY._USER_GUIDE_AND_ALSO,
    `and also...`,
  ),
  includeAudiences: getTranslateMessage(
    TRANSLATE_KEY._TITL_INCLUDED_AUDIENCES,
    'Include Audiences',
  ),
  excludeAudiences: getTranslateMessage(
    TRANSLATE_KEY._TITL_EXCLUDED_AUDIENCES,
    'Exclude Audiences',
  ),
  visitorSegment: getTranslateMessage(
    TRANSLATE_KEY._TITL_VISITOR_SEG,
    'Visitor segments',
  ),
  customerSegment: getTranslateMessage(
    TRANSLATE_KEY._TITL_CUSTOMER_SEG,
    'Customer segments',
  ),
  visitorSpecific: getTranslateMessage(
    TRANSLATE_KEY._TITL_SPECIFIC_VISITOR,
    'Specific visitors',
  ),
  customerSpecific: getTranslateMessage(
    TRANSLATE_KEY._TITL_SPECIFIC_CUSTOMER,
    'Specific customers',
  ),
  atleast1Segment: getTranslateMessage(
    TRANSLATE_KEY._NOTI_SELECT_LEAST_ONE_OPTION,
    'Must select at least one option.',
  ),
  visitorId: getTranslateMessage(TRANSLATE_KEY._TITL_VISITOR_ID, 'Visitor ID'),
  customerName: getTranslateMessage(
    TRANSLATE_KEY._MENU_SUB_CUSTOMER,
    'Customer',
  ),
  validateSchedule: getTranslateMessage(
    TRANSLATE_KEY._,
    'Must select at least one option.',
  ),
};

export const MAP_AUDIENCE_TARGET = {
  '-1003@@segmentAudiences': {
    value: '-1003@@segmentAudiences',
    itemTypeId: -1003,
    audienceTypes: 'segmentAudiences',
    label: MAP_TRANSLATE.customerSegment,
    disabled: false,
  },
  '-1003@@specificAudiences': {
    value: '-1003@@specificAudiences',
    itemTypeId: -1003,
    audienceTypes: 'specificAudiences',
    label: MAP_TRANSLATE.customerSpecific,
    disabled: false,
  },
  '-1007@@segmentAudiences': {
    value: '-1007@@segmentAudiences',
    itemTypeId: -1007,
    audienceTypes: 'segmentAudiences',
    label: MAP_TRANSLATE.visitorSegment,
    disabled: false,
  },
  '-1007@@specificAudiences': {
    value: '-1007@@specificAudiences',
    itemTypeId: -1007,
    audienceTypes: 'specificAudiences',
    label: MAP_TRANSLATE.visitorSpecific,
    disabled: false,
  },
};

export const LIST_AUDIENCE_TARGET = Object.values(MAP_AUDIENCE_TARGET);

export const initState = () => ({
  itemTypeId: '-1007',
  includeCluster: [],
  excludeCluster: [],
  mapCluster: {},
  tempItemTypeId: 0,
  tempAudienceTypes: '',
  isOpenModal: false,
  includeOptions: LIST_AUDIENCE_TARGET,
  excludeOptions: [],
  triggerOut: 0,
  isInit: true,
  errors: [],
  componentId: '',
});

const MAP_LABEL_BY_ITEM_TYPE_ID = {
  '-1007@@segmentAudiences': MAP_TRANSLATE.visitorSegment,
  '-1007@@specificAudiences': MAP_TRANSLATE.visitorId,
  '-1003@@segmentAudiences': MAP_TRANSLATE.customerSegment,
  '-1003@@specificAudiences': MAP_TRANSLATE.customerName,
};

const MAP_LABEL_BY_ITEM_TYPE_ID2 = {
  '-1007@@segmentAudiences': 'Segments',
  '-1007@@specificAudiences': MAP_TRANSLATE.visitorId,
  '-1003@@segmentAudiences': 'Segments',
  '-1003@@specificAudiences': MAP_TRANSLATE.customerName,
};

export const initCluster = ({
  itemTypeId,
  audienceTypes,
  use = 'include',
  viewId,
  viewObject,
}) => {
  const data = {
    itemTypeId,
    audienceTypes,
    key: `${use}${itemTypeId}@@${audienceTypes}`,
    id: `${use}${itemTypeId}@@${audienceTypes}`,
    use,
    initData: {
      isInit: true,
      audienceIds: [],
      listAudience: [],
      dataTypeBelongToSegment: MAP_OPTIONS_SELECT_SEGMENTS.includes,
      viewId,
      viewObject,
    },
    titleSelect:
      MAP_LABEL_BY_ITEM_TYPE_ID[`${itemTypeId}@@${audienceTypes}`] || '',
    titleSelectRight: `Selected ${MAP_LABEL_BY_ITEM_TYPE_ID2[
      `${itemTypeId}@@${audienceTypes}`
    ] || ''}`,
    titleHeader:
      use === 'include'
        ? MAP_TRANSLATE.includeAudiences.toUpperCase()
        : MAP_TRANSLATE.excludeAudiences.toUpperCase(),
  };

  return data;
};

export const validateDataAudienceSegment = data => {
  let isValidate = false;
  const { currentData = {} } = data;
  const { includeCluster = [], excludeCluster = [] } = currentData;

  const cluster = [...includeCluster, ...excludeCluster];

  if (cluster.length === 0) {
    isValidate = false;
  } else {
    isValidate = cluster.every(item => {
      const { initData = {}, audienceTypes } = item;
      const { audienceIds = [], listAudience = [] } = initData;

      if (audienceIds.length <= 0) return false;

      if (
        audienceTypes === 'segmentAudiences' &&
        !listAudience.every(validateSegment)
      ) {
        return false;
      }

      return true;
    });
  }

  return {
    status: isValidate,
    errors: isValidate ? [] : [MAP_TRANSLATE.atleast1Segment],
  };
};

export const toEntryFE = data => {
  const validateData = safeParse(data, {});
  // console.log(
  //   '🚀 ~ TargetAudienceMulti ~ toEntryFE ~ validateData:',
  //   validateData,
  // );
  const {
    itemTypeId = -1003,
    excludedAudiences = {},
    includedAudiences = {},
    viewObject,
  } = validateData;
  const dataToUI = {
    itemTypeId,
    isInit: true,
    backup: {},
    currentData: {},
  };

  if (Object.keys(validateData).length === 0) {
    return dataToUI;
  }
  dataToUI.isInit = true;
  dataToUI.backup = {
    includedAudiences,
    excludedAudiences,
    viewObject,
  };
  return dataToUI;
};

export const toEntryAPI = (data, operator) => {
  const { itemTypeId, isInit, backup, currentData = {} } = data;
  // console.log('🚀 ~ TargetAudienceMulti ~ toEntryAPI ~ data:', data);
  let targetData = {
    includedAudiences: {
      filters: { OR: [] },
      audienceTypes: [],
      specificAudienceIds: [],
      viewId: backup?.includedAudiences?.viewId,
      viewObject: backup?.includedAudiences?.viewObject
        ? { ...backup?.includedAudiences?.viewObject }
        : undefined,
    },
    excludedAudiences: {
      audienceTypes: [],
      filters: { OR: [] },
      specificAudienceIds: [],
    },
  };

  const { excludeCluster = [], includeCluster = [] } = currentData;
  if (!isInit) {
    const mapClusterToAPI = (item, fieldName) => {
      targetData[fieldName].audienceTypes.push(item.audienceTypes);
      if (item.audienceTypes === 'segmentAudiences') {
        const { initData = {} } = item;
        const { listAudience = [], dataTypeBelongToSegment } = initData;
        const dataFilter = toAPIAudienceSegment(
          listAudience,
          dataTypeBelongToSegment.value,
          itemTypeId,
        );
        targetData[fieldName].filters = dataFilter;
      } else if (item.audienceTypes === 'specificAudiences') {
        const { initData = {} } = item;
        const { audienceIds } = initData;

        const viewId = initData.viewId || backup?.includedAudiences?.viewId;
        const viewObject = initData.viewObject
          ? { ...initData.viewObject }
          : { ...backup?.includedAudiences?.viewObject };

        targetData[fieldName].specificAudienceIds = audienceIds;
        if (viewId) {
          targetData[fieldName].viewId = viewId;
        }
        if (viewObject) {
          targetData[fieldName].viewObject = viewObject;
        }
      }
    };
    includeCluster.forEach(each => mapClusterToAPI(each, 'includedAudiences'));
    excludeCluster.forEach(each => mapClusterToAPI(each, 'excludedAudiences'));
  } else {
    targetData = backup;
    const viewId = backup?.includedAudiences?.viewId;
    const viewObject = backup?.includedAudiences?.viewObject;
    if (viewId) {
      targetData.includedAudiences.viewId = viewId;
    }
    if (viewObject) {
      targetData.includedAudiences.viewObject = viewObject;
    }
  }
  const dataToAPI = {
    audienceSegmentType:
      parseInt(itemTypeId) === -1007 ? 'user_segment' : 'customer_segment',
    itemTypeId: parseInt(itemTypeId),
    ...targetData,
  };

  return dataToAPI;
};
export const toAPIAudienceSegment = (
  data = [],
  type = OPTIONS_SELECT_SEGMENTS[0].value,
  itemTypeId = '-1003',
) => {
  if (data.length === 0) {
    return { OR: [{ AND: [] }] };
  }

  const dataFilter = { OR: [] };

  // if (type === OPTIONS_SELECT_SEGMENTS[0].value) {
  if (type === OPTIONS_SELECT_SEGMENTS[1].value) {
    const dataAND = [];
    data.forEach(element => {
      const temp = {
        column: 'segment_ids',
        data_type: 'array_number',
        operator: 'matches',
        feOperator: 'doesnt_include',
        value: [element.value],
        code: element.code,
        conditionType: MAP_CONDITION_TYPE_SEGMENT[itemTypeId],
      };
      dataAND.push(temp);
    });
    dataFilter.OR = [{ AND: dataAND }];
  } else {
    data.forEach(element => {
      const temp = {
        AND: [
          {
            column: 'segment_ids',
            data_type: 'array_number',
            operator: 'matches',
            feOperator: 'includes',
            value: [element.value],
            code: element.code,
            conditionType: MAP_CONDITION_TYPE_SEGMENT[itemTypeId],
          },
        ],
      };
      dataFilter.OR.push(temp);
    });
  }

  return dataFilter;
};

export const mapBackupToState = (backup, itemTypeId, options = {}) => {
  let initClusterFn = options.initClusterFn || initCluster;
  // console.log('🚀 ~ mapBackupToState ~ backup:', backup);

  const data = {
    includeCluster: [],
    excludeCluster: [],
    mapCluster: {},
  };

  const { includedAudiences = {}, excludedAudiences = {} } = backup;

  const mapAPIToCluster = (audiences, use, clusterName) => {
    // console.log(
    //   '🚀 ~ TargetAudienceMulti ~ mapAPIToCluster ~ audiences:',
    //   audiences,
    // );
    const {
      filters = {},
      specificAudienceIds = [],
      viewId,
      viewObject,
    } = audiences;
    if (!isEmpty(filters)) {
      const cluster = initClusterFn({
        itemTypeId,
        audienceTypes: 'segmentAudiences',
        use,
        viewId,
        viewObject,
      });
      const initData = getAudienceFromFilter(filters);
      if (initData.audienceIds.length > 0) {
        cluster.initData = { ...cluster.initData, ...initData };
        data[clusterName].push(cluster);
        data.mapCluster[cluster.id] = cluster;
      }
    }
    if (specificAudienceIds.length > 0) {
      const cluster = initClusterFn({
        itemTypeId,
        audienceTypes: 'specificAudiences',
        use,
        viewId,
        viewObject,
      });
      cluster.initData.audienceIds = specificAudienceIds;
      data[clusterName].push(cluster);
      data.mapCluster[cluster.id] = cluster;
    }
  };
  mapAPIToCluster(includedAudiences, 'include', 'includeCluster');
  mapAPIToCluster(excludedAudiences, 'exclude', 'excludeCluster');

  return data;
};

export function getAudienceFromFilter(filters) {
  let audienceIds = [];
  let feOperator = 'includes';
  let dataTypeBelongToSegment = MAP_OPTIONS_SELECT_SEGMENTS.includes;
  const rulesOR = safeParse(filters.OR, []);
  rulesOR.forEach(ruleOR => {
    ruleOR.AND.forEach(item => {
      if (Array.isArray(item.value)) {
        audienceIds = audienceIds.concat(item.value);
        ({ feOperator = 'includes' } = item);
      }
    });
  });
  dataTypeBelongToSegment = MAP_OPTIONS_SELECT_SEGMENTS[feOperator];
  return { audienceIds, dataTypeBelongToSegment };
}

export const getActiveAudienceTypes = clusters => {
  let result = 'segmentAudiences';
  if (clusters.length) {
    if (clusters.length === 1) {
      result = clusters[0].audienceTypes;
    }
  }
  return result;
};

export const getGroupIdAudiences = (id, itemTypeId) => {
  const groupInclude = [
    `include${itemTypeId}@@segmentAudiences`,
    `include${itemTypeId}@@specificAudiences`,
  ];
  const groupExclude = [
    `exclude${itemTypeId}@@segmentAudiences`,
    `exclude${itemTypeId}@@specificAudiences`,
  ];
  let result = [];

  [groupInclude, groupExclude].forEach(group => {
    if (group.includes(id)) {
      result = group;

      return result;
    }
  });

  return result;
};

export const isSpecificAudiencesCluster = cluster => {
  const { itemTypeId, audienceTypes } = cluster;

  return ['-1003@@specificAudiences', '-1007@@specificAudiences'].some(
    key => key === `${itemTypeId}@@${audienceTypes}`,
  );
};
