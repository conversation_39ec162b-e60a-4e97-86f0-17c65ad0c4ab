/* eslint-disable indent */
/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { useRef, useEffect, useMemo, useState } from 'react';
import { useImmer } from 'use-immer';
import styled from 'styled-components';
import { UIButton } from '@xlab-team/ui-components';
import { FormControlLabel, Radio, RadioGroup } from '@material-ui/core';
import UITargetSelectionV2 from '../../../common/UITargetSelection/UITargetSelectionV2';
import {
  getActiveAudienceTypes,
  getGroupIdAudiences,
  initCluster,
  LIST_AUDIENCE_TARGET,
  MAP_TRANSLATE,
  mapBackupToState,
  validateDataAudienceSegment,
} from '../utils';

import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import UIIconXlab from '../../../common/UIIconXlab';
import { WrapperGroupRadioAudience } from './styled';
import ModalConfirmChangeTargetAudience from './ModalConfirmChangeAudience';
import { Dropdown } from '@antscorp/antsomi-ui';

// CSS ===========================
const WrapperTargetAudience = styled.div`
  .MuiTypography-body1 {
    font-size: 0.813rem;
  }
  position: relative;
  width: 100%;

  .custom-view {
    max-width: 150px;
    margin-right: 20px;
    text-align: end;
    color: #666666;
    white-space: nowrap;

    .MuiListItem-button:nth-child(3n + 1) {
      border-bottom: unset;
    }
  }
`;

const WrapperNotSendTo = styled.div`
  position: absolute;
  color: #666;
  left: -98px;
`;

const initState = () => ({
  itemTypeId: '',
  includeCluster: [],
  excludeCluster: [],
  mapCluster: {},
  tempItemTypeId: 0,
  tempAudienceTypes: '',
  isOpenModal: false,
  includeOptions: LIST_AUDIENCE_TARGET,
  excludeOptions: [],
  triggerOut: 0,
  isInit: true,
  errors: [],
  componentId: '',
});

const TargetAudienceV2 = props => {
  const [state, setState] = useImmer(initState());
  const { initData = {}, errors = [], isShowAlert } = props;
  const cacheInitDataDone = useRef();
  const [isOpen, setIsOpen] = useState({});
  const btnAddTarget = useRef();
  const [openAddTargeting, setOpenAddTargeting] = useState(false);
  const isPreventItemTypeId = !props.isFirstCampaign;
  // const [valueType, setValueType] = useState(+state.itemTypeId);
  // console.log(errors);

  useEffect(() => {
    // Need init data here for init node layout
    const { isInit, backup = {}, currentData = {}, itemTypeId } = initData;
    // console.log('🚀 ~ TargetAudienceMulti ~ useEffect ~ initData:', initData);

    if (!props.itemTypeId) {
      // TH design là update
      props.callback('ON_CHANGE_ITEM_TYPE_ID', { itemTypeId });
    }
    setState(draft => {
      if (isInit) {
        // setValueType(itemTypeId);
        const {
          includeCluster = [],
          excludeCluster = [],
          mapCluster = {},
        } = mapBackupToState(backup, itemTypeId);
        draft.itemTypeId = itemTypeId;
        draft.includeCluster = includeCluster;
        draft.excludeCluster = excludeCluster;
        draft.mapCluster = mapCluster;
        draft.triggerOut += 1;
        draft.activeAudienceInclude = getActiveAudienceTypes(includeCluster);
        draft.activeAudienceExclude = getActiveAudienceTypes(excludeCluster);
      } else {
        const {
          includeCluster = [],
          excludeCluster = [],
          mapCluster = {},
        } = currentData;
        draft.itemTypeId = itemTypeId;
        draft.includeCluster = includeCluster;
        draft.excludeCluster = excludeCluster;
        draft.activeAudienceInclude = getActiveAudienceTypes(includeCluster);
        draft.activeAudienceExclude = getActiveAudienceTypes(excludeCluster);
        draft.mapCluster = mapCluster;
      }
      draft.componentId = props.componentId;
      draft.excludeOptions = state.includeOptions.filter(
        each => parseInt(each.itemTypeId) === parseInt(itemTypeId),
      );
    });
    return () => {
      setState(draft => initState());
    };
  }, [props.componentId]);

  useEffect(() => {
    let data = {
      ...initData,
      isInit: false,
      itemTypeId: state.itemTypeId,
      currentData: {
        includeCluster: state.includeCluster,
        excludeCluster: state.excludeCluster,
        mapCluster: state.mapCluster,
      },
    };

    if (props.isBlastCampaign) {
      data = {
        initDataDone: cacheInitDataDone.current,
        ...data,
      };
    }
    props.onChange(data);
    const { status, errors } = validateDataAudienceSegment(data);
    // console.log(errors);
    props.callback('CHANGE_IS_VALIDATE_DESTINATION', status);
  }, [state.triggerOut]);

  const onInclude = ({ itemTypeId, audienceTypes }) => {
    const cluster = initCluster({
      itemTypeId,
      audienceTypes,
      use: 'include',
    });
    setState(draft => {
      draft.itemTypeId = itemTypeId;
      draft.includeCluster.push(cluster);
      draft.mapCluster[cluster.id] = cluster;
      draft.excludeOptions = state.includeOptions.filter(
        each => parseInt(each.itemTypeId) === parseInt(itemTypeId),
      );
      draft.triggerOut += 1;
    });
    props.callback('CHANGE_ITEM_TYPE_ID', itemTypeId);
  };

  const onHandleConfirmChangeTargetAudience = ({
    itemTypeId,
    audienceTypes,
  }) => {
    const cluster = initCluster({
      itemTypeId,
      audienceTypes,
      use: 'include',
    });
    setState(draft => {
      draft.itemTypeId = itemTypeId;
      draft.includeCluster = [cluster];
      draft.mapCluster = { [cluster.id]: cluster };
      draft.excludeOptions = state.includeOptions.filter(
        each => parseInt(each.itemTypeId) === parseInt(itemTypeId),
      );
      draft.excludeCluster = [];
      draft.triggerOut += 1;
    });
    props.callback('CHANGE_ITEM_TYPE_ID', itemTypeId);
  };

  const onExclude = value => {
    const { itemTypeId, audienceTypes } = value;
    const cluster = initCluster({
      itemTypeId,
      audienceTypes,
      use: 'exclude',
    });
    setState(draft => {
      draft.excludeCluster.push(cluster);
      draft.mapCluster[cluster.id] = cluster;
      draft.triggerOut += 1;
    });
  };

  const onAddMoreInclude = () => {
    const item = state.includeCluster[0] || {};
    const { itemTypeId } = item;
    const audienceTypes =
      item.audienceTypes === 'segmentAudiences'
        ? 'specificAudiences'
        : 'segmentAudiences';
    const cluster = initCluster({
      itemTypeId,
      audienceTypes,
      use: 'include',
    });
    setState(draft => {
      draft.includeCluster.push(cluster);
      draft.mapCluster[cluster.id] = cluster;
      draft.triggerOut += 1;
    });
  };

  const onAddMoreExclude = () => {
    const item = state.excludeCluster[0] || {};
    const { itemTypeId } = item;
    const audienceTypes =
      item.audienceTypes === 'segmentAudiences'
        ? 'specificAudiences'
        : 'segmentAudiences';
    const cluster = initCluster({
      itemTypeId,
      audienceTypes,
      use: 'exclude',
    });
    setState(draft => {
      draft.excludeCluster.push(cluster);
      draft.mapCluster[cluster.id] = cluster;
      draft.triggerOut += 1;
    });
  };

  const onChangeTargetAudience = dataIn => {
    // props.onChange(data);
    const { id, data, initDataDone = false } = dataIn;
    const cluster = state.mapCluster[id] || {};
    cacheInitDataDone.current = initDataDone;
    setState(draft => {
      if (cluster.use === 'include') {
        const indexOf = state.includeCluster.findIndex(each => each.id === id);
        if (indexOf >= 0) {
          if (data) draft.includeCluster[indexOf].initData = data;
          draft.mapCluster[id].initData = data;
        }
      } else if (cluster.use === 'exclude') {
        const indexOf = state.excludeCluster.findIndex(each => each.id === id);
        if (indexOf >= 0) {
          draft.excludeCluster[indexOf].initData = data;
          draft.mapCluster[id].initData = data;
        }
      }
      draft.triggerOut += 1;
    });
  };

  const toggleModal = value => {
    setState(draft => {
      draft.isOpenModal = value;
    });
  };

  const callback = (type, value) => {
    switch (type) {
      case 'CONFIRM_RESET_ITEM_TYPE_ID':
        if (value === true) {
          const { tempItemTypeId, tempAudienceTypes } = state;
          onInclude({
            itemTypeId: tempItemTypeId,
            audienceTypes: tempAudienceTypes,
          });
          props.callback('RESET_TRIGGER_TARGET_AUDIENCE', tempItemTypeId);
        }
        break;
      case 'ON_REMOVE_AUDIENCES': {
        const id = value;
        const groupId = getGroupIdAudiences(id, state.itemTypeId);
        groupId.forEach(id => {
          const cluster = state.mapCluster[id] || {};
          setState(draft => {
            if (cluster.use === 'include') {
              const indexOf = state.includeCluster.findIndex(
                each => each.id === id,
              );
              if (indexOf >= 0) {
                draft.includeCluster = [];
                delete draft.mapCluster[id];
              }
            } else if (cluster.use === 'exclude') {
              const indexOf = state.excludeCluster.findIndex(
                each => each.id === id,
              );
              if (indexOf >= 0) {
                draft.excludeCluster = [];
                delete draft.mapCluster[id];
              }
            }
            draft.triggerOut += 1;
          });
        });
        break;
      }

      case 'ON_REMOVE_ITEM': {
        const id = value;
        const cluster = state.mapCluster[id] || {};
        setState(draft => {
          if (cluster.use === 'include') {
            const indexOf = state.includeCluster.findIndex(
              each => each.id === id,
            );
            if (indexOf >= 0) {
              draft.includeCluster.splice(indexOf, 1);
              delete draft.mapCluster[id];
            }
          } else if (cluster.use === 'exclude') {
            const indexOf = state.excludeCluster.findIndex(
              each => each.id === id,
            );
            if (indexOf >= 0) {
              draft.excludeCluster.splice(indexOf, 1);
              delete draft.mapCluster[id];
            }
          }
          draft.triggerOut += 1;
        });
        break;
      }

      case 'ON_CHANGE_TYPE_INCLUDE':
        if (state.includeCluster.length === 1) {
          onAddMoreInclude();
        }
        setState(draft => {
          draft.activeAudienceInclude = value;
        });
        break;

      case 'ON_CHANGE_TYPE_EXCLUDE':
        if (state.excludeCluster.length === 1) {
          onAddMoreExclude();
        }
        setState(draft => {
          draft.activeAudienceExclude = value;
        });
        break;

      case 'CONFIRM_CHANGE_TARGET_AUDIENCE':
        // setValueType(value);
        props.callback('ON_CHANGE_ITEM_TYPE_ID', {
          itemTypeId: value,
          isResetAudience: true,
        });
        const audienceChange = optionAddTargeting[0].children.find(
          audience =>
            audience.itemTypeId === value &&
            audience.audienceTypes === 'segmentAudiences',
        );
        if (audienceChange) {
          onHandleConfirmChangeTargetAudience(audienceChange);
          setState(draft => {
            draft.activeAudienceInclude = audienceChange.audienceTypes;
          });
        }
        break;

      default:
        break;
    }
  };

  const optionAddTargeting = useMemo(
    () => [
      {
        key: 'includeAudiences',
        label: getTranslateMessage(
          TRANSLATE_KEY._TITL_INCLUDED_AUDIENCES,
          'Include Audiences',
        ),
        disabled: state.includeCluster.length > 0,
        children: props.isFirstCampaign
          ? state.includeOptions
          : state.includeOptions
              .filter(option => option.itemTypeId === props.itemTypeId)
              .map(each => {
                return {
                  key: each.value,
                  label: each.label,
                  disabled: each.disabled,
                  onClick: () =>
                    onClickTargetAudience(each, '0', 'includeAudiences'),
                };
              }),
      },
      {
        key: 'excludeAudiences',
        label: getTranslateMessage(
          TRANSLATE_KEY._TITL_EXCLUDED_AUDIENCES,
          'Exclude Audiences',
        ),
        disabled: !state.includeCluster.length || state.excludeCluster.length,
        children: state.excludeOptions.map(audience => {
          return {
            key: audience.value,
            label: audience.label,
            disabled: audience.disabled,
            onClick: () =>
              onClickTargetAudience(audience, '1', 'excludeAudiences'),
          };
        }),
      },
    ],
    [state, props.isFirstCampaign],
  );

  // Mặc định thểm include cho blast campaign ở channel sms
  useEffect(() => {
    if (state.componentId && !state.includeCluster.length) {
      // mặc định add include customer cho channel sms khi tạo mới
      if (props.isFirstCampaign) {
        const initInclude = state.includeOptions.find(
          option => option.value === '-1003@@segmentAudiences',
        );
        onInclude(initInclude);
        props.callback('ON_CHANGE_ITEM_TYPE_ID', {
          itemTypeId: initInclude.itemTypeId,
        });
      } else if (props.itemTypeId) {
        // sẽ tự động add include tương ứng dựa trên campaign đầu tiên đã chọn là customer hay visitor
        const initInclude = state.includeOptions.find(
          option =>
            option.itemTypeId === props.itemTypeId &&
            option.audienceTypes === 'segmentAudiences',
        );
        onInclude(initInclude);
      }
    }
  }, [state.includeCluster, props.itemTypeId]);

  const onClickTargetAudience = (each, key, type) => {
    switch (type) {
      case 'includeAudiences':
        onInclude(each);
        if (props.isFirstCampaign) {
          props.callback('ON_CHANGE_ITEM_TYPE_ID', {
            itemTypeId: each.itemTypeId,
          });
        }
        // setValueType(each.itemTypeId);
        setState(draft => {
          draft.activeAudienceInclude = each.audienceTypes;
        });
        break;

      case 'excludeAudiences':
        onExclude(each);

        setState(draft => {
          draft.activeAudienceExclude = each.audienceTypes;
        });
        break;

      default:
        break;
    }
    const id = `child-${key}`;
    togglePopover(id, false);
    setOpenAddTargeting(false);
  };

  const togglePopover = (id, isOpen = false) => {
    setIsOpen(prev => ({
      ...prev,
      [id]: isOpen,
    }));
  };

  const onchangeTypeAudience = event => {
    setState(draft => {
      draft.isOpenModal = true;
    });
  };

  if (!state.componentId) {
    return null;
  }
  return (
    <WrapperTargetAudience
      style={{
        position:
          props.isViewMode && props.isBlastCampaign ? 'unset' : 'relative',
      }}
    >
      {!props.isViewMode &&
        (!props.isFirstCampaign || state.includeCluster.length > 0) && (
          <WrapperGroupRadioAudience>
            <RadioGroup
              name="RadiosSelectType"
              value={props.itemTypeId}
              onChange={onchangeTypeAudience}
            >
              <FormControlLabel
                value={-1003}
                className="label-radio-config"
                control={<Radio color="primary" size="small" name="-1003" />}
                label="Customers"
                disabled={isPreventItemTypeId}
              />
              <FormControlLabel
                value={-1007}
                className="label-radio-config"
                control={<Radio color="primary" size="small" name="-1007" />}
                label="Visitors"
                disabled={isPreventItemTypeId}
              />
            </RadioGroup>
          </WrapperGroupRadioAudience>
        )}
      {state.includeCluster
        .filter(
          // khi viewMode thì ko filter
          cluster =>
            props.isViewMode ||
            cluster.audienceTypes === state.activeAudienceInclude,
        )
        .map((item, index) => {
          const isFirst = index === 0;
          const disabledRemove = true;
          return (
            <div
              key={`${item.key}-${state.componentId}`}
              className="m-bottom-5"
              style={{ minWidth: '660px' }}
            >
              {isFirst || (
                <div className="m-top-4 m-bottom-2">
                  {MAP_TRANSLATE.andAlso}
                </div>
              )}
              <UITargetSelectionV2
                activeRow={props.activeRow}
                key={`${item.key}`}
                id={item.id}
                viewObject={initData?.viewObject?.includedAudiences}
                isShowAlert={isShowAlert}
                titleSelect={item.titleSelect}
                titleHeader={item.titleHeader}
                titleSelectRight={item.titleSelectRight}
                // item={item}
                itemTypeId={item.itemTypeId}
                audienceTypes={item.audienceTypes}
                cluster={state.includeCluster}
                onChange={onChangeTargetAudience}
                initData={item.initData}
                validateKey={props.validateKey}
                isBlastCampaign={props.isBlastCampaign}
                dataValidateSwitchTabAudiences={
                  props.dataValidateSwitchTabAudiences
                }
                refreshKeyValidateAudiences={props.refreshKeyValidateAudiences}
                validateKeyBlast={props.validateKeyBlast}
                componentId={state.componentId}
                callback={callback}
                disabledRemove={disabledRemove}
                // isViewMode={true}
                isViewMode={props.isViewMode}
                isHiddentListViewMode
                moduleConfig={props.moduleConfig}
                use={item.use}
              />
            </div>
          );
        })}
      {props.isViewMode && state.excludeCluster.length ? (
        <WrapperNotSendTo
          style={{
            left: props.isBlastCampaign && props.isViewMode ? '0px' : '-98px',
          }}
        >
          <span>
            {getTranslateMessage(
              TRANSLATE_KEY._BLAST_CAMPAIGN_NOT_SEND_TO,
              'Not send to',
            )}
          </span>
        </WrapperNotSendTo>
      ) : null}
      {state.excludeCluster
        .filter(
          // khi viewMode thì ko filter
          cluster =>
            props.isViewMode ||
            cluster.audienceTypes === state.activeAudienceExclude,
        )
        .map((item, index) => {
          const isFirst = index === 0;
          return (
            <div
              key={`${item.key}-${state.componentId}`}
              className="m-bottom-2"
              style={{ minWidth: '660px' }}
            >
              {isFirst || (
                <div className="m-top-4 m-bottom-2">
                  {MAP_TRANSLATE.andAlso}
                </div>
              )}
              <UITargetSelectionV2
                key={`${item.key}`}
                id={item.id}
                titleSelect={item.titleSelect}
                titleHeader={item.titleHeader}
                viewObject={initData?.viewObject?.excludedAudiences}
                // item={item}
                isBlastCampaign={props.isBlastCampaign}
                cluster={state.excludeCluster}
                dataValidateSwitchTabAudiences={
                  props.dataValidateSwitchTabAudiences
                }
                refreshKeyValidateAudiences={props.refreshKeyValidateAudiences}
                itemTypeId={item.itemTypeId}
                audienceTypes={item.audienceTypes}
                onChange={onChangeTargetAudience}
                initData={item.initData}
                validateKey={props.validateKey}
                validateKeyBlast={props.validateKeyBlast}
                componentId={state.componentId}
                callback={callback}
                isViewMode={props.isViewMode}
                isHiddentListViewMode
                moduleConfig={props.moduleConfig}
                use={item.use}
              />
            </div>
          );
        })}
      {!props.isViewMode && state.excludeCluster.length === 0 ? (
        <div ref={btnAddTarget}>
          <Dropdown
            menu={{
              items: optionAddTargeting,
              expandIcon: <UIIconXlab name="arrow-right" fontSize="24px" />,

              inlineIndent: 0,
            }}
            trigger={['click']}
            destroyPopupOnHide
            overlayStyle={{
              width: '160px',
            }}
          >
            <UIButton
              className="m-bottom-1"
              iconName="add"
              theme="outline"
              reverse
              iconSize="24px"
              style={{
                fontSize: '12px',
                marginLeft: '4px',
                color: '#1F5FAC',
                borderRadius: '15px',
                outline: '1px solid #1f5fac',
                padding: '0 1px',
                height: '28px',
              }}
              onClick={() => setOpenAddTargeting(true)}
            >
              <span style={{ color: '#1F5FAC', fontSize: '12px' }}>
                {getTranslateMessage(
                  TRANSLATE_KEY._BLAST_CAMPAIGN_ADD_TARGETING,
                  'Add Targeting',
                )}
              </span>
              <UIIconXlab name="arrow-down" fontSize="24px" />
            </UIButton>
          </Dropdown>
        </div>
      ) : null}
      <ModalConfirmChangeTargetAudience
        isOpen={state.isOpenModal}
        toggle={toggleModal}
        callback={callback}
        itemTypeId={state.itemTypeId}
      />
    </WrapperTargetAudience>
  );
};

TargetAudienceV2.defaultProps = {
  initData: {},
  onChange: () => {},
  callback: () => {},
  isShowFullOption: true,
  isShowAlert: false,
  disabled: false,
  hasOpenModalConfirm: false,
};

export default TargetAudienceV2;
