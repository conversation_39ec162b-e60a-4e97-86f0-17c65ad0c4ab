/* eslint-disable no-param-reassign */
/* eslint-disable indent */
/* eslint-disable import/order */
// Libraries
import React, { useEffect, Fragment, useMemo, useCallback } from 'react';
import _isEmpty from 'lodash/isEmpty';
import classNames from 'classnames';
import _cloneDeep from 'lodash/cloneDeep';
import _isEqual from 'lodash/isEqual';
import _without from 'lodash/without';
import _omit from 'lodash/omit';
import _pick from 'lodash/pick';
import { useImmer } from 'use-immer';
import PropTypes from 'prop-types';

// Translations
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

// Styled
import {
  ContainerTemplate,
  ContainerSlider,
  AddBtnCustom,
  WrapperCenterFlexCenter,
  Title,
  GridContainer,
  Divider,
  CalloutText,
  WarningSMSRules,
  PaddingElement,
} from './styled';

// Components
import SlideTemplate from './_UI/SlideTemplate';
import ErrorBoundary from 'components/common/ErrorBoundary';
// eslint-disable-next-line import/no-cycle
import ButtonItem from './_UI/ButtonItem';
import { FormHelperText, Grid } from '@material-ui/core';
import InputPreview from '../../Atoms/InputPreview';
import UISelect from 'components/form/UISelectCondition';
import {
  UITextField,
  UITippy,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import { UploadImage } from '@antscorp/antsomi-ui';
// eslint-disable-next-line import/no-cycle
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
// eslint-disable-next-line import/no-cycle
import ActionTypeMapItem from './_UI/ActionTypeMapItem';

// Hooks
import { useDeepCompareEffect } from 'hooks/useDeepCompareEffect';

// Constants
import {
  ACTION_OPTIONS,
  ACTION_OPTIONS_V2,
  CONTENT_TYPES,
  GROUP_CODES,
  LIMIT_BUTTONS,
  LIMIT_BUTTONS_V2,
  LIMIT_SLIDE,
  MAPPING_FIELDS,
} from './constants';
import { TEMPLATE_TYPES } from '../../../modules/Dashboard/MarketingHub/Destination/CreateV2/Design/Templates/constants';

// Utils
import { random } from 'components/common/UIEditorPersonalization/utils.3rd';
import {
  getAdjustColumn,
  getInitialState,
  getInitialValidateState,
  getLabelDateTime,
  getLimitByName,
  handleResetAndChangeData,
  handleValidateSlide,
  initialBtnValidate,
  initialButtonItem,
  initialErrorValidate,
  initialSlideDataFactory,
  initialValidateSlidesFactory,
  rebuildValidateSlide,
} from './utils';
import { addMessageToQueue } from '../../../utils/web/queue';
import { getObjectPropSafely, isProduction } from '../../../utils/web/utils';
import { getCurrentUserId, getToken } from '../../../utils/web/cookie';

const PATH = 'app/components/Organisms/LineTemplate/index.jsx';

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';

const MAP_TITLE = {
  image: getTranslateMessage(TRANSLATE_KEY._, 'Image'),
  content: getTranslateMessage(TRANSLATE_KEY._, 'Content'),
  title: getTranslateMessage(TRANSLATE_KEY._, 'Title'),
  defaultAction: getTranslateMessage(
    TRANSLATE_KEY._LINE_DEFAULT_ACT,
    'Default Action',
  ),
  actionType: getTranslateMessage(TRANSLATE_KEY._LINE_ACT_TYPE, 'Action Type'),
  data: getTranslateMessage(TRANSLATE_KEY._, 'Data'),
  maxLengthError: getTranslateMessage(
    TRANSLATE_KEY._,
    'Limit of 120 characters',
  ),
};

function LineTemplate(props) {
  const { value, errors, design, isViewMode, templateId, onChange } = props;

  // States
  const [dataOut, setDataOut] = useImmer(getInitialState(value, templateId));
  const [validateSlides, setValidateSlides] = useImmer(
    getInitialValidateState(value, templateId),
  );

  const isConfirmTemplate = templateId === TEMPLATE_TYPES.CONFIRM;
  const isImgCarouselTempl = templateId === TEMPLATE_TYPES.IMAGE_CAROUSE;
  const isButtonTemplate = templateId === TEMPLATE_TYPES.BUTTONS;

  const isShowSlider = useMemo(
    () =>
      [TEMPLATE_TYPES.CAROUSEL, TEMPLATE_TYPES.IMAGE_CAROUSE].includes(
        templateId,
      ),
    [templateId],
  );

  const slideStaticActiveId = useMemo(
    () => getObjectPropSafely(() => dataOut[CONTENT_TYPES.STATIC].slideActive),
    [
      dataOut && dataOut.contentType,
      getObjectPropSafely(() => dataOut[CONTENT_TYPES.STATIC].slideActive),
    ],
  );

  const slideStaticActiveData = useMemo(
    () =>
      getObjectPropSafely(
        () => dataOut[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId],
      ),
    [
      slideStaticActiveId,
      getObjectPropSafely(() => dataOut[CONTENT_TYPES.STATIC].slideMaps),
    ],
  );

  const defaultErrorInfo = useMemo(
    () =>
      getObjectPropSafely(
        () =>
          validateSlides[dataOut.contentType] &&
          validateSlides[dataOut.contentType][slideStaticActiveId] &&
          validateSlides[dataOut.contentType][slideStaticActiveId][
            MAPPING_FIELDS.DEFAULT
          ],
      ),
    [validateSlides],
  );

  const callbackActionButtons = (type = '', dataIn = '') => {
    try {
      switch (type) {
        case 'ON_CHANGE_DATA_BTN_ITEM': {
          const {
            name: nameTmp = '',
            activeBtnId = '',
            value: valueTmp = '',
          } = dataIn;

          if (_isEmpty(slideStaticActiveId) || _isEmpty(activeBtnId)) return;

          setDataOut(draft => {
            draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
              MAPPING_FIELDS.ACTIONS
            ].buttonMaps[activeBtnId][nameTmp] = valueTmp;
          });

          if (
            validateSlides[dataOut.contentType] &&
            validateSlides[dataOut.contentType][slideStaticActiveId] &&
            validateSlides[dataOut.contentType][slideStaticActiveId][
              activeBtnId
            ]
          ) {
            setValidateSlides(draft => {
              draft[dataOut.contentType][slideStaticActiveId][activeBtnId][
                nameTmp
              ] = initialErrorValidate();
            });
          }
          break;
        }
        case 'ON_CHANGE_DATA_ACTIONS_FIELD': {
          const { name: nameTmp = '', value: valueTmp = '' } = dataIn;

          if (
            _isEmpty(slideStaticActiveId) ||
            _isEmpty(nameTmp) ||
            _isEmpty(
              dataOut[dataOut.contentType].slideMaps[slideStaticActiveId],
            ) ||
            _isEmpty(validateSlides[dataOut.contentType])
          )
            return;

          const { actions = {} } =
            dataOut[dataOut.contentType].slideMaps[slideStaticActiveId] || {};
          const { extraData = {} } =
            validateSlides[dataOut.contentType][slideStaticActiveId] || {};

          setDataOut(draft => {
            if (actions && actions.extraData) {
              draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
                MAPPING_FIELDS.ACTIONS
              ].extraData[nameTmp] = valueTmp;
            }
          });

          if (!_isEmpty(extraData)) {
            setValidateSlides(draft => {
              draft[dataOut.contentType][slideStaticActiveId].extraData[
                nameTmp
              ] = initialErrorValidate();
            });
          }
          break;
        }
        case 'ON_ADD_NEW_BTN': {
          if (_isEmpty(slideStaticActiveId)) return;

          const newBtnId = random(6);

          setDataOut(draft => {
            draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
              MAPPING_FIELDS.ACTIONS
            ].buttonList.push(newBtnId);

            draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
              MAPPING_FIELDS.ACTIONS
            ].buttonMaps[newBtnId] = initialButtonItem();
          });

          if (
            validateSlides[dataOut.contentType] &&
            validateSlides[dataOut.contentType][slideStaticActiveId]
          ) {
            setValidateSlides(draft => {
              draft[dataOut.contentType][slideStaticActiveId][
                newBtnId
              ] = initialBtnValidate();
            });
          }

          break;
        }
        case 'REMOVE_BUTTON_ITEM': {
          if (_isEmpty(slideStaticActiveId)) return;

          const { buttonList = [], buttonMaps = {} } = getObjectPropSafely(
            () =>
              dataOut[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
                MAPPING_FIELDS.ACTIONS
              ],
          );

          if (Array.isArray(buttonList) && !_isEmpty(buttonMaps)) {
            const removedList = _without(buttonList, dataIn);
            const removedMaps = _omit(buttonMaps, [dataIn]);

            setDataOut(draft => {
              draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
                MAPPING_FIELDS.ACTIONS
              ].buttonList = removedList;

              draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
                MAPPING_FIELDS.ACTIONS
              ].buttonMaps = removedMaps;
            });

            if (
              validateSlides[dataOut.contentType] &&
              validateSlides[dataOut.contentType][slideStaticActiveId]
            ) {
              const validateInfo = _cloneDeep(
                validateSlides[dataOut.contentType][slideStaticActiveId],
              );
              const removedValidateInfo = _omit(validateInfo, [dataIn]);

              setValidateSlides(draft => {
                draft[dataOut.contentType][
                  slideStaticActiveId
                ] = removedValidateInfo;
              });
            }
          }

          break;
        }
        case 'ON_RESET_DATA_ACTION_BUTTON': {
          const { id = '', type: typeHandler = '', data = {} } = dataIn;

          const validateInfo =
            validateSlides[dataOut.contentType] &&
            validateSlides[dataOut.contentType][slideStaticActiveId];

          if (!_isEmpty(slideStaticActiveId)) {
            if (typeHandler === 'BUTTON_ITEM') {
              setDataOut(draft => {
                draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
                  MAPPING_FIELDS.ACTIONS
                ].buttonMaps[id] = data;
              });

              // Update validates
              if (!_isEmpty(validateInfo) && !_isEmpty(data)) {
                if (id) {
                  const { format = {} } = data;
                  const btnInfoValidate = validateInfo[id];
                  const arrPicks = [MAPPING_FIELDS.BTN_LABEL];

                  if (!_isEmpty(format)) {
                    arrPicks.push(MAPPING_FIELDS.DATA_DATE_TIME);
                  }

                  const unChangeValidatedList = _pick(
                    btnInfoValidate,
                    arrPicks,
                  );
                  setValidateSlides(draft => {
                    draft[dataOut.contentType][slideStaticActiveId][
                      id
                    ] = unChangeValidatedList;
                  });
                }
              }
            } else if (typeHandler === 'DEFAULT_ACTION') {
              const { actionType = {}, ...restData } = data || {};

              setDataOut(draft => {
                if (
                  dataOut[dataOut.contentType].slideMaps[slideStaticActiveId]
                ) {
                  draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
                    MAPPING_FIELDS.ACTIONS
                  ][MAPPING_FIELDS.DEFAULT] = actionType;
                  draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
                    MAPPING_FIELDS.ACTIONS
                  ].extraData = restData;
                }
              });

              // Reset validate Default Action when Action change in Image Carousel template
              if (
                templateId === TEMPLATE_TYPES.IMAGE_CAROUSE &&
                !_isEmpty(actionType)
              ) {
                setValidateSlides(draft => {
                  draft[dataOut.contentType][slideStaticActiveId][
                    MAPPING_FIELDS.DEFAULT
                  ] = initialErrorValidate();
                });
              }

              // Update validates
              if (!_isEmpty(validateInfo) && !_isEmpty(restData)) {
                const { format = {} } = restData;
                const validateExtraData = validateInfo.extraData || {};
                const arrPicks = [];

                if (!_isEmpty(format)) {
                  arrPicks.push(MAPPING_FIELDS.DATA_DATE_TIME);
                }

                const unChangeValidatedList = _pick(
                  validateExtraData,
                  arrPicks,
                );

                setValidateSlides(draft => {
                  draft[dataOut.contentType][slideStaticActiveId].extraData =
                    unChangeValidatedList || {};
                });
              }
            }
          }
          break;
        }
        default: {
          break;
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'callbackActionButtons',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const callbackSlider = (type = '', dataIn = '') => {
    try {
      switch (type) {
        case 'UPDATE_SLIDE_LIST': {
          setDataOut(draft => {
            draft[CONTENT_TYPES.STATIC].slideList = dataIn;
          });
          break;
        }
        case 'ACTIVE_SLIDE': {
          const { slideId = '' } = dataIn;

          if (!_isEmpty(slideId)) {
            const { slideMaps = {} } = getObjectPropSafely(
              () => dataOut[CONTENT_TYPES.STATIC],
            );
            const selectedSlideInfo = _cloneDeep(
              slideMaps[slideStaticActiveId],
            );

            const isValidSlide = handleValidateSlide({
              setValidateSlides,
              templateId,
              slideActiveId: slideStaticActiveId,
              slideInfo: selectedSlideInfo,
              contentType: dataOut.contentType,
            });

            if (isValidSlide) {
              setDataOut(draft => {
                draft[CONTENT_TYPES.STATIC].slideActive = slideId;
              });
            }
          }
          break;
        }
        case 'DUPLICATE_SLIDE': {
          const { slideId = '' } = dataIn;

          if (!_isEmpty(slideId)) {
            const { slideMaps = {} } = getObjectPropSafely(
              () => dataOut[CONTENT_TYPES.STATIC],
            );
            const selectedSlideInfo = _cloneDeep(slideMaps[slideId]);

            const isValidSlide = handleValidateSlide({
              setValidateSlides,
              templateId,
              slideActiveId: slideId,
              slideInfo: selectedSlideInfo,
              contentType: dataOut.contentType,
            });

            if (isValidSlide && !_isEmpty(selectedSlideInfo)) {
              const newSlideId = random(6);

              setDataOut(draft => {
                draft[CONTENT_TYPES.STATIC].slideList.push({
                  slideId: newSlideId,
                });
                draft[CONTENT_TYPES.STATIC].slideMaps[
                  newSlideId
                ] = selectedSlideInfo;
                draft[CONTENT_TYPES.STATIC].slideActive = newSlideId;
              });

              const snapInfoValidate = _cloneDeep(
                validateSlides[dataOut.contentType][slideId],
              );
              setValidateSlides(draft => {
                draft[dataOut.contentType][newSlideId] = snapInfoValidate;
              });
            }
          }
          break;
        }
        case 'DELETE_SLIDE': {
          const { slideId = '' } = dataIn;

          if (!_isEmpty(slideId)) {
            const list = _cloneDeep(
              getObjectPropSafely(
                () => dataOut[CONTENT_TYPES.STATIC].slideList,
              ),
            );
            const maps = _cloneDeep(
              getObjectPropSafely(
                () => dataOut[CONTENT_TYPES.STATIC].slideMaps,
              ),
            );
            const slideActive = getObjectPropSafely(
              () => dataOut[CONTENT_TYPES.slideActive],
            );

            if (Array.isArray(list) && !_isEmpty(maps)) {
              const listFiltered = list.filter(
                each => each.slideId !== slideId,
              );
              const snapInfoValidate = _cloneDeep(
                validateSlides[dataOut.contentType],
              );

              const removedMaps = _omit(maps, [slideId]);
              const removedInfoValidate = _omit(snapInfoValidate, [slideId]);

              setDataOut(draft => {
                draft[CONTENT_TYPES.STATIC].slideList = listFiltered;
                draft[CONTENT_TYPES.STATIC].slideMaps = removedMaps;
                draft[CONTENT_TYPES.STATIC].slideActive =
                  listFiltered[0].slideId !== slideActive
                    ? listFiltered[0].slideId
                    : slideActive;
              });
              setValidateSlides(draft => {
                draft[dataOut.contentType] = removedInfoValidate;
              });
            }
          }

          break;
        }
        default: {
          break;
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'callbackSlider',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const handleAddNewSlide = () => {
    try {
      const list = _cloneDeep(
        getObjectPropSafely(() => dataOut[CONTENT_TYPES.STATIC].slideList, []),
      );
      const maps = _cloneDeep(
        getObjectPropSafely(() => dataOut[CONTENT_TYPES.STATIC].slideMaps, {}),
      );

      if (Array.isArray(list)) {
        const newSlideId = random(6);
        const newBtnId = random(6);

        list.push({ slideId: newSlideId });
        maps[newSlideId] = initialSlideDataFactory(newBtnId, templateId);

        setDataOut(draft => {
          draft[CONTENT_TYPES.STATIC].slideList = list;
          draft[CONTENT_TYPES.STATIC].slideMaps = maps;
        });
        setValidateSlides(draft => {
          draft[dataOut.contentType][newSlideId] = initialValidateSlidesFactory(
            newBtnId,
            templateId,
          );
        });
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleAddNewSlide',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const handleChangeDataStaticCommon = (
    mappingName = '',
    dataIn = '',
    isCommonActionFields = false,
    isExtraData = false,
  ) => {
    try {
      if (_isEmpty(slideStaticActiveId)) return;

      if (
        validateSlides[dataOut.contentType] &&
        validateSlides[dataOut.contentType][slideStaticActiveId]
      ) {
        setValidateSlides(draft => {
          draft[dataOut.contentType][slideStaticActiveId][
            mappingName
          ] = initialErrorValidate();
        });
      }

      if (isCommonActionFields) {
        setDataOut(draft => {
          draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
            MAPPING_FIELDS.ACTIONS
          ][mappingName] = dataIn;
        });
      } else if (isExtraData) {
        setDataOut(draft => {
          draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
            MAPPING_FIELDS.ACTIONS
          ].extraData[mappingName] = dataIn;
        });
      } else {
        setDataOut(draft => {
          draft[CONTENT_TYPES.STATIC].slideMaps[slideStaticActiveId][
            mappingName
          ] = dataIn;
        });
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleChangeDataStaticCommon',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const getErrorInfo = useCallback(
    ({ isExtraData = false, mappingField = '', buttonId = '' }) => {
      if (_isEmpty(slideStaticActiveId)) return {};

      return getObjectPropSafely(() => {
        if (
          validateSlides[dataOut.contentType] &&
          validateSlides[dataOut.contentType][slideStaticActiveId]
        ) {
          const { extraData = {}, ...restValidated } = validateSlides[
            dataOut.contentType
          ][slideStaticActiveId];

          if (isExtraData && !_isEmpty(extraData)) {
            return extraData[mappingField] || {};
          }

          if (!_isEmpty(buttonId) && !_isEmpty(restValidated[buttonId])) {
            return restValidated[buttonId][mappingField] || {};
          }

          return restValidated[mappingField] || {};
        }

        return {};
      });
    },
    [validateSlides, dataOut.contentType, slideStaticActiveId],
  );

  const getErrMsgByType = useCallback(
    ({ type = 'array', isExtraData = false, mappingField = '', buttonId }) => {
      const { isError = true, errorMessage = '' } = getErrorInfo({
        isExtraData,
        mappingField,
        buttonId,
      });

      if (type === 'string') {
        return isError ? errorMessage : undefined;
      }

      return isError && errorMessage ? [errorMessage] : [];
    },
    [getErrorInfo],
  );

  useEffect(() => {
    if (!_isEmpty(errors) && Array.isArray(errors)) {
      const [slideErrorId] = errors || [];

      // setDataOut(draft => {
      //   draft[dataOut.contentType].slideActive = slideErrorId;
      // });

      const slideInfo = getObjectPropSafely(
        () => dataOut[dataOut.contentType].slideMaps[slideErrorId],
      );

      handleValidateSlide({
        buttonId: '',
        slideInfo,
        templateId,
        setValidateSlides,
        contentType: dataOut.contentType,
        slideActiveId: slideErrorId,
      });
    }
  }, [errors]);

  // Refill initial validate data
  useEffect(() => {
    if (!_isEmpty(dataOut) && _isEmpty(validateSlides)) {
      const { contentType = '' } = dataOut;

      if (contentType === CONTENT_TYPES.STATIC) {
        const { slideList = [], slideMaps = {} } = dataOut[contentType];

        if (Array.isArray(slideList)) {
          const slideListInit = {};

          slideList.forEach(eachSlide => {
            const slideItem = slideMaps[eachSlide.slideId];
            slideListInit[eachSlide.slideId] = {};

            const { actions = {} } = slideItem;
            const { buttonList = [] } = actions;

            if (Array.isArray(buttonList)) {
              buttonList.forEach(eachBtn => {
                slideListInit[eachSlide.slideId][eachBtn] = {};
              });
            }
          });

          setValidateSlides(draft => {
            draft[CONTENT_TYPES.STATIC] = slideListInit;
            draft[CONTENT_TYPES.RECOMMENDATION] = {};
          });

          slideList.forEach(eachSlide => {
            const slideItem = slideMaps[eachSlide.slideId];

            rebuildValidateSlide({
              contentType,
              slideId: eachSlide.slideId,
              setValidateSlides,
              slideInfo: slideItem,
            });
          });
        }
      }
    }
  }, [design, validateSlides && validateSlides[dataOut.contentType], dataOut]);

  // Callback to sync data out
  useDeepCompareEffect(() => {
    if (!_isEmpty(dataOut) && !_isEqual(dataOut, value)) {
      onChange(dataOut);
    }
  }, [dataOut]);

  // Sync data when change active variant tab
  useDeepCompareEffect(() => {
    if (!_isEqual(value, dataOut) && !_isEmpty(value)) {
      setDataOut(() => value);
      setValidateSlides(() => ({}));
    }
  }, [value]);

  // Reset data
  useEffect(
    () => () => {
      setDataOut(() => getInitialState(value, templateId));
      setValidateSlides(() => getInitialValidateState(value, templateId));
    },
    [],
  );

  const renderError = (
    mappingField = '',
    valueField = {},
    isHideWarningLimit = false,
    buttonId = '',
    isExtraData = false,
  ) => {
    if (_isEmpty(slideStaticActiveId)) return null;

    const limitNumber = getLimitByName(mappingField, templateId);
    let txtLength = 0;

    if (typeof valueField === 'string') {
      txtLength = valueField.length;
    }

    const { isError = true, errorMessage = '' } = getErrorInfo({
      isExtraData,
      mappingField,
      buttonId,
    });

    return (
      <WarningSMSRules
        width="100%"
        style={{ marginTop: isHideWarningLimit ? 0 : 4 }}
      >
        <div className="d-flex align-items-center">
          <FormHelperText
            id="component-helper-text"
            error={isError}
            style={{ width: '100%' }}
          >
            {errorMessage}
          </FormHelperText>
          {!isHideWarningLimit && (
            <Fragment>
              <UITippy
                content={MAP_TITLE.maxLengthError.replace('120', limitNumber)}
              >
                <InfoOutlinedIcon className="icon-info-rule" />
              </UITippy>

              <p
                className="text-rule"
                style={{
                  whiteSpace: 'nowrap',
                  margin: 0,
                }}
              >
                {txtLength}/{limitNumber} characters
              </p>
            </Fragment>
          )}
        </div>
      </WarningSMSRules>
    );
  };

  const renderButtonList = (buttonList = []) => {
    if (_isEmpty(buttonList) || !Array.isArray(buttonList)) return null;

    const item = getObjectPropSafely(
      () => slideStaticActiveData[MAPPING_FIELDS.ACTIONS].buttonMaps,
    );

    return buttonList.map((eachBtnId, indexBtn) => (
      <ButtonItem
        key={eachBtnId}
        label={`Button ${indexBtn + 1}`}
        id={eachBtnId}
        isLimited={
          (templateId === TEMPLATE_TYPES.BUTTONS
            ? LIMIT_BUTTONS_V2
            : LIMIT_BUTTONS) === buttonList.length
        }
        isShowRemoveIcon={indexBtn > 0 && !isConfirmTemplate}
        isHideAddBtn={isConfirmTemplate}
        isLastItem={indexBtn === buttonList.length - 1}
        isViewMode={isViewMode}
        item={item[eachBtnId] || {}}
        validateButtonInfo={
          validateSlides &&
          validateSlides[dataOut.contentType] &&
          validateSlides[dataOut.contentType][slideStaticActiveId] &&
          validateSlides[dataOut.contentType][slideStaticActiveId][eachBtnId]
        }
        getErrMsgByType={getErrMsgByType}
        renderError={renderError}
        callback={callbackActionButtons}
      />
    ));
  };

  const renderStaticActionsSection = () => {
    if (
      _isEmpty(dataOut[CONTENT_TYPES.STATIC]) ||
      _isEmpty(slideStaticActiveData)
    )
      return null;

    const defaultAction = getObjectPropSafely(
      () =>
        slideStaticActiveData[MAPPING_FIELDS.ACTIONS][MAPPING_FIELDS.DEFAULT],
    );
    const { extraData = {} } = getObjectPropSafely(
      () => slideStaticActiveData[MAPPING_FIELDS.ACTIONS],
    );

    return (
      <ErrorBoundary path={PATH}>
        {!isImgCarouselTempl && (
          <Title style={{ fontWeight: '700' }}>
            {getTranslateMessage(TRANSLATE_KEY._, 'Actions')}
            <span style={{ color: '#ff0000' }}> *</span>
          </Title>
        )}
        {!isConfirmTemplate && (
          <Fragment>
            <GridContainer container style={{ paddingBottom: 0 }}>
              <Grid item sm={3} className={getAdjustColumn('left')}>
                <WrapperCenterFlexCenter>
                  <Title>
                    {isImgCarouselTempl ? (
                      <Fragment>
                        {MAP_TITLE.actionType}
                        <span style={{ color: '#ff0000' }}> *</span>
                      </Fragment>
                    ) : (
                      MAP_TITLE.defaultAction
                    )}
                  </Title>
                  {isImgCarouselTempl &&
                    defaultErrorInfo &&
                    defaultErrorInfo.isError && <PaddingElement />}
                </WrapperCenterFlexCenter>
              </Grid>
              <Grid item sm={9} className={getAdjustColumn('right')}>
                <InputPreview
                  isViewMode={isViewMode}
                  type="input"
                  value={defaultAction && defaultAction.value}
                >
                  <UISelect
                    onlyParent
                    use="tree"
                    isSearchable={false}
                    options={
                      isImgCarouselTempl ? ACTION_OPTIONS_V2 : ACTION_OPTIONS
                    }
                    value={defaultAction}
                    onChange={valueOut =>
                      handleResetAndChangeData({
                        type: 'DEFAULT_ACTION',
                        dataIn: valueOut,
                        infoData: extraData,
                        callback: callbackActionButtons,
                        nameFieldChange: MAPPING_FIELDS.DEFAULT,
                      })
                    }
                    errors={
                      isImgCarouselTempl
                        ? getErrMsgByType({
                            mappingField: MAPPING_FIELDS.DEFAULT,
                          })
                        : []
                    }
                    placeholder="Choose an action type"
                    placeholderTranslateCode="_"
                    fullWidthPopover
                    labelWidth="100%"
                  />
                  {isImgCarouselTempl &&
                    renderError(
                      MAPPING_FIELDS.DEFAULT,
                      slideStaticActiveData[MAPPING_FIELDS.DEFAULT],
                      true,
                    )}
                </InputPreview>
              </Grid>
              {!isImgCarouselTempl && (
                <CalloutText>
                  Action happens when image, title or text area is tapped
                </CalloutText>
              )}
            </GridContainer>
            <ActionTypeMapItem
              buttonId={null}
              isViewMode={isViewMode}
              infoData={{ ...(extraData || {}), actionType: defaultAction }}
              typeHandler="DEFAULT_ACTION"
              handleChangeDataItem={callbackActionButtons}
              getErrMsgByType={getErrMsgByType}
              renderError={renderError}
              callback={callbackActionButtons}
              getLabelDateTime={getLabelDateTime}
              actionTypeActive={defaultAction && defaultAction.value}
            />
          </Fragment>
        )}
        {!isImgCarouselTempl &&
          renderButtonList(
            getObjectPropSafely(
              () => slideStaticActiveData[MAPPING_FIELDS.ACTIONS].buttonList,
            ),
          )}
      </ErrorBoundary>
    );
  };

  const renderStatic = () => {
    if (
      _isEmpty(dataOut[CONTENT_TYPES.STATIC]) ||
      _isEmpty(slideStaticActiveData)
    )
      return null;

    // Constants
    const userId = getCurrentUserId();
    const token = getToken();

    return (
      <ErrorBoundary path={PATH}>
        {isShowSlider && (
          <ContainerSlider>
            <SlideTemplate
              slideActive={dataOut[CONTENT_TYPES.STATIC].slideActive}
              slideList={dataOut[CONTENT_TYPES.STATIC].slideList}
              isViewMode={isViewMode}
              errors={errors}
              callback={callbackSlider}
            />
            {!isViewMode && (
              <AddBtnCustom
                iconName="add"
                iconSize="24px"
                theme="primary"
                height={30}
                disabled={
                  (dataOut[CONTENT_TYPES.STATIC].slideList &&
                    dataOut[CONTENT_TYPES.STATIC].slideList.length ===
                      LIMIT_SLIDE.MAX) ||
                  isViewMode
                }
                borderRadius="50%"
                isBorderRadius
                onClick={handleAddNewSlide}
              />
            )}
          </ContainerSlider>
        )}
        {!isConfirmTemplate && (
          <Fragment>
            <GridContainer
              container
              style={{ marginTop: isShowSlider ? 10 : 0 }}
            >
              <Grid item sm={3} className={getAdjustColumn('left')}>
                <WrapperCenterFlexCenter>
                  <Title>
                    {MAP_TITLE.image}
                    {!isButtonTemplate && (
                      <span style={{ color: '#ff0000' }}> *</span>
                    )}
                  </Title>
                  {validateSlides &&
                    validateSlides[dataOut.contentType] &&
                    validateSlides[dataOut.contentType][slideStaticActiveId] &&
                    validateSlides[dataOut.contentType][slideStaticActiveId][
                      MAPPING_FIELDS.IMAGE_URL
                    ] &&
                    validateSlides[dataOut.contentType][slideStaticActiveId][
                      MAPPING_FIELDS.IMAGE_URL
                    ].isError && <PaddingElement />}
                </WrapperCenterFlexCenter>
              </Grid>
              <Grid item sm={9} className={getAdjustColumn('right')}>
                <InputPreview
                  value={slideStaticActiveData[MAPPING_FIELDS.IMAGE_URL]}
                  isViewMode={isViewMode}
                  type="input"
                  className="pd-left-5"
                >
                  <UploadImage
                    isInputMode
                    domainMedia={
                      isProduction() ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX
                    }
                    slug="api/v1"
                    width="100%"
                    paramConfigs={{
                      token,
                      userId,
                      accountId: userId,
                    }}
                    selectedImage={{
                      url: slideStaticActiveData[MAPPING_FIELDS.IMAGE_URL],
                    }}
                    onChangeImage={image => {
                      handleChangeDataStaticCommon(
                        MAPPING_FIELDS.IMAGE_URL,
                        image && image.url,
                      );
                    }}
                    onRemoveImage={() => {
                      handleChangeDataStaticCommon(
                        MAPPING_FIELDS.IMAGE_URL,
                        '',
                      );
                    }}
                  />
                  {renderError(
                    MAPPING_FIELDS.IMAGE_URL,
                    slideStaticActiveData[MAPPING_FIELDS.IMAGE_URL],
                    true,
                  )}
                </InputPreview>
              </Grid>
            </GridContainer>
            {isImgCarouselTempl ? (
              <GridContainer container style={{ paddingBottom: 0 }}>
                <Grid item sm={3} className={getAdjustColumn('left')}>
                  <WrapperCenterFlexCenter>
                    <Title>
                      {getTranslateMessage(
                        TRANSLATE_KEY._LINE_BUTTON_LABEL,
                        'Button Label',
                      )}
                    </Title>
                    <PaddingElement />
                  </WrapperCenterFlexCenter>
                </Grid>
                <Grid item sm={9} className={getAdjustColumn('right')}>
                  <InputPreview
                    value={slideStaticActiveData[MAPPING_FIELDS.BTN_LABEL]}
                    type="input"
                    isViewMode={isViewMode}
                  >
                    <UITextField
                      value={slideStaticActiveData[MAPPING_FIELDS.BTN_LABEL]}
                      onChange={valueOut =>
                        handleChangeDataStaticCommon(
                          MAPPING_FIELDS.BTN_LABEL,
                          valueOut,
                        )
                      }
                      textFieldProps={{
                        disabled: false,
                        size: 'small',
                        multiline: false,
                        rowsMax: 1,
                        className: classNames({
                          'input-text-field-error': !!getErrMsgByType({
                            type: 'string',
                            mappingField: MAPPING_FIELDS.BTN_LABEL,
                          }),
                        }),
                        style: {
                          width: '100%',
                        },
                      }}
                    />
                    {renderError(
                      MAPPING_FIELDS.BTN_LABEL,
                      slideStaticActiveData[MAPPING_FIELDS.BTN_LABEL],
                    )}
                  </InputPreview>
                </Grid>
              </GridContainer>
            ) : (
              <GridContainer container>
                <Grid item sm={3} className={getAdjustColumn('left')}>
                  <WrapperCenterFlexCenter>
                    <Title>
                      {MAP_TITLE.title}
                      {!isButtonTemplate && (
                        <span style={{ color: '#ff0000' }}> *</span>
                      )}
                    </Title>
                    <PaddingElement />
                  </WrapperCenterFlexCenter>
                </Grid>
                <Grid
                  item
                  sm={9}
                  className={getAdjustColumn('right')}
                  style={{ position: 'relative' }}
                >
                  <InputPreview
                    value={slideStaticActiveData[MAPPING_FIELDS.TITLE]}
                    type="input"
                    isViewMode={isViewMode}
                  >
                    <WrapperDisable disabled={false}>
                      <TinymceEditor
                        {...props}
                        groupCodes={GROUP_CODES}
                        errors={getErrMsgByType({
                          mappingField: MAPPING_FIELDS.TITLE,
                        })}
                        typeComponent="input"
                        onChange={valueOut =>
                          handleChangeDataStaticCommon(
                            MAPPING_FIELDS.TITLE,
                            valueOut,
                          )
                        }
                        initData={slideStaticActiveData[MAPPING_FIELDS.TITLE]}
                        enableShortLink={false}
                        width="100%"
                        isBlastCampaign={false}
                      />
                      {renderError(
                        MAPPING_FIELDS.TITLE,
                        slideStaticActiveData[MAPPING_FIELDS.TITLE],
                      )}
                    </WrapperDisable>
                  </InputPreview>
                </Grid>
              </GridContainer>
            )}
          </Fragment>
        )}

        {!isImgCarouselTempl && (
          <Fragment>
            <GridContainer container>
              <Grid item sm={3} className={getAdjustColumn('left')}>
                <WrapperCenterFlexCenter>
                  <Title>
                    {MAP_TITLE.content}
                    <span style={{ color: '#ff0000' }}> *</span>
                  </Title>
                  <PaddingElement />
                </WrapperCenterFlexCenter>
              </Grid>
              <Grid
                item
                sm={9}
                className={getAdjustColumn('right')}
                style={{ position: 'relative' }}
              >
                <InputPreview
                  value={slideStaticActiveData[MAPPING_FIELDS.CONTENT]}
                  type="input"
                  isViewMode={isViewMode}
                >
                  <WrapperDisable disabled={false}>
                    <TinymceEditor
                      {...props}
                      errors={getErrMsgByType({
                        mappingField: MAPPING_FIELDS.CONTENT,
                      })}
                      groupCodes={GROUP_CODES}
                      typeComponent="input"
                      onChange={valueOut =>
                        handleChangeDataStaticCommon(
                          MAPPING_FIELDS.CONTENT,
                          valueOut,
                        )
                      }
                      initData={slideStaticActiveData[MAPPING_FIELDS.CONTENT]}
                      enableShortLink
                      width="100%"
                      isBlastCampaign={false}
                    />
                    {renderError(
                      MAPPING_FIELDS.CONTENT,
                      slideStaticActiveData[MAPPING_FIELDS.CONTENT],
                    )}
                  </WrapperDisable>
                </InputPreview>
              </Grid>
            </GridContainer>
            <Divider style={{ margin: '15px 0px' }} />
          </Fragment>
        )}
        {renderStaticActionsSection()}
      </ErrorBoundary>
    );
  };

  const renderTemplateByContentType = () =>
    ({
      [CONTENT_TYPES.STATIC]: renderStatic(),
    }[dataOut && dataOut.contentType]);

  return (
    <ErrorBoundary path={PATH}>
      <ContainerTemplate>{renderTemplateByContentType()}</ContainerTemplate>
    </ErrorBoundary>
  );
}

LineTemplate.propTypes = {
  isViewMode: PropTypes.bool,
  errors: PropTypes.array,
  templateId: PropTypes.oneOf(
    TEMPLATE_TYPES.BUTTONS,
    TEMPLATE_TYPES.CAROUSEL,
    TEMPLATE_TYPES.IMAGE_CAROUSE,
    TEMPLATE_TYPES.CONFIRM,
  ),
  design: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.object, PropTypes.string]).isRequired,
  onChange: PropTypes.func.isRequired,
};
LineTemplate.defaultProps = {
  errors: [],
  isViewMode: false,
  templateId: TEMPLATE_TYPES.CAROUSEL,
  design: 'create',
};
export default LineTemplate;
