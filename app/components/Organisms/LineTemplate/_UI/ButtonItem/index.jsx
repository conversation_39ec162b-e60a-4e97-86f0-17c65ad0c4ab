/* eslint-disable indent */
/* eslint-disable import/order */
// Libraries
import React, { Fragment, memo } from 'react';
import classNames from 'classnames';
import _isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';

// Translations
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import Typography from '@material-ui/core/Typography';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { Button, Icon } from '@antscorp/antsomi-ui';
import { UIButton, UITextField } from '@xlab-team/ui-components';
import { Grid } from '@material-ui/core';
import InputPreview from '../../../../Atoms/InputPreview';
import UISelect from 'components/form/UISelectCondition';
// eslint-disable-next-line import/no-cycle
import ActionTypeMapItem from '../ActionTypeMapItem';

// Styled
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  WrapperCenterFlexCenter,
  WrapperIcon,
  useStyles,
} from './styled';
import { GridContainer, PaddingElement, Title } from '../../styled';

// Constants
import { ACTION_OPTIONS_V2, MAPPING_FIELDS } from '../../constants';

// Utils
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { getLabelDateTime, handleResetAndChangeData } from '../../utils';

const PATH = 'app/components/Organisms/LineTemplate/_UI/ButtonItem/index.jsx';

function ButtonItem(props) {
  const {
    label,
    id,
    isLastItem,
    isLimited,
    isShowRemoveIcon,
    isHideAddBtn,
    item,
    isViewMode,
    renderError,
    getErrMsgByType,
    validateButtonInfo,
    callback,
  } = props;

  const classes = useStyles();
  const isShowAddBtn = isLastItem && !isLimited && !isViewMode && !isHideAddBtn;

  const handleChangeDataItem = (type = '', dataIn = '') => {
    try {
      if (typeof callback === 'function') {
        callback(type, { ...(dataIn || {}), activeBtnId: id });
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleChangeDataItem',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const handleRemoveBtnItem = (event, btnId = '') => {
    try {
      event.stopPropagation();

      if (typeof callback === 'function') {
        callback('REMOVE_BUTTON_ITEM', btnId);
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'handleRemoveBtnItem',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  if (_isEmpty(item)) return null;

  const renderContentButton = () => (
    <Fragment>
      <GridContainer container>
        <Grid item sm={3}>
          <WrapperCenterFlexCenter>
            <Title>
              {getTranslateMessage(
                TRANSLATE_KEY._LINE_BUTTON_LABEL,
                'Button Label',
              )}
              <span style={{ color: '#ff0000' }}> *</span>
            </Title>
            <PaddingElement />
          </WrapperCenterFlexCenter>
        </Grid>
        <Grid item sm={9}>
          <InputPreview
            value={item[MAPPING_FIELDS.BTN_LABEL]}
            type="input"
            isViewMode={isViewMode}
          >
            <UITextField
              value={item[MAPPING_FIELDS.BTN_LABEL]}
              onChange={valueOut =>
                handleChangeDataItem('ON_CHANGE_DATA_BTN_ITEM', {
                  name: MAPPING_FIELDS.BTN_LABEL,
                  value: valueOut,
                })
              }
              // firstText={props.errors[0]}
              // maxLength={20}
              textFieldProps={{
                disabled: false,
                size: 'small',
                multiline: false,
                rowsMax: 1,
                className: classNames({
                  'input-text-field-error': !!getErrMsgByType({
                    type: 'string',
                    mappingField: MAPPING_FIELDS.BTN_LABEL,
                    buttonId: id,
                  }),
                }),
                style: {
                  width: '100%',
                },
              }}
            />
            {renderError(
              MAPPING_FIELDS.BTN_LABEL,
              item[MAPPING_FIELDS.BTN_LABEL],
              false,
              id,
            )}
          </InputPreview>
        </Grid>
      </GridContainer>
      <GridContainer container style={{ paddingBottom: 0, paddingTop: 0 }}>
        <Grid item sm={3}>
          <WrapperCenterFlexCenter>
            <Title>
              {getTranslateMessage(TRANSLATE_KEY._LINE_ACT_TYPE, 'Action Type')}
              <span style={{ color: '#ff0000' }}> *</span>
            </Title>

            {validateButtonInfo &&
              validateButtonInfo[MAPPING_FIELDS.ACTION_TYPE] &&
              validateButtonInfo[MAPPING_FIELDS.ACTION_TYPE].isError && (
                <PaddingElement />
              )}
          </WrapperCenterFlexCenter>
        </Grid>
        <Grid item sm={9}>
          <InputPreview
            isViewMode={isViewMode}
            type="input"
            value={
              item[MAPPING_FIELDS.ACTION_TYPE] &&
              item[MAPPING_FIELDS.ACTION_TYPE].value
            }
          >
            <UISelect
              onlyParent
              use="tree"
              isSearchable={false}
              errors={getErrMsgByType({
                mappingField: MAPPING_FIELDS.ACTION_TYPE,
                buttonId: id,
              })}
              options={ACTION_OPTIONS_V2}
              value={item[MAPPING_FIELDS.ACTION_TYPE]}
              onChange={valueOut => {
                handleResetAndChangeData({
                  type: 'BUTTON_ITEM',
                  callback,
                  buttonId: id,
                  dataIn: valueOut,
                  infoData: item,
                  nameFieldChange: MAPPING_FIELDS.ACTION_TYPE,
                });
              }}
              placeholder="Choose an action type"
              placeholderTranslateCode="_"
              fullWidthPopover
              labelWidth="100%"
            />
            {renderError(
              MAPPING_FIELDS.ACTION_TYPE,
              item[MAPPING_FIELDS.ACTION_TYPE],
              true,
              id,
            )}
          </InputPreview>
        </Grid>
      </GridContainer>
      <ActionTypeMapItem
        buttonId={id}
        typeHandler="BUTTON_ITEM"
        isViewMode={isViewMode}
        infoData={item}
        handleChangeDataItem={handleChangeDataItem}
        renderError={renderError}
        getErrMsgByType={getErrMsgByType}
        getLabelDateTime={getLabelDateTime}
        callback={callback}
        actionTypeActive={
          (item[MAPPING_FIELDS.ACTION_TYPE] &&
            item[MAPPING_FIELDS.ACTION_TYPE].value) ||
          ''
        }
      />
    </Fragment>
  );
  return (
    <ErrorBoundary path={PATH}>
      <div className={classes.root} style={{ margin: '10px 0px' }}>
        <Accordion defaultExpanded>
          <AccordionSummary
            id={id}
            expandIcon={<ExpandMoreIcon />}
            aria-controls="panel1a-content"
          >
            <Typography className={classes.heading}>{label}</Typography>
            {isShowRemoveIcon && !isViewMode && (
              <WrapperIcon>
                <Button
                  type="text"
                  icon={
                    <Icon
                      type="icon-ants-remove-slim"
                      size={12}
                      color="#005fb8"
                    />
                  }
                  onClick={e => handleRemoveBtnItem(e, id)}
                />
              </WrapperIcon>
            )}
          </AccordionSummary>
          <AccordionDetails>{renderContentButton()}</AccordionDetails>
        </Accordion>
        {isShowAddBtn && (
          <UIButton
            iconName="add"
            iconSize="24px"
            height={30}
            reverse
            theme="outline"
            style={{ marginTop: 10 }}
            disabled={isViewMode}
            onClick={() =>
              typeof callback === 'function' && callback('ON_ADD_NEW_BTN')
            }
          >
            Add Button
          </UIButton>
        )}
      </div>
    </ErrorBoundary>
  );
}

ButtonItem.propTypes = {
  id: PropTypes.string.isRequired,
  item: PropTypes.object.isRequired,
  isLimited: PropTypes.bool,
  validateButtonInfo: PropTypes.object,
  isShowRemoveIcon: PropTypes.bool,
  isLastItem: PropTypes.bool,
  isHideAddBtn: PropTypes.bool,
  label: PropTypes.string,
  isViewMode: PropTypes.bool,
  getErrMsgByType: PropTypes.func,
  renderError: PropTypes.func,
  callback: PropTypes.func,
};
ButtonItem.defaultProps = {
  label: 'Button 1',
  isViewMode: false,
  isShowRemoveIcon: false,
  isHideAddBtn: false,
  validateButtonInfo: {},
  isLimited: false,
  isLastItem: false,
  getErrMsgByType: () => [],
  renderError: () => {},
  callback: () => {},
};
export default memo(ButtonItem);
