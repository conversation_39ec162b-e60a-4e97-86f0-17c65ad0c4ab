/* eslint-disable import/order */
/* eslint-disable prefer-destructuring */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
// Libraries
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { useImmer } from 'use-immer';
import {
  cloneDeep,
  difference,
  get,
  has,
  isEmpty,
  isEqual,
  isObject,
  omit,
  take,
} from 'lodash';
import { original } from 'immer';

// Hooks
import { useDeepCompareEffect } from 'hooks/useDeepCompareEffect';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import { Grid, Popover } from '@material-ui/core';
import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';
import EditIcon from '@material-ui/icons/Edit';
import IconXlab from 'components/common/UIIconXlab';
import RadioGroup from '../../Molecules/RadioGroup';
import InputFieldRendering from './InputFieldRendering';

// Styled
import {
  ButtonAddItem,
  ButtonAddStyled,
  ContainerRendering,
  MarkRequired,
  BoxItem,
  Content,
  Title,
  WrapperContentList,
  ItemActive,
  CircleBox,
  WrapperIcon,
  ActionList,
  ActionItem,
} from './styled';

// Translations
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

// Constants
import { EXCLUDE_INIT_DEFAULT, LIMIT_LIST, SENDING_TYPES } from './constants';
import {
  TEMPLATES,
  TEMPLATE_TYPES,
} from '../../../modules/Dashboard/MarketingHub/Destination/CreateV2/Design/Templates/constants';
import { STICKER_MAPPINGS } from '../../Molecules/Sticker/constants';
import { SNAPSHOT_RENDER_FIELD } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/constants';

// Utils
import {
  SENDING_OPTIONS,
  getValidateFields,
  handleResetDepends,
  initDefaultDestinationInput,
  initialData,
  mapSettingsToFE,
  validateSetting,
} from './utils';
import { addMessageToQueue } from '../../../utils/web/queue';
import { generateKey, getObjectPropSafely } from '../../../utils/common';
import {
  checkIsHiddenInputComponent,
  reUpdateDataByCatalog,
} from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils';
import { pickBroadcastDestDelivery } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.variant';

const MAP_TRANSLATE = {
  sendType: getTranslateMessage(TRANSLATE_KEY._, 'Sending Type'),
  duplicate: getTranslateMessage(TRANSLATE_KEY._, 'Duplicate'),
  delete: getTranslateMessage(TRANSLATE_KEY._, 'Delete'),
};

const PATH = 'app/components/Organism/DestinationSnapRender/index.jsx';

function DestinationSnapRender(props) {
  const {
    initValue,
    errors,
    classes,
    dynamicFields,
    destinationInputs,
    selectionStartIndex,
    validateKey,
    refreshComponentKey,
    design,
    variants,
    variantExtraData,
    objectWidgetInput,
    isRequired,
    disabled,
    isViewMode,
    channelElement,
    catalogCode,
    step,
    setIsFetchData,
    configure,
    setSelectionStartIndex,
    isBlastCampaign,
    itemTypeId,
    destinationSettings,
    selectedTemplate,
    activeNode,
    componentId,
    observerActiveIndex,
    callback: callbackParent,
    onChange,
  } = props;

  const [data, setData] = useImmer(initialData());
  const [enableScrollAction, setEnableScrollAction] = useState(true);
  const [anchorEl, setAnchorEl] = useState(null);

  const isMulti = useMemo(() => data.type === SENDING_TYPES.MULTI, [data.type]);
  const stickerSet = useMemo(
    () =>
      getObjectPropSafely(
        () =>
          data.map[data.activeId].stickerType &&
          data.map[data.activeId].stickerType.value,
      ),
    [data.map[data.activeId], data.activeId],
  );
  const templateActive = useMemo(
    () =>
      getObjectPropSafely(
        () =>
          data.map[data.activeId].template &&
          data.map[data.activeId].template.value,
      ),
    [data.map[data.activeId], data.activeId],
  );

  const handleAnchorPopup = event => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  const callback = (type = '', dataIn = {}) => {
    try {
      switch (type) {
        case 'ADD_ITEM': {
          if (data.list.length >= LIMIT_LIST) break;

          const newId = generateKey();
          const newDataDestinationInputs = cloneDeep(
            data.cacheDestinationInputs,
          );

          if (has(newDataDestinationInputs, 'template')) {
            newDataDestinationInputs.template = {
              ...(newDataDestinationInputs.template || {}),
              initValue: {},
              value: {},
            };
          }

          setData(draft => {
            draft.list.push(newId);
            draft.map[newId] = newDataDestinationInputs;
          });
          break;
        }
        case 'ACTIVE_ITEM': {
          const { itemId = '' } = dataIn;
          const destInputs = data.map[data.activeId];
          let activeTemplate = getObjectPropSafely(
            () => destInputs.template.value,
          );

          if (typeof activeTemplate === 'object') {
            activeTemplate = activeTemplate.value;
          }

          const validateFields = getValidateFields({
            activeTemplate,
            dynamicFields: data.cacheDynamicFields,
            destinationInputs: destInputs,
          });

          if (!validateFields.includes('template')) {
            validateFields.push('template');
          }

          const isValidate = validateSetting({
            activeId: data.activeId,
            dynamicFields: validateFields,
            destinationInputs: destInputs,
            catalogCode,
            channelCode: channelElement,
            setData,
          });

          if (isValidate) {
            setEnableScrollAction(true);
            setData(draft => {
              draft.activeId = itemId;
            });
          }

          break;
        }
        case 'DUPLICATE_ITEM': {
          const { itemId = '' } = dataIn;

          if (!itemId || data.list.length >= LIMIT_LIST) break;

          const destInputs = data.map[itemId];
          let activeTemplate = getObjectPropSafely(
            () => destInputs.template.value,
          );

          if (typeof activeTemplate === 'object') {
            activeTemplate = activeTemplate.value;
          }

          const validateFields = getValidateFields({
            activeTemplate,
            dynamicFields: data.cacheDynamicFields,
            destinationInputs: destInputs,
          });

          if (!validateFields.includes('template')) {
            validateFields.push('template');
          }

          const isValidate = validateSetting({
            setData,
            catalogCode,
            channelCode: channelElement,
            activeId: itemId,
            dynamicFields: validateFields,
            destinationInputs: destInputs,
          });

          if (isValidate) {
            const cloneConfigs = cloneDeep(data.map[itemId]);
            const newId = generateKey();

            setData(draft => {
              draft.list.push(newId);
              draft.map[newId] = cloneConfigs;
            });
          }

          handleClose();
          break;
        }
        case 'REMOVE_ITEM': {
          const { itemId = '' } = dataIn;

          if (!itemId || data.list.length <= 1) break;

          const newList = difference(data.list, [itemId]);
          const newMap = omit(data.map, [itemId]);

          setData(draft => {
            draft.list = newList;
            draft.map = newMap;
            draft.activeId = newList[0];
          });
          handleClose();
          break;
        }
        default: {
          break;
        }
      }
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'callback',
        data: { err: err.stack, type, dataIn },
      });
    }
  };

  const handleChangeData = useCallback(
    name => value => {
      setData(draft => {
        const originalData = original(draft);

        // reset data depending on the action
        handleResetDepends({
          activeId: originalData.activeId,
          name,
          value,
          setData,
        });

        if (has(originalData.map, [originalData.activeId, name])) {
          draft.map[originalData.activeId][name].initValue = value;
          draft.map[originalData.activeId][name].value = value;
          draft.map[originalData.activeId][name].errors = [];
        }
      });
    },
    [],
  );

  const handleChangeTypeSending = (type = '') => {
    try {
      if (type) {
        if (type === SENDING_TYPES.SINGLE) {
          const firstKey = data.list[0];
          const firstItem = cloneDeep(data.map[firstKey]);

          setData(draft => {
            draft.list = [firstKey];
            draft.map = {
              [firstKey]: firstItem,
            };
            draft.activeId = firstKey;
          });
        }

        setData(draft => {
          draft.type = type;
        });
      }
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'handleChangeTypeSending',
        data: {
          err: err.stack,
          data: type,
        },
      });
    }
  };

  useEffect(() => {
    if (typeof observerActiveIndex === 'number') {
      const newActiveId = getObjectPropSafely(
        () => data.list[observerActiveIndex],
      );

      if (newActiveId) {
        setData(draft => {
          draft.activeId = newActiveId;
        });
        setEnableScrollAction(false); // NOTE: set false tránh việc loop scroll khi active new id từ scroll effect khác
      }
    }
  }, [observerActiveIndex]);

  // Initial destination inputs configs
  useDeepCompareEffect(() => {
    if (
      !isEmpty(destinationInputs) &&
      !isEqual(destinationInputs, data.cacheDestinationInputs)
    ) {
      let clonedDestinationInputs = cloneDeep(destinationInputs);

      if (
        has(destinationInputs, 'template') &&
        isEmpty(destinationInputs.template.value)
      ) {
        if (!isEmpty(selectedTemplate)) {
          const selectedTemplateValue = isObject(selectedTemplate)
            ? selectedTemplate.value
            : selectedTemplate;

          let initTemplate = get(clonedDestinationInputs, [
            'template',
            'mapOptions',
            selectedTemplateValue,
          ]);
          if (!initTemplate) {
            initTemplate = selectedTemplate;
          }

          clonedDestinationInputs.template = {
            ...(clonedDestinationInputs.template || {}),
            initValue: initTemplate,
            value: initTemplate,
          };
        } else {
          const itemTemplate = getObjectPropSafely(
            () => destinationInputs.template.options,
          );

          if (Array.isArray(itemTemplate) && itemTemplate.length > 0) {
            clonedDestinationInputs.template = {
              ...(clonedDestinationInputs.template || {}),
              initValue: itemTemplate[0],
              value: itemTemplate[0],
            };
          }
        }
      }
      const newId = generateKey();
      let dynamicFieldsTemp = cloneDeep(dynamicFields);

      if (isEmpty(dynamicFields)) {
        dynamicFieldsTemp = Object.keys(clonedDestinationInputs).filter(
          each => each !== SNAPSHOT_RENDER_FIELD,
        );
      }

      clonedDestinationInputs = initDefaultDestinationInput({
        dynamicFields: difference(dynamicFieldsTemp, EXCLUDE_INIT_DEFAULT),
        destinationInputs: clonedDestinationInputs,
      });

      setData(draft => {
        draft.type = SENDING_TYPES.SINGLE;
        draft.activeId = newId;
        draft.list = [newId];
        draft.map = {
          [newId]: clonedDestinationInputs,
        };
        draft.cacheDestinationInputs = clonedDestinationInputs;
        draft.cacheDynamicFields = dynamicFieldsTemp;
      });

      if (typeof callbackParent === 'function') {
        callbackParent('UPDATE_DYNAMIC_FIELDS', {
          validateConfigs: {
            dynamicFields: dynamicFieldsTemp,
            destinationInputs: clonedDestinationInputs,
          },
        }); // reset dynamic field to only get data for line
      }
    }
  }, [variants && variants.activeId]);

  // Re-map data with initValue
  useEffect(() => {
    if (
      isObject(initValue) &&
      Object.keys(initValue).length > 0 &&
      !isEmpty(data.cacheDynamicFields) &&
      !isEmpty(data.cacheDestinationInputs)
    ) {
      const { type = '', data: initData = [] } = initValue;
      const { cacheDynamicFields = [], cacheDestinationInputs = {} } = data;
      const map = {};
      const genIdList = [];

      initData.forEach(eachData => {
        const newId = generateKey();

        genIdList.push(newId);
        map[newId] = mapSettingsToFE({
          data: eachData,
          dynamicFields: cacheDynamicFields,
          destinationInputs: cacheDestinationInputs,
        });
      });

      const activeId = genIdList[0];

      setData(draft => {
        draft.type = type;
        draft.activeId = activeId;
        draft.list = genIdList;
        draft.map = map;
      });
    }
  }, [initValue, data.cacheDestinationInputs, data.cacheDynamicFields]);

  // Update list template
  useEffect(() => {
    try {
      const dataTemplates = getObjectPropSafely(
        () => destinationSettings.templates,
        {},
      );
      const currentTemplateOptions = getObjectPropSafely(
        () => data.map[data.activeId].template.options,
      );
      const { activeTemplate = '', selectedList = [] } = dataTemplates || {};

      if (
        !isEmpty(activeTemplate) &&
        !isEmpty(selectedList) &&
        Array.isArray(selectedList) &&
        Object.values(data.map).every(each => !isEmpty(each))
      ) {
        const mapOptions = {};
        const options = [];

        selectedList.forEach(selectedItem => {
          const item = {
            label: selectedItem.label,
            value: selectedItem.id,
          };

          mapOptions[selectedItem.id] = item;
          options.push(item);
        });

        const templateVariant = getObjectPropSafely(
          () =>
            data.map[data.activeId] &&
            data.map[data.activeId].template &&
            data.map[data.activeId].template.value,
        );
        const templateVariantValue =
          typeof templateVariant === 'object'
            ? templateVariant.value
            : templateVariant;

        // Re-append template if template saved in the past has been removed
        if (!has(mapOptions, templateVariantValue)) {
          const templateTmp = TEMPLATES.find(
            each => each.id === templateVariantValue,
          );

          if (!isEmpty(templateTmp)) {
            const templateAppend = {
              label: templateTmp.label,
              value: templateTmp.id,
            };
            mapOptions[templateTmp.id] = templateAppend;
            options.push(templateAppend);
          }
        }

        if (!isEqual(currentTemplateOptions, options)) {
          // Update cached list templates
          setData(draft => {
            if (has(draft.cacheDestinationInputs, 'template')) {
              draft.cacheDestinationInputs.template = {
                ...(draft.cacheDestinationInputs.template || {}),
                options,
                mapOptions,
              };
            }
          });

          data.list.forEach(eachId => {
            setData(draft => {
              if (has(draft.map, [eachId, 'template'])) {
                draft.map[eachId].template.options = options;
                draft.map[eachId].template.mapOptions = mapOptions;
              }
            });
          });

          if (!isEmpty(selectedTemplate) && isEmpty(templateVariant)) {
            handleChangeData('template')(selectedTemplate);
          } else {
            const tmp = mapOptions[templateVariantValue || activeTemplate];
            handleChangeData('template')(tmp);
          }
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'useEffect',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }, [destinationSettings, selectedTemplate]);

  // Set default sticker when change Sticker set
  useEffect(() => {
    try {
      const { activeId = '' } = data;

      if (typeof stickerSet === 'object' && activeId) {
        const { value: valueSet = '' } = stickerSet;
        const stickerValue = getObjectPropSafely(
          () => data.map[activeId].sticker.value,
          '',
        );
        const stickerSetMapping = getObjectPropSafely(
          () => STICKER_MAPPINGS[valueSet],
        );

        const isInvalidSticker =
          valueSet &&
          stickerValue &&
          Array.isArray(stickerSetMapping) &&
          !stickerSetMapping.includes(stickerValue);

        if ((valueSet && !stickerValue) || isInvalidSticker) {
          const stickerItem = stickerSetMapping[0];

          handleChangeData('sticker')(stickerItem);
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'useEffect',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }, [stickerSet]);

  // Update & reset & refill data when change template
  useEffect(() => {
    try {
      const { activeId = '', map = {} } = data;

      if (activeId && templateActive) {
        try {
          let templateActiveValue = cloneDeep(templateActive);
          if (isObject(templateActive))
            templateActiveValue = templateActive.value;

          const { resetList = [], refillList = [] } = reUpdateDataByCatalog({
            catalogCode,
            templateType: templateActiveValue,
            destinationInput: map[activeId],
          });

          (resetList || []).forEach(eachName => {
            setData(draft => {
              if (draft.map[activeId] && draft.map[activeId][eachName]) {
                draft.map[activeId][eachName] = {
                  ...(map[activeId][eachName] || {}),
                  isRequired: false,
                  value: '',
                  initValue: '',
                };
              }
            });
          });

          (refillList || []).forEach(eachName => {
            setData(draft => {
              if (draft.map[activeId] && draft.map[activeId][eachName]) {
                draft.map[activeId][eachName].isRequired = true;

                if (eachName === 'imagemapType') {
                  let imagemapTypeValue = get(
                    data.map[activeId],
                    'imagemapType.value',
                    {},
                  );
                  if (isObject(imagemapTypeValue))
                    imagemapTypeValue = imagemapTypeValue.value;

                  const initTemp = getObjectPropSafely(
                    () =>
                      draft.map[activeId][eachName].default ||
                      (draft.map[activeId][eachName].options &&
                        draft.map[activeId][eachName].options[0]) ||
                      '',
                  );

                  if (isEmpty(imagemapTypeValue)) {
                    draft.map[activeId][eachName].value = initTemp;
                  }
                }
              }
            });
          });

          // Pre-select default sticker set
          if (templateActiveValue === TEMPLATE_TYPES.STICKER) {
            const stickerSetOptions = getObjectPropSafely(
              () => map[activeId].stickerType.options || {},
            );

            if (
              !isEmpty(stickerSetOptions) &&
              Array.isArray(stickerSetOptions)
            ) {
              const preselectSet = stickerSetOptions.find(
                eachSet => eachSet.value === '11537',
              );
              const stickerSetTmp =
                typeof stickerSet === 'object' ? stickerSet.value : stickerSet;

              if (preselectSet && !stickerSetTmp) {
                handleChangeData('stickerType')(preselectSet);
              }
            }
          }
        } catch (err) {
          addMessageToQueue({
            path: PATH,
            func: 'useEffect',
            data: err.stack,
          });
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'useEffect',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }, [templateActive]);

  // Callback data out
  useDeepCompareEffect(() => {
    if (!isEmpty(data) && typeof onChange === 'function') {
      const { type = '', cacheDynamicFields = [], list = [], map = {} } = data;
      const dataOut = {
        type,
        data: [],
      };

      list.forEach(eachItem => {
        const destinationInputsTmp = map[eachItem];
        const tmp = {};

        cacheDynamicFields.forEach(each => {
          const { name = '', value = '', inputType = '' } =
            destinationInputsTmp?.[each] || {};

          if (inputType === 'select' && isObject(value)) {
            tmp[name] = value.value;
          } else {
            tmp[name] = value;
          }
        });

        dataOut.data.push(tmp);
      });

      onChange(dataOut);
    }
  }, [data.map, data.type]);

  useEffect(() => {
    if (!isEmpty(errors)) {
      const { list = [], map = {}, cacheDynamicFields = [] } = data;

      list.forEach(eachId => {
        const destInputs = map[eachId];
        let activeTemplate = getObjectPropSafely(
          () => destInputs.template.value,
        );

        if (typeof activeTemplate === 'object') {
          activeTemplate = activeTemplate.value;
        }
        const validateFields = getValidateFields({
          activeTemplate,
          dynamicFields: cacheDynamicFields,
          destinationInputs: destInputs,
        });

        if (!validateFields.includes('template')) {
          validateFields.push('template');
        }

        validateSetting({
          catalogCode,
          channelCode: channelElement,
          activeId: eachId,
          dynamicFields: validateFields,
          destinationInputs: destInputs,
          setData,
        });
      });
    }
  }, [errors]);

  // Observe scroll to message in previews
  useEffect(() => {
    if (data && data.activeId && enableScrollAction) {
      const activeIndex = data.list.findIndex(each => each === data.activeId);
      const boxScrollElement = document.getElementById(
        'box-scroll-observer-line',
      );

      if (boxScrollElement) {
        const innerElement = getObjectPropSafely(
          () => boxScrollElement.childNodes && boxScrollElement.childNodes[0],
        );
        const listChildNodes = getObjectPropSafely(
          () => innerElement && Array.from(innerElement.childNodes),
        );

        if (!isEmpty(listChildNodes)) {
          const restElements = take(listChildNodes, activeIndex);
          let totalHeight = 0;

          restElements.forEach(each => {
            const { scrollHeight = '' } = each;
            totalHeight += scrollHeight;
          });

          boxScrollElement.scrollTo({ top: totalHeight, behavior: 'smooth' });
        }
      }
    }
  }, [data.activeId]);

  // Clean up data
  useEffect(
    () => () => {
      setData(() => initialData());
    },
    [],
  );

  const renderContentSettings = (configs = {}) => {
    if (isEmpty(configs)) return null;

    const { activeId = '', list = [] } = configs;

    const content = list.map((eachId, index) => {
      const isActive = activeId === eachId;
      const messageIndex = index + 1;
      const messageName = `Message ${messageIndex}`;

      if (isActive) {
        return (
          <BoxItem key={eachId}>
            <ItemActive isViewMode={isViewMode}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 10,
                }}
              >
                {!isViewMode && (
                  <CircleBox>
                    <EditIcon style={{ fontSize: '16px', color: '#ffffff' }} />
                  </CircleBox>
                )}
                <div>{messageName}</div>
              </div>
              {!isViewMode && (
                <WrapperIcon aria-describedby={id} onClick={handleAnchorPopup}>
                  <IconXlab name="more-vertical" fontSize="24px" />
                </WrapperIcon>
              )}
            </ItemActive>
          </BoxItem>
        );
      }

      return (
        <BoxItem
          key={eachId}
          isCenter
          onClick={() => callback('ACTIVE_ITEM', { itemId: eachId })}
        >
          <CircleBox isText>{messageIndex}</CircleBox>
        </BoxItem>
      );
    });

    return <Content>{content}</Content>;
  };

  // eslint-disable-next-line react/prop-types
  const renderDynamicContent = ({ fields = [] }) => {
    if (!Array.isArray(fields)) return null;

    const content = fields.map(field => {
      if (
        isEmpty(data.map[data.activeId]) ||
        isEmpty(data.map[data.activeId][field])
      )
        return null;

      const itemField = getObjectPropSafely(
        () => data.map[data.activeId][field],
      );
      const { name = '', ...restConfigs } = itemField || {};

      const activeTemplate = getObjectPropSafely(() => {
        if (typeof data.map[data.activeId].template.value === 'object')
          return data.map[data.activeId].template.value.value;

        return data.map[data.activeId].template.value;
      });

      const { isSendMultiple, isBroadcast } = pickBroadcastDestDelivery(
        destinationSettings,
      );

      const isHidden = checkIsHiddenInputComponent({
        name: field,
        data: itemField,
        selectedTemplate: activeTemplate,
        catalogCode: activeNode.catalogCode,
        extraData: {
          data: {
            dynamicFields: data.cacheDynamicFields,
            destinationInput: data.map[data.activeId],
          },
        },
      });

      if (isHidden) return null;

      return (
        <InputFieldRendering
          {...restConfigs}
          key={name}
          name={name}
          isTitleAlignLeft
          isFullWidth
          isHidden={isHidden}
          stickerSet={stickerSet}
          selectionStartIndex={selectionStartIndex}
          objectWidgetInput={objectWidgetInput}
          componentKey={`${field}-${variants.activeId}-${refreshComponentKey}`}
          variantExtraData={
            !isEmpty(variantExtraData) && variantExtraData[field]
          }
          design={design}
          isViewMode={isViewMode}
          validateKey={validateKey}
          channelElement={channelElement}
          channelCode={channelElement}
          isForceHideBtnPersonalization={isBroadcast && !isSendMultiple}
          catalogCode={catalogCode}
          step={step}
          setIsFetchData={setIsFetchData}
          configure={configure}
          setSelectionStartIndex={setSelectionStartIndex}
          isBlastCampaign={isBlastCampaign}
          itemTypeId={itemTypeId}
          onChange={handleChangeData}
          onChangeOthers={() => () => {}}
          classes={classes}
        />
      );
    });

    return (
      <Grid container xs={12}>
        {content}
      </Grid>
    );
  };

  return (
    <ErrorBoundary path={PATH}>
      <ContainerRendering>
        <Grid
          container
          style={{
            padding: '5px 0px 0px',
            marginBottom: !isMulti ? '-5px' : 0,
          }}
        >
          <Grid
            item
            xs={3}
            className="grid-col-left"
            style={{ display: 'flex', alignItems: 'center' }}
          >
            <Title>
              {MAP_TRANSLATE.sendType}
              {isRequired && <MarkRequired>*</MarkRequired>}
            </Title>
          </Grid>
          <Grid item xs={9} className="grid-col-right">
            <WrapperDisable disabled={disabled}>
              <RadioGroup
                options={SENDING_OPTIONS}
                // label={label}
                name="sendingType"
                radioGroupClass="sending-type-class"
                value={data.type}
                row
                onChange={(_, dataOut) => {
                  handleChangeTypeSending(dataOut);
                }}
                disabled={isViewMode}
              />
            </WrapperDisable>
          </Grid>
        </Grid>
      </ContainerRendering>
      {isMulti && (
        <WrapperDisable disabled={disabled}>
          <WrapperContentList>
            {renderContentSettings(data)}
            {!isViewMode && (
              <ButtonAddItem
                isDisabled={data.list.length >= LIMIT_LIST}
                onClick={() => callback('ADD_ITEM')}
              >
                <ButtonAddStyled
                  iconName="add"
                  theme="outline"
                  disabled={data.list.length >= LIMIT_LIST}
                />
              </ButtonAddItem>
            )}
          </WrapperContentList>
        </WrapperDisable>
      )}
      {renderDynamicContent({
        fields: data.cacheDynamicFields,
      })}
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          style: {
            borderRadius: '10px',
          },
        }}
      >
        <ActionList>
          <ActionItem
            isDisabled={data.list.length >= 5}
            onClick={() =>
              callback('DUPLICATE_ITEM', { itemId: data.activeId })
            }
          >
            {MAP_TRANSLATE.duplicate}
          </ActionItem>
          <ActionItem
            isDisabled={data.list.length <= 1}
            onClick={() => callback('REMOVE_ITEM', { itemId: data.activeId })}
          >
            {MAP_TRANSLATE.delete}
          </ActionItem>
        </ActionList>
      </Popover>
    </ErrorBoundary>
  );
}

DestinationSnapRender.propTypes = {
  initValue: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  dynamicFields: PropTypes.array,
  destinationInputs: PropTypes.object,
  isRequired: PropTypes.bool,
  disabled: PropTypes.bool,
  isViewMode: PropTypes.bool,
  classes: PropTypes.object,
  selectionStartIndex: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  variants: PropTypes.any,
  variantExtraData: PropTypes.any,
  objectWidgetInput: PropTypes.object,
  validateKey: PropTypes.any,
  design: PropTypes.string,
  refreshComponentKey: PropTypes.number,
  channelElement: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  catalogCode: PropTypes.string,
  step: PropTypes.number,
  setIsFetchData: PropTypes.func,
  configure: PropTypes.object,
  setSelectionStartIndex: PropTypes.func,
  isBlastCampaign: PropTypes.bool,
  itemTypeId: PropTypes.string,
  destinationSettings: PropTypes.object,
  selectedTemplate: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  activeNode: PropTypes.object,
  componentId: PropTypes.string,
  callback: PropTypes.func,
  errors: PropTypes.array,
  observerActiveIndex: PropTypes.number,
  onChange: PropTypes.func,
};
DestinationSnapRender.defaultProps = {
  initValue: {},
  errors: [],
  dynamicFields: [],
  destinationInputs: {},
  isRequired: true,
  classes: {},
  selectionStartIndex: '',
  variants: {},
  variantExtraData: {},
  objectWidgetInput: {},
  refreshComponentKey: 1,
  validateKey: {},
  design: 'create',
  disabled: false,
  isViewMode: false,
  channelElement: '',
  catalogCode: '',
  step: '',
  setIsFetchData: () => {},
  configure: {},
  setSelectionStartIndex: () => {},
  isBlastCampaign: false,
  itemTypeId: '',
  destinationSettings: {},
  selectedTemplate: {},
  activeNode: {},
  componentId: '',
  observerActiveIndex: null,
  callback: () => {},
  onChange: () => {},
};

export default DestinationSnapRender;
