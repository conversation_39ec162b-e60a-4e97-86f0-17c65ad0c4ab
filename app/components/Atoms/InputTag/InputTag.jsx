// Libraries
import React, {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import {
  DEFAULT_ACCEPT_TAGS,
  TagifyInput,
  Input as InputFallback,
  VIBER_EMOJI_PTN,
  LINE_EMOJI_PTN,
  SHORT_LINK_PTN,
} from '@antscorp/antsomi-ui';

const PATH = 'app/components/Atoms/InputTag/InputTag.jsx';

const InputTag = forwardRef(function Input(props, ref) {
  const {
    initialValue,
    name,
    status,
    minWidth,
    readonly,
    readonlyTag,
    disabled,
    escapeHTML,
    isViberEmoji,
    isLineEmoji,
    // maxLength,
    maxHeight,
    maxPersonalizeTags,
    placeholder,
    children,
    mapAttributes,
    mapErrorAttributes,
    enableShortLink,
    onTagClick,
    onChange,
  } = props;

  // Refs
  const tagifyInputRef = useRef(null);

  // Memoization
  const acceptableTagPattern = useMemo(() => {
    let result = _.cloneDeep(DEFAULT_ACCEPT_TAGS);
    const differenceArr = [];

    if (!enableShortLink) {
      differenceArr.push(SHORT_LINK_PTN);
    }
    if (!isViberEmoji) {
      differenceArr.push(VIBER_EMOJI_PTN);
    }
    if (!isLineEmoji) {
      differenceArr.push(LINE_EMOJI_PTN);
    }

    if (!_.isEmpty(differenceArr)) {
      result = _.difference(result, differenceArr);
    }

    return result;
  }, [isViberEmoji, isLineEmoji, enableShortLink]);

  // Export functions
  useImperativeHandle(
    ref,
    () => {
      const fallbackFn = () => {};

      return {
        /*
         * Interface for Tagify
         * onAddNewTag: (newTag: TagDataCustomize | string) => void;
         */
        onAddNewTag: tagifyInputRef.current?.onAddNewTag || fallbackFn,
        /*
         * Interface for Tagify
         * onReplaceTag: (currentTagEle: HTMLElement, newTag: TagDataCustomize) => void;
         * */
        onUpdateTag: tagifyInputRef.current?.onReplaceTag || fallbackFn,
      };
    },
    [],
  );

  // Render fallback if initialValue is not string
  // -> Tagify Input only accept string
  // -> Render fallback component
  if (typeof initialValue !== 'string') {
    return (
      <InputFallback
        value={_.isString(initialValue) ? initialValue : ''}
        onChange={onChange}
      />
    );
  }

  return (
    <ErrorBoundary path={PATH}>
      <TagifyInput
        ref={tagifyInputRef}
        name={name}
        placeholder={placeholder}
        minWidth={minWidth}
        readonly={readonly}
        readonlyTag={readonlyTag}
        initialValue={initialValue}
        mapAttributes={mapAttributes}
        mapErrorAttributes={mapErrorAttributes}
        status={status}
        escapeHTML={escapeHTML}
        disabled={disabled}
        // maxLength={maxLength} // Not supported yet -> improve later
        maxPersonalizeTags={maxPersonalizeTags}
        maxHeight={maxHeight}
        acceptableTagPattern={acceptableTagPattern}
        onTagClick={onTagClick}
        onChange={onChange}
      >
        {children}
      </TagifyInput>
    </ErrorBoundary>
  );
});

InputTag.propTypes = {
  initialValue: PropTypes.string,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  // maxLength: PropTypes.number,
  maxPersonalizeTags: PropTypes.number,
  name: PropTypes.string,
  readonly: PropTypes.bool,
  readonlyTag: PropTypes.bool,
  status: PropTypes.oneOf(['error', 'warning', 'success', undefined]),
  enableShortLink: PropTypes.bool,
  isLineEmoji: PropTypes.bool,
  isViberEmoji: PropTypes.bool,
  mapAttributes: PropTypes.object,
  mapErrorAttributes: PropTypes.object,
  escapeHTML: PropTypes.bool,
  minWidth: PropTypes.oneOfType(PropTypes.number, PropTypes.string),
  maxHeight: PropTypes.number,
  onTagClick: PropTypes.func,
  children: PropTypes.node,
  onChange: PropTypes.func,
};
InputTag.defaultProps = {
  name: 'input-tag',
  initialValue: '',
  status: undefined,
  isLineEmoji: false,
  isViberEmoji: false,
  placeholder: undefined,
  // minWidth: '100%',
  mapAttributes: {},
  escapeHTML: false,
  onTagClick: () => {},
  children: null,
  onChange: () => {},
};
export default memo(InputTag);
