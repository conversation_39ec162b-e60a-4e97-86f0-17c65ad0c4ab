// Libraries
import classNames from 'classnames';
import * as React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

export const Divider = props => {
  const {
    type,
    dot,
    dashed,
    className,
    style,
    width,
    height,
    children,
    ...restOf
  } = props;

  return (
    <Wrapper
      className={classNames(`--${type}`, className, {
        '--dot': dot,
        '--dashed': dashed,
      })}
      style={{ ...style, height, width }}
      {...restOf}
    />
  );
};

Divider.propTypes = {
  type: PropTypes.string,
  dot: PropTypes.bool,
  dashed: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object,
  width: PropTypes.string,
  height: PropTypes.string,
  children: PropTypes.string,
};

Divider.defaultProps = {
  type: 'vertical',
  dot: true,
};

const Wrapper = styled.div`
  box-sizing: border-box;
  border-width: 0;

  &.--horizontal {
    border-top-width: 1px;
    border-color: #d2d2d2;
    width: 100%;
    margin: 0 15px;
  }

  &.--vertical {
    border-right-width: 1px;
    border-color: #d2d2d2;
    height: 35px;
    width: max-content;
    margin: 0 15px;
  }

  &.--dot {
    border-style: dotted;
    border-color: #d2d2d2;

    &.--vertical {
      border-right-width: 2px;
    }
  }

  &.--dashed {
    border-style: dashed;
    border-color: #d2d2d2;

    &.--vertical {
      border-right-width: 2px;
    }
  }
`;
