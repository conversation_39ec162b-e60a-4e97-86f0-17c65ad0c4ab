import ReduxTypes from '../../../../../redux/constants';

export const actionPersonalKeys = {
  INIT_PERSONALIZATIONS: `@@DASHBOARD_INIT_PERSONALIZATIONS${ReduxTypes.INIT}`,
  INIT_DONE_PERSONALIZATIONS: `@@DASHBOARD_INIT_DONE_PERSONALIZATIONS${
    ReduxTypes.INIT_DONE
  }`,
  START_PERSONALIZATIONS: '@@DASHBOARD_START_PERSONALIZATIONS',
  STOP_PERSONALIZATIONS: '@@DASHBOARD_STOP_PERSONALIZATIONS',
  STT_PERSONALIZATIONS: `@@DASHBOARD_STT_PERSONALIZATIONS${
    ReduxTypes.UPDATE_VALUE
  }`,
  UPDATE_SETTING_PERSONALIZATION: `@@DASHBOARD_SETTING_PERSONALIZATIONS${
    ReduxTypes.UPDATE_VALUE
  }`,
  UPDATE_PROMOTION_CODE_ATTR: `@@DASHBOARD_UPDATE_PROMOTION_CODE_ATTR${
    ReduxTypes.UPDATE_VALUE
  }`,
  STT_CONTENT_SOURCE: `@@DASHBOARD_STT_CONTENT_SOURCE${
    ReduxTypes.UPDATE_VALUE
  }`,
  START_FETCHING_CONTENT_SOURCE: '@@DASHBOARD_START_FETCHING_CONTENT_SOURCE',
  STOP_FETCHING_CONTENT_SOURCE: '@@DASHBOARD_STOP_FETCHING_CONTENT_SOURCE',
  UPDATE_CONTENT_SOURCE: `@@DASHBOARD_UPDATE_CONTENT_SOURCE${
    ReduxTypes.UPDATE_VALUE
  }`,
  REMOVE_RELATED_CONTENT_SOURCE: `@@DASHBOARD_REMOVE_RELATED_CONTENT_SOURCE${
    ReduxTypes.DELETE
  }`,
};

export const updateSTTPersonalizations = (payload = {}) => ({
  type: actionPersonalKeys.STT_PERSONALIZATIONS,
  payload,
});

export const initPersonalizations = () => ({
  type: actionPersonalKeys.INIT_PERSONALIZATIONS,
});

export const startPersonalizations = (payload = {}) => ({
  type: actionPersonalKeys.START_PERSONALIZATIONS,
  payload,
});

export const stopPersonalizations = () => ({
  type: actionPersonalKeys.STOP_PERSONALIZATIONS,
});

export const updateSettingPersonalizations = (payload = {}) => ({
  type: actionPersonalKeys.UPDATE_SETTING_PERSONALIZATION,
  payload,
});

export const updatePromotionCodeAttr = (payload = {}) => ({
  type: actionPersonalKeys.UPDATE_PROMOTION_CODE_ATTR,
  payload,
});

export const startFetchingContentSource = (payload = {}) => ({
  type: actionPersonalKeys.START_FETCHING_CONTENT_SOURCE,
  payload,
});

export const stopFetchingContentSource = () => ({
  type: actionPersonalKeys.STOP_FETCHING_CONTENT_SOURCE,
});

export const updateSTTContentSource = (payload = {}) => ({
  type: actionPersonalKeys.STT_CONTENT_SOURCE,
  payload,
});

export const updateContentSource = (payload = {}) => ({
  type: actionPersonalKeys.UPDATE_CONTENT_SOURCE,
  payload,
});

export const removeRelatedContentSource = () => ({
  type: actionPersonalKeys.REMOVE_RELATED_CONTENT_SOURCE,
});

export const initDonePersonalizations = () => ({
  type: actionPersonalKeys.INIT_DONE_PERSONALIZATIONS,
});
