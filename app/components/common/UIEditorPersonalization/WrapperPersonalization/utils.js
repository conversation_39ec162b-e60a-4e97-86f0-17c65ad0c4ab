// Libraries
import _ from 'lodash';

// Services
import JourneyServices from 'services/Journey';
import TemplatesMediaServices from 'services/TemplatesMedia';
import ObjectServices from 'services/Object';
import BOObjectServices from 'services/BusinessObject';
import MetaDataServices from 'services/MetaData';
import LinkManagementServices from 'services/LinkManagement';

// Locales
import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';

// Constants
import {
  DEFAULT_GROUP_CODES,
  VISITOR_CODE,
  CUSTOMER_CODE,
  COMMON_CODE,
  PROMOTION_CODE,
  DATA_GROUPS,
  OBJECT_WIDGET_CODE,
  JOURNEY_CUSTOM_FN_CODE,
  PRODUCT_TEMPLATE_CODE,
  EVENT_CODE,
  JOURNEY_CODE,
  CAMPAIG<PERSON>_CODE,
  VARIANT_CODE,
  MARKETING_HUB_CODE,
  SHORT_LINK_V2,
} from './constants';

// Utils
import { addMessageToQueue } from '../../../../utils/web/queue';
import {
  toUIAtributesPersonalization,
  toUIDataObjectWidgets,
  toUIDataProductTemplates,
  toUIDataPromotionCode,
  toUIDataShortener,
} from '../utils';
import {
  dtoCustomFunction,
  dtoObjectWidget,
  dtoPersonalizations,
  dtoProductTemplate,
  dtoPromotionCode,
  dtoSystemBOFunction,
  dtoShortLinkShortener,
} from './libs/utils.dto';
import { toEntryAPIList } from '../components/PopupPersonalization/utils';
import { getStatusItemCode } from '../../../../utils/web/properties';
import { safeParse } from '../../../../utils/common';

const PATH =
  'app/components/common/UIEditorPersonalization/WrapperPersonalization/utils.js';

/* eslint-disable no-else-return */
const MAP_NAME = {
  visitor: 'Visitor',
  customer: 'Customer',
  promotion_code: 'Promotion Code',
};

export const getInitialPersonalizations = () => ({
  isLoading: false,
  isLoadingContentSources: false,
  isInitDone: false,
  settings: {
    contentSources: [],
    mergeTags: [],
    personalizationType: {
      list: [],
      map: {},
    },
    personalizationData: {},
    promotionCodeAttr: {
      list: [],
      map: {},
    },
    personalizationDataError: null,
  },
});

/**
 * Function to check if group is common.
 *
 * @param {string} groupCode - The group code to check.
 *
 * @returns {boolean} Whether the group is common or not.
 * */
export const isCommonGroupPersonalize = groupCode =>
  [VISITOR_CODE, CUSTOMER_CODE, EVENT_CODE].includes(groupCode);

/**
 * Function to build merge tags based on type and data.
 *
 * @param {string} type - The type of data (e.g., 'customer', 'visitor', 'promotion_code').
 * @param {Array<Object>} data - The array of data objects containing label and bracketed_code.
 * @returns {Object} - The object containing the name and merge tags.
 *
 * @example
 * // Basic usage for customer type
 * const type = 'customer';
 * const data = [
 *   { label: 'Customer Name', bracketed_code: 'name' },
 *   { label: 'Customer Email', bracketed_code: 'email' },
 * ];
 * const result = buildMergeTag(type, data);
 * // result => {
 * //   name: 'Customer',
 * //   mergeTags: [
 * //     { name: 'Customer Name', variable: '{customer.name||""}' },
 * //     { name: 'Customer Email', variable: '{customer.email||""}' },
 * //   ],
 * // }
 *
 * @example
 * // Basic usage for promotion_code type
 * const type = 'promotion_code';
 * const data = [
 *   { label: 'Promo Code', bracketed_code: 'code' },
 *   { label: 'Discount', bracketed_code: 'discount' },
 * ];
 * const result = buildMergeTag(type, data);
 * // result => {
 * //   name: 'Promotion Code',
 * //   mergeTags: [
 * //     { name: 'Promo Code', variable: '{promotion_code.code}' },
 * //     { name: 'Discount', variable: '{promotion_code.discount}' },
 * //   ],
 * // }
 *
 * @example
 * // With empty data array
 * const result = buildMergeTag('customer', []);
 * // result => {
 * //   name: 'Customer',
 * //   mergeTags: [],
 * // }
 */
export function buildMergeTag(type, data) {
  // console.log('buildMergeTag', data);
  const arr = data.map(item => {
    if (type === 'customer' || type === 'visitor') {
      return {
        name: item.label,
        variable: `{${type}.${item.bracketed_code}||""}`,
      };
    } else if (type === 'promotion_code') {
      return {
        name: item.label,
        variable: `{${type}.${item.bracketed_code}}`,
      };
    }
  });
  return {
    name: MAP_NAME[type],
    mergeTags: arr,
  };
}

/**
 * Function to get group codes based on campaign type and item type.
 *
 * @param {Object} params - The parameters object.
 * @param {Array<string>} [params.groupCodes=[]] - The initial list of group codes.
 * @param {boolean} [params.isBlastCampaign=false] - Flag indicating if the campaign is a blast campaign.
 * @param {string} [params.itemTypeId=''] - The item type ID.
 * @returns {Array<string>} - The filtered list of group codes.
 *
 * @example
 * // Basic usage without blast campaign
 * const result = getGroupCodes({ groupCodes: ['visitor', 'event'] });
 * // result => ['visitor', 'event']
 *
 * @example
 * // With blast campaign
 * const result = getGroupCodes({ groupCodes: ['visitor', 'event'], isBlastCampaign: true });
 * // result => ['visitor', 'event', 'customer']
 *
 * @example
 * // With blast campaign and duplicate codes
 * const result = getGroupCodes({ groupCodes: ['visitor', 'event', 'visitor'], isBlastCampaign: true });
 * // result => ['visitor', 'event', 'customer']
 *
 * @example
 * // With blast campaign and specific itemTypeId
 * const result = getGroupCodes({ groupCodes: ['visitor', 'event'], isBlastCampaign: true, itemTypeId: -1003 });
 * // result => ['event', 'customer']
 *
 */
export const getGroupCodes = ({
  groupCodes = [],
  isBlastCampaign = false,
  itemTypeId = '',
}) => {
  let newGroupCodes = _.cloneDeep(groupCodes);

  if (isBlastCampaign) {
    newGroupCodes = [...newGroupCodes, VISITOR_CODE, CUSTOMER_CODE].filter(
      (value, index, self) => self.indexOf(value) === index,
    );
    if (+itemTypeId === -1003) {
      newGroupCodes.splice(newGroupCodes.indexOf(VISITOR_CODE), 1);
    }
  }

  return newGroupCodes.filter(item => DEFAULT_GROUP_CODES.includes(item));
};

export const getGroupAttrs = (groupCodes = []) =>
  groupCodes.filter(item => DEFAULT_GROUP_CODES.includes(item));

/**
 * Function to serialize personalizations from a data source based on payload definitions.
 *
 * @param {Object} params - The parameters object.
 * @param {Object} [params.dataSource={}] - The initial data source object.
 * @param {Object} [params.payload={}] - The payload object containing normalization and mapping functions.
 * @returns {Object} - The serialized personalizations.
 *
 */
export const serializationPersonalizations = ({
  dataSource = {},
  payload = {},
}) => {
  try {
    if (!_.isObject(dataSource) || _.isEmpty(payload)) return {};

    const result = {};

    Object.keys(dataSource).forEach(key => {
      if (_.has(payload, [key, 'savedKey'])) {
        const savedKey = _.get(payload, [key, 'savedKey']);
        const normalizeData = _.get(payload, [key, 'normalizeData']);
        const dtoFn = _.get(payload, [key, 'dtoFn']);
        let newDataSource = dataSource[key];

        if (_.isFunction(dtoFn)) {
          newDataSource = dtoFn(newDataSource);
        }

        if (_.isFunction(normalizeData)) {
          const newData = normalizeData(newDataSource);

          result[savedKey] = newData;
        } else {
          result[savedKey] = newDataSource;
        }
      }
    });

    return result;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'serializationPersonalizations',
      data: error.stack,
    });
    console.log(error);
    return {};
  }
};

/**
 * Function to map new personalization data to the stored data source.
 *
 * @param {Object} params - The parameters object.
 * @param {Object} [params.payload={}] - The payload object containing normalization and mapping functions.
 * @param {Object} [params.oldDataSource={}] - The old data source object.
 * @param {Object} [params.newDataSource={}] - The new data source object.
 * @returns {Object} - The updated data source object.
 *
 */
export const mapPersonalizeToStored = ({
  payload = {},
  oldDataSource = {},
  newDataSource = {},
}) => {
  const dataSourceCloned = _.cloneDeep(oldDataSource);

  try {
    if (!_.isObject(dataSourceCloned) || !_.isObject(newDataSource))
      return dataSourceCloned;

    Object.keys(newDataSource).forEach(key => {
      const currentTypeList = _.get(
        dataSourceCloned,
        ['personalizationType', 'list'],
        [],
      );
      const currentTypeMap = _.get(
        dataSourceCloned,
        ['personalizationType', 'map'],
        {},
      );
      const newType = DATA_GROUPS[key] || {};
      const newDataSourceByKey = _.get(newDataSource, key, {});
      const isExistData = _.has(dataSourceCloned, ['personalizationData', key]);

      let retrieveCode = _.cloneDeep(key);
      const isCommonGroup = isCommonGroupPersonalize(key);
      if (isCommonGroup) {
        retrieveCode = COMMON_CODE;
      }
      const isForceUpdateNew = _.get(
        payload,
        [retrieveCode, 'isForceUpdateNew'],
        false,
      );

      const allowInjectDataSource =
        (!isExistData || isForceUpdateNew) && !_.isEmpty(newDataSourceByKey);

      switch (key) {
        case COMMON_CODE:
        case MARKETING_HUB_CODE: {
          const personalizeType = _.get(newDataSource, [
            key,
            'personalizationType',
          ]);
          const personalizeData = _.get(newDataSource, [
            key,
            'personalizationData',
          ]);

          // If new data is not empty concatenate old data and new data
          if (!_.isEmpty(personalizeData)) {
            // Update Personalization Data
            Object.keys(personalizeData).forEach(personalizeKey => {
              // If old data not include this key in the stored -> add new data to stored
              if (
                !_.has(dataSourceCloned, [
                  'personalizationData',
                  personalizeKey,
                ]) ||
                allowInjectDataSource
              ) {
                const newData = _.get(newDataSource, [
                  key,
                  'personalizationData',
                  personalizeKey,
                ]);

                _.set(
                  dataSourceCloned,
                  ['personalizationData', personalizeKey],
                  newData,
                );
              }
            });
          }

          // Update personalization type
          if (!_.isEmpty(personalizeType)) {
            const newTypeList = _.unionBy(
              currentTypeList,
              personalizeType.list,
              'value',
            );
            const newTypeMap = {
              ...currentTypeMap,
              ...personalizeType.map,
            };

            _.set(
              dataSourceCloned,
              ['personalizationType', 'list'],
              newTypeList,
            );
            _.set(dataSourceCloned, ['personalizationType', 'map'], newTypeMap);
          }
          break;
        }
        case JOURNEY_CUSTOM_FN_CODE:
        case PROMOTION_CODE:
        case SHORT_LINK_V2:
        case OBJECT_WIDGET_CODE:
        case PRODUCT_TEMPLATE_CODE: {
          // Update Personalization Data
          // Check if old data not include this key in the stored -> add new data to stored
          if (allowInjectDataSource) {
            _.set(
              dataSourceCloned,
              ['personalizationData', key],
              newDataSourceByKey,
            );
          }

          // update personalization type
          if (!_.isEmpty(newType)) {
            // Merge union current type list
            const newTypeList = _.unionBy(currentTypeList, [newType], 'value');

            // Merge current type map
            const newTypeMap = {
              ...currentTypeMap,
              [key]: newType,
            };

            _.set(
              dataSourceCloned,
              ['personalizationType', 'list'],
              newTypeList,
            );
            _.set(dataSourceCloned, ['personalizationType', 'map'], newTypeMap);
          }
          break;
        }
        default: {
          break;
        }
      }
    });

    return dataSourceCloned;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'mapPersonalizeToStored',
      data: error.stack,
    });
    console.log(error);
    return dataSourceCloned;
  }
};

/**
 * Function to find missing group codes in personalization type and data.
 *
 * @param {Object} params - The parameters object.
 * @param {Array<string>} [params.groupCodes=[]] - The list of group codes to check.
 * @param {Object} [params.personalizationType={}] - The personalization type object containing maps.
 * @param {Object} [params.personalizationData={}] - The personalization data object.
 * @returns {Array<string>} - The list of missing group codes.
 *
 * @example
 * // Basic usage
 * const groupCodes = ['visitor', 'customer', 'event'];
 * const personalizationType = {
 *   map: {
 *     visitor: { value: 'visitor' },
 *     customer: { value: 'customer' },
 *   },
 * };
 * const personalizationData = {
 *   visitor: {},
 *   customer: {},
 * };
 * const result = findMissingGroupCodes({ groupCodes, personalizationType, personalizationData });
 * // result => ['event']
 *
 * @example
 * // When both types and data are missing
 * const personalizationType = {
 *   map: {},
 * };
 * const personalizationData = {};
 * const result = findMissingGroupCodes({ groupCodes, personalizationType, personalizationData });
 * // result => ['visitor', 'customer', 'event']
 *
 * @example
 * // When groupCodes is empty
 * const result = findMissingGroupCodes({ groupCodes: [], personalizationType, personalizationData });
 * // result => []
 *
 * @example
 * // With invalid personalizationType
 * const result = findMissingGroupCodes({ groupCodes, personalizationType: 'invalid', personalizationData });
 * // result => []
 */
export const findMissingGroupCodes = ({
  groupCodes = [],
  personalizationType = {},
  personalizationData = {},
}) => {
  try {
    const typeKeys = Object.keys(personalizationType.map || {});
    const dataKeys = Object.keys(personalizationData);

    const missingInType = groupCodes.filter(code => !typeKeys.includes(code));
    const missingInData = groupCodes.filter(code => !dataKeys.includes(code));

    return _.union(missingInType, missingInData);
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'findMissingGroupCodes',
      data: error.stack,
    });
    console.log(error);
    return [];
  }
};

function mapToDataType(isPk, dataType) {
  if (parseInt(isPk) === 1) {
    return 'object_id';
  }
  return dataType;
}

const mapAttrsSystemBOToUI = data => {
  const dataToUI = {
    list: [],
    map: {},
  };

  if (data.length === 0) {
    return dataToUI;
  }

  data.forEach(item => {
    if (Number(item.status) !== 4) {
      const temp = {
        value: item.itemPropertyName,
        bracketed_code: item.itemPropertyName,
        display: item.itemPropertyDisplay,
        dataType: mapToDataType(item.isPk, item.dataType),
        itemDataType: item.dataType,
        label: safeParse(item.translateLabel, item.itemPropertyDisplay),
        statusItemCode: getStatusItemCode('info', parseInt(item.status)),
      };

      dataToUI.list.push(temp);
      dataToUI.map[temp.value] = temp;
    }
  });

  return dataToUI;
};

export function normalizeSystemBOAttribute(data = []) {
  const dataToUI = {
    personalizationType: {
      list: [],
      map: {},
    },
    personalizationData: {},
  };

  if (safeParse(data, []).length === 0) {
    return dataToUI;
  }

  try {
    if (!_.isArray(data)) return dataToUI;

    data.forEach(group => {
      const { groupCode, properties = [] } = group;
      const groupName = getTranslateMessage(
        safeParse(TRANSLATE_KEY[group.translateCode], groupCode),
        groupCode,
      );

      const dataGroup = {
        value: groupCode,
        label: groupName,
      };

      dataToUI.personalizationType.list.push(dataGroup);
      dataToUI.personalizationType.map[groupCode] = dataGroup;

      if (properties && _.isArray(properties)) {
        switch (groupCode) {
          case JOURNEY_CODE:
          case CAMPAIGN_CODE:
          case VARIANT_CODE: {
            dataToUI.personalizationData[groupCode] = mapAttrsSystemBOToUI(
              properties,
            );
            break;
          }
          default: {
            break;
          }
        }
      }
    });

    return dataToUI;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'normalizeSystemBOAttribute',
      data: error.stack,
    });
    return dataToUI;
  }
}

/**
 * Function to get payloads for personalization to worker saga.
 *
 * @param {Object} params - The parameters object.
 * @param {Array<string>} [params.groupCodes=[]] - The list of group codes.
 * @param {Array<string>} [params.groupsAttrs=[]] - The list of group attributes.
 * @param {Array<string>} [params.dataTypes=[]] - The list of data types.
 * @param {number|null} [params.eventCategoryId=null] - The event category ID.
 * @param {number|null} [params.eventActionId=null] - The event action ID.
 * @param {Set<string>} [params.setInput=new Set(['input'])] - The set of input components.
 * @param {string} [params.typeComponent='editor'] - The type of component.
 * @returns {Object} - The payloads for personalization.
 *
 * @example
 * // Basic usage
 * const params = {
 *   groupCodes: ['visitor', 'promotion_code'],
 *   groupsAttrs: ['visitor'],
 *   dataTypes: ['string', 'number'],
 *   eventCategoryId: 1,
 *   eventActionId: 2,
 *   setInput: new Set(['input']),
 *   typeComponent: 'editor',
 * };
 * const result = getPayloadsPersonalizeToWorkerSaga(params);
 * // result => {
 * //   common: { ... },
 * //   promotion_code: { ... },
 * // }
 *
 * @example
 * // Without event category and action IDs
 * const params = {
 *   groupCodes: ['customer'],
 *   groupsAttrs: ['customer'],
 *   dataTypes: ['string'],
 *   setInput: new Set(['input']),
 *   typeComponent: 'editor',
 * };
 * const result = getPayloadsPersonalizeToWorkerSaga(params);
 * // result => {
 * //   common: { ... },
 * // }
 *
 * @example
 * // With empty group codes
 * const result = getPayloadsPersonalizeToWorkerSaga({ groupCodes: [] });
 * // result => {}
 *
 * @example
 * // With invalid group codes
 * const result = getPayloadsPersonalizeToWorkerSaga({ groupCodes: 'invalid' });
 * // result => {}
 *
 */
export const getPayloadsPersonalizeToWorkerSaga = ({
  groupCodes = [],
  groupsAttrs = [],
  dataTypes = [],
  eventCategoryId = null,
  eventActionId = null,
  setInput = new Set(['input']),
  typeComponent = 'editor',
}) => {
  const payloads = {};
  try {
    if (_.isArray(groupCodes) && groupCodes.length) {
      groupCodes.forEach(groupItem => {
        switch (groupItem) {
          case VISITOR_CODE:
          case CUSTOMER_CODE:
          case EVENT_CODE: {
            if (!_.has(payloads, COMMON_CODE)) {
              payloads[COMMON_CODE] = {
                savedKey: COMMON_CODE,
                serviceFn: JourneyServices.fetch.personalizationAttrs,
                dtoFn: dtoPersonalizations,
                payload: {
                  data: {
                    objectType: 'STORIES',
                    groupCodes: groupsAttrs,
                    dataTypes,
                    eventCategoryId: groupsAttrs.includes('event')
                      ? eventCategoryId
                      : null,
                    eventActionId: groupsAttrs.includes('event')
                      ? eventActionId
                      : null,
                  },
                },
                normalizeData: toUIAtributesPersonalization,
              };
            }
            break;
          }
          case PROMOTION_CODE: {
            payloads[PROMOTION_CODE] = {
              savedKey: PROMOTION_CODE,
              serviceFn: ObjectServices.suggestionMultilang.getList,
              dtoFn: dtoPromotionCode,
              payload: {
                data: {
                  filters: { OR: [{ AND: [{}] }] },
                  limit: 2000,
                  page: 0,
                  search: '',
                  sort: 'asc',
                  objectType: 'PROMOTION_POOL',
                },
              },
              normalizeData: toUIDataPromotionCode,
            };
            break;
          }
          case OBJECT_WIDGET_CODE: {
            payloads[OBJECT_WIDGET_CODE] = {
              savedKey: OBJECT_WIDGET_CODE,
              serviceFn: TemplatesMediaServices.objectWidgets.getList,
              dtoFn: dtoObjectWidget,
              payload: 'page=0&search=&limit=1000&sort=utime&sd=desc',
              normalizeData: toUIDataObjectWidgets,
            };
            break;
          }
          case PRODUCT_TEMPLATE_CODE: {
            if (!setInput.has(typeComponent)) {
              payloads[PRODUCT_TEMPLATE_CODE] = {
                savedKey: PRODUCT_TEMPLATE_CODE,
                serviceFn: MetaDataServices.productTemplate.getList,
                dtoFn: dtoProductTemplate,
                payload: 'page=0&search=&limit=1000&sort=utime&sd=desc',
                normalizeData: toUIDataProductTemplates,
              };
            }
            break;
          }
          case JOURNEY_CUSTOM_FN_CODE: {
            payloads[JOURNEY_CUSTOM_FN_CODE] = {
              savedKey: JOURNEY_CUSTOM_FN_CODE,
              serviceFn: JourneyServices.personalization.getListCustom,
              dtoFn: dtoCustomFunction,
              payload: null,
              normalizeData: toEntryAPIList,
            };
            break;
          }
          case JOURNEY_CODE:
          case CAMPAIGN_CODE:
          case VARIANT_CODE: {
            if (!_.has(payloads, MARKETING_HUB_CODE)) {
              payloads[MARKETING_HUB_CODE] = {
                savedKey: MARKETING_HUB_CODE,
                serviceFn:
                  BOObjectServices.BOAttribute.getPersonalizeAttributes,
                dtoFn: dtoSystemBOFunction,
                payload: {
                  data: {
                    objectType: 'STORIES',
                    groupCodes: [JOURNEY_CODE, CAMPAIGN_CODE, VARIANT_CODE],
                    dataTypes: ['string', 'number', 'datetime', 'array_string'],
                    eventCategoryId: null,
                    eventActionId: null,
                  },
                },
                normalizeData: normalizeSystemBOAttribute,
              };
            }

            break;
          }

          case SHORT_LINK_V2: {
            payloads[SHORT_LINK_V2] = {
              savedKey: SHORT_LINK_V2,
              serviceFn: LinkManagementServices.shortener.getPersonalizations,
              dtoFn: dtoShortLinkShortener,
              payload: {
                data: {
                  filters: {
                    OR: [
                      {
                        AND: [
                          {
                            column: 'status',
                            data_type: 'number',
                            operator: 'matches',
                            value: [53],
                          },
                        ],
                      },
                    ],
                  },
                  properties: [
                    'link_shortener_id',
                    'shortener_name',
                    'properties',
                  ],
                  limit: 2000,
                  page: 1,
                  sort: 'ctime',
                  sd: 'desc',
                  search: '',
                },
              },
              normalizeData: toUIDataShortener,
            };
            break;
          }

          default: {
            break;
          }
        }
      });
    }

    return payloads;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'getPayloadsPersonalizeToWorkerSaga',
      data: error.stack,
    });
    return payloads;
  }
};

/**
 * Function to get personalization information based on group codes, data, and type.
 *
 * @param {Object} params - The parameters object.
 * @param {Array<string>} params.groupCodes - The list of group codes.
 * @param {Object} [params.data={}] - The data object containing personalization data.
 * @param {Object} [params.type={}] - The type object containing personalization types.
 * @returns {Object} - The personalization information with data and type.
 *
 * @example
 * // Basic usage
 * const groupCodes = ['visitor', 'customer'];
 * const data = {
 *   visitor: { name: 'Visitor Data' },
 *   customer: { name: 'Customer Data' },
 * };
 * const type = {
 *   map: {
 *     visitor: { value: 'visitor', label: 'Visitor' },
 *     customer: { value: 'customer', label: 'Customer' },
 *   },
 * };
 * const result = getPersonalizationInfo({ groupCodes, data, type });
 * // result => {
 * //   personalizationData: {
 * //     visitor: { name: 'Visitor Data' },
 * //     customer: { name: 'Customer Data' },
 * //   },
 * //   personalizationType: {
 * //     list: [
 * //       { value: 'visitor', label: 'Visitor' },
 * //       { value: 'customer', label: 'Customer' },
 * //     ],
 * //     map: {
 * //       visitor: { value: 'visitor', label: 'Visitor' },
 * //       customer: { value: 'customer', label: 'Customer' },
 * //     },
 * //   },
 * // }
 *
 * @example
 * // With empty data and type
 * const groupCodes = ['visitor', 'customer'];
 * const result = getPersonalizationInfo({ groupCodes, data: {}, type: {} });
 * // result => {
 * //   personalizationData: {},
 * //   personalizationType: {
 * //     list: [],
 * //     map: {},
 * //   },
 * // }
 *
 * @example
 * // With invalid groupCodes
 * const result = getPersonalizationInfo({ groupCodes: 'invalid', data, type });
 * // result => {
 * //   personalizationData: {},
 * //   personalizationType: {
 * //     list: [],
 * //     map: {},
 * //   },
 * // }
 *
 */
export const getPersonalizationInfo = ({
  groupCodes,
  data = {},
  type = {},
}) => {
  const personalizationData = {};
  const personalizationType = { list: [], map: {} };

  try {
    groupCodes.forEach(code => {
      // Add to personalizationData if exists
      if (!_.isEmpty(data[code])) {
        personalizationData[code] = data[code];
      }

      // Add to personalizationType if exists
      if (!_.isEmpty(type.map[code])) {
        personalizationType.map[code] = type.map[code];
        personalizationType.list.push(type.map[code]);
      }
    });

    return {
      personalizationData,
      personalizationType,
    };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'getPersonalizationInfo',
      data: error.stack,
    });
    console.log(error);
    return {
      personalizationData,
      personalizationType,
    };
  }
};

export function excludeOptions({ list = [], map = {} }, blacklist = []) {
  const blacklistSet = new Set(blacklist);

  const filteredList = [];
  const filteredMap = {};

  for (const item of list) {
    if (!blacklistSet.has(item?.value)) {
      filteredList.push(item);
      filteredMap[item.value] = map[item.value];
    }
  }

  return {
    list: filteredList,
    map: filteredMap,
  };
}
