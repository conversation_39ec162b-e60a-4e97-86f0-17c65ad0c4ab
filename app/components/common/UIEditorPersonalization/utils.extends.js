/* eslint-disable */
import { get } from 'lodash';
import { getObjectPropSafely } from './utils.3rd';
import { addMessageToQueue } from '../../../utils/web/queue';
import {
  SET_OBJECT_WIDGET_AND_PRODUCT_TEMPLATE,
  regexAttrFormat,
} from './utils';
import {
  CAMPAIGN_CODE,
  JOURNEY_CODE,
  VARIANT_CODE,
} from './WrapperPersonalization/constants';

export function getClassNameColor(selectedProperties) {
  let className = '';
  if (
    getObjectPropSafely(
      () => selectedProperties.personalType.value === 'customer',
    )
  ) {
    className = 'blue';
  }

  if (
    getObjectPropSafely(() => selectedProperties.personalType.value === 'event')
  ) {
    className = 'orange';
  }
  if (
    getObjectPropSafely(
      () => selectedProperties.personalType.value === 'promotion_code',
    )
  ) {
    className = 'violet-pc';
  }
  if (
    getObjectPropSafely(
      () => selectedProperties.personalType.value === 'objectWidget',
    )
  ) {
    className = 'sky-blue';
  }

  if (
    getObjectPropSafely(
      () => selectedProperties.personalType.value === 'productTemplate',
    )
  ) {
    className = 'violet';
  }

  if (
    getObjectPropSafely(
      () => selectedProperties.personalType.value === JOURNEY_CODE,
    )
  ) {
    className = 'light-khaki';
  }

  if (
    getObjectPropSafely(
      () => selectedProperties.personalType.value === CAMPAIGN_CODE,
    )
  ) {
    className = 'baby-pink';
  }

  if (
    getObjectPropSafely(
      () => selectedProperties.personalType.value === VARIANT_CODE,
    )
  ) {
    className = 'misty-rose';
  }

  if (
    getObjectPropSafely(() => selectedProperties.attribute.value === undefined)
  ) {
    className = 'disabled';
  }
  if (getObjectPropSafely(() => selectedProperties.status === 'error')) {
    className = 'error';
  }
  if (getObjectPropSafely(() => selectedProperties.status === 'warning')) {
    className = 'warning';
  }

  if (
    getObjectPropSafely(() => {
      const contentSources = get(selectedProperties, 'contentSources', []);

      if (Array.isArray(contentSources)) {
        return contentSources.some(
          cs =>
            cs.groupId === get(selectedProperties, 'personalType.value', ''),
        );
      }

      return false;
    })
  ) {
    className = 'content-source-tag';
  }

  return className;
}
export function detectContentEdit(
  targetValue,
  personalizationType,
  personalizationData,
  promotionCodeAttr = { map: {}, list: [] },
  stateFormatAttr,
) {
  let personalType;
  let personalTypeCode;
  let attributeType;
  let attributeTypeCode;
  let attributePromotionCode;
  let valueString = '';
  let typeItem;
  const hasInfo = false;
  let csIndexAttribute = {
    label: 1,
    value: 1,
  };
  let displayFormat = {
    dataType: '',
    format: {},
  };
  let contentSourceGroupId = '';

  // console.log('Hello',{ targetValue, personalizationType, personalizationData });
  try {
    const found = targetValue.replace(/#|{|}|/g, '').split('.');
    const hasAppendBOContentSource = found[0] === 'groups';
    if (hasAppendBOContentSource) {
      contentSourceGroupId = (found[1] || '').replace(/\[.*?\]/g, '');

      const regexCsIndexAttr = /\[(\d+)\]/;
      const match = regexCsIndexAttr.exec(found[1]);

      if (match) {
        const numberInBrackets = match[1];

        if (numberInBrackets) {
          csIndexAttribute = {
            label: numberInBrackets,
            value: numberInBrackets,
          };
        }
      }
    }

    if (found && found.length) {
      personalTypeCode = found[0];

      const defaultPromotionCodeAttr =
        promotionCodeAttr.map.id || promotionCodeAttr.list[0];
      attributePromotionCode = defaultPromotionCodeAttr;
      for (const index in found) {
        if (+index === 0) {
          let predicate = found[0];
          if (hasAppendBOContentSource) {
            predicate = contentSourceGroupId;
          }

          typeItem = personalizationType.list.find(
            item => item.value === predicate,
          );

          if (typeItem) {
            personalType = {
              value: typeItem.value,
              label: typeItem.label,
            };
          } else {
            return;
          }
        }
        if (
          +index === 1 &&
          typeItem.value &&
          personalizationData &&
          personalizationData.hasOwnProperty(typeItem.value)
        ) {
          const groupAttribute = personalizationData[typeItem.value];
          let attribute = '';
          if (found.length === 2) {
            // if (
            //   typeItem.value === 'objectWidget' ||
            //   typeItem.value === 'productTemplate'
            // ) {
            //   attribute = groupAttribute && groupAttribute.map[found[1]];
            //   attributeTypeCode = found[1];
            // } else {
            attribute =
              groupAttribute && groupAttribute.map[found[1].split('||')[0]];
            attributeTypeCode = found[1].split('||')[0];
            // }
          } else if (found.length === 3) {
            // case promotion_code
            if (typeItem.value === 'promotion_code') {
              const groupAttribute = personalizationData[typeItem.value];
              attribute = groupAttribute && groupAttribute.map[found[1]];
              attributeTypeCode = found[1];
              attributePromotionCode =
                promotionCodeAttr.map[found[2]] || defaultPromotionCodeAttr;
              // case event?
            } else {
              const splitString = found[2].split('||');

              let propertyCode = `${found[1]}.${splitString[0]}`;

              if (hasAppendBOContentSource && groupAttribute) {
                propertyCode = splitString[0];
              }

              attribute = groupAttribute && groupAttribute.map[propertyCode];
              attributeTypeCode = propertyCode;
            }
          }

          attribute && (attributeType = attribute);
        }

        if (
          +index === 1 &&
          !SET_OBJECT_WIDGET_AND_PRODUCT_TEMPLATE.has(typeItem.value)
        ) {
          let tempValue = '';

          if (found.length === 2) {
            tempValue = found[1].split('||')[1];
          } else if (found.length === 3) {
            const splitString = found[2].split('||');
            tempValue = splitString[1];
          }
          valueString = (tempValue && tempValue.replace(/["']/g, '')) || '';
        }
      }
    }
    const keyAttribute = targetValue.replace(regexAttrFormat, '');
    if (stateFormatAttr?.formatAttributes[keyAttribute]) {
      displayFormat =
        stateFormatAttr.formatAttributes[keyAttribute].attribute.settingsFe;
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/components/common/UIEditorPersonalization/utils.extends.js',
      func: 'detectContentEdit',
      data: error.stack,
    });
    console.log(error);
  }
  return {
    personalType,
    personalTypeCode,
    attributeType,
    attributeTypeCode,
    attributePromotionCode,
    valueString,
    typeItem,
    hasInfo,
    displayFormat,
    csIndexAttribute,
  };
}

export const getInitialSelected = state => {
  let personalType = {};
  let attribute = {};
  const value = '';

  try {
    if (
      state.personalizationType.list &&
      state.personalizationType.list.length
    ) {
      personalType = {
        value: state.personalizationType.list[0].value,
        label: state.personalizationType.list[0].label,
      };

      if (
        getObjectPropSafely(() =>
          state.personalizationData.hasOwnProperty(
            state.personalizationType.list[0].value,
          ),
        )
      ) {
        attribute = getObjectPropSafely(
          () =>
            state.personalizationData[state.personalizationType.list[0].value]
              .list[0],
          {},
        );
      }
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/components/common/UIEditorPersonalization/index.jsx',
      func: 'getInitialSelected',
      data: error.stack,
    });
    console.log('error  ===>', error);
  }

  return {
    personalType,
    attribute,
    value,
  };
};

export const getInitialSelectedNotFound = (state, params) => {
  // const personalType = {
  //   value: params.personalTypeCode,
  //   // label: params.personalTypeCode,
  // };
  // console.log('state', state, params);
  const personalType =
    state.personalizationType.map[params.personalTypeCode] || null;
  const attribute = {
    // value: params.attributeTypeCode,
    label: params.attributeTypeCode,
    bracketed_code: params.attributeTypeCode,
  };
  const { promotionCodeAttr } = params;

  const { value } = params;

  return {
    personalType,
    attribute,
    value,
    promotionCodeAttr,
  };
};
