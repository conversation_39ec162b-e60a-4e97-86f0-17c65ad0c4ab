import { MAP_DATA_TYPE } from '../../../../../services/Abstract.data';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { serializeLabelToCodeAttr } from '../../../../../utils/web/utils';
import { initDisplayFormat } from '../../../Modal/ModalConfigFormat/utils';
import { getObjectPropSafely } from '../../utils.3rd';

export const getInitialSelected = state => {
  let personalType = {};
  let attribute = {};
  const value = '';
  let promotionCodeAttr = {};
  const displayFormat = {
    dataType: '',
    format: {},
  };

  try {
    if (
      state.personalizationType.list &&
      state.personalizationType.list.length
    ) {
      personalType = {
        value: state.personalizationType.list[0].value,
        label: state.personalizationType.list[0].label,
      };

      if (
        getObjectPropSafely(() =>
          state.personalizationData.hasOwnProperty(
            state.personalizationType.list[0].value,
          ),
        )
      ) {
        const tempDefault = getObjectPropSafely(
          () =>
            state.personalizationData[state.personalizationType.list[0].value]
              .list[0],
          {},
        );
        attribute = tempDefault;
        promotionCodeAttr = getObjectPropSafely(
          () =>
            state.promotionCodeAttr.map.id || state.promotionCodeAttr.list[0],
          {},
        );
        displayFormat.dataType = tempDefault.itemDataType;
        displayFormat.format = initDisplayFormat(tempDefault.itemDataType);
      }
    }
  } catch (error) {
    addMessageToQueue({
      path:
        'app/components/common/UIEditorPersonalization/components/PopupPersonalization/utils.js',
      func: 'getInitialSelected',
      data: error.stack,
    });
    console.log('error  ===>', error);
  }

  return {
    personalType,
    attribute,
    value,
    promotionCodeAttr,
    displayFormat,
    contentSources: state.contentSources || [],
    csIndexAttribute: {
      label: 1,
      value: 1,
    },
  };
};
export const DATA_ATTR_TYPE = [
  MAP_DATA_TYPE.number,
  MAP_DATA_TYPE.string,
  MAP_DATA_TYPE.datetime,
];
export const toEntryAPICreate = dataIn => {
  const { displayFormat, personalizationName, dataType, formular } = dataIn;
  return {
    templateCode: serializeLabelToCodeAttr(personalizationName),
    templateType: 'custom',
    templateName: personalizationName,
    customFunction: formular,
    outputDataType: dataType,
    outputFormat: displayFormat,
  };
};
export const toEntryAPIList = dataIn => {
  const dataOut = {
    list: [],
    map: {},
  };
  if (dataIn && dataIn.length) {
    dataIn.forEach(each => {
      const item = {
        personalizationName: each.template_name,
        dataType: each.output_data_type,
        displayFormat: each.output_format,
        formular: each.custom_function,
        ...each,
      };
      dataOut.list.push(item);
      dataOut.map[each.template_code] = item;
    });
  }
  return dataOut;
};
export const checkErrorFunction = data => {
  let isValid = true;
  if (data && data.length > 0) {
    data.forEach(each => {
      if (each.type && each.type === 'error') {
        isValid = false;
      }
    });
  }
  return isValid;
};
export const DATA_ATTR_WITH_TYPE = [
  MAP_DATA_TYPE.number.value,
  MAP_DATA_TYPE.datetime.value,
];

export const getPersonalizeCodeContentSource = ({
  personalizeType = '',
  attributeBracket = '',
  csIndexAttribute = {},
}) =>
  `#{groups.${personalizeType}[${csIndexAttribute.value ||
    1}].${attributeBracket}}`;
