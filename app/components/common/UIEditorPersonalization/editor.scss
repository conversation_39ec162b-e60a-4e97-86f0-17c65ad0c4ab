.popover {
  z-index: 200;
}

$base-color: #005eb8;
$bg-base-color: #fff;
$border-base-color: #e6e6e6;
$bg-btn-base-color: #f4f6f8;

.antsomi-wrapper-personalization {
  width: 100%;
  height: 100%;
  background: $bg-base-color;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
  font-size: 12px;
  font-family: Roboto, 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo,
    Courier, monospace;

  .header {
    padding: 13px 20px;
    position: relative;

    .title {
      font-size: 20px;
      font-weight: bold;
      color: $base-color;
    }

    &:before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      height: 15px;
      width: 95%;
      border-bottom: 2px solid $border-base-color;
    }
  }

  .body {
    overflow: auto;
    flex-grow: 1;
    padding: 0 5px;

    table {
      width: 100%;
      height: 100%;
      table-layout: fixed;
      border-collapse: collapse;
      border-spacing: 0;
      th,
      td {
        text-align: left;
        padding-right: 15px;
      }
    }
  }

  .footer {
    display: flex;
    padding: 15px 25px;
    justify-content: flex-end;
  }
}

.btn {
  min-width: 100px;
  min-height: 40px;
  border-radius: 3px;
  outline: none;
  border: none;
  cursor: pointer;

  &:focus {
    outline: none !important;
  }
}

.group-btn {
  width: 222px;
  display: flex;
  justify-content: space-between;
}

.btn-default {
  background-color: $bg-btn-base-color;
  color: $base-color;
  border: 1px solid #d7dfe8;

  &:hover {
    background-color: #e3e9ef;
  }
}

.btn-blue {
  background-color: $base-color;
  color: $bg-base-color;
  border: 1px solid #707070;
  opacity: 0.9;

  &:hover {
    opacity: 1;
  }
}

.input-outline {
  border-top-style: hidden;
  border-right-style: hidden;
  border-left-style: hidden;
  height: 27px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.4);

  &:focus {
    outline: none;
  }
}

.insert-word {
  max-width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 5px 8px;
  color: #000;
  border: 1px solid;
  border-radius: 20px;
  height: 24px;
  cursor: pointer;
  line-height: 1;
  background: #e8feca;
  border: 1px solid #e8feca;

  &.blue {
    background: #cae5fe;
    border: 1px solid #cae5fe;
  }

  &.orange {
    background: #fecaca;
    border: 1px solid #fecaca;
  }

  &.violet-pc {
    background: #d8cafe;
    border: 1px solid #d8cafe;
  }

  &.disabled {
    background: #ccc;
    border: 1px solid #ccc;
  }

  &.sky-blue {
    background: #cafedd;
    border: 1px solid #cafedd;
  }

  &.violet {
    background: #9238b5;
    border: 1px solid #9238b5;
  }
  &.custom {
    background: #bbefbe;
    border: 1px solid #bbefbe;
  }
  &.content-source-tag {
    background: #ffdd9f;
    border: 1px solid #ffdd9f;
  }
  &.error {
    background: #ffffff;
    border: 1px solid #f44336;
    height: 24px;
  }
  &.warning {
    background: #ffffff;
    border: 1px solid #f44336;
    height: 24px;
  }
  &.light-khaki {
    background: #fafaaf;
    border: 1px solid #fafaaf;
  }
  &.baby-pink {
    background: #fbcbe8;
    border: 1px solid #fbcbe8;
  }
  &.misty-rose {
    background: #ffd8db;
    border: 1px solid #ffd8db;
  }

  em {
    color: white;
    font-style: normal;
    font-size: 11px;
    opacity: 0;
  }
  &:hover {
    em {
      opacity: 1;
    }
  }
}
