/* eslint-disable consistent-return */
/* eslint-disable import/order */
/* eslint-disable no-else-return */
/* eslint-disable indent */
/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { Fragment, useEffect, memo, useMemo } from 'react';
import { useImmer } from 'use-immer';
import {
  usePopupState,
  bindTrigger,
  bindMenu,
} from 'material-ui-popup-state/hooks';
import UISuggestionList from 'components/common/UISuggestionList';
import {
  UIButton,
  UITextSearch as TextSearch,
  UIWrapperDisable as WrapperDisable,
  UILoading as Loading,
} from '@xlab-team/ui-components';
import CloseIcon from '@material-ui/icons/Close';

import {
  WrapperPreviewSource,
  StyleWapperListFilter,
  DivLoading,
  StyledUIButtonWrapper,
  DataSourceBlockContent,
} from '../../styled';
import { onSearchFilter } from 'containers/Filters/AddFilter/utils';
import GroupAttribute from 'containers/Filters/AddFilter/GroupAttribute';
import { Nodata } from 'components/form/UISelectCondition/DropdownAction/Tree/styled';
import { useFetchDataByEvent } from './useFetchDataByEvent';
import ItemPreviewSourceSelected from '../../../../Molecules/ItemPreviewSource';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import ConditionError from '../../../../../containers/Filters/AddFilter/FormCondition/ConditionError';
import ModalConfirmDeleteSource from './ModalConfirmDeleteSource';
import { convertConditionRefineToArrayString } from './utils';
// import { validateItemCondition } from '../../../../../containers/Filters/utils';
import { validatePropertiesRefines } from '../../utils.validate';
import { FormHelperText } from '@material-ui/core';
import { STATUS_SOURCE_CODE } from '../../../../../utils/constants';
import isEqual from 'react-fast-compare';
import classnames from 'classnames';
const style = {
  minWidth: '300px',
  // maxWidth: '300px',
  maxHeight: '380px',
  // minHeight: '380px',
  overflowX: 'hidden',
  overflowY: 'auto',
  // fontSize: '13px',
};

const initState = {
  searchValue: '',
  searchList: [],
  open: false,
  tempDataSources: [],
  sourceName: '',
  arrayRefineWillDeleted: [],
  sourceId: 0,
  errors: [],
  isRFM: false,
};

const labelAnySource = getTranslateMessage(
  TRANSLATE_KEY._TITL_IN_ANY_SOURCE,
  'In any source of',
);

const labelAddSource = getTranslateMessage(
  TRANSLATE_KEY._ACT_ADD_SOURCE,
  'Add source',
);

const SelectDataSources = props => {
  let isMounted = true;
  const {
    eventValue,
    sourcesSelected: sourceSelectedProp,
    isInitDataSources,
    refineWithProperties,
    isRFM,
    label,
  } = props;

  let isDisable = true;

  if (eventValue !== null && typeof eventValue === 'object') {
    if (Object.keys(eventValue).length > 0) {
      isDisable = false;
    }
  }

  const popupStateMenu = usePopupState({
    variant: 'popover',
    popupId: 'menu-filter-popup-popover',
  });

  const [state, setState] = useImmer(initState);

  const { group, isLoading } = useFetchDataByEvent({
    isInitDataSources,
    eventValue,
    sourcesSelected: sourceSelectedProp,
    paramsFetchEvent: props.paramsFetchEvent,
  });

  useEffect(
    () => () => {
      isMounted = false;
    },
    [],
  );

  const toggleModal = () => {
    setState(draft => {
      draft.open = false;
    });
  };

  useEffect(() => {
    // console.log('-------', isLoading, group, isInitDataSources);
    if (!isLoading) {
      const arr = [];

      group.list.forEach(tmp => {
        arr.push(tmp.value);
      });

      if (isInitDataSources !== true) {
        props.onChange({
          dataSourceSelected: arr,
          temp: false,
          isDeleteRefine: false,
        });
      }

      if (props.onChangeListDatasources) {
        props.onChangeListDatasources(group.list);
      }
    }
  }, [group, isLoading]);

  useEffect(() => {
    if (group.list.length > 0) {
      let errors = [];
      sourceSelectedProp.forEach(each => {
        const item = group.map[each];

        if (
          item &&
          item.statusItemCode === STATUS_SOURCE_CODE.NO_LONGER_ASSIGN_EVENT
        ) {
          errors = [
            'The source colored with red no more exists in any of model-source',
          ];
        }
      });

      setState(draft => {
        draft.errors = errors;
      });
    }
  }, [sourceSelectedProp, group]);

  const callback = (type, data) => {
    if (type === 'DELETE_SOURCE_ITEM') {
      const array = sourceSelectedProp;
      const { inValid, refineBelongtoSourceId } = validatePropertiesRefines(
        refineWithProperties,
        array[data],
      );

      if (
        refineWithProperties.size === 0 ||
        inValid ||
        !refineBelongtoSourceId
      ) {
        props.onChange({
          dataSourceSelected: [
            ...array.slice(0, data),
            ...array.slice(data + 1),
          ],
          isDeleteRefine: false,
        });
      } else {
        let sourceName = '--';
        const sourceId = array[data];
        const tempData = [...array.slice(0, data), ...array.slice(data + 1)];

        if (group.map[sourceId]) {
          sourceName = group.map[sourceId].label;
        }

        setState(draft => {
          draft.open = true;
          draft.sourceName = sourceName;
          draft.sourceId = sourceId;
          draft.arrayRefineWillDeleted = convertConditionRefineToArrayString(
            refineWithProperties,
            sourceId,
            tempData,
          );
          draft.tempDataSources = tempData;
        });
      }
    } else if (type === 'SELECT_ATRR') {
      popupStateMenu.close();
      const array = sourceSelectedProp;
      array.push(data.value);
      props.onChange({ dataSourceSelected: [...array], isDeleteRefine: false });
    } else if (type === 'CONFIRM_DELETE_SOURCE') {
      if (data === true) {
        props.onChange({
          dataSourceSelected: state.tempDataSources,
          isDeleteRefine: true,
          sourceIdWillDeleted: state.sourceId,
        });
      }
    }
    // onChange;
  };

  const onSearch = valueSearch => {
    const textSearch = valueSearch.trim().toLowerCase();
    setState(draft => {
      draft.searchValue = valueSearch;
    });
    if (textSearch.length > 0) {
      const { arrayGroupAttrs } = onSearchFilter(group.list, [], valueSearch);
      setState(draft => {
        draft.searchList = arrayGroupAttrs;
      });
    } else {
      setState(draft => {
        draft.searchList = [];
      });
    }
  };

  const sourceSelected = useMemo(() => {
    if (props.showAllSource && props.isViewMode) {
      return group.list.map(item => item.id);
    }

    return sourceSelectedProp;
  }, [props.isViewMode, sourceSelectedProp, props.showAllSource, group.list]);

  if (!isMounted) {
    return null;
  }

  const renderContentSelectDataSources = () => {
    if (!isDisable && isLoading && isMounted) {
      return (
        <DivLoading>
          <Loading isLoading={isLoading} size={20} />
        </DivLoading>
      );
    }

    console.log({ sourceSelected });

    return (
      <WrapperDisable disabled={isDisable}>
        {sourceSelected.map((source, index) => {
          const tmp = group.map[source] || {};
          const isError =
            tmp.statusItemCode === STATUS_SOURCE_CODE.NO_LONGER_ASSIGN_EVENT;
          if (tmp !== undefined) {
            return (
              <ItemPreviewSourceSelected
                rightIcon={
                  <CloseIcon
                    onClick={() => callback('DELETE_SOURCE_ITEM', index)}
                  />
                }
                index={Math.random()}
                label={tmp.label}
                value={tmp.value}
                readOnly={props.isViewMode}
                item={tmp}
                key={source}
                isError={isError}
              />
            );
          }
          return null;
        })}

        {!props.isViewMode && (
          <>
            <StyledUIButtonWrapper>
              <UIButton
                variant="contained"
                {...bindTrigger(popupStateMenu)}
                reverse
                iconName="add"
                theme="outline"
                borderRadius="1rem"
                fs="0.75rem"
                style={{
                  border: 'solid 1px',
                }}
              >
                {labelAddSource}
              </UIButton>
            </StyledUIButtonWrapper>
            <UISuggestionList
              popoverProps={{
                ...bindMenu(popupStateMenu),
              }}
              searchProps={{
                onChange: onSearch,
                value: state.searchValue,
              }}
              style={style}
            >
              <StyleWapperListFilter>
                {group.list.length === 0 ||
                (state.searchValue.length > 0 &&
                  state.searchList.length === 0) ? (
                  <Nodata height="150px">
                    {getTranslateMessage(
                      TRANSLATE_KEY._INFO_NO_DATA,
                      'No data',
                    )}
                  </Nodata>
                ) : null}
                {state.searchValue.length > 0 ? (
                  <GroupAttribute
                    // hasTopBorder={isRenderBorder()}
                    groups={state.searchList}
                    callback={callback}
                    itemsDisabled={sourceSelectedProp}
                  />
                ) : (
                  <GroupAttribute
                    // hasTopBorder={isRenderBorder()}
                    groups={group.list}
                    callback={callback}
                    itemsDisabled={sourceSelectedProp}
                  />
                )}
              </StyleWapperListFilter>
            </UISuggestionList>
          </>
        )}
      </WrapperDisable>
    );
  };

  if (props.hidden) {
    return null;
  }

  return (
    <DataSourceBlockContent isRFM={isRFM}>
      {label && isRFM ? (
        <Fragment>{label}</Fragment>
      ) : (
        <div className="title select-source">
          <div className="select-source-txt">{labelAnySource}</div>
        </div>
      )}
      <div
        className={classnames('content', {
          'flex-column': props.isViewMode,
        })}
      >
        <WrapperDisable disabled={props.disabledEventConditions}>
          <WrapperPreviewSource>
            {renderContentSelectDataSources()}
          </WrapperPreviewSource>
        </WrapperDisable>

        <ConditionError keyError={3} error={props.error} />
        {!!state.errors[0] && (
          <FormHelperText id="component-helper-text" error={!!state.errors[0]}>
            {state.errors}
          </FormHelperText>
        )}
      </div>
      <ModalConfirmDeleteSource
        isOpen={state.open}
        toggle={toggleModal}
        callback={callback}
        sourceName={state.sourceName}
        conditionRefine={state.arrayRefineWillDeleted}
      />
    </DataSourceBlockContent>
  );
};

SelectDataSources.defaultProps = {
  sourcesSelected: [],
  disabledEventConditions: false,
  isViewMode: false,
  showAllSource: false,
};
export default memo(SelectDataSources, isEqual);
