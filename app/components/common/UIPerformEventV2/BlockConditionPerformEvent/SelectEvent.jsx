/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import SelectTree from 'components/form/UISelectCondition';
import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';
import classnames from 'classnames';

import { getTranslateMessage } from '../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../messages/constant';
import ConditionError from '../../../../containers/Filters/AddFilter/FormCondition/ConditionError';
import { safeParse } from '../../../../utils/common';
import { STATUS_ITEM_CODE } from '../../../../utils/constants';
import { getDataDefautEvenActionBased } from '../utils';
import { DataSourceBlockContent } from '../styled';

const labelPerfEvent = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_EVENT,
  'Perform event',
);
const labelEvent = getTranslateMessage(TRANSLATE_KEY._TITL_EVENT, 'Events');

const labelSelectEvent = getTranslateMessage(
  TRANSLATE_KEY._TITL_PERFORM_EVENT,
  'Select event',
);

const SelectEvent = props => {
  const {
    item,
    options,
    property,
    groupIndex,
    itemIndex,
    version,
    rules,
    hiddeOption,
    defaultOption,
  } = props;

  const changeProperty = value => {
    const data = {
      groupIndex,
      itemIndex,
      value,
    };
    props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', { version, data });
    props.callback('UPDATE_CONDITIONS', data);
  };

  useEffect(() => {
    if (!property) {
      const dataDefault = getDataDefautEvenActionBased(
        options,
        rules,
        hiddeOption,
        defaultOption,
      );

      const data = {
        groupIndex,
        itemIndex,
        value: { ...dataDefault },
        isInit: true,
      };

      props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', { version, data });
      props.callback('UPDATE_CONDITIONS', data);
    }
  }, []);

  const statusItemCode = safeParse(
    (props.item.get('property') || {}).statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );

  if (props.hidden) {
    return null;
  }

  return (
    <DataSourceBlockContent isViewMode={props.isViewMode}>
      <div className="title select-event">{labelPerfEvent}</div>
      <div className="content">
        <WrapperDisable disabled={props.disabledEvent}>
          <div
            style={{ maxWidth: '16.438rem' }}
            className={classnames({
              'm-top-5': statusItemCode !== STATUS_ITEM_CODE.ACTIVE,
            })}
          >
            <SelectTree
              onlyParent={props.onlyParent}
              // displayFormat={displayFormat}
              use="tree"
              // isMulti
              // isSearchable
              options={options}
              isParentOpen={props.isParentOpen}
              value={property}
              onChange={changeProperty}
              placeholder={labelSelectEvent}
              isViewMode={props.isViewMode}
            />
          </div>
        </WrapperDisable>

        <ConditionError
          keyError={1}
          error={props.item.get('error')}
          objectLabel={labelEvent}
          statusItemCode={statusItemCode}
          item={item}
        />
      </div>
    </DataSourceBlockContent>
  );
};

export default React.memo(SelectEvent);
