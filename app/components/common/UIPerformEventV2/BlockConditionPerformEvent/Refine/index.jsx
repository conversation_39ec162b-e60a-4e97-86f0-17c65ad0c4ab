/* eslint-disable react/no-array-index-key */
/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';

import {
  UIButton,
  UIWrapperDisable as WrapperDisable,
  UILoading as Loading,
} from '@xlab-team/ui-components';

import { useFetchDataByEvent } from './useFetchDataByEvent';
import Block from './Block';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import {
  DivLoading,
  StyledUIButtonWrapper,
  DataSourceBlockContent,
  WrapperLabelRFM,
} from '../../styled';
import { filterSelectAchirve } from './utils';

const labelWhere = getTranslateMessage(TRANSLATE_KEY._TITL_WHERE, 'Where');
const labelAnd = getTranslateMessage(TRANSLATE_KEY._TITL_AND, 'And');
const labelRefineAttr = getTranslateMessage(
  TRANSLATE_KEY._ACT_REFINE_ATTRIBUTE,
  'Refine by attribute',
);

const Refine = props => {
  const {
    refineWithProperties,
    groupIndex,
    itemIndex,
    version,
    eventValue,
    sourcesSelected,
    isRFM = false,
    isAttributeBo = false,
    isStyledRFM = false,
    limit = Infinity,
    eventPropertyService,
    isUsingJourneyTemplate,
  } = props;

  let isDisable = true;
  if (eventValue !== null && typeof eventValue === 'object') {
    if (Object.keys(eventValue).length > 0) {
      isDisable = false;
    }
  }

  let isInit = false;
  let backup = {};
  if (refineWithProperties.size > 0) {
    isInit = refineWithProperties.first().get('isInit');
    backup = refineWithProperties.first().get('backup');
  }

  const { group, isLoading, conditions } = useFetchDataByEvent(
    eventValue,
    isInit,
    backup,
    refineWithProperties,
    props.moduleConfig,
    sourcesSelected,
    isAttributeBo,
    eventPropertyService,
    isUsingJourneyTemplate,
  );

  const changeRefineProperties = data => {
    props.callback('PERF_EVENT_CHANGE_REFINE_PROPERTIES', data);
  };

  // useEffect(() => {
  //   if (conditions.size > 0) {
  //     changeRefineProperties({
  //       type: 'INIT',
  //       data: {
  //         groupIndex,
  //         itemIndex,
  //         version,
  //         conditions,
  //       },
  //     });
  //   }
  // }, [conditions.size]);

  useEffect(() => {
    if (isLoading === false) {
      changeRefineProperties({
        type: 'INIT',
        data: {
          groupIndex,
          itemIndex,
          version,
          conditions,
        },
      });
    }
  }, [isLoading]);

  const callback = (type, data) => {
    changeRefineProperties({ version, type, data });
    // props.changeRefineProperties({ version, type, data });
    props.callback('UPDATE_CONDITIONS', data);
  };

  const addRefineProperties = () => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
    };
    changeRefineProperties({ version, type: 'ADD_ITEM', data });

    // props.changeRefineProperties({ version, type: 'ADD_ITEM', data });
  };

  const showRefineWithProperties = () => {
    const result = [];
    // const refineWithProperties = props.item.get('refineWithProperties');
    const firstKey = refineWithProperties.keySeq().first();

    // let listEventProperty = [];
    // const eventSchema = safeParse(props.item.get('property'), null);
    // if (eventSchema !== null) {
    //   const mapEventProperty = safeParse(group.map[eventSchema.value], null);
    //   if (mapEventProperty !== null) {
    //     listEventProperty = safeParse(mapEventProperty.list, []);
    //   }
    // }
    refineWithProperties.forEach((itemRefine, key) => {
      if (itemRefine.get('isInit') !== true) {
        result.push(
          <Block
            first={firstKey === key}
            moduleConfig={props.moduleConfig}
            key={`item-${groupIndex}-${key}`}
            options={isRFM ? filterSelectAchirve(group.list) : group.list}
            groupIndex={groupIndex}
            itemIndex={itemIndex}
            eventValue={eventValue}
            refineIndex={key}
            item={itemRefine}
            callback={callback}
            title={firstKey === key ? labelWhere : labelAnd}
            isViewMode={props.isViewMode}
            isRFM={isRFM}
            // operatorLabel={props.operatorLabel}
          />,
        );
      }
    });
    return result;
  };

  const showTitleRefine = () => {
    const result = [];

    if (refineWithProperties.size === 0) {
      result.push(labelWhere);
    } else if (!props.isViewMode) {
      result.push(labelAnd);
    }
    return result;
  };

  // if (props.isViewMode)
  //   return (
  //     <div style={{ display: 'flex', flexDirection: 'column' }}>
  //       {isLoading === false && showRefineWithProperties()}
  //     </div>
  //   );
  if (props.hidden) {
    return null;
  }

  if (isLoading)
    return (
      <DivLoading>
        <Loading isLoading={isLoading} size={20} />
      </DivLoading>
    );

  if (props.isViewMode && refineWithProperties.size === 0) return null;

  return (
    <>
      {/* <LayoutLoading isLoading={isLoading}> */}
      {isLoading === false && showRefineWithProperties()}

      {!props.isViewMode && refineWithProperties.size < limit && (
        <DataSourceBlockContent isRFM={isRFM}>
          {isRFM ? (
            <WrapperLabelRFM>{showTitleRefine()}</WrapperLabelRFM>
          ) : (
            <div className="title">
              <div className="refine-by-attr">{showTitleRefine()}</div>
            </div>
          )}
          <div className="content">
            <WrapperDisable
              disabled={isDisable || props.disabledEventConditions}
            >
              <StyledUIButtonWrapper>
                <UIButton
                  reverse
                  iconName="add"
                  fs="0.75rem"
                  theme="outline"
                  borderRadius="1rem"
                  onClick={addRefineProperties}
                  disabled={refineWithProperties.size >= 20}
                  style={{
                    color: isStyledRFM && isRFM && '#999',
                    border: 'solid 1px',
                  }}
                >
                  {labelRefineAttr}
                </UIButton>
              </StyledUIButtonWrapper>
            </WrapperDisable>
          </div>
        </DataSourceBlockContent>
      )}
      {/* </LayoutLoading> */}
    </>
  );
};

export default Refine;
