/* eslint-disable no-else-return */
/* eslint-disable no-undef */
import { OrderedMap } from 'immutable';
import {
  mapItemAttributes,
  mapItemEventPropertyForStory,
} from '../../../../../services/map';
import {
  safeParse,
  generateKey,
  snakeToCamel,
} from '../../../../../utils/common';
import {
  buildValueConditionFromAPI,
  getOperatorByValue,
  getOperatorByProperty,
  buildValueConditionFromUI,
  validateItemConditionV0,
} from '../../../../../containers/Filters/utils';
import { STATUS_ITEM_CODE } from '../../../../../utils/constants';

export const toEntryFE = (data, type) => {
  if (type === 'eventProperties') {
    return mapItemEventPropertyForStory(data);
  } else if (type === 'BOAttribute') {
    return mapItemAttributes(data.map(item => snakeToCamel(item)));
  }

  return [];
};

const commonValidateItemCondition = (item, type) => {
  const property = item.get('property');
  const operator = item.get('operator');
  const value = item.get('value');
  const valueEnd = item.get('valueEnd');

  return validateItemConditionV0(property, operator, value, valueEnd);
};

export function getLookupRefineWithProperties(refine) {
  const res = [];
  const ruleAND = safeParse(refine.AND, []);
  if (ruleAND.length > 0) {
    ruleAND.forEach(item => {
      res.push({
        eventPropertyName: item.column,
        itemTypeId: item.item_type_id,
      });
    });
  }
  return res;
}

export const toRefinePropertiesAPI = refineProperties => {
  const rule = [];
  if (!!refineProperties && refineProperties.size > 0) {
    const isInit = refineProperties.first().get('isInit');
    if (isInit === true) {
      return refineProperties.first().get('backup');
    }
    refineProperties.forEach(item => {
      if (commonValidateItemCondition(item)) {
        const statusItemCode = safeParse(
          item.get('statusItemCode'),
          STATUS_ITEM_CODE.ACTIVE,
        );
        let tempt = {};
        const objValue = buildValueConditionFromUI(item);

        if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
          const itemTypeId =
            item.get('property').itemTypeId === 0
              ? null
              : item.get('property').itemTypeId;

          const metaData = {
            itemTypeId,
            itemTypeName: item.get('property').itemTypeName,
            itemPropertyName: item.get('property').name,
            eventPropertySyntax: item.get('property').propertySyntax,
          };

          tempt = {
            type: item.get('property').type,
            column: item.get('property').name,
            dataType: item.get('property').itemDataType,
            data_type:
              item.get('property').itemDataType ||
              item.get('property').dataType,
            // fe_data_type: item.get('property').dataType,
            item_type_id: itemTypeId,
            operator: item.get('operator').value,
            syntax: item.get('property').propertySyntax,
            conditionType: 'event_attribute',
            ...objValue,
            metadata: metaData,
          };
        } else {
          tempt = item.get('backup');

          if (tempt !== null && tempt !== undefined) {
            tempt = { ...tempt, ...objValue };
          }
        }

        if (tempt !== null) {
          rule.push(tempt);
        }
      }
    });
  }

  return { OR: [{ AND: rule }] };
};

export function toRefinePropertiesUI(objRules, mapItem, mapInfoEventProperty) {
  let conditions = OrderedMap({});
  const ruleOr = safeParse(objRules.OR, [{ AND: [] }]);
  let rules = [];
  if (ruleOr.length > 0) {
    rules = safeParse(ruleOr[0].AND, []);
  }

  if (rules.length === 0) {
    return conditions;
  }
  rules.forEach(item => {
    let property = mapItem[`${item.column}-${safeParse(item.item_type_id, 0)}`];
    if ((property || {}).bracketed_code === 'customer_id') {
      property.options.forEach(each => {
        if (each.bracketed_code === 'customers.customer_id') {
          property = each;
        }
      });
    }
    // else if ((property || {}).bracketed_code === 'user_id') {
    //   property.options.forEach(each => {
    //     if (each.bracketed_code === 'users.user_id') {
    //       property = each;
    //     }
    //   });
    // }
    if (property === undefined) {
      property =
        mapInfoEventProperty[
        `${item.column}-${safeParse(item.item_type_id, 0)}`
        ];
    }
    if (property !== undefined) {
      const objValue = buildValueConditionFromAPI(item, property);
      const tempt = OrderedMap({
        ...objValue,
        property,
        statusItemCode: property.statusItemCode,
        operator: getOperatorByValue(`${property.dataType}-${item.operator}`),
        operators: getOperatorByProperty(property),
        dataType: property.dataType,
        syntax: property.syntax,
        backup: item,
      });

      conditions = conditions.set(generateKey(), tempt);
    }
  });
  return conditions;
}

export const filterSelectAchirve = data => {
  try {
    const result = [];
    data &&
      Array.isArray(data) &&
      data.forEach(item => {
        if (Number(item.status) !== 4) {
          if (item.options) {
            result.push({
              ...item,
              options: item.options.filter(opt => Number(opt.status) !== 4),
            });
          } else {
            result.push(item);
          }
        }
      });
    return result;
  } catch (e) {
    return [];
  }
};
export const MAX_CONVERISON = {
  minutes: 30,
  days: 365,
};
