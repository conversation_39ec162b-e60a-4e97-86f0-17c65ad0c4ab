/* eslint-disable react/prop-types */
import React, { memo, useMemo, useRef } from 'react';
import isEqual from 'react-fast-compare';

import SelectTree from 'components/form/UISelectCondition';
import Chip from '@material-ui/core/Chip';
import Icon from '@material-ui/core/Icon';
import {
  UITippy,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import classnames from 'classnames';
import { WrapperIcon } from '../../HeaderCondition/styled';
import {
  SelectedConditionTextMode,
  DataSourceBlockContent,
  BlockRefineCondition,
  StyleWrapperInputValue,
  SubContentTooltip,
  WrapperLabelRFM,
} from '../../styled';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import ConditionError from '../../../../../containers/Filters/AddFilter/FormCondition/ConditionError';
import ConditionValue from '../../../../../containers/Filters/AddFilter/FormCondition/ConditionValue';
import { formatValueItemByDataType } from '../../../../../containers/Filters/ItemRule/Preview/utils';
import {
  ARCHIVE_STATUS,
  REMOVE_STATUS,
  STATUS_ITEM_CODE,
  TYPE_ATTRIBUTE,
} from '../../../../../utils/constants';
import { safeParse } from '../../../../../utils/common';
import {
  filterDeep,
  makeArrayToLabelFilter,
} from '../../../../../utils/web/utils';
import { getInnerTextWidth } from '../../../../../modules/Dashboard/MarketingHub/Journey/Create/Content/InputAutoResize/utils';
import {
  getLabelFull,
  getLabelMulti,
} from '../../../../form/AutoSuggestion/utils';

const labelSelectAttrs = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_ATTRIBUTE,
  'Select attribute',
);
const labelSelectOperator = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_SELECT_OPERATOR,
  'Select operator',
);

const Refine = props => {
  const gridRef = useRef(0);
  const {
    item,
    options,
    groupIndex,
    itemIndex,
    refineIndex,
    // version,
    eventValue,
    isRFM,
  } = props;
  const filteredOptions = useMemo(
    () =>
      filterDeep(
        props.options,
        (_value, _key, option) => {
          const { status, type } = option;

          return (
            ![ARCHIVE_STATUS, REMOVE_STATUS].includes(Number(status)) ||
            type !== TYPE_ATTRIBUTE.COMPUTED
          );
        },
        {
          childrenPath: 'options',
        },
      ),
    [options],
  );

  const changeProperty = value => {
    props.callback('CHANGE_PROPERTY', {
      groupIndex,
      itemIndex,
      refineIndex,
      value,
    });
  };

  const changeOperator = value => {
    props.callback('CHANGE_OPERATOR', {
      groupIndex,
      itemIndex,
      refineIndex,
      value,
    });
  };

  const changeValue = (value, label, extend) => {
    props.callback('CHANGE_VALUE', {
      groupIndex,
      itemIndex,
      refineIndex,
      value,
      extendValue: extend,
    });
  };

  const changeOtherValue = (name, value) => {
    props.callback('CHANGE_VALUE', {
      groupIndex,
      itemIndex,
      refineIndex,
      value,
      name,
    });
  };

  const deleteItem = () => {
    props.callback('DELETE_ITEM', {
      groupIndex,
      itemIndex,
      refineIndex,
    });
  };

  const property = safeParse(item.get('property'), null);
  const error = safeParse(item.get('error'), 0);
  const statusItemCode = safeParse(
    (props.item.get('property') || {}).statusItemCode,
    STATUS_ITEM_CODE.ACTIVE,
  );
  const divDisabled =
    statusItemCode !== STATUS_ITEM_CODE.ACTIVE &&
    statusItemCode !== STATUS_ITEM_CODE.DISABLED;

  const getSelectedConditionTextMode = () => {
    const operator = item.get('operator');

    const initValue = formatValueItemByDataType(item, true, true);

    const value = Array.isArray(initValue)
      ? makeArrayToLabelFilter(initValue)
      : initValue;

    if (
      operator &&
      [
        'contains',
        'doesnt_contain',
        'start_with',
        'not_start_with',
        'end_with',
        'not_end_with',
        'equals',
        'not_equals',
      ].includes(operator.value)
    ) {
      return (
        <div>
          <Chip
            style={{ minHeight: '28px', height: 'unset' }}
            variant="outlined"
            label={
              <SelectedConditionTextMode>
                <span className="property-text">{property.label}&nbsp;</span>
                <span className="operator-text">{operator.label}&nbsp;</span>
                <span className="property-text">{`"${value}"`}</span>
              </SelectedConditionTextMode>
            }
          />
          <span>
            <ConditionError
              style={{ width: '100%' }}
              statusItemCode={statusItemCode}
              item={props.item}
              isErrorIcon
            />
          </span>
        </div>
      );
    }

    if (operator && ['matches', 'not_matches'].includes(operator.value)) {
      const label = safeParse(item.get('value'), null);
      const preText = `${options[0]?.label} >> ${property.label} ${
        operator.label
      } `;
      const preTexWidth = +getInnerTextWidth(preText, { fontSize: '12px' });
      const valueMulti = getLabelMulti(
        label,
        gridRef &&
          gridRef.current &&
          gridRef.current.offsetWidth - preTexWidth - 70,
        true,
        ['array_string', 'string'].includes(property.dataType),
        {
          fontSize: '12px',
        },
      );

      const valueTooltip = getLabelFull(
        label,
        true,
        ['array_string', 'string'].includes(property.dataType),
      );

      return (
        <div>
          <Chip
            style={{ minHeight: '28px', height: 'unset' }}
            variant="outlined"
            label={
              <SelectedConditionTextMode>
                <span className="property-text">{`${property.label} `}</span>
                <span className="operator-text">{operator.label}&nbsp;</span>
                <span className="property-text">{valueMulti.labels}&nbsp;</span>
                {valueMulti.labelMore && (
                  <>
                    <span className="property-text">and</span>
                    <UITippy
                      content={valueTooltip.labels}
                      showPopupWhenClick
                      subContent={
                        <SubContentTooltip>
                          Click <span>{valueMulti.labelMore}</span> to view more
                        </SubContentTooltip>
                      }
                    >
                      <span className="text-more">
                        {valueMulti.labelMore}&nbsp;
                      </span>
                    </UITippy>
                  </>
                )}
              </SelectedConditionTextMode>
            }
          />
          <span>
            <ConditionError
              style={{ width: '100%' }}
              statusItemCode={statusItemCode}
              item={props.item}
              isErrorIcon
            />
          </span>
        </div>
      );
    }
    return (
      <div>
        <Chip
          style={{ minHeight: '28px', height: 'unset' }}
          variant="outlined"
          label={
            <SelectedConditionTextMode>
              <span className="property-text">{property.label}&nbsp;</span>
              <span className="operator-text">{operator.label}&nbsp;</span>
              <span className="property-text">{value}</span>
            </SelectedConditionTextMode>
          }
        />
        <span>
          <ConditionError
            style={{ width: '100%' }}
            statusItemCode={statusItemCode}
            item={props.item}
            isErrorIcon
          />
        </span>
      </div>
    );
  };

  return (
    <React.Fragment>
      <DataSourceBlockContent
        className={props.isViewMode && 'padding-sm'}
        isRFM={isRFM}
      >
        {isRFM ? (
          <WrapperLabelRFM>{props.title}</WrapperLabelRFM>
        ) : (
          <div className="title">
            <div
              className={classnames('refine', {
                'view-mode': props.isViewMode,
              })}
            >
              {props.title}
            </div>
          </div>
        )}

        {props.isViewMode ? (
          <div className="w-100" ref={gridRef}>
            {getSelectedConditionTextMode()}
          </div>
        ) : (
          <div className="content">
            <BlockRefineCondition>
              <div className="property">
                <WrapperDisable disabled={divDisabled}>
                  <SelectTree
                    onlyParent={props.onlyParent}
                    displayFormat
                    use="tree"
                    // isMulti
                    // isSearchable
                    options={filteredOptions}
                    isParentOpen={props.isParentOpen}
                    value={property}
                    onChange={changeProperty}
                    placeholder={labelSelectAttrs}
                  />
                  <ConditionError keyError={1} error={error} />
                </WrapperDisable>
              </div>
              <div className="operator">
                <WrapperDisable disabled={divDisabled}>
                  <SelectTree
                    onlyParent={props.onlyParent}
                    use="tree"
                    // isMulti
                    isSearchable={false}
                    options={item.get('operators').list}
                    value={item.get('operator')}
                    onChange={changeOperator}
                    placeholder={labelSelectOperator}
                  />
                  <ConditionError keyError={2} error={error} />
                </WrapperDisable>
              </div>
              <div className="value">
                <WrapperDisable disabled={divDisabled}>
                  {property !== null && (
                    <StyleWrapperInputValue>
                      <ConditionValue
                        maxWidth="100%"
                        width="100%"
                        item={item}
                        // disabled={divDisabled}
                        dataType={property.dataType}
                        eventValue={eventValue}
                        changeValue={changeValue}
                        changeOtherValue={changeOtherValue}
                        error={error}
                        isShowLabel={false}
                        isUseLabel={false}
                      />
                    </StyleWrapperInputValue>
                  )}
                </WrapperDisable>
              </div>
              <ConditionError
                statusItemCode={statusItemCode}
                item={item}
                isErrorIcon
              />
              {!props.isViewMode && (
                <div className="cancel">
                  <WrapperIcon
                    isRFM={isRFM}
                    className="refine-close"
                    onClick={deleteItem}
                  >
                    <Icon>{isRFM ? 'cancel' : 'close'}</Icon>
                  </WrapperIcon>
                </div>
              )}
            </BlockRefineCondition>
          </div>
        )}
      </DataSourceBlockContent>

      {/* <DataSourceBlockContent className="condition-error">
        <div className="title" />
        <div className="content">
        </div>
      </DataSourceBlockContent> */}
    </React.Fragment>
  );
};

export default memo(Refine, (prevProps, props) => {
  const { item: prevItem, ...otherPrevProps } = prevProps;

  const { item, ...otherProps } = props;

  return isEqual(otherPrevProps, otherProps) && item.equals(prevItem);
});
