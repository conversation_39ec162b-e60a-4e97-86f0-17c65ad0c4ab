import { Grid } from '@material-ui/core';
import styled from 'styled-components';

export const StyleWapperListFilter = styled.div`
  position: relative;
  .private-overlay {
    padding-bottom: 30px !important;
    padding-top: 30px !important;
  }
`;

export const Wrapper = styled.div`
  overflow-x: hidden;
  align-items: center;
  cursor: pointer;
  display: flex;
  min-width: 0;
  /* min-height: 38px; */
  .chip-container {
    margin: 0px 4px 0 0px;
    overflow: hidden;
    font-size: 0.813rem;
    font-family: Roboto-Bold;
  }
`;

export const WrapperPreviewSource = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
  .predicates-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  gap: 10px;
`;

export const ContainerWrapperInputValue = styled.div`
  display: flex;
  align-items: center;
  ${({ justifyContent }) =>
    justifyContent && `justify-content: ${justifyContent};`}
  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: solid !important;
  }
`;

export const StyleWrapperInputValue = styled.div`
  max-width: 100%;
  flex: 1;
  .div-span-and {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    position: unset;
  }
  .private-form__control {
    width: 100%;
  }
  .MuiInputBase-root {
    width: 100%;
    height: 32px;
  }
  .button-dropdown-ui-select {
    white-space: nowrap;
  }
  .ui-button-dropndown {
    height: 32px;
  }
  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: solid !important;
  }
  [class*='ConditionValueNumberBetween'] {
  }
`;

/* export const StyleSpan = styled.span` */
/*   color: ${colors.tertiary500}; */
/* `; */

export const DivLoading = styled.div`
  height: 30px;
  width: 20%;
  position: relative;
  .private-overlay {
    padding: 0 !important;
  }
`;

/* export const StyleSpanAnd = styled.span` */
/*   white-space: nowrap; */
/*   padding: 0 10px; */
/* `; */

export const StyledUIButtonWrapper = styled.div`
  [class*='MuiIcon-root'] {
    font-size: 1.063rem;
    margin: 0 !important;
  }

  button {
    gap: 0.625rem;
    height: 1.688rem;
    padding: 0 0.625rem;
  }
`;

export const SelectedConditionTextMode = styled.div`
  font-size: 12px;
  color: #000;
  white-space: nowrap;
  text-wrap: wrap;

  .operator-text {
    font-size: 12px;
    color: #000;
  }
  .property-text,
  .value-text,
  .text-more {
    font-weight: 700;
  }
  .textAnd {
    font-weight: normal !important;
  }
  .text-more {
    color: #005eb8;
  }
`;

export const DataSourceBlockContent = styled.div`
  display: flex;
  padding: ${props => (props.isRFM ? '10px 20px 10px 0' : '10px 20px')};
  width: 100%;
  gap: 20px;

  &.condition-error {
    padding: 0 20px;
  }

  &.padding-sm {
    padding: 4px 20px;
  }

  .title {
    min-width: 90px;
    text-align: right;
    color: #595959;
    font-size: 12px;

    &.select-event {
      align-self: center;
    }

    .select-source-txt {
      position: relative;
      line-height: 27px;
      top: 13.5px;
      transform: translateY(-50%);
    }

    .refine-by-attr {
      position: relative;
      top: 13.5px;
      transform: translateY(-50%);
    }

    .refine {
      position: relative;

      line-height: 32px;
      top: 16px;

      &.view-mode {
        line-height: 28px;
        top: 14px;
      }

      transform: translateY(-50%);
    }
  }

  & > .content {
    flex-grow: 1;
    font-size: 12px;
    display: flex;
    flex-direction: ${props => (props.isViewMode ? 'column' : 'row')};
  }
`;

export const BlockRefineCondition = styled.div`
  width: 100%;
  display: flex;

  & > * {
    margin: 0;
    box-sizing: border-box;
  }

  .property {
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 25%;
    padding-right: 20px;
  }
  .operator {
    flex-grow: 0;
    max-width: 25%;
    flex-basis: 25%;
    padding-right: 20px;
  }
  .value {
    flex-grow: 0;
    max-width: 45%;
    flex-basis: 45%;
    padding-right: 20px;
  }
  .cancel {
  }
`;

export const SubContentTooltip = styled.div`
  color: #9a9a9a;
  span {
    color: #c4c4c4;
    font-weight: 700;
  }
`;

export const WrapperLabelRFM = styled.div`
  color: #000000;
  font-size: 12px;
  width: 150px;
  min-width: 150px;
  display: block;
  text-align: left;
`;

export const ContainerCondition = styled.div``;

export const UIPerformEventContainer = styled.div``;

export const ContentContainer = styled(Grid)``;
