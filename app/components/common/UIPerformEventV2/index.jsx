/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
import React, { useEffect, useMemo } from 'react';
import { useImmer } from 'use-immer';
import cls from 'classnames';
import SegmentServices from 'services/Segment';
import { OrderedMap } from 'immutable';
import MetaDataServices from 'services/MetaData';
import Grid from '@material-ui/core/Grid';
import { UIButton, UILoading } from '@xlab-team/ui-components';

import {
  initItem,
  initItemRefinePropertiesValue,
} from 'containers/Segment/Content/Condition/utils';
import BlockConditionPerformEvent from './BlockConditionPerformEvent';
import { toEventTrackingFE } from '../../../modules/Dashboard/Profile/Segment/Create/_reducer/utils';
import {
  MAP_SEGMENT_VALUES,
  PERF_EVENT_OPERATORS,
  SEGMENT_TYPES,
} from './constants';
import { generateKey, safeParse } from '../../../utils/common';
import {
  getFirstOperator,
  getOperatorByProperty,
} from '../../../containers/Filters/utils';
import {
  toComponentPerformEvent,
  deleteRefinesBySourceId,
  getUnusedEvents,
  getValueToRules,
  addRemoveElementArray,
} from './utils';
import { useStyles } from '../UISchedulerTrigger/styled';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import {
  validateRulesConditionPerformEvent,
  bindValidateResultToCondition,
} from './utils.validate';
import ModalFlowChartConfirmChangeEvent from '../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/modals/ModalFlowChartConfirmChangeEvent';
import { addMessageToQueue } from '../../../utils/web/queue';
import useUpdateEffect from '../../../hooks/useUpdateEffect';
import {
  ContentContainer,
  StyledUIButtonWrapper,
  UIPerformEventContainer,
} from './styled';
import { TextButton } from '../UIPerformEvent/HeaderCondition/styled';
import { useIsMounted } from '../../../hooks';

const INITIAL_STATE = {
  eventSchema: {
    list: [],
    map: {},
    listBackup: [],
  },
  isFetchDone: false,
  isLoading: true,
  rules: OrderedMap({}),
  isOpenModal: false,
  tempEventChanged: {},
};

const UIPerformEvent = props => {
  const isMounted = useIsMounted();

  const {
    initWithDefaultEvents,
    eventPropertyService,
    getFullEventTrackingService,
    getFullEventTrackingMapService,
    isUsingJourneyTemplate,
    initData,
    showAllSource,
  } = props;

  const [state, setState] = useImmer(INITIAL_STATE);

  const unUsedEvents = useMemo(
    () => getUnusedEvents(state.eventSchema.list, state.rules),
    [state.eventSchema.list, state.rules],
  );

  const fetchData = async () => {
    try {
      const res = await getFullEventTrackingService(props.paramsFetchEvent);

      if (isMounted()) {
        if (res.code === 200) {
          const { list, map } = toEventTrackingFE(
            res.data,
            'info',
            props.limitEvent,
          );

          const tmp = initItem(SEGMENT_TYPES[0]);
          setState(draft => {
            draft.eventSchema.list = list;
            draft.eventSchema.listBackup = list;
            draft.eventSchema.map = map;
            draft.rules = OrderedMap({ [generateKey()]: tmp });
            draft.isFetchDone = true;
          });
        } else {
          setState(draft => {
            draft.eventSchema.list = [];
            draft.eventSchema.listBackup = [];
            draft.eventSchema.map = {};
            draft.isFetchDone = true;
          });
        }
      }
    } catch (err) {
      if (isMounted()) {
        setState(draft => {
          draft.eventSchema.list = [];
          draft.eventSchema.listBackup = [];
          draft.eventSchema.map = {};
          draft.isFetchDone = true;
        });
        // console.log('err ===>', err);
        addMessageToQueue({
          path: 'app/components/common/UIPerformEvent/index.jsx',
          func: 'fetchData',
          data: err.stack,
        });
      }
    }
  };

  useEffect(() => {
    if (props.isLoading) return;

    setState(_ => INITIAL_STATE);
    fetchData();
  }, [props.componentId, props.isLoading]);

  useEffect(() => {
    if (!state.isFetchDone) return;

    let isInit = false;
    let backup = [];
    let condition = safeParse(initData, OrderedMap({}));
    const dataSourceID = '-1';

    if (condition.size === 0 || Object.keys(condition).length === 0) {
      condition = handleInitDefaultCondition();
    } else if (condition.size > 0) {
      isInit = condition.first().get('isInit');
      backup = condition.first().get('backup', []);
    }

    if (backup.length) {
      const promises = backup.map(item => {
        const { eventActionId, eventCategoryId } = item;
        // check key isInit in First = true thì set property cho nó
        const paramLookupEvent = {
          insightPropertyId: dataSourceID,
          dataPost: {
            eventIds: [
              {
                eventActionId,
                eventCategoryId,
              },
            ],
          },
        };

        return getFullEventTrackingMapService(paramLookupEvent);
      });

      Promise.all(promises).then(values => {
        values.forEach((item, index) => {
          const tmpConditions = toComponentPerformEvent(backup[index], {
            map: { eventSchema: state.eventSchema.map },
            info: { eventSchema: item.map },
          });

          setState(draft => {
            if (isInit) {
              if (index === 0) {
                let conditions = OrderedMap({});
                conditions = conditions.setIn([generateKey()], tmpConditions);
                draft.rules = conditions;
              } else {
                draft.rules = draft.rules.setIn([generateKey()], tmpConditions);
              }
            } else {
              draft.rules = condition;
            }
            draft.isLoading = false;
          });
        });
      });
    } else {
      setState(draft => {
        draft.rules = condition;
        draft.isLoading = false;
      });
    }
  }, [state.isFetchDone]);

  const callback = (type, data) => {
    if (state.isLoading) return;

    switch (type) {
      case 'CONFIRM_CHANGE_EVENT': {
        if (data === true) {
          const { groupIndex, itemIndex, value } = state.tempEventChanged;

          const oldItem = safeParse(
            state.rules.getIn([groupIndex, itemIndex, 'property']),
            null,
          );

          if (oldItem === null || oldItem.value !== value.value) {
            // const { eventActionId, eventCategoryId } = value.value;
            const operators = PERF_EVENT_OPERATORS;

            const tempt = state.rules
              .getIn([groupIndex, itemIndex])
              .withMutations(map => {
                map
                  .set('value', '1')
                  .set('valueEnd', '')
                  .set('initValue', '')
                  // .set('keyEvent', `${eventCategoryId}-${eventActionId}`)
                  .set('property', value)
                  .set('error', 0)
                  .set('dataSources', [])
                  .set('dataType', value.dataType)
                  .set('operator', operators[0]) // no use getFirstOperator
                  .set('operators', operators)
                  .set('refineWithProperties', OrderedMap({}));
              });

            setState(draft => {
              draft.rules = state.rules.setIn([groupIndex, itemIndex], tempt);
            });
          }
          props.callback('RESET_TRIGGER_PERFORM_EVENT', value);
          props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', value);
        }
        break;
      }
      case 'COMP_PROP_CONDITION_CHANGE_PROPERTY': {
        const { groupIndex, itemIndex, value, isInit = false } = data.data;
        if (props.hasOpenModalConfirm && !isInit) {
          setState(draft => {
            draft.isOpenModal = true;
            draft.tempEventChanged = data.data;
          });
        } else {
          const oldItem = safeParse(
            state.rules.getIn([groupIndex, itemIndex, 'property']),
            null,
          );

          if (oldItem === null || oldItem.value !== value.value) {
            // const { eventActionId, eventCategoryId } = value.value;
            const operators = PERF_EVENT_OPERATORS;

            const tempt = state.rules
              .getIn([groupIndex, itemIndex])
              .withMutations(map => {
                map
                  .set('value', '1')
                  .set('valueEnd', '')
                  .set('initValue', '')
                  // .set('keyEvent', `${eventCategoryId}-${eventActionId}`)
                  .set('property', value)
                  .set('error', 0)
                  .set('isInitDataSources', false)
                  .set('dataSources', [])
                  .set('dataType', value.dataType)
                  .set('operator', operators[0]) // no use getFirstOperator
                  .set('operators', operators)
                  .set('refineWithProperties', OrderedMap({}));
              });

            setState(draft => {
              draft.rules = state.rules.setIn([groupIndex, itemIndex], tempt);
            });
          }

          props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', value);
        }

        break;
      }

      case 'PERF_EVENT_CONDITION_CHANGE_SOURCE': {
        const { groupIndex, itemIndex, value } = data.data;
        const currentRule = state.rules.getIn([groupIndex, itemIndex]);
        let dataRefine = currentRule.get('refineWithProperties');

        if (value.isDeleteRefine) {
          dataRefine = deleteRefinesBySourceId(
            currentRule.get('refineWithProperties'),
            value.sourceIdWillDeleted,
          );
        }

        const tempt = state.rules
          .getIn([groupIndex, itemIndex])
          .withMutations(map => {
            map.set('errorDataSources', 0);
            map.set('isInitDataSources', false);
            map.set('dataSources', value.dataSourceSelected);
            map.set(
              'refineWithProperties',
              value.dataSourceSelected.length === 0
                ? OrderedMap({})
                : dataRefine,
            );
          });

        setState(draft => {
          draft.rules = state.rules.setIn([groupIndex, itemIndex], tempt);
        });
        break;
      }

      case 'PERF_EVENT_CONDITION_CHANGE_CONVERSION_WINDOW': {
        const { groupIndex, itemIndex } = data;
        setState(draft => {
          draft.rules = state.rules.setIn(
            [groupIndex, itemIndex, 'conversionWindow'],
            data.data,
          );
        });
        break;
      }

      case 'PERF_EVENT_CHANGE_REFINE_PROPERTIES': {
        const { type: typeChange } = data;
        const { groupIndex, itemIndex, refineIndex, value } = data.data;
        if (typeChange === 'INIT') {
          setState(draft => {
            draft.rules = state.rules.setIn(
              [groupIndex, itemIndex, 'refineWithProperties'],
              data.data.conditions,
            );
          });
        }
        if (typeChange === 'CHANGE_PROPERTY') {
          const oldItem = safeParse(
            state.rules.getIn([
              groupIndex,
              itemIndex,
              'refineWithProperties',
              refineIndex,
              'property',
            ]),
            null,
          );

          if (oldItem === null || oldItem.value !== value.value) {
            const operators = getOperatorByProperty(value);
            const tempt = state.rules
              .getIn([
                groupIndex,
                itemIndex,
                'refineWithProperties',
                refineIndex,
              ])
              .withMutations(map => {
                map
                  .set('value', '')
                  .set('valueEnd', '')
                  .set('initValue', '')
                  .set('property', value)
                  .set('error', 0)
                  .set('dataType', value.dataType)
                  .set('operator', getFirstOperator(operators))
                  .set('operators', operators)
                  .set('syntax', value.propertySyntax);
              });

            setState(draft => {
              draft.rules = state.rules.setIn(
                [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
                tempt,
              );
            });
          }
        } else if (typeChange === 'CHANGE_OPERATOR') {
          const tempt = state.rules
            .getIn([groupIndex, itemIndex, 'refineWithProperties', refineIndex])
            .withMutations(map => {
              map
                .set('value', '')
                .set('valueEnd', '')
                .set('initValue', '')
                .set('error', 0)
                .set('operator', value);
            });

          setState(draft => {
            draft.rules = state.rules.setIn(
              [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
              tempt,
            );
          });
        } else if (typeChange === 'CHANGE_VALUE') {
          const { name = 'value', extendValue } = data.data;
          // console.log('data', name, value);
          let tempt = state.rules
            .getIn([groupIndex, itemIndex, 'refineWithProperties', refineIndex])
            .withMutations(map => {
              map.set('error', 0).set(name, value);
            });
          if (extendValue && extendValue.length > 0) {
            tempt = tempt.set('extendValue', extendValue);
          }
          setState(draft => {
            draft.rules = state.rules.setIn(
              [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
              tempt,
            );
          });
        } else if (typeChange === 'ADD_ITEM') {
          setState(draft => {
            draft.rules = state.rules.setIn(
              [groupIndex, itemIndex, 'refineWithProperties', generateKey()],
              initItemRefinePropertiesValue(),
            );
          });
        } else if (typeChange === 'DELETE_ITEM') {
          setState(draft => {
            draft.rules = state.rules.deleteIn(
              [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
              value,
            );
          });
        }
        break;
      }

      case 'PERF_EVENT_CHANGE_TIME_WINDOW': {
        const { groupIndex, itemIndex, value, object } = data.data;
        // object === 'operator|value|start_date|end_date';

        if (object === 'start_date') {
          const end_date = state.rules.getIn([
            groupIndex,
            itemIndex,
            'timeWindow',
            'end_date',
          ]);
          if (parseInt(value) > parseInt(end_date)) {
            setState(draft => {
              draft.rules = state.rules
                .setIn(
                  [groupIndex, itemIndex, 'timeWindow', 'start_date'],
                  value,
                )
                .setIn(
                  [groupIndex, itemIndex, 'timeWindow', 'end_date'],
                  value,
                );
            });
          }
        }
        setState(draft => {
          draft.rules = state.rules.setIn(
            [groupIndex, itemIndex, 'timeWindow', object],
            value,
          );
        });

        break;
      }
      case 'PERF_EVENT_CHANGE_HAS_FILTER': {
        const { groupIndex, itemIndex, hasFilter } = data;

        setState(draft => {
          draft.rules = draft.rules.setIn(
            [groupIndex, itemIndex, 'hasFilter'],
            hasFilter,
          );
        });

        break;
      }
      case 'DELETE_ITEM': {
        const {
          groupIndex,
          // itemIndex
        } = data;

        setState(draft => {
          draft.rules = draft.rules.deleteIn([groupIndex]);
        });
        break;
      }
      default:
        break;
    }
  };

  // useEffect(() => {
  //   fetchData();
  // }, []);

  useUpdateEffect(() => {
    if (state.rules.size > 0) {
      const validate = validateRulesConditionPerformEvent(state.rules);

      const tmpCondition = bindValidateResultToCondition(
        state.rules,
        validate.errors,
      );
      setState(draft => {
        draft.rules = tmpCondition;
      });
    }
  }, [props.validateKey]);

  useUpdateEffect(() => {
    const valuePropertive = getValueToRules(state.rules);
    if (props.hiddeOption) {
      setState(draft => {
        draft.eventSchema.list = addRemoveElementArray(
          state.eventSchema.listBackup,
          valuePropertive,
        );
      });
      // setState(draft => {
      //   draft.eventSchema.list.forEach(each => {
      //     if (valuePropertive.includes(each.eventTrackingCode)) {
      //       each.disabled = true;
      //     } else {
      //       each.disabled = false;
      //     }
      //   });
      // });
    }

    // const rules = toAPIPerformEvent(state.rules);
    props.onChange(state.rules);
  }, [state.rules]);

  const toggleModal = value => {
    setState(draft => {
      draft.isOpenModal = value;
    });
  };

  const handleAddPerformEvent = addInfo => {
    setState(draft => {
      const tmp = initItem(SEGMENT_TYPES[0], undefined, {
        property: addInfo.event,
      });

      draft.rules = state.rules.setIn([generateKey()], tmp);
    });
  };

  const handleInitDefaultCondition = () => {
    let condition = OrderedMap({});

    const {
      enable = false,
      useDefaultInitWhenEmpty = false,
      limit = 10,
      predicate = () => false,
    } = initWithDefaultEvents;

    const defaultInit = () => {
      const tmp = initItem(SEGMENT_TYPES[0]);

      condition = condition.setIn([generateKey()], tmp);
    };

    if (enable) {
      state.eventSchema.list.filter(predicate).forEach(event => {
        const tmp = initItem(SEGMENT_TYPES[0], undefined, {
          property: event,
        });

        if (condition.size < limit) {
          condition = condition.setIn([generateKey()], tmp);
        }
      });

      if (condition.isEmpty() && useDefaultInitWhenEmpty) {
        defaultInit();
      }
    } else {
      defaultInit();
    }

    return condition;
  };

  const showContent = rules => {
    const isCustomRender = typeof props.renderUI === 'function';

    if (rules.size > 0) {
      const result = [];

      let index = 0;

      let title = null;

      if (index === 0 && props.showCustomTitle) {
        title = getTranslateMessage(TRANSLATE_KEY._INFO_STORY_TRIGGER_WHEN, '');
      } else if (props.showHeader) {
        title = <div style={{ fontWeight: 700 }}>OR</div>;
      }
      rules.forEach((item, key) => {
        item.forEach((element, itemIndex) => {
          if (
            element.get('conditionType') &&
            element.get('conditionType').value === MAP_SEGMENT_VALUES.perf_event
          ) {
            const options = props.allowDuplicateEvents
              ? state.eventSchema.list
              : unUsedEvents;

            const renderBlockEvent = otherProps => (
              <BlockConditionPerformEvent
                isViewMode={props.isViewMode}
                key={itemIndex}
                moduleConfig={props.moduleConfig}
                paramsFetchEvent={props.paramsFetchEvent}
                groupIndex={key}
                itemIndex={itemIndex}
                item={element}
                options={options}
                callback={callback}
                isLoading={state.isLoading}
                disabledEvent={props.disabledEvent}
                disabledEventConditions={props.disabledEventConditions}
                showButtonClose={rules.size > 1}
                isConversion={props.isConversion}
                showHeader={props.showHeader}
                rules={rules}
                hiddeOption={props.hiddeOption}
                defaultOption={props.defaultOption}
                eventPropertyService={eventPropertyService}
                isUsingJourneyTemplate={isUsingJourneyTemplate}
                showAllSource={showAllSource}
                {...otherProps}
              />
            );

            if (isCustomRender) {
              result.push({
                key,
                event: element,
                renderBlockEvent,
              });
            } else {
              result.push(
                <Grid item container sm={12} key={itemIndex}>
                  {!props.hiddenTitle && !props.isUIConversion && (
                    <Grid
                      item
                      sm={2}
                      style={{
                        textAlign: 'left',
                        // marginRight: '1.25rem',
                        maxWidth: '150px',
                      }}
                    >
                      {title}
                    </Grid>
                  )}

                  <Grid item sm={9} lg={10}>
                    {renderBlockEvent()}
                  </Grid>
                </Grid>,
              );
            }

            index += 1;
          }
        });
      });

      if (isCustomRender) {
        return props.renderUI({
          renderedEvents: result,
          callback,
        });
      }

      return result;
    }

    return null;
  };

  const renderAddEventBtn = () => {
    if (
      !props.isMultiple ||
      props.isViewMode ||
      props.hiddenButtonAdd ||
      props.maximumEvent <= state.rules.size
    )
      return null;

    if (typeof props.renderAddButton === 'function') {
      return props.renderAddButton({
        addEvent: addInfo => handleAddPerformEvent(addInfo),
        unUsedEvents,
      });
    }
    if (props.isConversion) {
      return (
        <Grid item sm={2} style={{ maxWidth: '150px' }}>
          <StyledUIButtonWrapper style={{ marginLeft: '-5px' }}>
            {state.rules.size < 3 && (
              <UIButton
                theme="outline"
                fs="12px"
                style={{
                  border: 'solid 1px',
                  background: 'none',
                  padding: '5px',
                  color: '#005Eb8',
                }}
                borderRadius="1rem"
                onClick={handleAddPerformEvent}
                disabled={state.rules.size >= 3}
                reverse
                iconName="add"
              >
                <TextButton style={{ width: '100px' }}>
                  More Contribute
                </TextButton>
              </UIButton>
            )}
          </StyledUIButtonWrapper>
        </Grid>
      );
    }
    return (
      <>
        <Grid item sm={2} style={{ maxWidth: '150px' }} />
        <Grid item sm={9} lg={10}>
          <div style={{ marginLeft: '1.25rem' }}>
            <StyledUIButtonWrapper>
              <UIButton
                theme="outline"
                fs="12px"
                borderRadius="1rem"
                onClick={handleAddPerformEvent}
                disabled={state.rules.size >= 3}
                reverse
                style={{
                  border: 'solid 1px',
                }}
                iconName="add"
              >
                <TextButton>OR</TextButton>
              </UIButton>
            </StyledUIButtonWrapper>
          </div>
        </Grid>
      </>
    );
  };

  const classes = useStyles();
  if (state.isLoading) {
    return <UILoading isLoading />;
  }

  return (
    <UIPerformEventContainer className={cls(classes.root, props.className)}>
      {props.showTitle && (
        <Grid container className={classes.padding}>
          {props.showCustomeTitle && (
            <Grid item sm={12} className={classes.customView}>
              {props.title}
              {/* {getTranslateMessage(TRANSLATE_KEY._INFO_STORY_TRIGGER_WHEN, '')} */}
            </Grid>
          )}
        </Grid>
      )}

      <ContentContainer
        container
        className={`${!props.isUIConversion &&
          classes.padding} wrapper-perform-event`}
      >
        {showContent(state.rules)}

        {renderAddEventBtn()}
      </ContentContainer>

      <ModalFlowChartConfirmChangeEvent
        isOpen={state.isOpenModal}
        toggle={toggleModal}
        callback={callback}
      />
    </UIPerformEventContainer>
  );
};

UIPerformEvent.defaultProps = {
  initData: undefined,
  onChange: () => {},
  callback: () => {},
  validateKey: 1,
  showTitle: true,
  showCustomTitle: true,
  disabledEvent: false,
  disabledEventConditions: false,
  hasOpenModalConfirm: false,
  paramsFetchEvent: {},
  maximumEvent: Infinity,
  initWithDefaultEvents: {},
  allowDuplicateEvents: true,
  hiddenButtonAdd: false,
  title: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_TRIGGER_WHEN, ''),
  isConversion: false,
  showHeader: true,
  limitEvent: [],
  hiddeOption: false,
  defaultOption: ['view_pageview'],
  isUIConversion: false,
  eventPropertyService: MetaDataServices.eventProperty.lookupByIds,
  getFullEventTrackingService: SegmentServices.fetch.getListEvent,
  getFullEventTrackingMapService: MetaDataServices.eventSchema.lookupByIds,
};

export default UIPerformEvent;
