/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/prop-types */
import React from 'react';
import { UITippy } from '@xlab-team/ui-components';
import Icon from '@material-ui/core/Icon';
import { MaterialChip, Wrapper } from './styles';
import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';
import { bindTrigger } from 'material-ui-popup-state/hooks';

const ItemPreviewSourceSelected = props => {
  const {
    index,
    label,
    value,
    item,
    isError,
    disabled,
    popupStateMenu,
    // isUsesPersonal,
    isComfirmDelete,
  } = props;

  const onClickButtonDelete = () => {
    props.callback('DELETE_SOURCE_ITEM', { index, item });
  };

  return (
    <div className="predicates-container" key={`${label}-${index.toString()}`}>
      <Wrapper id={`item-rule-${value}`}>
        <div className="chip-container">
          <UITippy content={label} arrow distance={10}>
            <div>
              <WrapperDisable disabled={disabled}>
                <MaterialChip variant="contained" isError={isError}>
                  <div
                    className="content-chip"
                    style={{ fontSize: props.isViewMode && '12px' }}
                    {...bindTrigger(popupStateMenu)}
                    onClick={() => {
                      if (popupStateMenu) {
                        bindTrigger(popupStateMenu).onClick();
                      }
                      if (isComfirmDelete) {
                        props.callback('IS_CHANGE_ITEM_TYPE_IDS');
                        // onClickButtonDelete();
                      }
                    }}
                  >
                    {label}
                  </div>
                  {!props.isViewMode && (
                    <div className="delete-button">
                      <Icon onClick={onClickButtonDelete} fontSize="small">
                        close
                      </Icon>
                    </div>
                  )}
                </MaterialChip>
              </WrapperDisable>
            </div>
          </UITippy>
        </div>
      </Wrapper>
    </div>
  );
};
ItemPreviewSourceSelected.defaultProps = {
  popupStateMenu: 'tmp',
};
export default ItemPreviewSourceSelected;
