/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import { PropTypes } from 'prop-types';
import { connect } from 'react-redux';

import {
  UIModal,
  UIModalHeader,
  UIModalBody,
  UIModalFooter,
} from '@xlab-team/ui-components';
import styled from 'styled-components';
import ModalFooter from 'components/Molecules/ModalFooter/index';
import { UIButton as Button } from '@xlab-team/ui-components';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { addNotification } from '../../../redux/actions';
import { ModalV2 } from '@antscorp/antsomi-ui';

const WrapperBody = styled.div``;

export const ButtonStyle = styled(Button)`
  margin: 0rem 0.25rem;
`;

export const ModalFooterStyle = styled(ModalFooter)`
  justify-content: flex-end;
  padding-right: 2rem;
`;

function ModalConfirmDeleteSource(props) {
  const onCancel = () => {
    props.setIsOpenModal(false);
  };

  const onApply = () => {
    if (props.isChangeItemTypeId) {
      props.callback('CONFIRM_CHANGE_ITEM_TYPE_ID', {
        itemTypeId: props.itemTypeId,
      });
    } else {
      props.callback('CONFIRM_DELETE_SOURCE', true);
    }
    props.setIsOpenModal(false);
  };

  return (
    <ModalV2
      open={props.isOpen}
      title={
        props.header || getTranslateMessage(TRANSLATE_KEY._WARNING, 'Warning!')
      }
      okText={getTranslateMessage(TRANSLATE_KEY._ACT_CONFIRM, 'Confirm')}
      cancelText={getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel')}
      onOk={onApply}
      onCancel={onCancel}
      zIndex={1400}
      closeIcon={null}
      styles={{ header: { marginBottom: 0 }, footer: { marginTop: 0 } }}
      centered
    >
      <div>{props.content}</div>
    </ModalV2>
  );

  // return (
  //   <UIModal isOpen={props.isOpen}>
  //     <UIModalHeader>
  //       {props.header ||
  //         getTranslateMessage(TRANSLATE_KEY._WARNING, 'Warning!')}
  //     </UIModalHeader>
  //     <UIModalBody>
  //       <WrapperBody className="p-bottom-2">{props.content}</WrapperBody>
  //     </UIModalBody>
  //     <UIModalFooter>
  //       <ButtonStyle theme="outline" onClick={onCancel}>
  //         {getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel')}
  //       </ButtonStyle>
  //       <ButtonStyle theme="primary" onClick={onApply}>
  //         {getTranslateMessage(TRANSLATE_KEY._ACT_CONFIRM, 'Confirm')}
  //       </ButtonStyle>
  //     </UIModalFooter>
  //   </UIModal>
  // );
}

function mapDispatchToProps(dispatch) {
  return {
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

ModalConfirmDeleteSource.propTypes = {
  isOpen: PropTypes.bool,
  setIsOpenModal: PropTypes.func,
};
ModalConfirmDeleteSource.defaultProps = {
  isOpen: false,
  setIsOpenModal: () => {},
};
export default connect(
  null,
  mapDispatchToProps,
)(ModalConfirmDeleteSource);
