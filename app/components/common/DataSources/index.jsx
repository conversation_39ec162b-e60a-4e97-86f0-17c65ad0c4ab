/* eslint-disable no-param-reassign */
import React, { useState, useEffect, memo } from 'react';
import PropTypes from 'prop-types';
import Grid from '@material-ui/core/Grid';
import {
  usePopupState,
  bindTrigger,
  bindMenu,
} from 'material-ui-popup-state/hooks';
import {
  UIButton,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import UISuggestionList from 'components/common/UISuggestionList';

import GroupAttribute from 'containers/Filters/AddFilter/GroupAttribute';
import { Nodata } from '../../form/UISelect/DropdownAction/Tree/styled';

import { useFetchDataByEvent } from './useFetchDataByEvent';
import { onSearchFilter } from '../../../containers/Filters/AddFilter/utils';
import {
  useStyles,
  WrapperPreviewSource,
  StyleWapperListFilter,
  DivLoading,
} from './styles';
import ItemPreviewSourceSelected from './ItemPreviewSourceSelected';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import ModalConfirmDeleteSource from './ModalConfirmDeleteSource';
import AbtractsService from '../../../services/Abstract';
import { Icon } from '@antscorp/antsomi-ui';

const style = {
  maxWidth: '250px',
  maxHeight: '300px',
  overflowX: 'hidden',
  overflowY: 'auto',
};
const popoverStyle = {
  maxWidth: '350px',
  maxHeight: '380px',
};
const labelAnySource = getTranslateMessage(
  TRANSLATE_KEY._TITL_IN_ANY_SOURCE,
  'In any source of',
);

const labelAddSource = getTranslateMessage(
  TRANSLATE_KEY._ACT_ADD_SOURCE,
  'Add source',
);

const SelectDataSources = props => {
  const classes = useStyles();
  const popupStateMenu = usePopupState({
    variant: 'popover',
    popupId: 'menu-filter-popup-popover',
  });
  const {
    extendParams,
    ServiceFn,
    extraDeps,
    validateToCallApiFn,
    disabled,
    disableSelectedSource,
    initData,
    defaultDataSource,
    mapDisabledDataSource,
    useDefaultListSource,
    isComfirmDelete,
    isUsesPersonal,
  } = props;
  // console.log('defaultDataSource: ', defaultDataSource, useDefaultListSource);
  const [{ state }, { setState }] = useFetchDataByEvent({
    extendParams,
    ServiceFn,
    extraDeps,
    validateToCallApiFn,
    defaultDataSource,
    useDefaultListSource,
  });
  // nen them isOpenModalChange
  const [isOpenModalRemove, setIsOpenModalRemove] = useState(false);
  const [isChangeItemTypeId, setIsChangeItemTypeIds] = useState(false);

  useEffect(() => {
    if (Array.isArray(initData)) {
      setState(draft => {
        draft.sourcesSelected = initData;
      });
    }
  }, [initData]);

  useEffect(() => {
    // neu khong co modal nao => isChange = false
    if (!popupStateMenu.isOpen && !isOpenModalRemove) {
      setIsChangeItemTypeIds(false);
    }
  }, [popupStateMenu.isOpen, isOpenModalRemove]);

  const callback = (type, data) => {
    if (type === 'DELETE_SOURCE_ITEM') {
      const { index, item } = data;
      if (isComfirmDelete) {
        setState(draft => {
          draft.activeItem = item;
          draft.indexActiveItem = index;
        });
        setIsOpenModalRemove(true);
      } else {
        setState(draft => {
          const out = [].concat(state.sourcesSelected);
          out.splice(index, 1);
          draft.sourcesSelected = out;
          props.onChange(out);
        });
      }
      //
    } else if (type === 'SELECT_ATRR') {
      popupStateMenu.close();
      if (isChangeItemTypeId) {
        setState(draft => {
          draft.activeItem = data;
          // draft.indexActiveItem = index;
        });
        setIsOpenModalRemove(true);
      } else {
        setState(draft => {
          let out = [];
          if (isUsesPersonal) {
            out = [parseInt(data.value)];
          } else {
            out = [...state.sourcesSelected, parseInt(data.value)];
          }
          draft.sourcesSelected = out;
          props.onChange(out);
        });
      }
    } else if (type === 'CONFIRM_DELETE_SOURCE') {
      setState(draft => {
        const out = state.sourcesSelected;
        out.splice(state.indexActiveItem, 1);
        draft.sourcesSelected = out;
        props.onChange(out);
      });
    } else if (type === 'CONFIRM_CHANGE_ITEM_TYPE_ID') {
      const { itemTypeId } = data;
      setState(draft => {
        let out = [];
        if (isUsesPersonal) {
          out = [parseInt(itemTypeId)];
        } else {
          out = [...state.sourcesSelected, parseInt(itemTypeId)];
        }
        draft.sourcesSelected = out;
        props.onChange(out);
      });
    } else if (type === 'IS_CHANGE_ITEM_TYPE_IDS') {
      setIsChangeItemTypeIds(true);
    }
  };

  const onSearch = valueSearch => {
    const textSearch = valueSearch.trim().toLowerCase();
    if (textSearch.length > 0) {
      const { arrayGroupAttrs } = onSearchFilter(
        state.dataSource.list,
        [],
        textSearch,
      );
      setState(draft => {
        draft.searchValue = valueSearch;
        draft.searchList = arrayGroupAttrs;
      });
    } else {
      setState(draft => {
        draft.searchValue = valueSearch;
        draft.searchList = [];
      });
    }
  };
  const renderContentSelectDataSources = () => {
    if (state.isLoading) {
      return (
        <DivLoading>
          <Loading isLoading={state.isLoading} size={20} />
        </DivLoading>
      );
    }
    return (
      <WrapperDisable disabled={disableSelectedSource}>
        {state.sourcesSelected.map((source, index) => {
          const item = state.dataSource.map[source];
          const disabledItem = mapDisabledDataSource[source];
          if (item !== undefined) {
            return (
              <ItemPreviewSourceSelected
                popupStateMenu={isUsesPersonal && popupStateMenu}
                disabled={disabledItem}
                index={index}
                label={item.label}
                value={item.value}
                isError={item.isError}
                callback={callback}
                item={item}
                key={source}
                isComfirmDelete={isComfirmDelete}
                isViewMode={props.isViewMode}
              />
            );
          }
        })}
        {!props.isViewMode && (
          <>
            <UIButton
              disabled={isUsesPersonal && !!state.sourcesSelected.length}
              variant="contained"
              {...bindTrigger(popupStateMenu)}
              theme="outline"
              borderRadius="1rem"
              style={{ height: '27px' }}
            >
              <Icon
                type="icon-ants-add"
                size={10}
                style={{ marginRight: '5px' }}
              />
              {props.labelAdd || labelAddSource}
            </UIButton>
            <UISuggestionList
              popoverProps={{
                ...bindMenu(popupStateMenu),
                // style: popoverStyle,
              }}
              searchProps={{
                onChange: onSearch,
                value: state.searchValue,
              }}
              style={style}
            >
              <StyleWapperListFilter>
                {state.dataSource.list.length === 0 ||
                (state.searchValue.length > 0 &&
                  state.searchList.length === 0) ? (
                  <Nodata height="150px">
                    {getTranslateMessage(
                      TRANSLATE_KEY._INFO_NO_DATA,
                      'No data',
                    )}
                  </Nodata>
                ) : null}
                {state.searchValue.length > 0 ? (
                  <GroupAttribute
                    groups={state.searchList}
                    callback={callback}
                    itemsDisabled={state.sourcesSelected}
                  />
                ) : (
                  <GroupAttribute
                    groups={state.dataSource.list}
                    callback={callback}
                    itemsDisabled={state.sourcesSelected}
                  />
                )}
              </StyleWapperListFilter>
            </UISuggestionList>
          </>
        )}
      </WrapperDisable>
    );
  };

  return (
    <Grid container item xs={12} className={classes.padding}>
      <Grid item xs={12} sm={12} className={classes.paddingLeft}>
        <WrapperDisable disabled={disabled}>
          <WrapperPreviewSource>
            {renderContentSelectDataSources()}
          </WrapperPreviewSource>
        </WrapperDisable>
      </Grid>
      {isOpenModalRemove && (
        <ModalConfirmDeleteSource
          isOpen={isOpenModalRemove}
          setIsOpenModal={setIsOpenModalRemove}
          callback={callback}
          sourceName={state.activeItem.label || ''}
          header={props.modalDeleteHeader}
          content={props.modalDeleteContent}
          itemTypeId={state.activeItem.value}
          isChangeItemTypeId={isChangeItemTypeId}
        />
      )}
    </Grid>
  );
};

SelectDataSources.defaultProps = {
  sourcesSelected: [],
  disabled: false,
  validateToCallApiFn: () => true,
  extendParams: {},
  ServiceFn: AbtractsService.lookupInfo.callApi.source_names,
  extraDeps: 0,
  disableSelectedSource: false,
  errors: [],
  initData: [],
  onChange: () => {},
  useDefaultListSource: false,
  defaultDataSource: {
    list: [],
    map: {},
  },
  isComfirmDelete: true,
  mapDisabledDataSource: {},
  isUsesPersonal: false,
};
SelectDataSources.propTypes = {
  defaultDataSource: PropTypes.object,
  sourcesSelected: PropTypes.array,
  disabled: PropTypes.bool,
  validateToCallApiFn: PropTypes.func,
  extendParams: PropTypes.object,
  ServiceFn: PropTypes.func,
  extraDeps: PropTypes.any,
  disableSelectedSource: PropTypes.bool,
  errors: PropTypes.array,
  initData: PropTypes.array,
  onChange: PropTypes.func,
  useDefaultListSource: PropTypes.bool,
  isComfirmDelete: PropTypes.bool,
  mapDisabledDataSource: PropTypes.object,
  isUsesPersonal: PropTypes.bool,
  modalDeleteHeader: PropTypes.string,
  modalDeleteContent: PropTypes.string,
  isViewMode: PropTypes.bool,
};
export default memo(SelectDataSources);
