import { makeStyles } from '@material-ui/core';
import styled, { css } from 'styled-components';
import colors from 'utils/colors';

export const StyleFooter = styled.div`
  background-color: #fff;
  width: 100%;
`;

export const TextCenter = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const StyleWapperListFilter = styled.div`
  position: relative;
`;

export const Wrapper = styled.div`
  overflow-x: hidden;
  align-items: center;
  cursor: pointer;
  display: flex;
  min-width: 0;
  .chip-container {
    margin: 0px 4px 0 0px;
    overflow: hidden;
    font-size: 12px;
  }
`;

export const WrapperPreviewSource = styled.div`
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    position: relative;
    gap: 10px;
  .predicates-container {
    display: flex;
    align-items: center;
    /* ${props => css`
      flex-wrap: ${() => (props.isFilter ? 'wrap' : 'nowrap')};
    `} */
    flex-wrap: wrap;
    text-overflow: ellipsis;
    /* width: 100%; */
    /* margin: 5px 0; */
    /* padding-bottom: 8px; */
    overflow: hidden;
    /* margin-bottom: 4px; */
  }
`;

export const MaterialChip = styled.div`
  margin: 0;
  background-color: #cae5fe;
  color: #000000;
  position: relative;

  ${({ isError }) =>
    isError &&
    `background: #ed5240 !important;
    cursor: not-allowed !important;`}

  border-radius: 16px;
  display: flex;
  padding: 5px;
  align-items: center;
  height: 27px;
  .content-chip {
    margin: 0 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .delete-button {
    display: flex;
    position: absolute;
    visibility: hidden;

    right: 10px;
    border-radius: 50%;
    border: 0;

    cursor: pointer;
    outline: none;
    background-color: #ffffff;

    span {
      font-size: 1rem !important;
      color: #005eb8;
    }
  }

  &:hover .delete-button {
    visibility: visible;
  }
`;
export const ContainerWrapperInputValue = styled.div`
  display: flex;
  align-items: center;
  ${({ justifyContent }) =>
    justifyContent && `justify-content: ${justifyContent};`}
  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: solid !important;
  }
`;

export const StyleWrapperInputValue = styled.div`
  max-width: 100%;
  flex: 1;
  margin-right: 20px;
  position: relative;
  .div-span-and {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    position: unset;
  }
  .private-form__control {
    width: 100%;
  }
  .MuiInputBase-root {
    width: 100%;
    height: 32px;
  }
  .button-dropdown-ui-select {
    white-space: nowrap;
  }
  .ui-button-dropndown {
    height: 32px;
  }
  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: solid !important;
  }
`;

export const StyleSpan = styled.span`
  color: ${colors.tertiary500};
`;

export const DivLoading = styled.div`
  height: 30px;
  width: 20%;
  position: relative;
  .private-overlay {
    padding: 0 !important;
  }
`;

export const StyleSpanAnd = styled.span`
  white-space: nowrap;
  padding: 0 10px;
`;

export const Title = styled.div`
  color: #7f7f7f;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const useStyles = makeStyles(() => ({
  root: {
    flexGrow: 1,
    backgroundColor: 'rgb(250, 250, 250)',
    marginBottom: '20px',

    fontSize: '0.813rem',
    position: 'relative',
  },
}));
