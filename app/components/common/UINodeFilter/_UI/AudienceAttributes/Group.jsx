/* eslint-disable react/prop-types */
import React, { useEffect, useMemo } from 'react';
import {
  UIButton,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import HeaderCondition from 'containers/Segment/Content/Condition/_UI/HeaderCondition';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { GroupConditionWrapper } from '../../styled';
import { MAP_SEGMENT_VALUES } from '../../../UIPerformEvent/constants';
import ItemCondition from './Item';
import { filterDeep } from '../../../../../utils/web/utils';
import { ARCHIVE_STATUS, TYPE_ATTRIBUTE } from '../../../../../utils/constants';

const labelHaveAttrs = getTranslateMessage(
  TRANSLATE_KEY._TITL_HAVE_ATTRIBUTE,
  'Have attribute',
);

const GroupConditionHaveAttribute = props => {
  const { item, groupIndex, options, isViewMode } = props;

  const optionsProperty = useMemo(
    () =>
      filterDeep(
        props.options,
        (_value, _key, option) => {
          const { status, attrType } = option;

          return (
            Number(status) !== ARCHIVE_STATUS ||
            attrType !== TYPE_ATTRIBUTE.COMPUTED
          );
        },
        {
          childrenPath: 'options',
        },
      ),
    [options],
  );

  const onClick = () => {
    props.callback('ADD_GROUP');
  };

  const deleteGroup = () => {
    props.callback('DELETE_GROUP', { groupIndex });
  };

  useEffect(() => {
    if (item.size === 0) {
      deleteGroup();
    }
  }, [item.size]);

  const renderItemCondition = rule => {
    const result = [];
    if (rule.size === 0) {
      return null;
    }

    let indexItem = 0;
    const firstKey = rule.keySeq().first();

    rule.forEach((element, index) => {
      indexItem += 1;
      if (element.get('conditionType')) {
        if (
          element.get('conditionType').value === MAP_SEGMENT_VALUES.comp_attr
        ) {
          result.push(
            <ItemCondition
              moduleConfig={props.moduleConfig}
              keyItem={indexItem.toString()}
              groupIndex={groupIndex}
              itemIndex={index}
              item={element}
              options={optionsProperty}
              callback={props.callback}
              showAdd={item.size === indexItem && item.size <= 19}
              showDelIcon={!(props.totalGroupSize === 1 && item.size === 1)}
              isFirstItem={firstKey === index}
              key={indexItem.toString()}
              disabled={props.disabled}
              isViewMode={isViewMode}
            />,
          );
        }
        return null;
      }
      return null;
    });

    return result;
  };

  const renderSeparator = () => {
    if (props.showButtonOr) {
      if (isViewMode) return null;

      return (
        <UIButton
          className="btn-or-condition"
          theme="outline"
          borderRadius="1rem"
          onClick={onClick}
          iconName="add"
          reverse
          data-test="item-condition-btn-or"
        >
          {`${getTranslateMessage(TRANSLATE_KEY._ACT_OR, 'OR')}`}
        </UIButton>
      );
    }

    return (
      <span
        style={{
          color: isViewMode ? '#666' : '#000',
          fontWeight: 700,
          fontSize: '12px',
        }}
      >
        OR
      </span>
    );
  };

  return (
    <React.Fragment key={props.keyGroup}>
      <GroupConditionWrapper
        className="condition-audience-attr width-690"
        style={{ width: '850px' }}
        data-test="group-condition"
      >
        {isViewMode && (
          <WrapperDisable disabled={props.disabled}>
            <HeaderCondition label={labelHaveAttrs} />
          </WrapperDisable>
        )}
        <div style={{ padding: props.design === 'preview' ? '7px' : '0' }}>
          {renderItemCondition(item)}
        </div>
      </GroupConditionWrapper>
      {renderSeparator() && (
        <div className="pt-12 pb-12">
          <WrapperDisable disabled={props.disabled}>
            {renderSeparator()}
          </WrapperDisable>
        </div>
      )}
    </React.Fragment>
  );
};

export default React.memo(GroupConditionHaveAttribute);
