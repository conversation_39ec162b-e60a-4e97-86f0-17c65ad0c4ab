/* eslint-disable no-lonely-if */
/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
/* eslint-disable eqeqeq */
/* eslint-disable indent */
import React, { useEffect } from 'react';
import { useImmer } from 'use-immer';
import { Map, OrderedMap } from 'immutable';
import { UILoading } from '@xlab-team/ui-components';

import {
  initItem,
  initItemValue,
} from 'containers/Segment/Content/Condition/utils';
import {
  MAP_SEGMENT_TYPES,
  SEGMENT_TYPES,
} from '../../../UIPerformEvent/constants';
import MetaDataServices from '../../../../../services/MetaData';
import GroupConditionHaveAttribute from './Group';
import { mapGroupItemAttributeUniqueWithItemTypeId } from '../../../../../services/map';
import { generateKey, safeParse } from '../../../../../utils/common';
import {
  getFirstOperator,
  getOperatorByProperty,
} from '../../../../../containers/Filters/utils';
import { GroupsConditionWrapper, WrapperLoading } from '../../styled';
import {
  validateRulesAudienceAttributes,
  toComponentAudienceAttrs,
  getInputLookupFromRule,
} from './utils';
import { bindValidateResultToCondition } from '../../../UIPerformEvent/utils.validate';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import useUpdateEffect from '../../../../../hooks/useUpdateEffect';

const initState = () => ({
  itemTypeId: '-1003',
  dataGroupAttrs: {
    list: [],
    map: {},
    isLoading: true,
  },
  rules: OrderedMap({}),
  loadDataDone: false,
  isInit: false,
});

const UIHaveAudienceAttributes = props => {
  // console.log('UIHaveAudienceAttributes: ', props);
  let _isMounted = true;
  let _IS_FIRST = true;
  const { initData } = props;
  const [state, setState] = useImmer(initState());

  const fetchData = () => {
    MetaDataServices.item.properties
      .getByGroupIds({
        itemTypeIds: '-1007,-1003',
        isSupportGroupAttr: false,
        isFilter: 1,
        excludeItemPropertyName: 'segment_ids',
      })
      .then(res => {
        if (res.code === 200) {
          const data = mapGroupItemAttributeUniqueWithItemTypeId(res.data);
          // const tmp = initItem(SEGMENT_TYPES[1]);

          if (_isMounted && !state.isInit && initData !== undefined) {
            const conditions = OrderedMap.isOrderedMap(initData)
              ? initData.getIn(['data-init', 'backup'])
              : {};

            const rulesOR = safeParse(conditions?.OR, []);
            const { comp_attr } = getInputLookupFromRule(rulesOR);
            const params = { dataPost: { attributes: comp_attr } };
            // console.log(params);

            MetaDataServices.item.properties
              .lookupByIdsV2(params)
              .then(resLookup => {
                // console.log('resLookup: ', resLookup);

                const { list = [], map = {} } = resLookup;
                setState(draft => {
                  draft.dataGroupAttrs.list = data.filterGroups;
                  draft.dataGroupAttrs.tempList = data.filterGroups;
                  draft.dataGroupAttrs.map = data.map;
                  list.forEach(each => {
                    if (!data.map[each.value]) {
                      draft.dataGroupAttrs.map[each.value] = map[each.value];
                    }
                  });

                  draft.loadDataDone = true;
                });
              });
          } else {
            setState(draft => {
              draft.dataGroupAttrs.list = data.filterGroups;
              draft.dataGroupAttrs.tempList = data.filterGroups;
              draft.dataGroupAttrs.map = data.map;
              draft.loadDataDone = true;
            });
          }
        } else {
          if (_isMounted) {
            setState(draft => {
              draft.dataGroupAttrs.list = [];
              draft.dataGroupAttrs.tempList = [];
              draft.dataGroupAttrs.map = {};
              draft.dataGroupAttrs.isLoading = false;
              draft.loadDataDone = true;
            });
          }
        }
      })
      .catch(err => {
        if (_isMounted) {
          setState(draft => {
            draft.dataGroupAttrs.list = [];
            draft.dataGroupAttrs.tempList = [];
            draft.dataGroupAttrs.map = {};
            draft.dataGroupAttrs.isLoading = false;
            draft.loadDataDone = true;
          });
          console.log('err ===>', err);
        }
      })
      .catch(err => {
        addMessageToQueue({
          path:
            'app/components/common/UINodeFilter/_UI/AudienceAttributes/index.jsx',
          func: 'fetchData',
          data: err.stack,
        });
        setState(draft => {
          draft.dataGroupAttrs.list = [];
          draft.dataGroupAttrs.tempList = [];
          draft.dataGroupAttrs.map = {};
          draft.dataGroupAttrs.isLoading = false;
          draft.loadDataDone = true;
        });
        console.log('err ===>', err);
      });
  };

  useEffect(() => {
    setState(() => initState());
    fetchData();
    return () => {
      _isMounted = false;
    };
  }, [props.componentId]);

  useUpdateEffect(() => {
    const validate = validateRulesAudienceAttributes(state.rules);
    // console.log('i will validate ====>', validate);
    const tmpCondition = bindValidateResultToCondition(
      state.rules,
      validate.errors,
    );
    setState(draft => {
      draft.rules = tmpCondition;
    });
    // fetchData();
  }, [props.validateKey]);

  // useEffect(() => {
  //   if (Object.keys(safeParse(props.initData, {})).length > 0) {
  //     setState(draft => {
  //       draft.rules = props.initData;
  //     });
  //   }
  // }, [props.initData]);

  useUpdateEffect(() => {
    if (props.preItemTypeId.current != props.itemTypeId) {
      _IS_FIRST = false;
    }

    let isInit = false;

    if (Object.keys(safeParse(initData, {})).length === 0) {
      const dataTemp = [];
      if (state.loadDataDone) {
        state.dataGroupAttrs.tempList.forEach(element => {
          if (element.groupId == props.itemTypeId) {
            dataTemp.push(element);
          }
        });

        const tmp = initItem(SEGMENT_TYPES[1]);
        setState(draft => {
          draft.rules = OrderedMap({}).setIn([generateKey()], tmp);
          draft.dataGroupAttrs.list = dataTemp;
          draft.dataGroupAttrs.isLoading = false;
        });
      }
    } else {
      const dataTemp = [];
      if (state.loadDataDone) {
        state.dataGroupAttrs.tempList.forEach(element => {
          if (element.groupId == props.itemTypeId) {
            dataTemp.push(element);
          }
        });
        if (Map.isMap(props.initData) && props.initData.size > 0) {
          // const firstRule = props.initData.first();
          // const itemFirstRule = firstRule.first();
          // const propertyItemRule = itemFirstRule.get('property');
          // const itemTypeIdOfItemRule = propertyItemRule.itemTypeId;
          isInit = props.initData.first().get('isInit');
          setState(draft => {
            if (isInit) {
              if (!_IS_FIRST) {
                const tmp = initItem(SEGMENT_TYPES[1]);
                draft.rules = OrderedMap({}).setIn([generateKey()], tmp);
              } else {
                draft.rules = toComponentAudienceAttrs(
                  props.initData.first().get('backup'),
                  {
                    map: {
                      conditionType: MAP_SEGMENT_TYPES,
                      itemAttribute: state.dataGroupAttrs.map,
                    },
                    info: {},
                  },
                );
              }
            } else {
              if (!_IS_FIRST) {
                const tmp = initItem(SEGMENT_TYPES[1]);
                draft.rules = OrderedMap({}).setIn([generateKey()], tmp);
              } else {
                draft.rules = props.initData;
              }
            }
            draft.dataGroupAttrs.list = dataTemp;
            draft.dataGroupAttrs.isLoading = false;
          });
        }
      }
    }
  }, [props.componentId, state.loadDataDone, props.itemTypeId]);
  // }, [state.loadDataDone, props.itemTypeId]);

  useUpdateEffect(() => {
    props.onChange(state.rules);
  }, [state.rules]);

  const callback = (type, data) => {
    switch (type) {
      case 'COMP_PROP_CHANGE_PROPERTY': {
        const { groupIndex, itemIndex, value } = data.data;

        const oldItem = safeParse(
          state.rules.getIn([groupIndex, itemIndex, 'property']),
          null,
        );

        if (oldItem === null || oldItem.value !== value.value) {
          const operators = getOperatorByProperty(value);
          const tempt = state.rules
            .getIn([groupIndex, itemIndex])
            .withMutations(map => {
              map
                .set('value', '')
                .set('label', '')
                .set('valueEnd', '')
                .set('initValue', '')
                .set('property', value)
                .set('error', 0)
                .set('dataType', value.dataType)
                .set('operator', getFirstOperator(operators))
                .set('operators', operators);
            });

          setState(draft => {
            draft.rules = state.rules.setIn([groupIndex, itemIndex], tempt);
          });
        }
        break;
      }

      case 'COMP_PROP_CHANGE_OPERATOR': {
        const { groupIndex, itemIndex, value } = data.data;
        const tempt = state.rules
          .getIn([groupIndex, itemIndex])
          .withMutations(map => {
            map
              .set('value', '')
              .set('label', '')
              .set('valueEnd', '')
              .set('initValue', '')
              .set('error', 0)
              .set('operator', value);
          });

        setState(draft => {
          draft.rules = state.rules.setIn([groupIndex, itemIndex], tempt);
        });

        break;
      }

      case 'COMP_PROP_CHANGE_VALUE': {
        const {
          groupIndex,
          itemIndex,
          value,
          name = 'value',
          label,
          extendValue,
        } = data.data;
        let tempt = state.rules
          .getIn([groupIndex, itemIndex])
          .withMutations(map => {
            map.set('error', 0);
            map.set(name, value);
            label && map.set('label', label);
          });
        if (extendValue && extendValue.length > 0) {
          tempt = tempt.set('extendValue', extendValue);
        }
        setState(draft => {
          draft.rules = state.rules.setIn([groupIndex, itemIndex], tempt);
        });

        break;
      }

      case 'DELETE_ITEM': {
        const { groupIndex, itemIndex } = data;
        const tempState = state.rules.deleteIn([groupIndex, itemIndex]);
        setState(draft => {
          draft.rules = tempState;
        });
        break;
      }

      case 'ADD_ITEM': {
        const { groupIndex } = data;
        const tmp = initItemValue(SEGMENT_TYPES[1]);

        setState(draft => {
          draft.rules = state.rules.setIn([groupIndex, generateKey()], tmp);
        });
        break;
      }

      case 'ADD_GROUP': {
        const tmp = initItem(SEGMENT_TYPES[1]);
        setState(draft => {
          draft.rules = state.rules.setIn([generateKey()], tmp);
        });

        break;
      }

      case 'DELETE_GROUP': {
        const { groupIndex } = data;

        const tempState = state.rules.deleteIn([groupIndex]);
        setState(draft => {
          draft.rules = tempState;
        });
        break;
      }
      default:
        break;
    }
  };

  const showContent = rules => {
    if (rules.size > 0) {
      const result = [];

      // const firstKey = rules.keySeq().first();

      let indexGroup = 0;
      rules.forEach((item, key) => {
        indexGroup += 1;
        result.push(
          <GroupConditionHaveAttribute
            key={indexGroup.toString()}
            moduleConfig={props.moduleConfig}
            keyGroup={key}
            groupIndex={key}
            totalGroupSize={rules.size}
            // itemIndex={itemIndex}
            item={item}
            options={state.dataGroupAttrs.list}
            callback={callback}
            showButtonOr={rules.size === indexGroup && indexGroup <= 19}
            // showIconDeleteGroup={rules.size > 1}
            disabled={props.disabled}
            design={props.design}
            isViewMode={props.isViewMode}
          />,
        );
      });
      return result;
    }
    return null;
  };

  if (state.dataGroupAttrs.isLoading) {
    return (
      <WrapperLoading isLoading>
        <UILoading isLoading isWhite />;
      </WrapperLoading>
    );
  }

  return (
    <GroupsConditionWrapper
      data-test={props['data-test'] || 'groups-condition'}
    >
      {showContent(state.rules)}
    </GroupsConditionWrapper>
  );
};

UIHaveAudienceAttributes.defaultProps = {
  initData: undefined,
  onChange: () => {},
  validateKey: 1,
  showTitle: true,
  showCustomTitle: true,
  disabled: false,
};

export default UIHaveAudienceAttributes;
