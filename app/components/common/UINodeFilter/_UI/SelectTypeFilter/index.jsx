/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useEffect } from 'react';
import SelectTree from 'components/form/UISelectCondition';
import { useImmer } from 'use-immer';
import { UIWrapperDisable as WrapperDisable } from '@xlab-team/ui-components';

import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { BlockContentWrapper, Title } from '../../styled';
import {
  DATA_OPTIONS_FILTER,
  DATA_OPTIONS_FILTER_NO_EVENT_ATTR,
  // MAP_DATA_OPTIONS_FILTER,
} from '../../utils';
import { safeParse } from '../../../../../utils/common';

const initTypeDelay = {
  options: DATA_OPTIONS_FILTER,
  value: DATA_OPTIONS_FILTER[0],
};

const SelectTypeFilter = props => {
  const { initData } = props;
  const [state, setState] = useImmer(initTypeDelay);

  const onChangeTypeFilter = data => {
    setState(draft => {
      draft.value = data;
    });

    props.onChange('filterType', data.value);
  };

  useEffect(() => {
    if (Object.keys(safeParse(initData, {})).length > 0) {
      setState(draft => {
        draft.value = initData;
      });
    }

    return () => {
      setState(() => initTypeDelay);
    };
  }, [props.componentId]);

  return (
    <BlockContentWrapper>
      <Title data-test="filter-type-label">
        {getTranslateMessage(
          TRANSLATE_KEY._INFO_STORY_FILTER_TYPE,
          'Filter type',
        )}
      </Title>
      <div className="w-250" data-test="filter-type">
        <WrapperDisable disabled={props.disabled}>
          <SelectTree
            onlyParent={props.onlyParent}
            use="tree"
            isSearchable={false}
            isParentOpen={props.isParentOpen}
            options={
              props.isActionBase
                ? state.options
                : DATA_OPTIONS_FILTER_NO_EVENT_ATTR
            }
            value={state.value}
            onChange={onChangeTypeFilter}
            placeholder={getTranslateMessage(
              TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
              'Select an item',
            )}
            fullWidthPopover
            isViewMode={props.isViewMode}
          />
        </WrapperDisable>
      </div>
    </BlockContentWrapper>
  );
};

SelectTypeFilter.defaultProps = {
  onChange: () => {},
  initData: {},
};

export default SelectTypeFilter;
