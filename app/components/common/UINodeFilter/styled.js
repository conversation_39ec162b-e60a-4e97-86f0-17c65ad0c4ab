/* eslint-disable indent */
import styled, { css } from 'styled-components';

export const ContainerNodeContent = styled.div`
  color: #000;
  font-size: 12px;
  margin-top: 20px;

  .wrapper-perform-event {
    padding-left: 0;
    padding-right: 0;
  }

  .container-condition-perform-event {
    margin-bottom: 0 !important;
  }

  .MuiChip-root.view-mode-value {
    height: 28px;
  }

  .left-side-title {
    text-align: right;
    margin-right: 20px;
  }

  .w-200 {
    width: 200px;
  }
  .w-150 {
    width: 150px;
  }
  .w-164 {
    width: 164px;
  }
  .w-250 {
    width: 250px;
  }
  .w-300 {
    width: 300px;
  }
  .w-775 {
    width: 775px;
  }
  .mr-20 {
    margin-right: 20px;
  }
  .pt-12 {
    padding-top: 12px;
  }
  .pb-12 {
    padding-bottom: 12px;
  }
`;

export const Title = styled.div`
  ${({ centerInRow }) =>
    centerInRow &&
    css`
      position: relative;
      top: 16px;
      transform: translateY(-50%);
    `}

  color: #666666;
  margin-right: 20px;
  width: 100px;
  min-width: 100px;
  text-align: right;
`;

export const WrapperDays = styled.div`
  font-size: 1rem;
  display: flex;
  margin-top: 10px;
  & > div {
    margin-right: 20px;
  }

  & > div:last-child {
    margin-right: 0px;
  }
`;

export const BlockContentWrapper = styled.div`
  display: flex;
  align-items: ${({ alignItems }) => alignItems || 'center'};
  margin-bottom: 20px;
`;

export const GroupsConditionWrapper = styled.div`
  display: flex;
  flex-direction: column;

  .btn-or-condition {
    font-size: 12px;
    height: 23px;
    padding: 2px 7px 1px;
    border-radius: 20px;
    border: solid 1px #005eb8;
    .MuiIcon-root {
      font-size: 18px;
    }
  }
`;

export const GroupConditionWrapper = styled.div`
  box-shadow: 0px 1px 4px rgb(208 206 206);
  background: #fff;
  border-radius: 3px;
  border-left: 5px solid #005eb8;

  @media screen and (max-width: 1024px) {
    &.width-690 {
      width: 690px !important;
    }
  }
`;

export const ItemConditionWrapper = styled.div`
  display: flex;
  flex-wrap: nowrap;
  margin: ${({ isViewMode }) => (isViewMode ? '7px' : '20px')};
  align-item: center;

  .btn-and-condition {
    font-size: 12px;
    height: 23px;
    padding: 0 8px;
    white-space: nowrap;

    .MuiIcon-root {
      font-size: 18px;
    }
  }

  .center-in-row {
    position: relative;
    top: 16px;
    transform: translateY(-50%);
  }

  .m-left-auto {
    margin-left: auto;
  }

  .property,
  .operator,
  .value {
    margin-right: 1.25rem;
  }

  .property {
    max-width: 8.75rem;
    flex-basis: 8.75rem;
  }

  .operator {
    max-width: 8.125rem;
    flex-basis: 8.125rem;
  }

  .value {
    max-width: 22.5rem;
    flex-basis: 22.5rem;

    .calendar {
      &.MuiTextField-root {
        width: 100%;
        max-width: 10rem;
      }
      .MuiInputBase-root {
        max-width: none;
      }
    }

    .number-between,
    .calendar-betweeen {
      .MuiTextField-root {
        width: 100%;
        max-width: 10rem;
      }
      .MuiInputBase-root {
        max-width: none;
      }
    }
  }

  @media screen and (max-width: 1024px) {
    .property,
    .operator,
    .value {
      margin-right: 0.625rem;
    }

    .property {
      max-width: 7.5rem;
      flex-basis: 7.5rem;
    }

    .operator {
      max-width: 6.25rem;
      flex-basis: 6.25rem;
    }

    .value {
      max-width: 17.5rem;
      flex-basis: 17.5rem;
    }
  }
`;

export const LabelError = styled.div`
  font-size: 0.785rem;
  color: rgb(244, 67, 54);
`;

export const WrapperRadio = styled.div`
  .MuiTypography-body1 {
    font-size: 0.813rem;
  }

  .add-delay-time {
    padding: 0 !important;
  }
`;

export const WrapperChildContent = styled.div`
  .MuiFormControl-marginNormal {
    margin-top: 8px !important;
  }

  .select-days-of-week {
    margin-bottom: 10px !important;
  }
`;

export const WrapperIcon = styled.div`
  position: absolute;
  top: 20%;
  right: -40px;
  span {
    cursor: pointer;
    display: block;
    color: #7f7f7f;
  }
`;

export const WrapperShowTime = styled.div`
  position: relative;
`;

export const VisibleWrapper = styled.div`
  display: flex;
  align-items: center;
  visibility: ${props => (props.$isHidden ? 'hidden' : 'visible')};
`;

export const WrapperLoading = styled.div`
  height: ${({ isLoading }) => (isLoading ? '100px;' : 'auto')};
`;

export const SelectedConditionTextMode = styled.div`
  font-size: 12px;
  color: #000;
  line-height: 26px;
  white-space: nowrap;

  .operator-text {
    font-size: 12px;
    color: #000;
  }
  .property-text,
  .value-text,
  .text-more {
    font-weight: 700;
  }
  .textAnd {
    line-height: 26px;
  }
  .text-more {
    color: #005eb8;
  }
`;

export const WrapperConditionError = styled.div`
  padding-left: 20px;
`;

export const SubContentTooltip = styled.div`
  color: #9a9a9a;
  span {
    color: #c4c4c4;
    font-weight: 700;
  }
`;
