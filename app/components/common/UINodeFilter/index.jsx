/* eslint-disable indent */
/* eslint-disable no-else-return */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */

import React, { useEffect } from 'react';
import { useImmer } from 'use-immer';
import { UILoading } from '@xlab-team/ui-components';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { generateKey, safeParse } from '../../../utils/common';
import TargetAudienceMultiFilter from '../../Templates/TargetAudienceFilter';
import { BlockContentWrapper, ContainerNodeContent, Title } from './styled';
import {
  initDataByTypeFilter,
  initDataUIFilter,
  MAP_DATA_OPTIONS_FILTER,
} from './utils';
import SelectTypeFilter from './_UI/SelectTypeFilter';
import WrapperAudienceAttributes from './_UI/WrapperAudienceAttribute';
import EventAttributes from './_UI/EventAttributes';
import useUpdateEffect from '../../../hooks/useUpdateEffect';

const UINodeFilter = props => {
  const { initData, isViewMode } = props;
  const [state, setState] = useImmer(initDataUIFilter());
  const [stateLocal, setStateLocal] = useImmer({
    isInitDone: false,
    initKey: null,
  });

  useEffect(() => {
    // console.log('props.componentId', props.componentId, initData);

    setStateLocal(draft => {
      draft.isInitDone = false;
      draft.initKey = generateKey();
    });

    return () => {
      setState(() => initDataUIFilter());
      setStateLocal(draft => {
        draft.isInitDone = false;
        draft.initKey = null;
      });
    };
  }, [props.componentId]);

  useUpdateEffect(() => {
    // setState(() => initDataUIFilter());
    if (stateLocal.initKey !== null) {
      // console.log('props.componentId 2', props.componentId, initData);
      if (Object.keys(safeParse(initData, {})).length > 0) {
        const filterType = safeParse(
          initData.filterType,
          MAP_DATA_OPTIONS_FILTER.user_attributes,
        );

        setState(draft => {
          draft.isFetchInfoData = true;
          draft.filterType = MAP_DATA_OPTIONS_FILTER[filterType.value];
          if (initData.event_attribute) {
            draft.event_attribute = initData.event_attribute;
          }
          if (initData.item_segment) {
            draft.item_segment = initData.item_segment;
          }
          if (initData.user_attributes) {
            draft.user_attributes = initData.user_attributes;
          }
        });
      }
      setStateLocal(draft => {
        draft.isInitDone = true;
      });
    }

    // return () => {
    //   setState(() => initDataUIFilter());
    // };
  }, [stateLocal.initKey]);

  useUpdateEffect(() => {
    props.onChange(state);
  }, [state]);

  const setStateCommon = objects => {
    setState(draft => {
      Object.keys(objects).forEach(key => {
        draft[key] = objects[key];
      });
    });
  };

  const onChangeTypeFilter = (key, data) => {
    setStateCommon({
      [`${key}`]:
        MAP_DATA_OPTIONS_FILTER[data] ||
        MAP_DATA_OPTIONS_FILTER.user_attributes,
      [`${data}`]: initDataByTypeFilter[data],
    });
  };

  const onChange = (type, data) => {
    setStateCommon({ [type]: data });
  };

  const onChangeSegment = data => {
    // console.log(data);
    setState(draft => {
      draft.item_segment = data;
    });
  };
  const onChangeEventAttributes = data => {
    setState(draft => {
      draft.event_attribute = data;
    });
  };

  // console.log('123', {
  //   itemTypeId: props.itemTypeId,
  //   initData: props.initData.item_segment,
  //   validateKey: props.validateKey,
  //   initKey: stateLocal.initKey,
  // });

  const renderContentByTypeDelay = () => {
    if (state.filterType.value === 'item_segment') {
      return (
        <div style={{ maxWidth: '50rem' }}>
          <TargetAudienceMultiFilter
            itemTypeId={props.itemTypeId}
            initData={
              Object.keys(safeParse(props.initData, {})).length === 0
                ? undefined
                : props.initData.item_segment
            }
            onChange={onChangeSegment}
            validateKey={props.validateKey}
            componentId={stateLocal.initKey}
            isViewMode={isViewMode}
          />
        </div>
      );
    } else if (state.filterType.value === 'user_attributes') {
      return (
        <WrapperAudienceAttributes
          onChange={onChange}
          initData={
            Object.keys(safeParse(props.initData, {})).length === 0
              ? undefined
              : props.initData.user_attributes
          }
          isInit={
            Object.keys(safeParse(props.initData, {})).length === 0
              ? false
              : props.initData.isInitUserAttrs
          }
          validateKey={props.validateKey}
          componentId={stateLocal.initKey}
          itemTypeId={props.itemTypeId}
          isShowFullOption={props.triggerType !== 'SCHEDULED'}
          disabled={props.disabled}
          isViewMode={isViewMode}
          // data-test="condition"
        />
      );
    } else if (state.filterType.value === 'event_attribute') {
      let dataRules;

      if (
        props.initData.event_attribute &&
        props.initData.event_attribute.rules
      ) {
        dataRules = props.initData.event_attribute.rules;
      }
      return (
        <EventAttributes
          moduleConfig={props.moduleConfig}
          onChange={onChangeEventAttributes}
          eventValue={props.eventValue}
          initData={dataRules}
          validateKey={props.validateKey}
          componentId={stateLocal.initKey}
          disabled={props.disabled}
          isViewMode={isViewMode}
          // data-test="condition"
        />
      );
    }

    return null;
  };

  if (!stateLocal.isInitDone) {
    return <UILoading isLoading />;
  }

  return (
    <ContainerNodeContent>
      <SelectTypeFilter
        onChange={onChangeTypeFilter}
        initData={initData.filterType}
        componentId={stateLocal.initKey}
        isActionBase={props.triggerType !== 'SCHEDULED'}
        disabled={props.disabled}
        design={props.design}
        isViewMode={isViewMode}
      />
      <BlockContentWrapper alignItems="start">
        <Title
          centerInRow={
            state.filterType.value === 'user_attributes' && !isViewMode
          }
          data-test="condition-label"
        >
          {getTranslateMessage(
            TRANSLATE_KEY._INFO_STORY_FILTER_CONDITION,
            'Condition',
          )}
        </Title>
        <div data-test="condition">{renderContentByTypeDelay()}</div>
      </BlockContentWrapper>
    </ContainerNodeContent>
  );
};

UINodeFilter.defaultProps = {
  initData: {},
  onChange: () => {},
  validateKey: 1,
  disabled: false,
};

export default UINodeFilter;
