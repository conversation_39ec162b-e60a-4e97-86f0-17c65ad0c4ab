/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useState, memo, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useImmer } from 'use-immer';
import ErrorBoundary from 'components/common/ErrorBoundary';
import './country-flag.scss';
import {
  UIButton,
  UIWrapperDisable as WrapperDisable,
  UILoading as Loading,
} from '@xlab-team/ui-components';
import { generateKey, safeParse } from 'utils/common';
import _isEmpty from 'lodash/isEmpty';
import ObjectServices from '../../../services/Object';
import Item from './Item';
import {
  getPortalLocaleLanguage,
  getListLanguage,
} from '../../../utils/web/portalSetting';
import {
  initState,
  initItem,
  mapLanguageToFE,
  validateItem,
  toEntryOutput,
  toEntryInput,
  updateLanguageRef,
  getNextLang,
} from './utils';
import { WrapperRelative } from './styled';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import { isFunction } from 'lodash';
import { Button } from '@antscorp/antsomi-ui';
const noPadding = { padding: '0' };
const listLangDefault = getListLanguage();
const portalLang = getPortalLocaleLanguage() || 'en';
const InputLanguage = props => {
  const {
    label,
    isRequired,
    listLang,
    initData,
    value,
    defaultLang,
    maxLength,
    inputProps = {},
    isViewMode,
    isJourneyTemplateMode,
    inputWrapperStyle,
  } = props;
  const [state, setState] = useImmer(initState());
  const [isLoadingAPI, setIsLoadingAPI] = useState(false);
  useEffect(() => {
    // console.log('initdata', listLang, defaultLang);
    // if (!initData) return;
    const mapLanguage = mapLanguageToFE(listLang).map;
    const newState = toEntryInput(value || initData, mapLanguage, defaultLang);
    if (_isEmpty(value || initData)) {
      const defaultLangValue = newState.mapLanguage[defaultLang || 'en'];
      newState.map[newState.firstKey].langValue = defaultLangValue;
    }
    // updateMapLanguage(newState);
    setState(() => newState);
  }, [initData]);

  const updateMapLanguage = newState => {
    const existedLang = Object.keys(newState.map).map(
      each => newState.map[each].langValue.name,
    );
    Object.keys(newState.mapLanguage).forEach(each => {
      newState.mapLanguage[each].disabled = existedLang.includes(each);
    });
  };
  const callbackHandler = (type, data) => {
    switch (type) {
      case 'UPDATE_ITEM': {
        updateItem(data);
        break;
      }
      case 'DELETE_ITEM': {
        deleteItem(data);
        break;
      }
      default:
        break;
    }
  };

  const addItem = () => {
    const nextLang = getNextLang(state.mapLanguage); // auto get next language

    const firstItem = state.map[state.firstKey];
    const params = {
      data: {
        text: firstItem.value,
        fromLanguageCode: firstItem.langValue.value,
        toLanguageCode: nextLang.value,
      },
    };
    setIsLoadingAPI(true);
    ObjectServices.suggestion.translate(params).then(res => {
      let textConverted = '';
      if (res.code === 200) {
        textConverted = res.data[0].textConverted || '';
        textConverted = maxLength
          ? textConverted.substring(0, maxLength)
          : textConverted;
      }
      setState(draft => {
        const newKey = generateKey();
        draft.map[newKey] = initItem();
        draft.map[newKey].langValue = nextLang;
        draft.map[newKey].value = textConverted;
        const { isValidate, errors } = validateItem(draft.map[newKey]);
        draft.map[newKey].errors = errors;
        draft.map[newKey].isValidate = isValidate;
        updateMapLanguage(draft);
        toOutput(draft);
      });
      setIsLoadingAPI(false);
    });
  };
  const deleteItem = data => {
    const { itemIndex } = data;
    setState(draft => {
      delete draft.map[itemIndex];
      updateMapLanguage(draft);
      toOutput(draft);
    });
  };
  const updateItem = data => {
    // setIsLoadingStranslate(true);
    const { name, value, itemIndex } = data;
    if (itemIndex === state.firstKey || name === 'value') {
      setState(draft => {
        draft.map[itemIndex][name] = value;
        const { isValidate, errors } = validateItem(draft.map[itemIndex]);
        draft.map[itemIndex].errors = errors;
        draft.map[itemIndex].isValidate = isValidate;
        updateMapLanguage(draft);
        toOutput(draft);
      });
    } else if (name === 'langValue') {
      // ### CHANGE LANGUAGE
      const firstItem = state.map[state.firstKey];
      const params = {
        data: {
          text: firstItem.value,
          fromLanguageCode: firstItem.langValue.value,
          toLanguageCode: value.value, // new language
        },
      };
      setIsLoadingAPI(true);
      ObjectServices.suggestion.translate(params).then(res => {
        let textConverted = '';
        if (res.code === 200) {
          textConverted = res.data[0].textConverted || '';
        }
        setState(draft => {
          draft.map[itemIndex][name] = value;
          draft.map[itemIndex].value = textConverted;
          const { isValidate, errors } = validateItem(draft.map[itemIndex]);
          draft.map[itemIndex].errors = errors;
          draft.map[itemIndex].isValidate = isValidate;
          updateMapLanguage(draft);
          toOutput(draft);
        });
        setIsLoadingAPI(false);
      });
    }
  };

  const toOutput = newState => {
    const out = toEntryOutput(newState);

    if (isFunction(props.onChange)) {
      props.onChange(out);
    }
  };

  const isFirstItemValid = !_isEmpty(
    (state.map[state.firstKey].value || '').trim(),
  );
  const listLanguage = useMemo(
    () => Object.keys(state.mapLanguage).map(each => state.mapLanguage[each]),
    [state.mapLanguage],
  );
  const isOutOptionLang = useMemo(
    () => Object.keys(state.map).length >= listLang.length,
    [listLang, state.map],
  );

  const MAP_TITLE = {
    actAddAnOption: getTranslateMessage(
      TRANSLATE_KEY._ACT_ADD_LANGUAGE,
      'Add language',
    ),
  };

  // console.log('state', state, state.mapLanguage);
  return (
    <ErrorBoundary path="app/components/common/InputLanguage/index.jsx">
      <WrapperRelative>
        <Loading isLoading={isLoadingAPI} />
        <WrapperDisable disabled={!isViewMode && props.disabled}>
          {useMemo(
            () =>
              // const result = [];
              Object.keys(state.map).map((key, index) => {
                const each = state.map[key];
                const isFirst = key === state.firstKey;
                return (
                  <Item
                    isRequired={isRequired}
                    isFirst={isFirst}
                    key={key}
                    item={each}
                    itemIndex={key}
                    listLanguage={listLanguage}
                    maxLength={maxLength}
                    callbackHandler={callbackHandler}
                    isShowLabelFlagSelected={props.isShowLabelFlagSelected}
                    isDropdownSizeMd={props.isDropdownSizeMd}
                    disabledAll={props.disabled}
                    parentErrors={[props.errors[index] || '']}
                    inputProps={inputProps}
                    isViewMode={isViewMode}
                    placeholder={props.placeholder}
                    inputWrapperStyle={inputWrapperStyle}
                    wrapperType={props.wrapperType}
                  />
                );
              }),
            // return result;
            [state.map, props.disabled, props.errors],
          )}
        </WrapperDisable>
        {!isViewMode && (
          <Button
            type="text"
            style={noPadding}
            onClick={addItem}
            loading={isLoadingAPI}
            isNoBackround
            disabled={!isFirstItemValid || isOutOptionLang || props.disabled}
          >
            + {MAP_TITLE.actAddAnOption}
          </Button>
        )}
      </WrapperRelative>
    </ErrorBoundary>
  );
};

InputLanguage.defaultProps = {
  label: '',
  isRequired: true,
  defaultLang: portalLang,
  disabled: false,
  listLang: listLangDefault,
  isViewMode: false,
  inputWrapperStyle: {},
  placeholder: '',
};

InputLanguage.propTypes = {
  label: PropTypes.string,
  isRequired: PropTypes.bool,
  defaultLang: PropTypes.string,
  disabled: PropTypes.bool,
  listLang: PropTypes.array,
  isViewMode: PropTypes.bool,
  inputWrapperStyle: PropTypes.object,
  placeholder: PropTypes.string,
};
export default memo(InputLanguage);
