/* eslint-disable indent */
/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import { useImmer } from 'use-immer';
import SegmentServices from 'services/Segment';
import { OrderedMap } from 'immutable';
import MetaDataServices from 'services/MetaData';
import Grid from '@material-ui/core/Grid';

import { UILoading } from '@xlab-team/ui-components';

import {
  initItem,
  initItemRefinePropertiesValue,
} from 'containers/Segment/Content/Condition/utils';
import BlockConditionPerformEvent from './BlockConditionPerformEvent';
import { toEventTrackingFE } from '../../../modules/Dashboard/Profile/Segment/Create/_reducer/utils';
import {
  MAP_SEGMENT_VALUES,
  PERF_EVENT_OPERATORS,
  SEGMENT_TYPES,
} from './constants';
import { generateKey, safeParse } from '../../../utils/common';
import {
  getFirstOperator,
  getOperatorByProperty,
} from '../../../containers/Filters/utils';
import { toComponentPerformEvent, deleteRefinesBySourceId } from './utils';
import { useStyles } from '../UISchedulerTrigger/styled';
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';
import {
  validateRulesConditionPerformEvent,
  bindValidateResultToCondition,
} from './utils.validate';
import ModalFlowChartConfirmChangeEvent from '../../../modules/Dashboard/MarketingHub/Journey/Create/_UI/modals/ModalFlowChartConfirmChangeEvent';
import { addMessageToQueue } from '../../../utils/web/queue';
import useUpdateEffect from '../../../hooks/useUpdateEffect';

const UIPerformEvent = props => {
  let _isMounted = true;
  const { initData } = props;
  const [state, setState] = useImmer({
    eventSchema: {
      list: [],
      map: {},
      isLoading: true,
      loadDataDone: false,
    },
    rules: OrderedMap({}),
    isOpenModal: false,
    tempEventChanged: {},
  });

  const fetchData = () => {
    SegmentServices.fetch
      .getListEvent(props.paramsFetchEvent)
      .then(res => {
        if (_isMounted) {
          if (res.code === 200) {
            const { list, map } = toEventTrackingFE(res.data);
            const tmp = initItem(SEGMENT_TYPES[0]);

            setState(draft => {
              draft.eventSchema.list = list;
              draft.eventSchema.map = map;
              draft.rules = state.rules.setIn([generateKey()], tmp);
              draft.loadDataDone = true;
            });
          } else {
            setState(draft => {
              draft.eventSchema.list = [];
              draft.eventSchema.map = {};
              draft.loadDataDone = true;
            });
          }
        }
      })
      .catch(err => {
        if (_isMounted) {
          setState(draft => {
            draft.eventSchema.list = [];
            draft.eventSchema.map = {};
            draft.loadDataDone = true;
          });
          console.log('err ===>', err);
        }
      })
      .catch(err => {
        addMessageToQueue({
          path: 'app/components/common/UIPerformEvent/index.jsx',
          func: 'fetchData',
          data: err.stack,
        });
        setState(draft => {
          draft.eventSchema.list = [];
          draft.eventSchema.map = {};
          draft.loadDataDone = true;
        });
        console.log('err ===>', err);
      });
  };

  useEffect(() => {
    fetchData();
    return () => {
      _isMounted = false;
    };
  }, [props.componentId]);

  useUpdateEffect(() => {
    const validate = validateRulesConditionPerformEvent(state.rules);
    // console.log('i will validate ====>', validate);
    const tmpCondition = bindValidateResultToCondition(
      state.rules,
      validate.errors,
    );
    setState(draft => {
      draft.rules = tmpCondition;
    });
    // fetchData();
  }, [props.validateKey]);

  useEffect(() => {
    if (state.loadDataDone) {
      let isInit = false;
      let backup = {};
      const dataSourceID = '-1';
      let condition = safeParse(initData, OrderedMap({}));
      if (condition.size === 0 || Object.keys(condition).length === 0) {
        const tmp = initItem(SEGMENT_TYPES[0]);
        condition = condition.setIn([generateKey()], tmp);
      } else if (condition.size > 0) {
        isInit = condition.first().get('isInit');
        backup = condition.first().get('backup', {});
      }
      // const { perf_event } = getInputLookupFromRule(condition);
      const { eventActionId, eventCategoryId } = backup;

      // check key isInit in First = true thì set property cho nó
      const paramLookupEvent = {
        insightPropertyId: dataSourceID,
        dataPost: {
          eventIds: [
            {
              eventActionId,
              eventCategoryId,
            },
          ],
        },
      };
      MetaDataServices.eventSchema
        .lookupByIds(paramLookupEvent)
        .then(res2 => {
          setState(draft => {
            if (isInit) {
              const tmpConditions = toComponentPerformEvent(backup, {
                map: { eventSchema: state.eventSchema.map },
                info: { eventSchema: res2.map },
              });
              draft.rules = tmpConditions;
            } else {
              draft.rules = condition;
            }
          });
        })
        .catch(err => {
          addMessageToQueue({
            path: 'app/components/common/UIPerformEvent/index.jsx',
            func: 'useEffect',
            data: err.stack,
          });
          console.log('err ===>', err);
        })
        .finally(() => {
          setState(draft => {
            draft.eventSchema.isLoading = false;
          });
        });
    }
  }, [props.componentId, state.loadDataDone]);

  useUpdateEffect(() => {
    // const rules = toAPIPerformEvent(state.rules);
    props.onChange(state.rules);
  }, [state.rules]);

  const toggleModal = value => {
    setState(draft => {
      draft.isOpenModal = value;
    });
  };

  const callback = (type, data) => {
    switch (type) {
      case 'CONFIRM_CHANGE_EVENT': {
        if (data === true) {
          const { groupIndex, itemIndex, value } = state.tempEventChanged;

          const oldItem = safeParse(
            state.rules.getIn([groupIndex, itemIndex, 'property']),
            null,
          );

          if (oldItem === null || oldItem.value !== value.value) {
            // const { eventActionId, eventCategoryId } = value.value;
            const operators = PERF_EVENT_OPERATORS;

            const tempt = state.rules
              .getIn([groupIndex, itemIndex])
              .withMutations(map => {
                map
                  .set('value', '1')
                  .set('valueEnd', '')
                  .set('initValue', '')
                  // .set('keyEvent', `${eventCategoryId}-${eventActionId}`)
                  .set('property', value)
                  .set('error', 0)
                  .set('dataSources', [])
                  .set('dataType', value.dataType)
                  .set('operator', operators[0]) // no use getFirstOperator
                  .set('operators', operators)
                  .set('refineWithProperties', OrderedMap({}));
              });

            setState(draft => {
              draft.rules = state.rules.setIn([groupIndex, itemIndex], tempt);
            });
          }
          props.callback('RESET_TRIGGER_PERFORM_EVENT', value);
          props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', value);
        }
        break;
      }
      case 'COMP_PROP_CONDITION_CHANGE_PROPERTY': {
        const { groupIndex, itemIndex, value, isInit = false } = data.data;
        if (props.hasOpenModalConfirm && !isInit) {
          setState(draft => {
            draft.isOpenModal = true;
            draft.tempEventChanged = data.data;
          });
        } else {
          const oldItem = safeParse(
            state.rules.getIn([groupIndex, itemIndex, 'property']),
            null,
          );

          if (oldItem === null || oldItem.value !== value.value) {
            // const { eventActionId, eventCategoryId } = value.value;
            const operators = PERF_EVENT_OPERATORS;

            const tempt = state.rules
              .getIn([groupIndex, itemIndex])
              .withMutations(map => {
                map
                  .set('value', '1')
                  .set('valueEnd', '')
                  .set('initValue', '')
                  // .set('keyEvent', `${eventCategoryId}-${eventActionId}`)
                  .set('property', value)
                  .set('error', 0)
                  .set('isInitDataSources', false)
                  .set('dataSources', [])
                  .set('dataType', value.dataType)
                  .set('operator', operators[0]) // no use getFirstOperator
                  .set('operators', operators)
                  .set('refineWithProperties', OrderedMap({}));
              });

            setState(draft => {
              draft.rules = state.rules.setIn([groupIndex, itemIndex], tempt);
            });
          }
          props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', value);
        }

        break;
      }

      case 'PERF_EVENT_CONDITION_CHANGE_SOURCE': {
        const { groupIndex, itemIndex, value } = data.data;
        const currentRule = state.rules.getIn([groupIndex, itemIndex]);
        let dataRefine = currentRule.get('refineWithProperties');
        if (value.isDeleteRefine) {
          dataRefine = deleteRefinesBySourceId(
            currentRule.get('refineWithProperties'),
            value.sourceIdWillDeleted,
            value.array,
          );
        }

        const tempt = state.rules
          .getIn([groupIndex, itemIndex])
          .withMutations(map => {
            map.set('errorDataSources', 0);
            map.set('isInitDataSources', false);
            map.set('dataSources', value.dataSourceSelected);
            map.set(
              'refineWithProperties',
              value.dataSourceSelected.length === 0
                ? OrderedMap({})
                : dataRefine,
            );
          });
        setState(draft => {
          draft.rules = state.rules.setIn([groupIndex, itemIndex], tempt);
        });
        break;
      }

      case 'PERF_EVENT_CHANGE_REFINE_PROPERTIES': {
        const { type: typeChange } = data;
        const { groupIndex, itemIndex, refineIndex, value } = data.data;

        if (typeChange === 'INIT') {
          setState(draft => {
            draft.rules = state.rules.setIn(
              [groupIndex, itemIndex, 'refineWithProperties'],
              data.data.conditions,
            );
          });
        }
        if (typeChange === 'CHANGE_PROPERTY') {
          const oldItem = safeParse(
            state.rules.getIn([
              groupIndex,
              itemIndex,
              'refineWithProperties',
              refineIndex,
              'property',
            ]),
            null,
          );

          if (oldItem === null || oldItem.value !== value.value) {
            const operators = getOperatorByProperty(value);
            const tempt = state.rules
              .getIn([
                groupIndex,
                itemIndex,
                'refineWithProperties',
                refineIndex,
              ])
              .withMutations(map => {
                map
                  .set('value', '')
                  .set('valueEnd', '')
                  .set('initValue', '')
                  .set('property', value)
                  .set('error', 0)
                  .set('dataType', value.dataType)
                  .set('operator', getFirstOperator(operators))
                  .set('operators', operators)
                  .set('syntax', value.propertySyntax);
              });

            setState(draft => {
              draft.rules = state.rules.setIn(
                [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
                tempt,
              );
            });
          }
        } else if (typeChange === 'CHANGE_OPERATOR') {
          const tempt = state.rules
            .getIn([groupIndex, itemIndex, 'refineWithProperties', refineIndex])
            .withMutations(map => {
              map
                .set('value', '')
                .set('valueEnd', '')
                .set('initValue', '')
                .set('error', 0)
                .set('operator', value);
            });

          setState(draft => {
            draft.rules = state.rules.setIn(
              [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
              tempt,
            );
          });
        } else if (typeChange === 'CHANGE_VALUE') {
          const { name = 'value' } = data.data;
          // console.log('data', name, value);
          const tempt = state.rules
            .getIn([groupIndex, itemIndex, 'refineWithProperties', refineIndex])
            .withMutations(map => {
              map.set('error', 0).set(name, value);
            });

          setState(draft => {
            draft.rules = state.rules.setIn(
              [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
              tempt,
            );
          });
          // return state.setIn(
          //   [groupIndex, itemIndex, 'refineWithProperties', refineIndex, name],
          //   value,
          // );
        } else if (typeChange === 'ADD_ITEM') {
          setState(draft => {
            draft.rules = state.rules.setIn(
              [groupIndex, itemIndex, 'refineWithProperties', generateKey()],
              initItemRefinePropertiesValue(),
            );
          });
        } else if (typeChange === 'DELETE_ITEM') {
          setState(draft => {
            draft.rules = state.rules.deleteIn(
              [groupIndex, itemIndex, 'refineWithProperties', refineIndex],
              value,
            );
          });
        }
        break;
      }

      case 'PERF_EVENT_CHANGE_TIME_WINDOW': {
        const { groupIndex, itemIndex, value, object } = data.data;
        // object === 'operator|value|start_date|end_date';

        if (object === 'start_date') {
          const end_date = state.rules.getIn([
            groupIndex,
            itemIndex,
            'timeWindow',
            'end_date',
          ]);
          if (parseInt(value) > parseInt(end_date)) {
            setState(draft => {
              draft.rules = state.rules
                .setIn(
                  [groupIndex, itemIndex, 'timeWindow', 'start_date'],
                  value,
                )
                .setIn(
                  [groupIndex, itemIndex, 'timeWindow', 'end_date'],
                  value,
                );
            });
          }
        }
        setState(draft => {
          draft.rules = state.rules.setIn(
            [groupIndex, itemIndex, 'timeWindow', object],
            value,
          );
        });

        break;
      }
      default:
        break;
    }
  };

  const showContent = rules => {
    if (rules.size > 0) {
      const result = [];
      // const firstKey = rules.keySeq().first();
      rules.forEach((item, key) => {
        item.forEach((element, itemIndex) => {
          if (element.get('conditionType')) {
            if (
              element.get('conditionType').value ===
              MAP_SEGMENT_VALUES.perf_event
            ) {
              result.push(
                <BlockConditionPerformEvent
                  key={itemIndex}
                  // moduleConfig="perform-event"
                  moduleConfig={props.moduleConfig}
                  paramsFetchEvent={props.paramsFetchEvent}
                  groupIndex={key}
                  itemIndex={itemIndex}
                  item={element}
                  isViewMode={props.isViewMode}
                  options={state.eventSchema.list}
                  callback={callback}
                  isLoading={state.eventSchema.isLoading}
                  disabledEvent={props.disabledEvent}
                  disabledEventConditions={props.disabledEventConditions}
                  componentId={props.componentId}
                  showHeader={props.showHeader}
                  isConversion={props.isConversion}
                  isBlastCampaign={props.isBlastCampaign}
                />,
              );
            }
          }
        });
      });
      return result;
    }
    return null;
  };

  const classes = useStyles();

  if (state.eventSchema.isLoading) {
    return <UILoading isLoading />;
  }

  return (
    <div className={classes.root}>
      {props.showTitle && (
        <Grid container className={classes.padding}>
          <Grid item sm={12}>
            {getTranslateMessage(
              props.translateLabelTitle ||
                TRANSLATE_KEY._INFO_STORY_TRIGGER_WHEN,
              '',
            )}
          </Grid>
        </Grid>
      )}

      <Grid
        container
        className={`${classes.paddingX} ${
          classes.flexNoWrap
        } wrapper-perform-event`}
      >
        {props.showCustomTitle && (
          <Grid
            item
            sm={2}
            className={classes.customView}
            style={props.styledLabel || {}}
          >
            {props.title}
            {/* {getTranslateMessage(TRANSLATE_KEY._INFO_STORY_TRIGGER_WHEN, '')} */}
          </Grid>
        )}

        <Grid container item sm={10} md={10} style={{ maxWidth: '800px' }}>
          {showContent(state.rules)}
        </Grid>
      </Grid>
      <ModalFlowChartConfirmChangeEvent
        isOpen={state.isOpenModal}
        toggle={toggleModal}
        callback={callback}
      />
    </div>
  );
  // return <>{showContent(state.rules)}</>;
};

UIPerformEvent.defaultProps = {
  initData: undefined,
  onChange: () => {},
  callback: () => {},
  validateKey: 1,
  showTitle: true,
  showCustomTitle: true,
  disabledEvent: false,
  disabledEventConditions: false,
  hasOpenModalConfirm: false,
  showHeader: true,
  paramsFetchEvent: {},
  title: getTranslateMessage(TRANSLATE_KEY._INFO_STORY_TRIGGER_WHEN, ''),
  isConversion: false,
};

export default UIPerformEvent;
