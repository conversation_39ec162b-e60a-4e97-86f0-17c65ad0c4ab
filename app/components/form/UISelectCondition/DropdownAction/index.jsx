/* eslint-disable no-nested-ternary */
/* eslint-disable no-unneeded-ternary */
/* eslint-disable no-else-return */
/* eslint-disable indent */
/* eslint-disable no-lonely-if */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import { UITippy } from '@xlab-team/ui-components';
import Popover from '@material-ui/core/Popover';
// import PopupState, { bindTrigger, bindPopover } from 'material-ui-popup-state';
// import { RootRef } from '@material-ui/core';

import {
  usePopupState,
  bindTrigger,
  bindMenu,
} from 'material-ui-popup-state/hooks';
import { get, isFunction } from 'lodash';
import { OrderedSet } from 'immutable';
import { getTranslateMessage } from 'containers/Translate/util';

import Tree from './Tree';
import { generateKey, safeParse } from '../../../../utils/common';
import { initValue, getLabelMulti, getLabelTree } from './Tree/utils';
import { DATA_TEST, KEY_CODE } from '../constants';
import { selectTreeCheckOptionsHasParent } from '../utils';
import { WrapperSuggestionList } from '../../../common/UISuggestionList/styles';
import ButtonDropDown from './Tree/ButtonDropDown';
import { SpanSelectTree } from './Tree/styled';
import DropdownTagsLabel from './DropdownTagsLabel';
import { WrapperAddMore } from './styled';

const style = {
  minWidth: 'fit-contain',
  // maxWidth: '300px',
  maxHeight: '380px',
  // minHeight: '380px',
  overflowX: 'hidden',
  overflowY: 'auto',

  // fontSize: '13px',
};

function DropdownAction(props) {
  // console.log('phong');
  // const domRef = React.createRef();
  const { minWidth = '120px' } = props;
  const {
    isMulti,
    mode,
    DropdownButnComponent,
    isDisabledBorder,
    type,
    labelButton = '',
    widthButton,
    disabled,
  } = props;
  // const [open, setOpen] = useState(false);
  const [value, setValue] = useState(isMulti ? OrderedSet([]) : '');
  const [open, setOpen] = useState(false);

  const [indexCurrentValue, setIndexCurrentValue] = useState(-1);
  const [indexChild, setIndexChild] = useState(0);
  const [isOpenParent, setIsOpenParent] = useState(false);

  const [pressEnter, setPressEnterState] = useState(false);

  const [stateOptions, setStateOptions] = useState([]);
  const [isSearch, setIsSearch] = useState(false);
  const [stateHasChilds, setStateHasChilds] = useState(false);
  const [isPress, setIsPress] = useState(false);
  // const [stateOptionIsParent, setStateOptionIsParent] = useState(false);
  // const [anchorEl, setAnchorEl] = useState(null);
  const [widthPopover, setWidthPopover] = useState('inherit');
  const randomKey = generateKey();

  const popupStateTree = usePopupState({
    variant: 'popover',
    popupId: 'tree-popup-popover',
  });

  const commonValidateValueSelected = () => {
    if (
      Array.isArray(props.options) &&
      props.options !== undefined &&
      props.options.length > 0 &&
      props.value !== undefined &&
      props.value !== null &&
      props.value !== ''
    ) {
      let index = 0;
      const checkIsParent = selectTreeCheckOptionsHasParent(props.options);
      // setStateOptionIsParent(checkIsParent);
      // trường hợp là parent
      if (checkIsParent) {
        props.options.forEach((option, indexParent) => {
          if (Array.isArray(option.options)) {
            // check xem có childs hay không
            option.options.forEach((item, indexChildrent) => {
              if (item.value === props.value.value) {
                index = indexParent;
                setIndexChild(indexChildrent);
                setIsOpenParent(true);
                setPressEnter(true);
                setIndexCurrentValue(indexParent);
              }
            });
          }
          // else {
          //   // trường hợp parent không có child
          //   console.log('khong os child');
          //   index = 0;
          //   setIndexCurrentValue(index);
          // }
        });
      } else {
        index = props.options.findIndex(
          item => item.value === props.value.value,
        );
        setIndexCurrentValue(index);
      }
      // console.log('index useEffect ====>', index);

      // if (index === -1) {
      //   setIndexCurrentValue(-1);
      // } else {
      //   setIndexCurrentValue(index);
      // }
    }
  };

  useEffect(() => {
    setStateOptions(props.options);
    setIndexCurrentValue(-1);
    setIsPress(false);
    setIsSearch(false);
    setIsOpenParent(false);
    setStateHasChilds(false);
    setPressEnter(false);

    commonValidateValueSelected();
  }, [props.options, popupStateTree.isOpen, open]);

  useEffect(() => {
    const temp = initValue(safeParse(props.value, isMulti ? [] : ''), isMulti);

    setValue(temp);
  }, [props.value, props.feKey]);

  useEffect(() => {
    const button = document.getElementById(`button-drop-down-${randomKey}`);
    if (button) {
      const { width } = button.getBoundingClientRect();

      setWidthPopover(safeParse(`${width}px`, 'inherit'));
    }
  }, [popupStateTree.isOpen]);

  useEffect(() => {
    if (popupStateTree.isOpen && props.fullWidthPopover) {
      const button = document.getElementById(`button-drop-down-${randomKey}`);
      const { width } = button.getBoundingClientRect();
      setWidthPopover(
        safeParse(`${type === 'button' ? widthButton : width}px`, 'inherit'),
      );
    }
  }, [popupStateTree.isOpen]);

  const setPressEnter = temp => {
    if (temp === true || temp === false) {
      setPressEnterState(temp);
    }
  };

  // const toggle = event => {
  //   console.log('toggle ===>', event.currentTarget, event.keyCode);
  //   setAnchorEl(event.currentTarget);
  //   setOpen(true);
  // }
  const onClose = () => {
    popupStateTree.close();
    // setAnchorEl(null);
    // setOpen(false);
  };
  const onChange = item => {
    if (!isMulti) {
      onClose();
      onChangeSingle(item);
    } else {
      onChangeMulti(item);
    }
  };

  const onChangeSingle = item => {
    setOpen(false);
    // popupStateTree.close();
    props.onChange(item);
  };

  const onChangeMulti = item => {
    // setOpen(false);
    let temptValue = value;
    if (value.has(item.value)) {
      temptValue = value.remove(item.value);
    } else {
      temptValue = value.add(item.value);
    }
    // setValue(temptValue);
    if (props.FooterComponent) {
      props.onChange(item);
    } else {
      props.onChange(temptValue.toArray());
    }
  };

  const callback = (type, data) => {
    switch (type) {
      case 'DELETE_SOURCE_ITEM': {
        onChangeMulti(data.item);
        break;
      }
      default:
        break;
    }
  };

  // overwrite onClick of bindTrigger
  const onClickButtonDropdown = event => {
    const clickedElement = get(event, 'target.innerText', '');
    if (clickedElement === 'close') {
      // khong mo lai popup khi nhan remove
      return;
    }
    bindTrigger(popupStateTree).onClick(event);
    if (isMulti) {
      props.callback('OPEN_POPOVER');
    }
  };

  const renderButtonDropdown = () => {
    if (isFunction(props.renderButtonDropdown)) {
      return props.renderButtonDropdown({
        bindTrigger: () => ({
          ...bindTrigger(popupStateTree),
          onClick: onClickButtonDropdown,
          id: `button-drop-down-${randomKey}`,
        }),
      });
    }

    const temptValue = safeParse(props.value, {});

    const temp = props.isMulti
      ? getLabelMulti(value, props.mapOptions, props.placeholder)
      : getLabelTree(
          temptValue,
          props.placeholder,
          props.placeholderTranslateCode,
        );

    const labelSpan = getTranslateMessage(temp.translateCode, temp.label);

    return (
      <>
        {type === 'button' ? (
          <WrapperAddMore
            id={`button-drop-down-${randomKey}`}
            onClick={onClickButtonDropdown}
          >
            {labelButton}
          </WrapperAddMore>
        ) : (
          <ButtonDropDown
            disabled={disabled}
            variant="contained"
            {...bindTrigger(popupStateTree)}
            onClick={onClickButtonDropdown}
            idButton={`button-drop-down-${randomKey}`}
            width={props.selectWidth ? props.selectWidth : props.labelWidth}
            maxWidth={
              props.selectMaxWidth ? props.selectmMaxWidth : props.labelMaxWidth
            }
            hasError={props.errors && props.errors.length > 0}
            isErrBtnBottom={props.isErrBtnBottom}
            isDisabledBorder={isDisabledBorder}
          >
            {mode === 'tags' ? (
              <DropdownTagsLabel
                options={temptValue}
                callback={callback}
                placeholder={props.placeholder}
                maxTags={2}
              />
            ) : (
              <UITippy
                content={
                  props.tooltip ? props.tooltip : labelSpan ? labelSpan : ''
                }
                arrow
                distance={10}
              >
                <SpanSelectTree
                  isDisabledBorder={isDisabledBorder}
                  style={{ ...(props.styleLabelDropdown || {}) }}
                  data-test={DATA_TEST.VALUE}
                >
                  {DropdownButnComponent ? (
                    <DropdownButnComponent {...props} />
                  ) : (
                    labelSpan
                  )}
                </SpanSelectTree>
              </UITippy>
            )}
          </ButtonDropDown>
        )}
      </>

      // <DropdownToggle tag="div" className="selenium-test-marker-filter-options">

      // </DropdownToggle>
    );
  };

  const showError = errors => {
    if (errors.length > 0) {
      const result = [];
      errors.forEach((error, index) => {
        if (error !== '') {
          if (index === 0) {
            result.push(
              <div key={index} className="is--text--error p-top-1">
                {error}
              </div>,
            );
          } else {
            result.push(
              <div key={index} className="is--text--error">
                {error}
              </div>,
            );
          }
        }
      });
      return result;
    }
    return null;
  };
  const classError = props.errors.length > 0 ? 'xlab-select--error' : '';

  const handleKeyDown = e => {
    const { keyCode } = e;
    setIsPress(true);

    const indexLastItem = stateOptions.length - 1;
    // Giành cho trường hợp di chuyển qua các parent (nếu có),
    // hoặc di chuyển qua các single element
    if (
      (!isOpenParent && !isSearch && !props.isParentOpen) ||
      (isSearch && !stateHasChilds)
    ) {
      if (keyCode === KEY_CODE.PG_DOWN) {
        if (indexCurrentValue === indexLastItem) {
          setIndexCurrentValue(0);
        } else {
          setIndexCurrentValue(indexCurrentValue + 1);
        }
      } else if (keyCode === KEY_CODE.PG_UP) {
        if (indexCurrentValue === 0) {
          setIndexCurrentValue(indexLastItem);
        } else {
          setIndexCurrentValue(indexCurrentValue - 1);
        }
      } else if (keyCode === KEY_CODE.ENTER && indexCurrentValue >= 0) {
        if (
          stateOptions[indexCurrentValue].options !== undefined &&
          stateOptions[indexCurrentValue].options.length > 0
        ) {
          // isParent
          // setPressEnter(!pressEnter);
          // setIsOpenParent(!isOpenParent);
          if (!pressEnter && !isOpenParent) {
            // setPressEnter(true);
            // setIsOpenParent(true);
            setPressEnter(true);
            setIsOpenParent(true);
          }
        } else {
          onChange(stateOptions[indexCurrentValue]);
          setIsOpenParent(false);
          setIndexCurrentValue(indexCurrentValue);
          setIndexChild(-1);
        }
        // setStateOptions(props.options);
      } else if (keyCode === KEY_CODE.RIGHT && indexCurrentValue >= 0) {
        if (
          stateOptions[indexCurrentValue].options !== undefined &&
          stateOptions[indexCurrentValue].options.length > 0
        ) {
          // isParent
          setPressEnter(!pressEnter);
          setIsOpenParent(!isOpenParent);
          if (!pressEnter && !isOpenParent) {
            // setPressEnter(true);
            // setIsOpenParent(true);
            setPressEnter(true);
            setIsOpenParent(true);
          }
        }
      } else if (keyCode === KEY_CODE.LEFT && indexCurrentValue >= 0) {
        if (
          stateOptions[indexCurrentValue].options !== undefined &&
          stateOptions[indexCurrentValue].options.length > 0
        ) {
          // isParent
          if (pressEnter && isOpenParent) {
            // setPressEnter(true);
            // setIsOpenParent(true);
            setPressEnter(false);
            setIsOpenParent(false);
          }
        }
      }
      // else if (keyCode === 32) {
      //   setOpen(true);
      // }
    } else if (
      (props.isParentOpen && !isSearch) ||
      (isOpenParent && !isSearch)
    ) {
      // Giành cho trường hợp di chuyển qua các element trong một parent
      // khi parent đã được open
      const childOptions = stateOptions[indexCurrentValue].options || [];

      const childOptionsLength = childOptions.length;
      const lastIndexChildOption = childOptionsLength - 1;

      if (keyCode === KEY_CODE.PG_DOWN) {
        if (indexChild === lastIndexChildOption) {
          // last element of a child
          // last parent
          // if (pressEnter) {
          //   setPressEnter(false);
          //   setIsOpenParent(false);
          //   setIndexChild(-1);
          // } else {
          //   setPressEnter(true);
          //   setIsOpenParent(true);
          // }
          setIndexChild(0);
          // setIsOpenParent(false);
          // setIndexCurrentValue(indexCurrentValue + 1);
          // setIndexChild(-1);
          // }
        } else {
          setIndexChild(indexChild + 1);
        }
      } else if (keyCode === KEY_CODE.PG_UP) {
        if (indexChild === 0) {
          // first element of a child
          // first parent
          // if (pressEnter) {
          //   setPressEnter(false);
          //   setIsOpenParent(false);
          // } else {
          //   setPressEnter(true);
          //   setIsOpenParent(true);
          // }
          setIndexChild(lastIndexChildOption);
        } else if (indexChild === -1) {
          setIndexChild(-1);
          // setPressEnter(false);
          // setIsOpenParent(false);
        } else {
          setIndexChild(indexChild - 1);
        }
      } else if (keyCode === KEY_CODE.ENTER && indexCurrentValue >= 0) {
        if (
          stateOptions[indexCurrentValue].options.length > 0 &&
          indexChild < 0
        ) {
          // isParent
          if (!pressEnter && !isOpenParent) {
            setPressEnter(true);
            setIsOpenParent(true);
            // setIndexChild(-1);
          }
          // setPressEnter(!pressEnter);
          // setIsOpenParent(!isOpenParent);
        } else {
          onChange(childOptions[indexChild]);
          setIsOpenParent(false);
          setIndexCurrentValue(indexCurrentValue);
          setIndexChild(-1);
        }
      } else if (keyCode === KEY_CODE.RIGHT && indexCurrentValue >= 0) {
        if (
          stateOptions[indexCurrentValue].options.length > 0 &&
          indexChild < 0
        ) {
          // isParent
          if (!pressEnter && !isOpenParent) {
            setPressEnter(true);
            setIsOpenParent(true);
            // setIndexChild(-1);
          }
          // setPressEnter(!pressEnter);
          // setIsOpenParent(!isOpenParent);
        }
      } else if (keyCode === KEY_CODE.LEFT) {
        if (
          stateOptions[indexCurrentValue].options !== undefined &&
          stateOptions[indexCurrentValue].options.length > 0
        ) {
          if (pressEnter && isOpenParent) {
            setPressEnter(false);
            setIsOpenParent(false);
            setIndexChild(-1);
          }
        }
      }
    } else if (isSearch && stateHasChilds) {
      // trường hợp search có parent
      const childOptions = stateOptions[indexCurrentValue].options || [];
      const childOptionsLength = childOptions.length;

      if (childOptionsLength > 0) {
        if (keyCode === KEY_CODE.PG_DOWN) {
          setIndexCurrentValue(indexCurrentValue + 1);
          return;
        } else if (keyCode === KEY_CODE.PG_UP) {
          if (indexCurrentValue === 0) return;
          setIndexCurrentValue(indexCurrentValue - 1);
          return;
        }
      }

      const lastIndexChildOption = childOptionsLength - 1;

      if (keyCode === KEY_CODE.PG_DOWN) {
        // trường hợp khi search xong thực hiện di chuyển vào child đầu của parent tương ứng
        if (indexCurrentValue >= 0 && indexChild === -1) {
          setIndexChild(indexChild + 1);
          setIsOpenParent(true);
        } else if (indexCurrentValue >= 0 && indexChild >= 0) {
          if (indexChild === lastIndexChildOption) {
            if (indexCurrentValue === indexLastItem) {
              // trường hợp di chuyển xuống child cuối của parent
              // mà parent đó là parent cuối nên quay ngược lại parent đầu
              setIndexCurrentValue(0);
              setIsOpenParent(false);
              setIndexChild(-1);
            } else {
              // trường hợp di chuyển xuống child cuối của parent nào đó
              // sẽ thực hiện nhảy tới parent kế tiếp
              setIndexCurrentValue(indexCurrentValue + 1);
              setIsOpenParent(false);
              setIndexChild(-1);
            }
          } else {
            // trường hợp di chuyển trong một list child
            setIndexChild(indexChild + 1);
            setIsOpenParent(true);
          }
        }
      } else if (keyCode === KEY_CODE.PG_UP) {
        if (
          (indexCurrentValue === 0 && indexChild === -1) ||
          (indexCurrentValue === 0 && indexChild === 0)
        ) {
          const lengthOfLastParent = stateOptions[indexLastItem].options.length;
          setIndexCurrentValue(indexLastItem);
          setIndexChild(lengthOfLastParent - 1);
          setIsOpenParent(true);
        } else if (indexCurrentValue > 0 && indexChild >= 0) {
          if (indexChild === 0) {
            const lengthOfPrevParent =
              stateOptions[indexCurrentValue - 1].options.length;
            setIndexCurrentValue(indexCurrentValue - 1);
            setIndexChild(lengthOfPrevParent - 1);
            setIsOpenParent(true);
          } else {
            setIndexChild(indexChild - 1);
            setIsOpenParent(true);
          }
        }
        // else if (indexCurrentValue >= 0 && indexChild >= 0) {
        //   if (indexChild === lastIndexChildOption) {
        //     if (indexCurrentValue === indexLastItem) {
        //       // trường hợp di chuyển xuống child cuối của parent
        //       // mà parent đó là parent cuối nên quay ngược lại parent đầu
        //       setIndexCurrentValue(0);
        //       setIsOpenParent(false);
        //       setIndexChild(-1);
        //     } else {
        //       // trường hợp di chuyển xuống child cuối của parent nào đó
        //       // sẽ thực hiện nhảy tới parent kế tiếp
        //       setIndexCurrentValue(indexCurrentValue + 1);
        //       setIsOpenParent(false);
        //       setIndexChild(-1);
        //     }
        //   } else {
        //     // trường hợp di chuyển trong một list child
        //     setIndexChild(indexChild + 1);
        //     setIsOpenParent(true);
        //   }
        // }
      } else if (keyCode === KEY_CODE.ENTER && indexCurrentValue >= 0) {
        // if (
        //   stateOptions[indexCurrentValue].options !== undefined &&
        //   stateOptions[indexCurrentValue].options.length > 0
        // ) {
        // isParent
        // setPressEnter(!pressEnter);
        // setIsOpenParent(!isOpenParent);
        if (indexChild >= 0) {
          onChange(childOptions[indexChild]);
          setIsOpenParent(false);
          setIndexCurrentValue(indexCurrentValue);
          setIndexChild(-1);
        }
        // } else {
        //   onChange(stateOptions[indexCurrentValue]);
        // }
        // setStateOptions(props.options);
      } else if (keyCode === KEY_CODE.RIGHT && indexCurrentValue >= 0) {
        // if (
        //   stateOptions[indexCurrentValue].options !== undefined &&
        //   stateOptions[indexCurrentValue].options.length > 0
        // ) {
        // isParent
        // setPressEnter(!pressEnter);
        // setIsOpenParent(!isOpenParent);
        // }
      } else if (keyCode === KEY_CODE.LEFT) {
        // setPressEnter(!pressEnter);
        // setIsOpenParent(!pressEnter);
        // setIndexChild(-1);
      }
    }
  };

  const handleTextSearch = (arrayTree, lengthValueSearch, hasChilds) => {
    setIsOpenParent(false);
    setIndexCurrentValue(0);
    setStateHasChilds(hasChilds);

    if (lengthValueSearch > 0) {
      setIndexChild(-1);
      setStateOptions(arrayTree);
      setIsSearch(true);
    } else if (lengthValueSearch === 0 && arrayTree.length === 0) {
      setStateOptions(props.options);
      setIsSearch(false);
      setIndexChild(0);
      setIsOpenParent(true);

      commonValidateValueSelected();
    }
  };

  const handleOnClick = (checkOpen, indexItem) => {
    // setOpen(false);
    if (indexItem.toString().includes('-')) {
      const spliceIndex = indexItem.split('-');
      setIndexCurrentValue(parseInt(spliceIndex[0]));
      setIndexChild(parseInt(spliceIndex[1]));
    } else {
      setPressEnter(checkOpen);
      setIsOpenParent(checkOpen);
      setIndexCurrentValue(indexItem);
      setIndexChild(-1);
      setIsPress(false);
    }
  };

  const renderTree = () => {
    if (props.use === 'inline') {
      return (
        <div
          className={`UIColumn-content private-form__control-wrapper ${classError} ${
            props.className
          }`}
          tabIndex={0}
          role="menuitem"
          onKeyDown={handleKeyDown}
        >
          <WrapperSuggestionList style={style}>
            <div className="private-dropdown uiDropdown__dropdown private-dropdown--list">
              <Tree
                searchPlaceholderTranslateCode={
                  props.searchPlaceholderTranslateCode
                }
                searchNoDataTranslateCode={props.searchNoDataTranslateCode}
                searchPlaceholder={props.searchPlaceholder}
                noDataTranslateCode={props.noDataTranslateCode}
                isMulti={isMulti}
                mode={mode}
                isSearchable={props.isSearchable}
                onlyParent={props.onlyParent}
                onlyHasData={props.onlyHasData}
                onlyChild={props.onlyChild}
                options={props.options}
                optionSearch={props.optionSearch || []}
                temptOptions={stateOptions}
                value={value}
                onChange={onChange}
                isParentOpen={props.isParentOpen}
                indexCurrentValue={indexCurrentValue}
                // isOpen={open}
                isOpen={popupStateTree.isOpen}
                popupStateTree={popupStateTree}
                pressEnter={pressEnter}
                handleTextSearch={handleTextSearch}
                isOpenParent={isOpenParent}
                indexChild={indexChild}
                handleOnClick={handleOnClick}
                isPress={isPress}
                ItemComponent={props.ItemComponent}
                FooterComponent={props.FooterComponent}
                callback={props.callback}
                isFooter={props.isFooter}
              />
            </div>
          </WrapperSuggestionList>
          {showError(props.errors)}
        </div>
      );
    }

    return (
      <>
        {renderButtonDropdown()}

        <Popover
          {...bindMenu(popupStateTree)}
          getContentAnchorEl={null}
          keepMounted
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
          PaperProps={{
            style: {
              borderRadius: 10,
            },
            'data-test': DATA_TEST.POPOVER_WRAPPER,
          }}
          // onClose={onClose}
          // open={open}
          // anchorEl={anchorEl}
          // onEntered={() => {
          //   console.log('onEntered===>');
          // }}
          // onEntering={() => {
          //   console.log('onEntonEnteringer===>');
          // }}
          // onEnter={() => {
          //   console.log('onEnter===>');
          // }}
          // className="width-100"
          // isOpen={open}
          // toggle={toggle}
          onKeyDown={handleKeyDown}
          {...props.popoverProps || {}}
        >
          <div
            className={`UIColumn-content private-form__control-wrapper ${classError} ${
              props.className
            }`}
            tabIndex={0}
            role="menuitem"
            style={
              props.fullWidthPopover
                ? { width: `${widthPopover}`, minWidth }
                : { width: 'inherit', minWidth }
            }
            // onKeyDown={handleKeyDown}
          >
            {/* <Dropdown
            direction="down"
            className="width-100"
            isOpen={open}
            toggle={toggle}
            flip="false"
            modifiers={{ preventOverflow: { boundariesElement: 'window' } }}
            // positionFixed
          > */}
            {/* <DropdownMenu
              className="width-100 cdp-tree-min-width"
              // modifiers={{ preventOverflow: { boundariesElement: 'window' } }}
              caret="true"
              // positionFixed={true}
            > */}
            <WrapperSuggestionList style={style}>
              <div
                className="private-dropdown uiDropdown__dropdown private-dropdown--list"
                data-test={DATA_TEST.POPOVER_WRAPPER}
              >
                <Tree
                  searchPlaceholderTranslateCode={
                    props.searchPlaceholderTranslateCode
                  }
                  searchNoDataTranslateCode={props.searchNoDataTranslateCode}
                  searchPlaceholder={props.searchPlaceholder}
                  noDataTranslateCode={props.noDataTranslateCode}
                  isMulti={isMulti}
                  mode={mode}
                  isSearchable={props.isSearchable}
                  onlyParent={props.onlyParent}
                  onlyHasData={props.onlyHasData}
                  onlyChild={props.onlyChild}
                  options={props.options}
                  optionSearch={props.optionSearch || []}
                  temptOptions={stateOptions}
                  value={value}
                  onChange={onChange}
                  isParentOpen={props.isParentOpen}
                  indexCurrentValue={indexCurrentValue}
                  // isOpen={open}
                  isOpen={popupStateTree.isOpen}
                  popupStateTree={popupStateTree}
                  pressEnter={pressEnter}
                  handleTextSearch={handleTextSearch}
                  isOpenParent={isOpenParent}
                  indexChild={indexChild}
                  handleOnClick={handleOnClick}
                  isPress={isPress}
                  ItemComponent={props.ItemComponent}
                  FooterComponent={props.FooterComponent}
                  callback={props.callback}
                  isFooter={props.isFooter}
                  searchNodataLabel={props.searchNodataLabel}
                  searchNodataIcon={props.searchNodataIcon}
                />
              </div>
            </WrapperSuggestionList>
            {/* </DropdownMenu> */}
            {/* </Dropdown> */}
          </div>
        </Popover>
      </>
    );
  };

  // console.log('lllllllprops', props);

  return (
    <React.Fragment>
      {/* <RootRef rootRef={domRef}>{renderTree()}</RootRef> */}
      {renderTree()}
    </React.Fragment>
  );
}

export default DropdownAction;
