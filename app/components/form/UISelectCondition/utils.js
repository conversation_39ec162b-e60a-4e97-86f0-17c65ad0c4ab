import { OrderedMap } from 'immutable';
import { generateKey, safeParse } from '../../../utils/common';
import { STATUS_ITEM_CODE } from '../../../utils/constants';

/* eslint-disable no-param-reassign */
export function serializeValue(
  propertyValues,
  value,
  isMulti = false,
  onlyHasData = false,
) {
  const temptValue = safeParse(value, {});
  const valueCompare = safeParse(temptValue.value, '');
  let rules = OrderedMap({});
  if (
    propertyValues === undefined ||
    propertyValues.length === 0 ||
    !Array.isArray(propertyValues)
  ) {
    return rules;
  }
  // console.log({ propertyValues });

  propertyValues.forEach((item, indexParent) => {
    let tempt = OrderedMap({
      name: item.value,
      label: item.label,
      tooltip: item.tooltip,
      translateCode: item.translateCode,
      item,
      open: propertyValues.length === 1 || false,
      hidden: false,
      disabled: safeParse(item.disabled, false),
      statusItemCode: safeParse(item.statusItemCode, STATUS_ITEM_CODE.ACTIVE),
      index: indexParent,
      dataTest: item.dataTest,
    });

    const { options } = item;
    // if options == null => case do not have child, else => this is parent but have no child

    if (options !== undefined && options.length > 0) {
      let childs = OrderedMap({});
      options.forEach((obj, indexChild) => {
        if (obj) {
          const active = isMulti ? false : obj.value === valueCompare;

          const child = OrderedMap({
            name: obj.value,
            label: obj.label,
            translateCode: obj.translateCode,
            item: obj,
            active,
            hidden: false,
            statusItemCode: safeParse(
              obj.statusItemCode,
              STATUS_ITEM_CODE.ACTIVE,
            ),
            disabled: safeParse(obj.disabled, false),
            index: `${indexParent}-${indexChild}`,
            dataTest: obj.dataTest,
          });

          childs = childs.set(generateKey(), child);

          if (active) {
            tempt = tempt.set('open', true);
          }
        }
      });
      tempt = tempt.set('childs', childs);
    }
    const active = isMulti ? false : item.value === valueCompare;

    // if (options !== undefined && options.length === 0 && onlyHasData) {
    //   // tempt = tempt.set('active', active);
    //   // rules = rules.set(generateKey(), tempt);
    // } else {
    tempt = tempt.set('active', active);
    rules = rules.set(generateKey(), tempt);
    // }
    // tempt = tempt.set('active', active);
    // rules = rules.set(generateKey(), tempt);
  });

  return rules;
}

export const selectTreeCheckOptionsHasParent = options => {
  let countParent = 0;

  options.forEach(option => {
    if (Array.isArray(option.options) && option.options.length > 0) {
      countParent += 1;
    }
  });

  if (countParent > 0) {
    // trường hợp có parent
    return true;
  }
  return false;
};
