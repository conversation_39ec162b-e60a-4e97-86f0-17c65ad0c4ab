import styled from 'styled-components';

export const MaterialChip = styled.div`
  padding: 0 0.625rem 0 0.625rem;
  max-width: 200px;
  background-color: #005eb8;
  color: #fff;

  ${({ isError }) =>
    isError &&
    `background: #ed5240 !important;
    cursor: not-allowed !important;`}

  ${({ $readOnly }) => $readOnly && 'cursor: default !important;'}

  ${({ isBlastCampaign, isViewMode }) =>
    isBlastCampaign &&
    isViewMode &&
    'background-color: #fff !important; color: #000; border: 1px solid #8888; '}

  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  height: 1.688rem;
  .content-chip {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
  }
  [class*='MuiSvgIcon'] {
    border: 0;
    cursor: pointer;
    outline: none;
    display: flex;
    font-size: 1rem;
  }
`;

export const Wrapper = styled.div`
  overflow-x: hidden;
  align-items: center;
  cursor: pointer;
  display: flex;
  min-width: 0;

  .chip-container {
    overflow: hidden;
    font-size: 0.75rem;
    font-family: Roboto-Bold;
  }
`;
