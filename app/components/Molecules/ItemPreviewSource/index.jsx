import React, { forwardRef } from 'react';
import { UITippy } from '@xlab-team/ui-components';
import PropTypes from 'prop-types';
import { MaterialChip, Wrapper } from './styles';

const ItemPreviewSource = forwardRef((props, ref) => {
  const { label, value, isError, leftIcon, rightIcon, readOnly } = props;

  return (
    <div className="predicates-container" ref={ref}>
      <Wrapper id={`item-rule-${value}`}>
        <div className="chip-container">
          <UITippy content={label} arrow distance={10} >
            <MaterialChip
              $readOnly={readOnly}
              variant="contained"
              isError={isError}
              isBlastCampaign={props.isBlastCampaign}
              isViewMode={props.isViewMode}
            >
              {!readOnly && leftIcon && (
                <div className="icon icon-left">{leftIcon}</div>
              )}
              <div className="content-chip">{label}</div>
              {!readOnly && rightIcon && (
                <div className="icon icon-right">{rightIcon}</div>
              )}
            </MaterialChip>
          </UITippy>
        </div>
      </Wrapper>
    </div>
  );
});

ItemPreviewSource.propTypes = {
  label: PropTypes.string,
  value: PropTypes.any,
  isError: PropTypes.bool,
  leftIcon: PropTypes.element,
  rightIcon: PropTypes.element,
  readOnly: PropTypes.bool,
};

export default ItemPreviewSource;
