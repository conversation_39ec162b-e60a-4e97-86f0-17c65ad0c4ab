/* eslint-disable import/no-cycle */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
// Libraries
import React, { useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { has, isEmpty, omit, isFunction } from 'lodash';
import { useImmer } from 'use-immer';
import produce from 'immer';

// Styled
import ErrorBoundary from 'components/common/ErrorBoundary';
import {
  Container,
  Heading,
  ButtonListWrapper,
  RenderButtonList,
} from './styled';

// Components
import ItemButton from './ItemButton';
import { Button, Icon } from '@antscorp/antsomi-ui';

// Translations
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

// Utils
import {
  buildDataInitValue,
  initialButtonItem,
  initialState,
  serializedDataOut,
} from './utils';
import { addMessageToQueue } from '../../../utils/web/queue';
import { getObjectPropSafely } from '../../../utils/common';
import { random } from '../../common/UIEditorPersonalization/utils.3rd';

// Constants
import { ACTION_TYPE_KEYS } from './constants';

const MAP_TRANSLATE = {
  addBtn: getTranslateMessage(TRANSLATE_KEY._, 'Add Button'),
};

const PATH = 'app/components/Molecules/ActionsButtons/index.jsx';

function ActionButtons(props) {
  const {
    label,
    design,
    isViewMode,
    initValue,
    errors,
    isRequired,
    limitTotal,
    onChange,
  } = props;

  const [state, setState] = useImmer(initialState(initValue));

  const isHideAddBtn = useMemo(() => state.buttonList.length >= limitTotal, [
    limitTotal,
    state.buttonList,
  ]);

  const callback = (type = '', dataIn = {}) => {
    try {
      switch (type) {
        case 'ON_CHANGE_ITEM': {
          const {
            buttonId = '',
            name: nameTmp = '',
            value: valueTmp = '',
          } = dataIn;

          const item = getObjectPropSafely(
            () =>
              state.buttonMap[buttonId] && state.buttonMap[buttonId][nameTmp],
          );

          if (buttonId && nameTmp && item) {
            setState(prevState => {
              const nextState = produce(prevState, draft => {
                draft.buttonMap[buttonId][nameTmp] = {
                  ...(item || {}),
                  value: valueTmp,
                  isValidate: true,
                  errors: [],
                };

                if (nameTmp === 'actionType' && valueTmp) {
                  const { value: valueAction = '' } = valueTmp;
                  const dynamicFields = [];

                  if (valueAction === ACTION_TYPE_KEYS.URL)
                    dynamicFields.push('url');
                  if (valueAction === ACTION_TYPE_KEYS.MESSAGE)
                    dynamicFields.push('displayMode', 'contentMessage');
                  if (valueAction === ACTION_TYPE_KEYS.PHONE)
                    dynamicFields.push('phone');
                  if (valueAction === ACTION_TYPE_KEYS.SMS)
                    dynamicFields.push('phoneSms', 'contentSms');

                  draft.buttonMap[buttonId].dynamicFields = dynamicFields;
                }
              });

              if (isFunction(onChange)) {
                onChange(serializedDataOut(nextState));
              }
              return nextState;
            });
          }
          break;
        }
        case 'REMOVE_BUTTON_ITEM': {
          const { buttonId = '' } = dataIn;

          if (
            !buttonId ||
            !state.buttonList.includes(buttonId) ||
            !has(state.buttonMap, buttonId)
          )
            break;

          const newMapButton = omit(state.buttonMap, buttonId);

          setState(prevState => {
            const nextState = produce(prevState, draft => {
              draft.buttonList = state.buttonList.filter(
                eachBtnId => eachBtnId !== buttonId,
              );
              draft.buttonMap = newMapButton;
            });

            if (isFunction(onChange)) {
              onChange(serializedDataOut(nextState));
            }
            return nextState;
          });

          break;
        }
        default: {
          break;
        }
      }
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'callback',
        data: err.stack,
      });
    }
  };

  const handleAddNewButton = () => {
    try {
      const newBtnId = random(10);

      setState(prevState => {
        const nextState = produce(prevState, draft => {
          draft.buttonList.push(newBtnId);
          draft.buttonMap[newBtnId] = initialButtonItem();
        });

        if (isFunction(onChange)) {
          onChange(serializedDataOut(nextState));
        }
        return nextState;
      });
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'handleAddNewButton',
        data: err.stack,
      });
    }
  };

  // Fill data case update
  useEffect(() => {
    try {
      if (
        !isEmpty(initValue) &&
        Array.isArray(initValue) &&
        design !== 'create'
      ) {
        const builtInitValue = buildDataInitValue(initValue);

        setState(() => builtInitValue);
      }
    } catch (err) {
      addMessageToQueue({
        path: PATH,
        func: 'useEffect',
        data: err.stack,
      });
    }
  }, []);

  // Validate data
  useEffect(() => {
    if (!isEmpty(errors) && Array.isArray(errors)) {
      try {
        errors.forEach((error, index) => {
          if (!isEmpty(error)) {
            const errorKeys = Object.keys(error);
            const btnId = state.buttonList[index];

            if (btnId) {
              errorKeys.forEach(eachErr => {
                const item = getObjectPropSafely(
                  () => state.buttonMap[btnId][eachErr],
                );

                if (!isEmpty(item)) {
                  const {
                    errors: errorsValidate = [],
                    isValidate = true,
                  } = item.validate(item);

                  setState(draft => {
                    draft.buttonMap[btnId][eachErr] = {
                      ...item,
                      errors: errorsValidate,
                      isValidate,
                    };
                  });
                }
              });
            }
          }
        });
      } catch (err) {
        addMessageToQueue({
          path: PATH,
          func: 'useEffect validate',
          data: err.stack,
        });
      }
    }
  }, [errors]);

  useEffect(
    () => () => {
      setState(() => initialState());
    },
    [],
  );

  const renderContent = () => {
    const { buttonMap = {}, buttonList = [] } = state;

    if (buttonList.length === 0) return null;

    const content = buttonList.map((eachBtnId, index) => (
      <ItemButton
        key={eachBtnId}
        {...buttonMap[eachBtnId]}
        isViewMode={isViewMode}
        isShowRemoveIcon
        callback={callback}
        label={`Button ${index + 1}`}
        id={eachBtnId}
      />
    ));

    return <RenderButtonList>{content}</RenderButtonList>;
  };

  return (
    <ErrorBoundary path={PATH}>
      <Container>
        <Heading>
          {label}
          {isRequired && !isViewMode && (
            <span style={{ color: '#ff0000', marginLeft: '4px' }}>*</span>
          )}
        </Heading>
        <ButtonListWrapper>
          {renderContent()}
          {!isHideAddBtn && !isViewMode && (
            <Button
              icon={<Icon type="icon-ants-plus-slim" size={12} />}
              style={{ marginBottom: '15px' }}
              onClick={handleAddNewButton}
            >
              {MAP_TRANSLATE.addBtn}
            </Button>
          )}
        </ButtonListWrapper>
      </Container>
    </ErrorBoundary>
  );
}

ActionButtons.propTypes = {
  label: PropTypes.string,
  initValue: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  isRequired: PropTypes.bool,
  limitTotal: PropTypes.number,
  design: PropTypes.string,
  isViewMode: PropTypes.bool,
  errors: PropTypes.array,
  onChange: PropTypes.func,
};
ActionButtons.defaultProps = {
  label: '',
  initValue: {},
  isRequired: false,
  limitTotal: 4,
  design: 'create',
  isViewMode: false,
  errors: [],
  onChange: () => {},
};
export default ActionButtons;
