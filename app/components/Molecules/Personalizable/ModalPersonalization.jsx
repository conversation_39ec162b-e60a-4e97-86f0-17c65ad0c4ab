/* eslint-disable no-prototype-builtins */
/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
// Libraries
import React, { useMemo, useRef, memo, useCallback } from 'react';
import PropTypes from 'prop-types';
import produce from 'immer';
import { useDispatch } from 'react-redux';
import { debounce } from 'lodash';

// Services
import JourneyServices from 'services/Journey';

// Actions
import { startPersonalizations } from '../../common/UIEditorPersonalization/WrapperPersonalization/libs/action';

// Translations
import { getTranslateMessage } from '../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../messages/constant';

// Components
import SelectTree from 'components/form/UISelectCondition';
import { ViewDetailsInformationIcon } from '@antscorp/antsomi-ui/es/components/icons';
import WrapperDisable from '../../common/WrapperDisable';
import ModalConfigFormat from '../../common/Modal/ModalConfigFormat';
import {
  Typography,
  Checkbox,
  Flex,
  Input,
  Tooltip,
} from '@antscorp/antsomi-ui';
import { Label, StyledAutoComplete, StyledModal, WrapperBody } from './styled';
import { UITippy, UIWrapperDisable } from '@xlab-team/ui-components';
import { FormHelperText } from '@material-ui/core';
import AceEditor from 'react-ace';
import { translate, translations } from '@antscorp/antsomi-locales';

// Utils
import { SET_OBJECT_WIDGET_AND_PRODUCT_TEMPLATE } from 'components/common/UIEditorPersonalization/utils';
import {
  DATA_ATTR_TYPE,
  DATA_ATTR_WITH_TYPE,
  checkErrorFunction,
  getPersonalizeCodeContentSource,
  toEntryAPICreate,
  toEntryAPIList,
} from 'components/common/UIEditorPersonalization/components/PopupPersonalization/utils';
import { getObjectPropSafely } from '../../../utils/common';
import { getClassNameColor } from '../../common/UIEditorPersonalization/utils.extends';
import { MAP_DATA_TYPE } from '../../../services/Abstract.data';
import { initDisplayFormat } from '../../common/Modal/ModalConfigFormat/utils';
import { serializeLabelToCodeAttr } from '../../../utils/web/utils';
import { addMessageToQueue } from '../../../utils/web/queue';
import { dtoCustomFunction } from '../../common/UIEditorPersonalization/WrapperPersonalization/libs/utils.dto';

// Ace editor
import 'ace-builds/webpack-resolver';
import 'ace-builds/src-noconflict/mode-javascript';
import 'ace-builds/src-noconflict/mode-css';
import 'ace-builds/src-noconflict/theme-tomorrow';
import 'ace-builds/src-noconflict/ext-language_tools';

// Constants
import { initCustomFunction } from './constants';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import { JOURNEY_CUSTOM_FN_CODE } from '../../common/UIEditorPersonalization/WrapperPersonalization/constants';

const MAP_TITLE = {
  boxTitle: getTranslateMessage(
    TRANSLATE_KEY._BOX_TITL_ADD_PERSONALIZE,
    'Add Personalization',
  ),
  personalizationType: translate(
    translations._TITL_PERSONALIZATION_TYPE,
    'Content Source',
  ),
  personalizationAttr: translate(
    translations._TITL_PERSONALIZE_ATTR,
    'Attribute',
  ),
  personalizationName: getTranslateMessage(TRANSLATE_KEY._, 'Name'),
  defaultValue: getTranslateMessage(
    TRANSLATE_KEY._TITL_PERSONALIZE_DEFAULT,
    'Default Value',
  ),
  personalizationCode: translate(
    translations._TITL_PERSONALIZATION_CODE,
    'Merge Code',
  ),
  personalizationTag: translate(
    translations._TITL_PERSONALIZATION_TAG,
    'Merge Tag',
  ),
  promotionPool: getTranslateMessage(
    TRANSLATE_KEY._TAB_PROMOTION_POOL,
    'Promotion pool',
  ),
  promotionCodeAttr: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Promotion Code Attribute',
  ),
  outPutDataType: getTranslateMessage(TRANSLATE_KEY._, 'Output Data Type'),
  displayFormat: translate(translations._RENDER_FORMAT, 'Display format'),
  outPutFormat: getTranslateMessage(TRANSLATE_KEY._, 'Output Format'),
  saveTemplate: getTranslateMessage(TRANSLATE_KEY._, 'Save as Template'),
  function: getTranslateMessage(TRANSLATE_KEY._, 'Function'),
};

const stylePopover = { position: 'absolute', top: 45, left: 50 };

const PATH =
  'app/components/Molecules/Personalizationable/ModalPersonalization.jsx';

const ModalPersonalization = props => {
  // Props
  const {
    design,
    dataState,
    disabled,
    open,
    onCancel,
    listTemplateCustomFn,
    customFunction,
    onAddTag,
    selectedProperties,
    onChangeSelectedProperties,
    onChangeCustomFunction,
  } = props;

  // Hooks
  const ref = useRef(null);
  const dispatch = useDispatch();

  const {
    personalizationType,
    personalizationData,
    promotionCodeAttr,
  } = dataState;

  const dispatchFetchingListCustomFn = useCallback(() => {
    dispatch(
      startPersonalizations({
        [JOURNEY_CUSTOM_FN_CODE]: {
          savedKey: JOURNEY_CUSTOM_FN_CODE,
          serviceFn: JourneyServices.personalization.getListCustom,
          dtoFn: dtoCustomFunction,
          payload: null,
          normalizeData: toEntryAPIList,
          isForceUpdateNew: true,
        },
      }),
    );
  }, [dispatch]);

  const handleInsertText = debounce(() => {
    let isValid = true;

    // validate Function
    if (ref && ref.current) {
      isValid = checkErrorFunction(ref.current.editor.session.getAnnotations());
      if (!isValid) {
        onChangeCustomFunction(draft => {
          draft.errorsFunction = 'Invalid Function';
        });
      }
    }

    // TH create new template
    if (isValid) {
      if (
        selectedProperties.personalType.value === 'custom' &&
        customFunction.saveAsTemplate
      ) {
        const params = {
          data: toEntryAPICreate(customFunction),
        };
        JourneyServices.personalization
          .create(params)
          .then(res => {
            if (res.code === 200 && res.data) {
              onAddTag({
                selectedProperties,
                personalizationCode,
                customFunction,
                design,
              });

              dispatchFetchingListCustomFn();
            } else if (
              res.code === 500 &&
              res.codeMessage === '_NOTIFICATION_NAMESAKE'
            ) {
              onChangeCustomFunction(draft => {
                draft.errors = getTranslateMessage(
                  res.codeMessage,
                  'This name already existed',
                );
              });
            }
          })
          .catch(err => {
            if (!err.isCanceled) {
              addMessageToQueue({
                path: PATH,
                func: 'handleInsertText',
                data: err.stack,
              });

              // eslint-disable-next-line no-console
              console.warn('err', err);
            }
          });
      }
      // TH update template
      else if (
        selectedProperties.personalType.value === 'custom' &&
        customFunction.templateId &&
        !customFunction.saveAsTemplate
      ) {
        const params = {
          data: toEntryAPICreate(customFunction),
          templateId: customFunction.templateId,
        };
        JourneyServices.personalization
          .update(params)
          .then(res => {
            if (res.code === 200 && res.data) {
              onAddTag({
                selectedProperties,
                personalizationCode,
                customFunction,
                design,
              });

              dispatchFetchingListCustomFn();
            } else if (
              res.code === 500 &&
              res.codeMessage === '_NOTIFICATION_NAMESAKE'
            ) {
              onChangeCustomFunction(draft => {
                draft.errors = getTranslateMessage(
                  res.codeMessage,
                  'This name already existed',
                );
              });
            }
          })
          .catch(err => {
            if (!err.isCanceled) {
              addMessageToQueue({
                path: PATH,
                func: 'handleInsertText',
                data: err.stack,
              });

              // eslint-disable-next-line no-console
              console.warn('err', err);
            }
          });
      } else {
        onAddTag({
          selectedProperties,
          personalizationCode,
          customFunction,
          design,
        });
      }
    }
  }, 300);

  const isShowContentSourceIndex = useMemo(() => {
    if (
      Array.isArray(selectedProperties.contentSources) &&
      selectedProperties.personalType
    ) {
      return (
        selectedProperties.contentSources.findIndex(
          cs => cs.groupId === selectedProperties.personalType.value,
        ) !== -1
      );
    }
    return false;
  }, [selectedProperties.contentSources, selectedProperties.personalType]);

  const { sourceAttribute, classNameTag = '' } = useMemo(() => {
    let result = [];

    result = getObjectPropSafely(() => {
      if (
        selectedProperties.personalType &&
        selectedProperties.personalType.value &&
        personalizationData &&
        personalizationData.hasOwnProperty(
          selectedProperties.personalType.value,
        )
      ) {
        const dataGroupAttrs =
          personalizationData[selectedProperties.personalType.value];
        return dataGroupAttrs && dataGroupAttrs.list;
      }
      return [];
    }, []);

    const className = getClassNameColor(selectedProperties);

    return { sourceAttribute: result, classNameTag: className };
  }, [selectedProperties.personalType, selectedProperties.status]);

  const personalizationCode = useMemo(() => {
    if (selectedProperties.personalType.value === 'promotion_code') {
      return `#{${getObjectPropSafely(
        () => selectedProperties.personalType.value,
      )}.${getObjectPropSafely(
        () => selectedProperties.attribute.bracketed_code,
      )}.${getObjectPropSafely(
        () => selectedProperties.promotionCodeAttr.value,
      )}}`;
    }
    if (
      selectedProperties.personalType.value === 'objectWidget' ||
      selectedProperties.personalType.value === 'productTemplate'
    ) {
      return `#{${getObjectPropSafely(
        () => selectedProperties.personalType.value,
      )}.${getObjectPropSafely(
        () => selectedProperties.attribute.bracketed_code,
      )}}`;
    }
    if (selectedProperties.personalType.value === 'custom') {
      return `#{${getObjectPropSafely(
        () => selectedProperties.personalType.value,
      )}.${serializeLabelToCodeAttr(customFunction.personalizationName)}}`;
    }

    if (
      Array.isArray(selectedProperties.contentSources) &&
      selectedProperties.contentSources.some(
        contentSource =>
          contentSource.groupId === selectedProperties.personalType.value,
      )
    ) {
      const tag = getPersonalizeCodeContentSource({
        personalizeType: getObjectPropSafely(
          () => selectedProperties.personalType.value,
        ),
        attributeBracket: getObjectPropSafely(
          () => selectedProperties.attribute.bracketed_code,
        ),
        csIndexAttribute: selectedProperties.csIndexAttribute,
      });

      return tag;
    }

    return `#{${getObjectPropSafely(
      () => selectedProperties.personalType.value,
    )}.${getObjectPropSafely(
      () => selectedProperties.attribute.bracketed_code,
    )}||"${getObjectPropSafely(() => selectedProperties.value)}"}`;
  }, [
    selectedProperties,
    customFunction.personalizationName,
    selectedProperties.csIndexAttribute,
  ]);

  const isValidateSelectedAttribute = useMemo(() => {
    return (
      selectedProperties.personalType.value !== 'promotion_code' ||
      (sourceAttribute || []).some(
        attr => selectedProperties?.attribute?.value === attr.value,
      )
    );
  }, [
    sourceAttribute,
    selectedProperties?.attribute,
    selectedProperties.personalType,
  ]);

  const onChangePersonalType = option => {
    try {
      if (option.value === 'custom') {
        onChangeSelectedProperties(
          produce(selectedProperties, draft => {
            draft.displayFormat.dataType = '';
            draft.displayFormat.format = {};
            draft.personalType = {
              value: option.value,
              label: option.label,
            };
          }),
        );
      } else {
        const foundItem = getObjectPropSafely(() => {
          if (
            option &&
            typeof option.value !== 'undefined' &&
            personalizationData &&
            personalizationData.hasOwnProperty(option.value)
          ) {
            return personalizationData[option.value].list;
          }

          return [];
        }, []);

        if (option && foundItem) {
          onChangeSelectedProperties(
            produce(selectedProperties, draft => {
              draft.personalType = {
                value: option.value,
                label: option.label,
              };

              if (foundItem && foundItem.length) {
                const tempAttrs = {
                  value: foundItem[0].value,
                  label: foundItem[0].label,
                  bracketed_code: foundItem[0].bracketed_code,
                  ...foundItem[0],
                };

                if (option.value === 'event') {
                  tempAttrs.name = foundItem[0].name;
                }

                draft.attribute = tempAttrs;
                draft.displayFormat.dataType = tempAttrs.itemDataType;
                draft.displayFormat.format = initDisplayFormat(
                  tempAttrs.itemDataType,
                );
                if (option.value === 'promotion_code') {
                  draft.displayFormat.dataType =
                    selectedProperties.promotionCodeAttr.dataType;
                  draft.displayFormat.format = initDisplayFormat(
                    selectedProperties.promotionCodeAttr.dataType,
                  );
                }
              } else {
                draft.attribute = {
                  value: '',
                  label: '',
                  bracketed_code: '',
                };
              }
            }),
          );
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'onChangePersonalType',
        data: error.stack,
      });

      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const onChangeAttribute = option => {
    try {
      if (option) {
        onChangeSelectedProperties(
          produce(selectedProperties, draft => {
            draft.attribute = option;
            draft.status = '';
            draft.statusMsg = '';
            if (selectedProperties.personalType.value !== 'promotion_code') {
              draft.displayFormat.dataType = option.itemDataType;
              draft.displayFormat.format = initDisplayFormat(
                option.itemDataType,
              );
            }
          }),
        );
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'onChangeAttribute',
        data: error.stack,
      });

      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const onChangePromotionCode = option => {
    try {
      if (option) {
        onChangeSelectedProperties(
          produce(selectedProperties, draft => {
            draft.promotionCodeAttr = option;
            draft.displayFormat.dataType = option.dataType;
            draft.displayFormat.format = initDisplayFormat(option.dataType);
          }),
        );
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'onChangeAttribute',
        data: error.stack,
      });

      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const onChangeInputValue = value => {
    try {
      onChangeSelectedProperties(
        produce(selectedProperties, draft => {
          draft.value = value;
        }),
      );
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'onChangeInputValue',
        data: error.stack,
      });

      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const setRawStyleEditorOutput = value => {
    onChangeCustomFunction(draft => {
      draft.formular = value;
      draft.errorsFunction = '';
    });
  };

  const onChangedataType = option => {
    onChangeCustomFunction(draft => {
      draft.dataType = option.value;
      draft.displayFormat = initDisplayFormat(option.value);
    });
  };
  const onChangeModalConfig = value => {
    onChangeCustomFunction(draft => {
      draft.displayFormat = value;
    });
  };

  const onChangeDisplayFormat = value => {
    try {
      onChangeSelectedProperties(
        produce(selectedProperties, draft => {
          draft.displayFormat.format = value;
        }),
      );
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'onChangeDisplayFormat',
        data: error.stack,
      });

      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const onChangeNamePersonal = value => {
    if (value?.length >= 255) {
      onChangeCustomFunction(draft => {
        draft.errors = getTranslateMessage(
          TRANSLATE_KEY._NAME_RULE_INVALID_MAX_LENGTH,
          'Invalid name! Name contains no more than 255 characters',
        );
      });
    } else {
      onChangeCustomFunction(draft => {
        draft.personalizationName = value;
        draft.errors = '';
      });
    }
  };

  const onChangeSelectName = (_event, value) => {
    if (value) {
      onChangeCustomFunction(draft => {
        draft.personalizationName = value.personalizationName;
        draft.displayFormat = value.displayFormat;
        draft.dataType = value.dataType;
        draft.formular = value.formular;
        draft.templateId = value.template_id;
        draft.templateCode = value.template_code;
        draft.errors = '';
      });
    }
  };

  const onSaveAsTemplate = () => {
    onChangeCustomFunction(draft => {
      draft.saveAsTemplate = !customFunction.saveAsTemplate;
      draft.errors = '';
    });
  };

  return (
    <div id="antsomi-popover-customer" style={stylePopover}>
      <StyledModal
        centered
        title={MAP_TITLE.boxTitle}
        open={open}
        width="650px"
        onCancel={onCancel}
        destroyOnClose
        okText={translate(translations._ACT_APPLY)}
        cancelText={translate(translations._ACT_CANCEL)}
        onOk={handleInsertText}
        closeIcon={false}
        okButtonProps={{
          disabled: !isValidateSelectedAttribute,
        }}
      >
        <WrapperBody className="antsomi-wrapper-personalization">
          <div className="body">
            <table>
              <tbody>
                <tr>
                  <td style={{ verticalAlign: 'top' }}>
                    <Flex vertical gap={8}>
                      <Label>{MAP_TITLE.personalizationTag}</Label>

                      <div style={{ wordBreak: 'break-all' }}>
                        {
                          <UITippy
                            content={getObjectPropSafely(() =>
                              selectedProperties.personalType.value === 'custom'
                                ? customFunction.personalizationName
                                : selectedProperties.attribute.label,
                            )}
                          >
                            <button
                              type="button"
                              className={`insert-word ${
                                selectedProperties?.personalType?.value ===
                                'custom'
                                  ? 'custom'
                                  : classNameTag
                              }`}
                              style={{
                                maxWidth: '100%',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                              }}
                            >
                              {getObjectPropSafely(() =>
                                selectedProperties.personalType.value ===
                                'custom'
                                  ? customFunction.personalizationName
                                  : selectedProperties.attribute.label,
                              )}
                            </button>
                          </UITippy>
                        }
                      </div>
                    </Flex>
                  </td>

                  <td colSpan={2} style={{ verticalAlign: 'top' }}>
                    <Flex vertical gap={8}>
                      <Label>{MAP_TITLE.personalizationCode}</Label>

                      <Typography.Text
                        style={{ wordBreak: 'break-all', lineHeight: '30px' }}
                        ellipsis={{ tooltip: true }}
                      >
                        {personalizationCode.replace(/&nbsp;/gm, ' ')}
                      </Typography.Text>
                    </Flex>
                  </td>
                </tr>

                <tr>
                  <td>
                    <Flex vertical gap={8}>
                      <Label>
                        {MAP_TITLE.personalizationType}

                        <span style={{ color: globalToken.colorError }}>
                          &nbsp;*
                        </span>
                      </Label>

                      <SelectTree
                        use="tree"
                        isSearchable
                        options={personalizationType.list}
                        value={selectedProperties.personalType}
                        onChange={onChangePersonalType}
                        placeholder={getTranslateMessage(
                          TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                          'Select an item',
                        )}
                        fullWidthPopover
                      />
                    </Flex>
                  </td>

                  <td
                    {...selectedProperties.personalType.value === 'custom' && {
                      colSpan: 2,
                    }}
                  >
                    <Flex vertical gap={8}>
                      <Label>
                        {selectedProperties.personalType.value ===
                        'promotion_code'
                          ? MAP_TITLE.promotionPool
                          : selectedProperties.personalType.value === 'custom'
                          ? MAP_TITLE.personalizationName
                          : MAP_TITLE.personalizationAttr}

                        <span style={{ color: globalToken.colorError }}>
                          &nbsp;*
                        </span>
                      </Label>

                      {selectedProperties.personalType.value === 'custom' ? (
                        <Flex vertical>
                          <StyledAutoComplete
                            showSearch
                            style={{ width: 400 }}
                            placeholder="Custom function name..."
                            value={customFunction.personalizationName}
                            options={listTemplateCustomFn.map(i => ({
                              ...i,
                              value: i.template_code,
                              label: i.template_name,
                            }))}
                            onSelect={onChangeSelectName}
                            onSearch={onChangeNamePersonal}
                          />

                          <FormHelperText
                            id="component-helper-text"
                            error={!!customFunction.errors}
                            style={{ width: '350px' }}
                          >
                            {customFunction.errors}
                          </FormHelperText>
                        </Flex>
                      ) : (
                        <SelectTree
                          use="tree"
                          // isMulti
                          isSearchable
                          options={sourceAttribute}
                          value={selectedProperties.attribute}
                          onChange={onChangeAttribute}
                          placeholder={getTranslateMessage(
                            TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                            'Select an item',
                          )}
                          fullWidthPopover
                          errors={
                            !isValidateSelectedAttribute
                              ? ['Invalid Selected']
                              : []
                          }
                        />
                      )}
                    </Flex>
                  </td>

                  {selectedProperties.personalType.value ===
                  'promotion_code' ? (
                    <td>
                      <Flex vertical gap={8}>
                        <Label>{MAP_TITLE.promotionCodeAttr}</Label>

                        <SelectTree
                          use="tree"
                          isSearchable
                          options={promotionCodeAttr.list}
                          value={selectedProperties.promotionCodeAttr}
                          onChange={onChangePromotionCode}
                          placeholder={getTranslateMessage(
                            TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                            'Select an item',
                          )}
                          fullWidthPopover
                        />
                      </Flex>
                    </td>
                  ) : !SET_OBJECT_WIDGET_AND_PRODUCT_TEMPLATE.has(
                      selectedProperties.personalType.value,
                    ) ? (
                    <td>
                      <Flex vertical gap={8}>
                        <Flex align="center" gap={4}>
                          <Label>{MAP_TITLE.defaultValue}</Label>

                          <Tooltip title="Value to be displayed if there's no dynamic content">
                            <ViewDetailsInformationIcon size={14} />
                          </Tooltip>
                        </Flex>

                        <UIWrapperDisable disabled={isShowContentSourceIndex}>
                          <Input
                            className="input-outline"
                            placeholder={translate(
                              translations._USER_GUIDE_ENTER_VALUE,
                              'Enter value',
                            )}
                            value={getObjectPropSafely(() =>
                              selectedProperties.value.replace(/&nbsp;/gm, ' '),
                            )}
                            onChange={event =>
                              onChangeInputValue(event.target.value)
                            }
                          />
                        </UIWrapperDisable>
                      </Flex>
                    </td>
                  ) : null}
                </tr>

                {isShowContentSourceIndex && (
                  <tr>
                    <td>
                      <Label>
                        Index
                        <span style={{ color: globalToken.colorError }}>
                          &nbsp;*
                        </span>
                      </Label>
                    </td>
                    <td>
                      <SelectTree
                        use="tree"
                        isSearchable
                        options={Array.from({ length: 90 }).map((_, index) => ({
                          label: index + 1,
                          value: index + 1,
                        }))}
                        value={selectedProperties.csIndexAttribute}
                        onChange={valueOut => {
                          onChangeSelectedProperties(
                            produce(selectedProperties, draft => {
                              // eslint-disable-next-line no-param-reassign
                              draft.csIndexAttribute = valueOut;
                            }),
                          );
                        }}
                        placeholder={getTranslateMessage(
                          TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                          'Select an item',
                        )}
                        fullWidthPopover
                      />
                    </td>
                  </tr>
                )}

                {selectedProperties.personalType.value === 'custom' ? (
                  <>
                    <tr>
                      <td />

                      <td>
                        <Flex
                          vertical
                          gap={8}
                          style={{ width: 400, margin: '5px 0px 5px 0px' }}
                        >
                          <Label>
                            {MAP_TITLE.function}
                            <span style={{ color: globalToken.colorError }}>
                              &nbsp;*
                            </span>
                          </Label>

                          <WrapperDisable disabled={disabled}>
                            <AceEditor
                              ref={ref}
                              setOptions={{
                                enableBasicAutocompletion: true,
                                enableLiveAutocompletion: true,
                                enableSnippets: true,
                                showLineNumbers: true,
                                tabSize: 2,
                              }}
                              mode="javascript"
                              fontSize={14}
                              showPrintMargin
                              showGutter
                              highlightActiveLine
                              value={customFunction.formular}
                              wrapEnabled
                              onChange={(value, event, tmp) =>
                                setRawStyleEditorOutput(value, event, tmp)
                              }
                              style={{ width: '100%', height: '200px' }}
                            />
                            <FormHelperText
                              id="component-helper-text"
                              error={!!customFunction.errorsFunction}
                              style={{ width: '350px' }}
                            >
                              {customFunction.errorsFunction}
                            </FormHelperText>
                          </WrapperDisable>
                        </Flex>
                      </td>
                    </tr>
                    <tr>
                      <td />

                      <td>
                        <Flex vertical gap={8}>
                          <Label>
                            {MAP_TITLE.outPutDataType}

                            <span style={{ color: globalToken.colorError }}>
                              &nbsp;*
                            </span>
                          </Label>

                          <SelectTree
                            onlyParent
                            use="tree"
                            placeholder={getTranslateMessage(
                              TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                              'Select an item',
                            )}
                            fullWidthPopover
                            mapOptions={MAP_DATA_TYPE}
                            options={DATA_ATTR_TYPE}
                            value={MAP_DATA_TYPE[customFunction.dataType]}
                            // disabled={state.displayAs.disabled}
                            onChange={onChangedataType}
                          />
                        </Flex>
                      </td>
                    </tr>

                    <tr>
                      <td />
                      <td colSpan={2}>
                        <Flex vertical gap={8} style={{ width: 400 }}>
                          <Label>
                            {MAP_TITLE.displayFormat}

                            <span style={{ color: globalToken.colorError }}>
                              &nbsp;*
                            </span>
                          </Label>

                          <ModalConfigFormat
                            usePortal={[]}
                            dataType={customFunction.dataType}
                            onChange={onChangeModalConfig}
                            mode="edit"
                            initData={customFunction.displayFormat}
                          />
                        </Flex>
                      </td>
                    </tr>
                  </>
                ) : (
                  <></>
                )}

                {DATA_ATTR_WITH_TYPE.includes(
                  selectedProperties?.displayFormat?.dataType,
                ) ? (
                  <tr>
                    <td style={{ verticalAlign: 'top' }}>
                      <Label style={{ lineHeight: '30px' }}>
                        {MAP_TITLE.displayFormat}
                        <span style={{ color: globalToken.colorError }}>
                          &nbsp;*
                        </span>
                      </Label>
                    </td>

                    <td colSpan={2}>
                      <div style={{ wordBreak: 'break-all' }}>
                        <ModalConfigFormat
                          usePortal={[]}
                          dataType={selectedProperties?.displayFormat?.dataType}
                          onChange={onChangeDisplayFormat}
                          mode="edit"
                          initData={selectedProperties?.displayFormat?.format}
                        />
                      </div>
                    </td>
                  </tr>
                ) : (
                  <></>
                )}

                {selectedProperties?.personalType?.value === 'custom' ? (
                  <tr>
                    <td />
                    <td colSpan={2}>
                      <div
                        style={{
                          wordBreak: 'break-all',
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <Checkbox
                          checked={customFunction.saveAsTemplate}
                          onChange={onSaveAsTemplate}
                        >
                          {MAP_TITLE.saveTemplate}
                        </Checkbox>
                      </div>
                    </td>
                  </tr>
                ) : (
                  <></>
                )}
              </tbody>
            </table>
          </div>
        </WrapperBody>
      </StyledModal>
    </div>
  );
};

ModalPersonalization.propTypes = {
  open: PropTypes.bool,
  design: PropTypes.string,
  dataState: PropTypes.object,
  disabled: PropTypes.bool,
  listTemplateCustomFn: PropTypes.array,
  customFunction: PropTypes.object,
  selectedProperties: PropTypes.object,
  onCancel: PropTypes.func,
  onChangeSelectedProperties: PropTypes.func,
  onChangeCustomFunction: PropTypes.func,
  onAddTag: PropTypes.func,
};
ModalPersonalization.defaultProps = {
  open: false,
  disabled: false,
  design: 'create',
  listTemplateCustomFn: [],
  selectedProperties: {},
  dataState: {
    personalizationType: {},
    personalizationData: {},
    promotionCodeAttr: {},
  },
  customFunction: initCustomFunction,
  onChangeSelectedProperties: () => {},
  onChangeCustomFunction: () => {},
  onCancel: () => {},
  onAddTag: () => {},
};
export default memo(ModalPersonalization);
