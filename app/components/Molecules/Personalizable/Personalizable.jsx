/* eslint-disable import/no-cycle */
/* eslint-disable no-param-reassign */
// Libraries
import React, {
  forwardRef,
  memo,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import PropTypes from 'prop-types';
import { original } from 'immer';
import _, { isEmpty, isFunction } from 'lodash';
import { useImmer } from 'use-immer';
import { translate, translations } from '@antscorp/antsomi-locales';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import ModalPersonalization from './ModalPersonalization';
import { ModalShortlink } from '../ModalShortlink';
import {
  Button,
  Dropdown,
  SHORT_LINK_V2,
  SHORT_LINK_TYPE,
  TAG_TYPE,
  useDeepCompareEffect,
  useDeepCompareMemo,
} from '@antscorp/antsomi-ui';
import { PersonUserAccountIcon } from '@antscorp/antsomi-ui/es/components/icons';

// Constants
import { initCustomFunction } from './constants';

// Utils
import {
  ADD_DYNAMIC_OPTION,
  genShortLinkValue,
  getAddDynamicContentOptions,
  mapDataCustomFucntion,
  mapDataFormatAttribute,
} from '../../common/UIEditorPersonalization/Input/utils';
import {
  DATA_ATTR_WITH_TYPE,
  getInitialSelected,
} from '../../common/UIEditorPersonalization/components/PopupPersonalization/utils';
import {
  STATUS_CREATE,
  STATUS_UPDATE,
  regexAttrFormat,
} from 'components/common/UIEditorPersonalization/utils';
import { addMessageToQueue } from '../../../utils/web/queue';
import { safeParse } from '../../../utils/common';
import { detectContentEdit } from '../../common/UIEditorPersonalization/utils.extends';

const PATH = 'app/components/Molecules/Personalizable/Personalizable.jsx';

const { INDIVIDUAL } = SHORT_LINK_TYPE;

const Personalizable = forwardRef(function Personalization(props, ref) {
  // Props
  const {
    disabled,
    dataState,
    enableShortLink,
    hiddenDynamicOption,
    showShortLinkWithBroadcast,
    onlyShowGeneralShortlink,
    isForceHideBtnPersonalization,
    isViewMode,
    mapAttributes,
    listTemplateCustom,
    variantExtraData,
    onAddNewTag,
    onUpdateTag,
  } = props;

  // Refs
  const shortlinkRef = useRef(null);

  // States
  const [shortlinkState, setShortlinkState] = useImmer({
    isOpen: false,
  });

  const [personalizeState, setPersonalizeState] = useImmer({
    isOpen: false,
    design: STATUS_CREATE,
    selectedProperties: getInitialSelected(dataState),
    formatAttributes: variantExtraData?.formatAttributes || {},
    extraData: {
      customFnAttribute: variantExtraData?.customFunction || {},
    },
    cacheTag: null,
  });

  const [customFunctionState, setCustomFunctionState] = useImmer(
    initCustomFunction,
  );

  // Exposed functions
  useImperativeHandle(
    ref,
    () => ({
      getFormatAttributes: (data = personalizeState) => {
        return data.formatAttributes;
      },
      getExtraData: (data = personalizeState) => {
        return data.extraData;
      },
      onUpdateShortlink: (
        url = '',
        shortlinkType = SHORT_LINK_TYPE.INDIVIDUAL,
        tagElement,
        shortener,
      ) => {
        if (shortlinkRef.current && tagElement) {
          setShortlinkState(draft => {
            draft.isOpen = true;
          });

          shortlinkRef.current.onUpdateShortlink(
            url,
            shortlinkType,
            tagElement,
            shortener,
          );
        }
      },
      onUpdatePersonalizationTag: (tagElement, tagData) => {
        const {
          value: tagValue,
          type: tagType,
          status = '',
          statusMsg = '',
          label,
          attributeName = '',
        } = tagData;

        try {
          if (tagType === TAG_TYPE.CUSTOM_FN) {
            const found = tagValue.replace(/#|{|}|/g, '').split('.');

            setPersonalizeState(draft => {
              const originalPersonalize = original(draft);

              const newCustomFn = _.cloneDeep(
                originalPersonalize.extraData.customFnAttribute[found[1]],
              );

              draft.selectedProperties.personalType = {
                value: 'custom',
                label: 'Custom',
              };
              draft.selectedProperties.customFunction = newCustomFn;

              if (newCustomFn) {
                setCustomFunctionState(draftCustomFn => {
                  Object.keys(newCustomFn).forEach(key => {
                    draftCustomFn[key] = newCustomFn[key];
                  });
                });
              }
            });
          } else {
            setPersonalizeState(draft => {
              const originalPersonalize = original(draft);

              const detectContent = safeParse(
                detectContentEdit(
                  tagValue,
                  dataState.personalizationType,
                  dataState.personalizationData,
                  dataState.promotionCodeAttr,
                  { formatAttributes: originalPersonalize.formatAttributes },
                ),
                {},
              );

              if (!isEmpty(detectContent)) {
                const {
                  personalType = { value: tagType },
                  attributeType = {
                    label,
                    bracketed_code: label,
                    value: attributeName || label,
                  },
                  valueString = '',
                  attributePromotionCode = {},
                  displayFormat = {
                    dataType: '',
                    format: {},
                  },
                  csIndexAttribute = {
                    label: 1,
                    value: 1,
                  },
                } = detectContent;

                draft.selectedProperties = {
                  ...(originalPersonalize.selectedProperties || {}),
                  personalType,
                  attribute: attributeType,
                  value: valueString,
                  promotionCodeAttr: attributePromotionCode,
                  displayFormat,
                  contentSources: dataState.contentSources || [],
                  csIndexAttribute,
                  status,
                  statusMsg,
                };
              }
            });
          }
        } catch (error) {
          addMessageToQueue({
            path: PATH,
            function: 'onUpdatePersonalizationTag',
            data: { tagElement, tagData },
          });
        }

        setPersonalizeState(draft => {
          draft.isOpen = true;
          draft.design = STATUS_UPDATE;
          draft.cacheTag = tagElement;
        });
      },
    }),
    [
      personalizeState.formatAttributes,
      personalizeState.extraData,
      dataState.personalizationType,
      dataState.personalizationData,
      dataState.promotionCodeAttr,
      dataState.contentSources,
    ],
  );

  // Memoizations
  const hideOptionShortlinkType = useMemo(() => {
    // Hide INDIVIDUAL if showShortLinkWithBroadcast or onlyShowGeneralShortlink enabled
    // => not allow dynamic url
    if (
      (showShortLinkWithBroadcast && isForceHideBtnPersonalization) ||
      onlyShowGeneralShortlink
    ) {
      return INDIVIDUAL;
    }

    return null;
  }, [
    showShortLinkWithBroadcast,
    isForceHideBtnPersonalization,
    onlyShowGeneralShortlink,
  ]);

  const addDynamicOptions = useMemo(
    () =>
      getAddDynamicContentOptions({
        enableShortLink,
      }).filter(option => !hiddenDynamicOption.includes(option.value)),
    [enableShortLink, hiddenDynamicOption],
  );

  const initialSelectedProperties = useDeepCompareMemo(
    () => getInitialSelected(dataState),
    [dataState],
  );

  const listTemplateCustomFn = useMemo(() => listTemplateCustom?.list || [], [
    listTemplateCustom,
  ]);

  const personalizationProps = useMemo(
    () => ({
      listTemplateCustom,
      mapAttributes,
      otherData: {
        variantExtraData,
      },
    }),
    [listTemplateCustom, mapAttributes, variantExtraData],
  );

  const toggleModalShortlink = useCallback(() => {
    setShortlinkState(draft => {
      draft.isOpen = !draft.isOpen;
    });
  }, []);

  const onCloseModalShortlink = useCallback(() => {
    setShortlinkState(draft => {
      draft.isOpen = false;
    });
  }, []);

  const toggleModalPersonalize = useCallback(() => {
    setPersonalizeState(draft => {
      draft.isOpen = !draft.isOpen;
    });
  }, []);

  const onCloseModalPersonalize = useCallback(() => {
    setPersonalizeState(draft => {
      draft.isOpen = false;
    });
  }, []);

  const resetPersonalizable = useCallback(() => {
    setCustomFunctionState(() => initCustomFunction);
    setPersonalizeState(draft => {
      draft.selectedProperties = initialSelectedProperties;
      draft.design = STATUS_CREATE;
    });
  }, [initialSelectedProperties]);

  const handleOpenAddDynamicOption = useCallback(
    option => {
      if (option.value === ADD_DYNAMIC_OPTION.addPer.value) {
        resetPersonalizable();
        toggleModalPersonalize();
      }

      if (option.value === ADD_DYNAMIC_OPTION.shortLink.value) {
        toggleModalShortlink();
      }
    },
    [toggleModalShortlink, toggleModalPersonalize, resetPersonalizable],
  );

  const handleOkShortlink = useCallback(
    shortlinkInfo => {
      const {
        url = '',
        shortlinkType = '',
        design = 'create',
        cacheTag = null,
        shortener = '',
        dynamicSettings,
      } = shortlinkInfo;

      const type =
        shortlinkType === SHORT_LINK_TYPE.INDIVIDUAL ? 'individual' : 'general';

      const shortLink = genShortLinkValue(url, type, shortener);

      const newTag = {
        label: shortLink.label,
        value: shortLink.value,
        type: SHORT_LINK_V2,
        shortlinkType,
        shortener,
        url,
      };

      if (design === 'create' && isFunction(onAddNewTag)) {
        onAddNewTag(newTag);
      }

      if (design === 'update' && isFunction(onUpdateTag) && cacheTag) {
        onUpdateTag(cacheTag, newTag);
      }

      if (!isEmpty(dynamicSettings)) {
        const { customFunction, formatAttributes } = dynamicSettings;

        setPersonalizeState(draft => {
          draft.formatAttributes = {
            ...personalizeState.formatAttributes,
            ...formatAttributes,
          };

          draft.extraData.customFnAttribute = customFunction;
        });
      }
    },
    [onAddNewTag, onUpdateTag],
  );

  const handleChangeSelectedProperties = useCallback(
    (newSelectedProperties = {}) => {
      setPersonalizeState(draft => {
        draft.selectedProperties = newSelectedProperties;
      });
    },
    [],
  );

  const handleAddPersonalizeTag = useCallback(
    ({
      selectedProperties = {},
      personalizationCode = '',
      customFunction = {},
      design = 'create',
    } = {}) => {
      const isCreate = design === STATUS_CREATE;
      const isUpdate = design === STATUS_UPDATE;

      try {
        if (
          typeof onAddNewTag !== 'function' ||
          typeof onUpdateTag !== 'function'
        )
          return;

        let tagType = _.get(selectedProperties, 'personalType.value', '');
        const contentSources = _.get(selectedProperties, 'contentSources', []);
        // Re-assign tagType if it's content source
        if (contentSources && contentSources.length > 0) {
          const csIdx = contentSources.findIndex(cs => cs.groupId === tagType);

          if (csIdx !== -1) {
            tagType = TAG_TYPE.CONTENT_SOURCE_GROUP;
          }
        }

        const tagDataType = _.get(
          selectedProperties,
          'displayFormat.dataType',
          '',
        );

        switch (tagType) {
          case TAG_TYPE.CUSTOM_FN: {
            const tagLabel = _.get(customFunction, 'personalizationName', '');
            const newTag = {
              label: tagLabel,
              value: personalizationCode,
              type: tagType,
            };

            const found = personalizationCode.replace(/#|{|}|/g, '').split('.');
            setPersonalizeState(draft => {
              draft.extraData.customFnAttribute[found[1]] = customFunction;

              if (isCreate) {
                onAddNewTag(newTag);
              }

              if (isUpdate) {
                onUpdateTag(draft.cacheTag, newTag);
              }
            });
            break;
          }
          case TAG_TYPE.VISITOR:
          case TAG_TYPE.CUSTOMER:
          case TAG_TYPE.EVENT:
          case TAG_TYPE.PROMOTION_CODE:
          case TAG_TYPE.CONTENT_SOURCE_GROUP:
          case TAG_TYPE.JOURNEY:
          case TAG_TYPE.CAMPAIGN:
          case TAG_TYPE.VARIANT:
          case TAG_TYPE.OBJECT_WIDGET: {
            const tagLabel = _.get(selectedProperties, 'attribute.label', '');
            const newTag = {
              label: tagLabel,
              value: personalizationCode,
              type: tagType,
            };

            setPersonalizeState(draft => {
              if (tagDataType && DATA_ATTR_WITH_TYPE.includes(tagDataType)) {
                const key = personalizationCode.replace(regexAttrFormat, '');
                const newFormatAttributeItem = mapDataFormatAttribute(
                  selectedProperties.displayFormat,
                );

                draft.formatAttributes[key] = newFormatAttributeItem;
              }

              if (isCreate) {
                onAddNewTag(newTag);
              }

              if (isUpdate) {
                onUpdateTag(draft.cacheTag, newTag);
              }
            });
            break;
          }
          default: {
            break;
          }
        }
      } catch (error) {
        addMessageToQueue({
          path: PATH,
          function: 'handleAddPersonalizeTag',
          data: {
            selectedProperties,
            personalizationCode,
            customFunction,
          },
        });
      }

      setPersonalizeState(draft => {
        draft.isOpen = false;
        draft.cacheTag = isUpdate ? draft.cacheTag : null;
      });
      resetPersonalizable();
    },
    [onAddNewTag, onUpdateTag, resetPersonalizable],
  );

  useDeepCompareEffect(() => {
    if (
      variantExtraData?.formatAttributes &&
      Object.keys(variantExtraData?.formatAttributes || {}).length > 0
    ) {
      setPersonalizeState(draft => {
        draft.formatAttributes = variantExtraData.formatAttributes;
      });
    }
  }, [variantExtraData?.formatAttributes]);

  useDeepCompareEffect(() => {
    if (
      variantExtraData?.customFunction &&
      Object.keys(variantExtraData?.customFunction || {}).length > 0
    ) {
      const objTmp = mapDataCustomFucntion(variantExtraData?.customFunction);
      setPersonalizeState(draft => {
        draft.extraData.customFnAttribute = { ...objTmp };
      });
    }
  }, [variantExtraData?.customFunction]);

  const renderPersonalize = useCallback(() => {
    if (isForceHideBtnPersonalization) {
      if (showShortLinkWithBroadcast) {
        return (
          <Button
            type="link"
            icon={<PersonUserAccountIcon />}
            disabled={isViewMode || disabled}
            onClick={() =>
              handleOpenAddDynamicOption(ADD_DYNAMIC_OPTION.shortLink)
            }
          />
        );
      }

      return null;
    }

    // Only one option
    if (addDynamicOptions.length === 1) {
      return (
        <Button
          type="link"
          icon={<PersonUserAccountIcon />}
          disabled={isViewMode || disabled}
          onClick={() => handleOpenAddDynamicOption(addDynamicOptions[0])}
        />
      );
    }

    // Multiple options
    const items = addDynamicOptions.map(option => ({
      key: option.value,
      label:
        translate(translations?.[option.labelTranslateCode], option.label) ||
        '--',
      onClick: () => handleOpenAddDynamicOption(option),
    }));

    return (
      <Dropdown
        trigger={['click']}
        menu={{ items }}
        placement="bottomLeft"
        arrow
        disabled={isViewMode || disabled}
      >
        <Button type="link" icon={<PersonUserAccountIcon />} />
      </Dropdown>
    );
  }, [
    disabled,
    addDynamicOptions,
    isViewMode,
    showShortLinkWithBroadcast,
    isForceHideBtnPersonalization,
    handleOpenAddDynamicOption,
  ]);

  return (
    <ErrorBoundary path={PATH}>
      {renderPersonalize()}

      <ModalPersonalization
        open={personalizeState.isOpen}
        design={personalizeState.design}
        customFunction={customFunctionState}
        listTemplateCustomFn={listTemplateCustomFn}
        dataState={dataState}
        selectedProperties={personalizeState.selectedProperties}
        onChangeSelectedProperties={handleChangeSelectedProperties}
        onChangeCustomFunction={setCustomFunctionState}
        onCancel={onCloseModalPersonalize}
        onAddTag={handleAddPersonalizeTag}
      />

      <ModalShortlink
        ref={shortlinkRef}
        open={shortlinkState.isOpen}
        onClose={onCloseModalShortlink}
        onOk={handleOkShortlink}
        hideOption={hideOptionShortlinkType}
        personalizationProps={personalizationProps}
        dataState={dataState}
      />
    </ErrorBoundary>
  );
});

Personalizable.propTypes = {
  disabled: PropTypes.bool,
  dataState: PropTypes.object,
  enableShortLink: PropTypes.bool,
  isViewMode: PropTypes.bool,
  hiddenDynamicOption: PropTypes.array,
  showShortLinkWithBroadcast: PropTypes.bool,
  onlyShowGeneralShortlink: PropTypes.bool,
  isForceHideBtnPersonalization: PropTypes.bool,
  listTemplateCustom: PropTypes.object,
  variantExtraData: PropTypes.object,
  onAddNewTag: PropTypes.func,
  onUpdateTag: PropTypes.func,
  mapAttributes: PropTypes.object,
};

Personalizable.defaultProps = {
  disabled: false,
  dataState: {
    personalizationType: { list: [], map: {} },
    promotionCodeAttr: { list: [], map: {} },
    personalizationData: {},
  },
  isViewMode: false,
  enableShortLink: true,
  hiddenDynamicOption: [],
  showShortLinkWithBroadcast: false,
  onlyShowGeneralShortlink: false,
  isForceHideBtnPersonalization: false,
  listTemplateCustom: { list: [], map: {} },
  variantExtraData: {
    formatAttributes: {},
    customFunction: {},
  },
  onAddNewTag: () => {},
  onUpdateTag: () => {},
  mapAttributes: {},
};

export default memo(Personalizable);
