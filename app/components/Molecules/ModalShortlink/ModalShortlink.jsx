/* eslint-disable no-param-reassign */
/* eslint-disable import/no-cycle */
/* eslint-disable react/prop-types */
// Libraries
import React, {
  forwardRef,
  memo,
  Suspense,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import PropTypes from 'prop-types';
import { useImmer } from 'use-immer';
import { get } from 'lodash';
import { useDebounce, useDeepCompareEffect } from '../../../hooks';

// Translations
import { translate, translations } from '@antscorp/antsomi-locales';

// Components
import { FormHelperText, Grid } from '@material-ui/core';
import RadioGroup from '../RadioGroup';
import {
  Flex,
  ModalV2,
  SHORT_LINK_TYPE,
  SHORT_LINK_TYPE_V2,
  Spin,
  Typography,
} from '@antscorp/antsomi-ui';
import { BodyWrapper, StyledTextArea } from './styled';
import SelectTree from 'components/form/UISelectCondition';
import { UILoading } from '@xlab-team/ui-components';

// Utils
import WrapperPersonalization from '../../common/UIEditorPersonalization/WrapperPersonalization';
import {
  combineVariantExtraData,
  hasPersonalizationTags,
  initialState,
  validateURL,
} from './utils';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

// Constants
import { SHORT_LINK_V2 } from '../../common/UIEditorPersonalization/WrapperPersonalization/constants';

const TRANSLATE_MAP = {
  addShortLink: translate(
    translations._BOX_TITL_ADD_SHORTLINK,
    'Add Shortlink',
  ),
  type: translate(translations._ADD_SHORTLINK_FIELD_LABEL_1, 'Type'),
  shortener: translate(translations._ADD_SHORTLINK_FIELD_1, 'Shortener'),
  originalURL: translate(
    translations._ADD_SHORTLINK_FIELD_LABEL_2,
    'Original URL',
  ),
  cancel: translate(translations._ACT_CANCEL, 'Cancel'),
  insert: translate(translations._ACT_INSERT, 'Insert'),
};

const { INDIVIDUAL, GENERAL } = SHORT_LINK_TYPE;

const typeLabels = {
  [INDIVIDUAL]: translate(
    translations._ADD_SHORTLINK_FIELD_TYPE_1,
    'Individual',
  ),
  [GENERAL]: translate(translations._ADD_SHORTLINK_FIELD_TYPE_2, 'General'),
};

const ModalShortlink = forwardRef(function Modal(
  {
    hideOption,
    open = false,
    onClose = () => {},
    onOk = () => {},
    personalizationProps = {},
    dataState,
  },
  ref,
) {
  const [state, setState] = useImmer(initialState({ hideOption }));
  const [isDisableSubmit, setIsDisableSubmit] = useState(true);
  const urlDebounced = useDebounce(state.url, 500);

  const { personalizationData } = dataState;
  const personalizationDataShortener = get(
    personalizationData,
    SHORT_LINK_V2,
    {},
  );

  const [dynamicSettings, setDynamicSettings] = useState({
    customFunction: {},
    formatAttributes: {},
  });

  const otherDataMemoized = useMemo(
    () => ({
      ...(personalizationProps?.otherData || {}),
      variantExtraData: combineVariantExtraData({
        otherData: personalizationProps?.otherData,
        dynamicSettings,
      }),
    }),
    [personalizationProps?.otherData, dynamicSettings],
  );

  useImperativeHandle(
    ref,
    () => ({
      onUpdateShortlink: (
        newUrl = '',
        shortlinkType = SHORT_LINK_TYPE.INDIVIDUAL,
        tagElement,
        shortener,
      ) => {
        setState(draft => {
          draft.isLoading = false;
          draft.url = newUrl?.trim();
          draft.shortlinkType = shortlinkType;
          draft.design = 'update';
          draft.cacheTag = tagElement;
          draft.shortener = personalizationDataShortener?.map?.[shortener] || {
            value: shortener,
            label: shortener,
          };
        });
      },
    }),
    [JSON.stringify(personalizationDataShortener)],
  );

  useDeepCompareEffect(() => {
    if (!state.shortener && personalizationDataShortener?.list?.length) {
      setState(draft => {
        draft.shortener = personalizationDataShortener?.list?.[0];
      });
    }
  }, [JSON.stringify(personalizationDataShortener), open]);

  useEffect(() => {
    if (open) {
      setState(draft => {
        draft.isLoading = false;
      });
    }
  }, [open]);

  useEffect(() => {
    if (hideOption) {
      setState(draft => {
        if (hideOption === draft.shortlinkType) {
          const isIndividual = hideOption === INDIVIDUAL;

          draft.shortlinkType = isIndividual ? GENERAL : INDIVIDUAL;
        }
      });
    }
  }, [hideOption]);

  useEffect(() => {
    let isDisabled = false;
    let isValidURL = false;
    if (urlDebounced) {
      if (
        state.shortlinkType === SHORT_LINK_TYPE.INDIVIDUAL ||
        state.shortlinkType === SHORT_LINK_TYPE_V2.INDIVIDUAL
      ) {
        isValidURL = hasPersonalizationTags(urlDebounced)
          ? true
          : validateURL(urlDebounced);
      }
      if (
        state.shortlinkType === SHORT_LINK_TYPE.GENERAL ||
        state.shortlinkType === SHORT_LINK_TYPE_V2.GENERAL
      ) {
        isValidURL = validateURL(urlDebounced);
      }

      setState(draft => {
        draft.error = !isValidURL ? 'Invalid URL' : '';
      });
    }

    if (
      !isValidURL ||
      !urlDebounced ||
      !state.shortener ||
      !personalizationDataShortener?.map?.[state.shortener?.value]
    ) {
      isDisabled = true;
    }

    setIsDisableSubmit(isDisabled);
  }, [
    state.shortlinkType,
    JSON.stringify(personalizationDataShortener),
    urlDebounced,
    JSON.stringify(state.shortener),
  ]);

  const handleClose = useCallback(() => {
    setState(() => initialState({ hideOption }));

    if (onClose) onClose();
  }, [hideOption, onClose, setState]);

  const handleOnChange = value => {
    const urlString = value.replace(/(\r\n|\n|\r)/gm, '');

    if (state.error) {
      handleValidate({ shortlinkType: state.shortlinkType, urlString });
    }

    setState(draft => {
      draft.url = urlString.trim();
    });
  };

  const handleValidate = ({
    shortlinkType = state.shortlinkType,
    urlString = state.url,
    afterValidate,
  }) => {
    let isValid = false;
    if (
      shortlinkType === SHORT_LINK_TYPE.INDIVIDUAL ||
      shortlinkType === SHORT_LINK_TYPE_V2.INDIVIDUAL
    ) {
      isValid = hasPersonalizationTags(urlString)
        ? true
        : validateURL(urlString);
    }

    if (
      shortlinkType === SHORT_LINK_TYPE.GENERAL ||
      shortlinkType === SHORT_LINK_TYPE_V2.GENERAL
    ) {
      isValid = validateURL(urlString);
    }

    setState(draft => {
      draft.error = !isValid ? 'Invalid URL' : '';
    });

    if (afterValidate) afterValidate(isValid);
  };

  const handleOnOK = () => {
    setTimeout(() => {
      handleValidate({
        shortlinkType: state.shortlinkType,
        urlString: state.url,
        afterValidate: isValid => {
          if (isValid && onOk) {
            onOk({
              ...state,
              url: state.url,
              shortener: state.shortener?.value,
              dynamicSettings,
            });

            handleClose();
          }
        },
      });
    }, 500);
  };

  const onChangeType = event => {
    setState(draft => {
      draft.shortlinkType = event.target.value;
    });
  };

  const handleChangeDynamicSettings = (_, info = {}) => {
    const { customFunction = {}, formatAttributes = {} } = info;

    setDynamicSettings({
      customFunction: customFunction.map || {},
      formatAttributes: formatAttributes.formatAttributes || {},
    });
  };

  const handleChangeShortener = shortener => {
    setState(draft => {
      draft.shortener = shortener;
    });
  };

  const renderType = () => {
    const isHideOpt = [INDIVIDUAL, GENERAL].includes(hideOption);

    if (isHideOpt) {
      const isUpdate = state.design === 'update';
      if (isUpdate) {
        return (
          <Typography.Text>{typeLabels[state.shortlinkType]}</Typography.Text>
        );
      }

      const isIndividual = hideOption === INDIVIDUAL;
      return (
        <Typography.Text>
          {typeLabels[isIndividual ? GENERAL : INDIVIDUAL]}
        </Typography.Text>
      );
    }

    return (
      <RadioGroup
        value={state.shortlinkType}
        onChange={onChangeType}
        name="shortlink-type"
        styleGroup={{ flexDirection: 'row' }}
        options={[
          {
            value: INDIVIDUAL,
            label: typeLabels[INDIVIDUAL],
          },
          {
            value: GENERAL,
            label: typeLabels[GENERAL],
          },
        ]}
      />
    );
  };

  const renderShortener = () => {
    return (
      <Grid container alignItems="center">
        <Grid item xs={3}>
          <span style={{ fontSize: 12, color: '#000' }}>
            {TRANSLATE_MAP.shortener}
          </span>
        </Grid>
        <Grid item xs={9}>
          <SelectTree
            use="tree"
            isSearchable
            options={personalizationDataShortener?.list || []}
            value={state.shortener}
            onChange={handleChangeShortener}
            // placeholder={translate('', 'Select a shortener')}
            placeholderTranslateCode={translations._USER_GUIDE_SELECT_ITEM}
            fullWidthPopover
            errors={
              state?.shortener?.value &&
              !personalizationDataShortener?.map?.[state.shortener?.value]
                ? ['This shortener is inactivated or removed']
                : []
            }
          />
        </Grid>
      </Grid>
    );
  };

  const renderUrlInput = () => {
    const label = (
      <Grid item xs={3} style={{ alignSelf: 'flex-start' }}>
        <Flex align="center" gap={10} style={{ minHeight: '35px' }}>
          <Typography.Text>{TRANSLATE_MAP.originalURL}</Typography.Text>
          <Typography.Text style={{ color: globalToken.colorError }}>
            *
          </Typography.Text>
        </Flex>
      </Grid>
    );

    return (
      <Grid container alignItems="center">
        {state.shortlinkType === SHORT_LINK_TYPE.INDIVIDUAL && (
          <>
            {label}

            <Grid item xs={9}>
              <Suspense fallback={<Spin indicatorSize={16} />}>
                <WrapperPersonalization
                  typeComponent="input"
                  initData={state.url}
                  onChange={handleOnChange}
                  enableShortLink={false}
                  otherData={otherDataMemoized}
                  mapAttributes={personalizationProps.mapAttributes}
                  listTemplateCustom={personalizationProps.listTemplateCustom}
                  onChangeOthers={handleChangeDynamicSettings}
                />
              </Suspense>
            </Grid>
          </>
        )}

        {state.shortlinkType === SHORT_LINK_TYPE.GENERAL && (
          <>
            {label}

            <Grid item xs={9}>
              <StyledTextArea
                id="input-short-link-url"
                autoSize
                value={state.url}
                onChange={e => handleOnChange(e.target.value)}
              />
            </Grid>
          </>
        )}
      </Grid>
    );
  };

  return (
    <ModalV2
      centered
      title={TRANSLATE_MAP.addShortLink}
      open={open}
      onCancel={handleClose}
      destroyOnClose
      onOk={handleOnOK}
      okButtonProps={{
        disabled: isDisableSubmit,
      }}
      okText={TRANSLATE_MAP.insert}
      cancelText={TRANSLATE_MAP.cancel}
      closeIcon
    >
      {state?.isLoading ? (
        <UILoading isLoading />
      ) : (
        <BodyWrapper>
          <Grid container alignItems="center">
            <Grid item xs={3}>
              <span style={{ fontSize: 12, color: '#000' }}>
                {TRANSLATE_MAP.type}
              </span>
            </Grid>
            <Grid item xs={9}>
              {renderType()}
            </Grid>
          </Grid>

          {renderShortener()}

          {renderUrlInput()}

          {state.error && (
            <Grid container alignItems="center">
              <Grid item xs={3} />
              <Grid item xs={9}>
                <FormHelperText error>{state.error}</FormHelperText>
              </Grid>
            </Grid>
          )}
        </BodyWrapper>
      )}
    </ModalV2>
  );
});

ModalShortlink.propTypes = {
  open: PropTypes.bool,
  hideOption: PropTypes.oneOf([
    SHORT_LINK_TYPE.INDIVIDUAL,
    SHORT_LINK_TYPE.GENERAL,
    null,
  ]),
  onClose: PropTypes.func,
  onOk: PropTypes.func,
};

export default memo(ModalShortlink);
