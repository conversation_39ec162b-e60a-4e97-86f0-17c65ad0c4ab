/* eslint-disable camelcase */
/* eslint-disable no-undef */
import {
  all,
  call,
  put,
  takeLatest,
  take,
  select,
  fork,
} from 'redux-saga/effects';
import _ from 'lodash';

import SelectorServices from 'services/Selector';
import ServiceUserInfo from 'services/UserInfo';
import ServiceUserAttributes from 'services/UserAttributes';
// import ServiceNotify from 'services/Notify';
import MetaDataServices from 'services/MetaData';
import WhoAmIServices from 'services/WhoAmI';
import DataViewServices from 'services/DataView';
// import local for translate
import enUS from 'date-fns/locale/en-US';
import vi from 'date-fns/locale/vi';
import ja from 'date-fns/locale/ja';

import Types from './constants';
// import ReduxTypes from '../../redux/constants';
import {
  userVerifySuccess,
  appInitSuccess,
  fetchUserAttributesDone,
  fetchPortalsSuccess,
  updateDashboardCallbackDecryptData,
} from './actions';
import APP from '../../appConfig';
import {
  mapDataPortal,
  serializeDecryptPermission,
  buildDecryptPermissionFromLocalStorage,
  serializeNetworkInfo,
  setCrossAppAuthentication,
} from './utils';
import { setAppCookie } from '../../services/utils';
import { safeParse, safeParseInt } from '../../utils/common';
import {
  makeSelectPersonalizations,
  makeSelectSettingPersonalizations,
  makeSelectUser,
} from './selector';
import {
  setDefaultLocale,
  registerLocale,
  getLocaleDate,
} from '../../utils/date';
import {
  getUserInfoPortal,
  setUserAndPortalDefaultConfig,
  getUserLocaleLanguage,
} from '../../utils/web/portalSetting';
import {
  getAppSession,
  setAppCookieSession,
  getRootToken,
  getTokenNetwork,
  setWindowParam,
  getAppParamURL,
  setCurrentAccessUserId,
  setSessionStorage,
} from '../../utils/web/cookie';
import { getListDone, updateValue, getList } from '../../redux/actions';
import { addMessageToQueue } from '../../utils/web/queue';
import {
  removeDataDecrypt,
  getCacheDataEncrypted,
  setDataDecryptFromCache,
} from '../../containers/DataDecrypt/utils';
import {
  intervalHandleJob,
  // intervalHandleJob5minutes,
  workerFetchMenu,
  applicationValidateUrlEmmbedded,
  workerDashboardHandleChangeOwnerBreadcrumb,
  workerDashboardHandleChangeShareAccessUser,
  workerFetchAllAccount,
} from './saga.extends';
import { PORTAL_KEYS } from '../../utils/constants';
import ReduxTypes from '../../redux/constants';
import {
  actionPersonalKeys,
  initDonePersonalizations,
  stopFetchingContentSource,
  stopPersonalizations,
  updateContentSource,
  updatePromotionCodeAttr,
  updateSettingPersonalizations,
  updateSTTContentSource,
  updateSTTPersonalizations,
} from '../../components/common/UIEditorPersonalization/WrapperPersonalization/libs/action';
import {
  mapPersonalizeToStored,
  serializationPersonalizations,
} from '../../components/common/UIEditorPersonalization/WrapperPersonalization/utils';
import { PROMOTION_CODE } from '../../components/common/UIEditorPersonalization/WrapperPersonalization/constants';
import {
  toUIDataContentSource,
  toUIDataPromotionCodeAttr,
} from '../../components/common/UIEditorPersonalization/utils';

// import { dashboardChangeLocale } from '../../containers/LanguageProvider/actions';
// import { serializeFilterClientCache } from '../../utils/common';
const COOKIE_EXPIRE = 2592000; // 60 * 60 * 24 * 30 // 30 days

// watcher saga: watches for actions dispatched to the store, starts worker saga
export default function* watcherSaga() {
  // console.log('...payload', ...payload);
  yield takeLatest(Types.USER_VERIFY, workerVerifySaga);
  yield takeLatest(Types.APP_INIT, workerAppInitSaga);
  // yield takeLatest(Types.SWITCH_PORTAL, workerSwitchPortal);
  yield takeLatest(Types.UPDATE_DECRYPT_DATA, updateEncryptData);

  yield takeLatest(
    `@@DASHBOARD_ACCOUNT_SHARE_ACCESS_ID@@${ReduxTypes.UPDATE_VALUE}`,
    workerDashboardHandleChangeShareAccessUser,
  );
  yield takeLatest(
    `@@DASHBOARD_OWNER_BREADCRUMB@@${ReduxTypes.UPDATE_VALUE}`,
    workerDashboardHandleChangeOwnerBreadcrumb,
  );
  yield takeLatest(Types.UPDATE_DECRYPT_DATA, updateEncryptData);
  yield takeLatest(
    actionPersonalKeys.INIT_PERSONALIZATIONS,
    watcherPersonalizations,
  );
  yield takeLatest(
    actionPersonalKeys.INIT_PERSONALIZATIONS,
    watcherContentSource,
  );
}

function* workerAppInitSaga(action) {
  try {
    // console.log('workerAppInitSaga');
    const { pathname } = action.payload.location;

    const { portalId, accessUserId } = getAppParamURL(pathname);

    // call check and validate if using url embedded
    yield call(applicationValidateUrlEmmbedded, { portalId });

    // set share_access_user_id để call rồi check data sau
    // setCurrentAccessUserId(accessUserId);

    // return;
    let token = safeParse(getTokenNetwork(portalId), '');

    // console.log('portalId', portalId, '---', token);

    token = yield call(workerValidateToken, { token, portalId });

    // console.log('portalId 2', portalId, '---', token);

    // if (window.location.href === `${window.location.origin}${APP.PREFIX}`) {
    //   window.location.href = `${window.location.origin}${
    //     APP.PREFIX
    //   }/${portalId}/dashboard`;
    // }

    yield put(appInitSuccess({ portalId, token }));

    yield all([
      call(workerFetchNetworkInfo, { portalId }),
      call(workerFetchIamUserInfo),
      // call(workerPortalsSaga, { portalId, token }),
      call(workerVerifySaga, { portalId, token }),
    ]);

    // 2021-09-29
    // Account login vào CDP chưa tồn tại trong CDP nhưng đã tồn tại trong Portal
    // ex, user A has permission on Portal A, but on CDP database not yet add.
    // when user login and redirect to CDP, API will automation check and add to CDP database
    // Action: call workerPortalsSaga after call workerVerifySaga, because workerVerifySaga will check and add user in to portal
    // if call concurrent, user A will be redirect to login page, and make use fail permission
    yield all([
      call(workerPortalsSaga, { portalId, token }),
      call(workerFetchAllAccount),
    ]);

    // fetch menu after user info for get language
    yield call(workerFetchMenu);
    // register for case language
    registerLocale('en-US', enUS);
    registerLocale('vi', vi);
    registerLocale('ja', ja);
    setDefaultLocale(getLocaleDate(getUserLocaleLanguage()));

    yield put(updateValue('@@DASHBOARD@@LOADING@@', false));

    // build cache permission decrypt
    buildDecryptPermissionFromLocalStorage();

    // call job
    // yield all([call(intervalHandleJob), call(intervalHandleJob5minutes)]);
    yield call(intervalHandleJob);
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'workerAppInitSaga',
      data: err.stack,
    });
    console.log(err);
  }
}

// function* workerSwitchPortal(action) {
//   try {
//     const portalId = parseInt(safeParse(action.payload.portalId, 0));
//     if (portalId > 0) {
//       yield call(workerResetTokenSwitchPortal, portalId);

//       setAppCookie({
//         api_pid: portalId,
//         c_pid: '1',
//       });
//       window.location.href = `${
//         window.location.origin
//       }${APP.getLinkDashboard()}`;
//     }
//   } catch (err) {
//     addMessageToQueue({
//       path: 'app/modules/Dashboard/saga.js',
//       func: 'workerSwitchPortal',
//       data: err.stack,
//     });
//     console.log(err);
//   }
// }

// function* workerResetTokenSwitchPortal(portalId) {
//   try {
//     // const res = yield call(AuthenticationService.loginPortal, {
//     //   portalId,
//     //   token: getRootToken(),
//     // });

//     const res = yield call(WhoAmIServices.account.authenticate, {
//       app_id: `${portalId}`,
//       token: getRootToken(),
//     });

//     const userInfo = safeParse(res.data, {});
//     if (res.code === 200 && Object.keys(userInfo).length > 0) {
//       userInfo.portalId = portalId;
//       setCrossAppAuthentication(userInfo);
//     }
//   } catch (err) {
//     addMessageToQueue({
//       path: 'app/modules/Dashboard/saga.js',
//       func: 'workerResetTokenSwitchPortal',
//       data: err.stack,
//     });
//     console.log(err);
//   }
// }

function* workerValidateToken({ token, portalId }) {
  try {
    setAppCookie({
      api_pid: portalId,
    });

    const rootToken = safeParse(getRootToken(), '');

    // check neu root token chua co, doc vao INSIGHT_U_OGS cookie de lay token
    if (rootToken === '') {
      setAppCookieSession('api_r_token', token, COOKIE_EXPIRE, '');
    }

    if (token === '') {
      // const res = yield call(AuthenticationService.loginPortal, {
      //   portalId,
      //   token: getRootToken(),
      // });

      const res = yield call(WhoAmIServices.account.authenticate, {
        app_id: `${portalId}`,
        token: getRootToken(),
      });
      console.log({ res });
      // {"token":"9474s20374z294e4x296l464z294x2m5f4h4x2v2","app_id":"1183"}

      const userInfo = safeParse(res.data, {});
      if (res.code === 200 && Object.keys(userInfo).length > 0) {
        userInfo.portalId = portalId;
        setCrossAppAuthentication(userInfo);
      }

      const messageContent = {
        type: 'cdp_user_authenticated_change',
      };

      window.postMessage(messageContent);

      return userInfo.token;
    }
    return token;
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'workerResetTokenSwitchPortal',
      data: err.stack,
    });
    console.log(err);
  }
  return token;
}

function* workerPortalsSaga(action) {
  try {
    const data = yield call(ServiceUserInfo.getPortals);
    if (data.code !== 200 && data.data.length < 1) {
      window.location.href = `${window.location.origin}${APP.PREFIX}/login`;
    } else {
      const mapPortal = mapDataPortal(data.data, action.portalId);
      // console.log('---', mapPortal, data.data, action.portalId);
      if (mapPortal.map[action.portalId] === undefined) {
        // url portal doest not have matching any user's portal
        window.location.href = `${window.location.origin}${APP.PREFIX}/login`;
      } else {
        const { portalId } = mapPortal.map[action.portalId];
        const api_pid = safeParseInt(getAppSession('api_pid'), 0);
        if (api_pid !== portalId) {
          setAppCookie({
            api_pid: portalId,
            c_pid: '1',
          });
        }

        yield put(
          fetchPortalsSuccess({
            mapPortal,
            activePortal: mapPortal.map[action.portalId],
          }),
        );
      }
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'workerPortalsSaga',
      data: err.stack,
    });
    console.log(err);
    // yield put(push(`${getLocationOrigin()}/login`));
    window.location.href = `${window.location.origin}${APP.PREFIX}/login`;
  }
}

export function* workerVerifySaga(action = {}) {
  try {
    const portalId = safeParse(action.portalId, null);

    // const token = getToken();
    // if (token.includes('.')) {
    //   window.location.href = `${window.location.origin}/login`;
    // }

    const data = yield call(ServiceUserInfo.verify, { portalId });
    if (Object.keys(data.portal).length === 0) {
      // yield put(push(`${getLocationOrigin()}/login`));
      window.location.href = `${window.location.origin}${APP.PREFIX}/login`;
    } else {
      setAppCookieSession('user_id', data.user.userId);
      setAppCookieSession('user_name', data.user.fullName);
      setSessionStorage('api_name', data.portal.portalName);
      // verify token not availabel current portal
      const dataUser = safeParse(data.user, {});
      const portal = getUserInfoPortal(data.portal);
      // set portal default local params for performance get

      // set decryptPermission
      serializeDecryptPermission(dataUser.decryptPermission);

      setUserAndPortalDefaultConfig(portal.defaultConfig, dataUser, portal);
      yield put(userVerifySuccess({ data, portal }));
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'workerVerifySaga',
      data: err.stack,
    });
    console.log(err);
  }
}

export function* workerFetchNetworkInfo() {
  try {
    // if (isShowMenuV2()) {
    const res = yield call(WhoAmIServices.network.info);
    const data = safeParse(res.data, {});
    yield put(
      updateValue(
        '@@DASHBOARD@@NETWORK_INFO@@',
        serializeNetworkInfo(data.networkInfo),
      ),
    );
    // }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'workerFetchNetworkInfo',
      data: err.stack,
    });
    console.log(err);
  }
}

export function* workerFetchIamUserInfo() {
  try {
    // if (isShowMenuV2()) {
    const res = yield call(WhoAmIServices.user.info);
    const user = safeParse(res.data, {});
    // console.log('data', data)
    // yield put(updateValue('@@DASHBOARD@@IAM_USER_INFO@@', data));
    setWindowParam(
      PORTAL_KEYS.USER_LANGUAGE,
      safeParse(user.language, 'en').toLowerCase(),
    );
    setWindowParam(PORTAL_KEYS.AVATAR, safeParse(user.avatar, ''));
    // }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'workerFetchNetworkInfo',
      data: err.stack,
    });
    console.log(err);
  }
}

export function* initWorkerVerifySaga(action) {
  try {
    const userReducer = yield select(makeSelectUser);

    if (userReducer.userId === '') {
      yield call(workerVerifySaga);
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'initWorkerVerifySaga',
      data: err.stack,
    });
    console.log(err);
  }
}

export function* dashboardFetchUserAttributesSaga(action) {
  try {
    const data = yield call(ServiceUserAttributes.getAll, action);

    yield put(fetchUserAttributesDone(data));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'dashboardFetchUserAttributesSaga',
      data: err.stack,
    });
    console.log(err);
    const output = { attributes: {} };
    yield put(fetchUserAttributesDone(output));
  }
}

export function* dashboardGetGroupItemAttributesDataView(PREFIX, params) {
  let res = {};
  try {
    res = yield call(DataViewServices.BOAttribute.getGroupAttrs, params);
    const data = res.data || [];
    yield put(getListDone(PREFIX, data));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'dashboardGetGroupItemAttributesDataView',
      data: err.stack,
    });
    console.log(err);
    const output = { attributes: {} };
    yield put(fetchUserAttributesDone(output));
  }
  return res;
}

export function* dashboardGetGroupItemAttributes(PREFIX, params, others = {}) {
  let res = {};
  try {
    res = yield call(
      MetaDataServices.item.properties.getByGroupIds,
      params,
      others,
    );

    let data = [];
    if (params.segmentId) {
      data = res.data.map(item => {
        return {
          ...item,
          segmentId: params.segmentId,
        };
      });
    } else {
      data = res.data;
    }

    yield put(getListDone(PREFIX, data));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'dashboardFetchUserAttributesSaga',
      data: err.stack,
    });
    console.log(err);
    const output = { attributes: {} };
    yield put(fetchUserAttributesDone(output));
  }
  return res;
}

export function* dashboardLookupItemAttributes(PREFIX, params) {
  // console.log('dashboardLookupItemAttributes', PREFIX, params);
  const data = yield call(
    MetaDataServices.item.properties.lookupByIdsV2,
    params,
  );
  yield put(
    updateValue(`${PREFIX}DATA_LOOKUP`, {
      type: 'itemAttribute',
      data,
    }),
  );
  return data;
}

export function* dashboardLookupEventSchema(PREFIX, params) {
  const data = yield call(MetaDataServices.eventSchema.lookupByIds, params);
  yield put(
    updateValue(`${PREFIX}DATA_LOOKUP`, {
      type: 'eventSchema',
      data,
    }),
  );
  return data;
}

export function* dashboardGetListEventTracking(PREFIX) {
  const res = yield call(MetaDataServices.eventTracking.getList);
  yield put(getListDone(`${PREFIX}`, res.data));
  return res;
}

export function* dashboardGetListDataSource(PREFIX) {
  const res = yield call(MetaDataServices.dataSource.getList);
  yield put(getListDone(PREFIX, res.data));
  return res;
}

export function* dashboardLookupDataSource(PREFIX, insightPropertyIds) {
  const data = yield call(MetaDataServices.dataSource.lookupByIds, {
    insightPropertyIds,
  });
  yield put(
    updateValuePrefix(`${PREFIX}DATA_LOOKUP`, {
      type: 'dataSource',
      data,
    }),
  );
  return data;
}

function* updateEncryptData(action) {
  // console.log('updateEncryptData', action);
  try {
    const { isOpenModal, type, cacheData = {} } = action.payload;
    const cacheDataEncrypt = getCacheDataEncrypted(
      cacheData.itemTypeId,
      cacheData.attributeCode,
    );
    if (type === 'ENCRYPT_DATA') {
      const { prefix = '', prefixes = [] } = cacheData;
      removeDataDecrypt(cacheData);
      if (prefixes.length > 0) {
        const arr = [];

        prefixes.forEach(item => {
          // eslint-disable-next-line redux-saga/yield-effects
          arr.push(put(getList(item)));
        });
        yield all(arr);
      }
      if (prefix !== '') {
        yield put(getList(prefix));
      }
      yield put(updateDashboardCallbackDecryptData(action.payload));
    } else if (
      type === 'DECRYPT_DATA' &&
      isOpenModal &&
      cacheDataEncrypt !== undefined
    ) {
      // nếu attribute đó đã được mở khóa trước đó thì encrypt luôn không cần show modal
      const { prefix = '', prefixes = [] } = cacheData;
      // removeDataDecrypt(cacheData);
      setDataDecryptFromCache(
        cacheData.itemTypeId,
        cacheData.attributeCode,
        cacheDataEncrypt,
      );
      if (prefixes.length > 0) {
        const arr = [];

        prefixes.forEach(item => {
          // eslint-disable-next-line redux-saga/yield-effects
          arr.push(put(getList(item)));
        });
        yield all(arr);
      }
      if (prefix !== '') {
        yield put(getList(prefix));
      }
    } else {
      yield put(updateDashboardCallbackDecryptData(action.payload));
    }
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'updateEncryptData',
      data: err.stack,
    });
    console.log(err);
  }
}

export function* fetchPersonalizations(payload) {
  try {
    const personalizations = yield select(makeSelectPersonalizations());

    if (!_.isEmpty(payload) && !personalizations.isLoading) {
      // Start Loading
      yield put(updateSTTPersonalizations(true));

      // Fetching personalizations
      const responses = yield all(
        (function combineFetchPersonalizations() {
          const objPromises = {};

          Object.keys(payload).forEach(key => {
            const {
              savedKey = '',
              payload: itemPayload = {},
              serviceFn = null,
            } = payload[key];

            if (_.isFunction(serviceFn) && savedKey) {
              objPromises[savedKey] = call(serviceFn, itemPayload);
            }
          });

          return objPromises;
        })(),
      );

      if (!_.isEmpty(responses)) {
        const serializedResponses = serializationPersonalizations({
          dataSource: responses,
          payload,
        });

        const dataToStored = mapPersonalizeToStored({
          payload,
          oldDataSource: personalizations.settings,
          newDataSource: serializedResponses,
        });

        delete dataToStored.personalizationDataError;

        yield put(
          updateSettingPersonalizations({
            metadata: payload,
            entries: dataToStored,
          }),
        );
      }
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'fetchPersonalizations',
      data: error.stack,
    });
  } finally {
    yield put(updateSTTPersonalizations(false));
    yield put(stopPersonalizations());
    yield put(initDonePersonalizations());
  }
}

function* fetchPromotionCodeAttribute() {
  try {
    const params = {
      data: {
        filters: {
          OR: [
            {
              AND: [
                {
                  column: 'item_type_id',
                  data_type: 'number',
                  operator: 'equals',
                  value: -100,
                },
                {
                  type: 1,
                  column: 'status',
                  data_type: 'number',
                  operator: 'matches',
                  value: [1, 2],
                },
              ],
            },
          ],
        },
        limit: null,
        page: null,
      },
    };

    const response = yield call(
      SelectorServices.attribute.getListBOAttribute,
      params,
    );

    if (response && response.code === 200 && response.data) {
      const serializedResponse = toUIDataPromotionCodeAttr(response.data);
      yield put(updatePromotionCodeAttr(serializedResponse));
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'fetchPromotionCodeAttribute',
      data: error.stack,
    });
    console.log(error);
  }
}

function* fetchContentSource(payload) {
  try {
    if (!_.isEmpty(payload)) {
      const {
        params = {},
        personalizeMap = {},
        contentSources = [],
        appendPersonalizeType = {},
      } = payload;

      if (!_.isEmpty(params)) {
        yield put(updateSTTContentSource(true));

        const response = yield call(
          SelectorServices.attribute.getListBOAttribute,
          params,
        );

        if (response && response.code === 200 && !_.isEmpty(response.data)) {
          const serializedResponse = toUIDataContentSource({
            appendPersonalizeType,
            data: response.data,
          });

          if (!_.isEmpty(serializedResponse)) {
            yield put(
              updateContentSource({
                personalizeMap,
                contentSources,
                personalizationData: serializedResponse,
              }),
            );
          }
        }
      }
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'fetchContentSource',
      data: error.stack,
    });
    console.log(error);
  } finally {
    yield put(updateSTTContentSource(false));
    yield put(stopFetchingContentSource());
  }
}

function* watcherPersonalizations() {
  try {
    while (true) {
      const { payload = {} } = yield take(
        actionPersonalKeys.START_PERSONALIZATIONS,
      );

      // Fetching personalizations
      yield fork(fetchPersonalizations, payload);

      const settingsPersonalizations = yield select(
        makeSelectSettingPersonalizations(),
      );

      const promotionCodeAttrMap = _.get(
        settingsPersonalizations,
        'promotionCodeAttr.map',
        {},
      );

      // Fetching extra API to get promotion code attribute
      if (_.has(payload, PROMOTION_CODE) && _.isEmpty(promotionCodeAttrMap)) {
        yield fork(fetchPromotionCodeAttribute);
      }

      yield take(actionPersonalKeys.STOP_PERSONALIZATIONS);
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'watcherPersonalizations',
      data: error.stack,
    });
    console.log(error);
  }
}

function* watcherContentSource() {
  try {
    while (true) {
      const { payload = {} } = yield take(
        actionPersonalKeys.START_FETCHING_CONTENT_SOURCE,
      );

      // Fetching content source
      yield fork(fetchContentSource, payload);

      yield take(actionPersonalKeys.STOP_FETCHING_CONTENT_SOURCE);
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/saga.js',
      func: 'watcherContentSource',
      data: error.stack,
    });
    console.log(error);
  }
}
