/* eslint-disable no-param-reassign */
/* eslint-disable no-plusplus */
/* eslint-disable eqeqeq */
import { Map } from 'immutable';
import _ from 'lodash';
import moment from 'moment';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { getUntitledName } from '../../../../../../utils/web/properties';
import { safeParseArray } from '../../../../../../utils/web/utils';
import { NODE_TYPE } from '../Content/Nodes/constant';

export const initialStateConfigure = () => ({
  //   activeTypeSegment: { label: 'User', value: '-1007' },
  isLoading: true,
  name: getUntitledName(
    getTranslateMessage(TRANSLATE_KEY._UNTITLED_STORY, 'Untitled Story'),
  ),
  nodes: Map({}),
  activeNode: {},
});

export const initialStateMainCreate = () => ({
  // action: 'create',
  // design: 'create',
  design: 'preview',
  itemTypeId: 0,
  isLoading: true,
  isDoing: false,
  activeRow: {},
  activeRowClone: {},
  disabled: true,
  actionDisabled: true,
  isCollapse: false,
  dataRunTest: {},
  isExpand: false,
  isCloseDataflow: false,
  isLoadingFlowChart: false,
  modalSaveAsTemplate: {
    step: 0,
    show: false,
    savedData: null,
  },
  modalConfirmActive: {
    show: false,
  },
  modalConfirmSaveChanged: {
    show: false,
    changedInfos: [],
  },
  isBlastCampaign: false,
  isInitSettingBlastCampaignDone: false,
  firstActionDisabled: true,
});

export const isValidName = name => {
  let countValidCharacter = 0;
  for (let i = 0; i < name.length; i++) {
    if (name[i] !== ' ') {
      countValidCharacter += 1;
    }
  }
  return countValidCharacter > 0;
};

export const labelCantBlank = getTranslateMessage(
  TRANSLATE_KEY._NOTI_EMPTY_NAME,
  'Name can’t be blank',
);

export const labelInvalidName = getTranslateMessage(
  TRANSLATE_KEY._NOTI_INVALID_NAME,
  'Invailid name',
);

export const labelMaxLength = getTranslateMessage(
  TRANSLATE_KEY._INVALID_NAME_MAX_LENGTH,
  'Valid name is no more than 255 characters',
);

export function getHasOpenModalConfirm(flattenNodes = []) {
  const hasOpenModalConfirm =
    flattenNodes.findIndex(
      item =>
        item.type === NODE_TYPE.FILTER ||
        item.type === NODE_TYPE.CONDITION_YES ||
        item.type === NODE_TYPE.UPDATE_INFO ||
        item.type === NODE_TYPE.UPDATE_SEGMENT,
    ) > -1;
  return hasOpenModalConfirm;
}

export const reInitNodeMenus = ({ rootNode, channelId, triggerNode }) => {
  const newNode = _.cloneDeep(rootNode);
  const { menus, tableNodes, list, map } = newNode;

  if (channelId == 8) {
    if (triggerNode === NODE_TYPE.EVENT_BASED) {
      return newNode;
    }
  }

  if (Number(channelId) === 2) {
    // menus
    const newMenuNodes = menus[1].nodes.filter(
      item => item.value !== NODE_TYPE.WAIT_EVENT,
    );

    // tableNodes
    const newTableNodesHeader = tableNodes.header[1].filter(
      item => item.value !== NODE_TYPE.WAIT_EVENT,
    );

    // list
    const newList = list.filter(item => item.value !== NODE_TYPE.WAIT_EVENT);

    // map
    const newMap = { ...map };
    delete newMap[NODE_TYPE.WAIT_EVENT];

    // newNode.menus = newMenus;
    newNode.menus[1].nodes = newMenuNodes;
    newNode.tableNodes.header[1] = newTableNodesHeader;
    newNode.list = newList;
    newNode.map = newMap;
    // console.log({ rootNode, newNode });
  }
  return newNode;
  // return newNode;
};

export const mapInputToAPI = dataIn => {
  // console.log('dataIn', dataIn);
  let dataOut = {};
  if (dataIn.length > 0) {
    dataIn.forEach(item => {
      item.sections.forEach(tmp => {
        // console.log('tmp____', tmp);
        let { value } = tmp.properties;

        const { input_type: inputType } = tmp.properties;

        if (tmp.name === 'datePicker') {
          value = moment(value).format();
        }

        if (inputType === 'singleCheckbox') {
          value = Boolean(value);
        }

        if (value !== undefined) {
          dataOut = {
            ...dataOut,
            [tmp.properties.internalName]: value,
          };
        }
      });
    });
  }
  return dataOut;
};

// fix case custom_input bị disable, remove, archive,...
export const getRootCustomInputs = (rootCustomInput, inputAPI) => {
  const customInput = rootCustomInput || {};
  return Object.keys(inputAPI).reduce((acc, cur) => {
    const rootValue = customInput[cur];
    if (rootValue) {
      acc[cur] = rootValue;
    }
    return acc;
  }, {});
};

export const checkStatusNode = (dataIn, nodeId) => {
  if (
    dataIn.getIn([nodeId, 'destination']) &&
    dataIn.getIn([nodeId, 'destination']).campaignId
  ) {
    return 'update';
  }
  return 'create';
};

export const mapNodeToFlattenNodes = (data, arrayFlatten = []) => {
  const { ...others } = data;
  const clonedFlatten = [...arrayFlatten];
  const index = arrayFlatten.findIndex(item => item.nodeId === data.oleNodeId);
  // arrayFlatten.push({
  //   ...others,
  // });
  clonedFlatten.splice(index + 1, 0, { ...others });
  if (data.branchs && data.branchs.length > 0) {
    data.branchs.forEach(item => mapNodeToFlattenNodesTMP(item, clonedFlatten));
  }
  return clonedFlatten;
};

const mapNodeToFlattenNodesTMP = (data, arrayFlatten = []) => {
  const { ...others } = data;
  arrayFlatten.push({
    ...others,
  });
  if (data.branchs && data.branchs.length > 0) {
    data.branchs.forEach(item => mapNodeToFlattenNodesTMP(item, arrayFlatten));
  }
  return arrayFlatten;
};

export const mapDataCopyToNodes = (copyNodes, stateNode, nodeAB = []) => {
  /**
   * Hàm đệ quy xử lý các node và thêm hậu tố "- Copy" vào các tên nhánh
   * @param {Object} nodes - Node cần được xử lý
   * @returns {Object} - stateNode đã được cập nhật
   */
  const processNode = nodes => {
    let dataMap = nodes.map;

    // Xử lý các loại node khác nhau
    if ([NODE_TYPE.CONDITION_YES, NODE_TYPE.WFR_NODE].includes(nodes.type)) {
      dataMap = appendCopyToBranchName(dataMap);
    } else if (nodes.type === NODE_TYPE.SPLIT_NODE) {
      dataMap = handleSplitNode(nodes, dataMap);
    } else if (nodes.type === NODE_TYPE.DESTINATION) {
      dataMap = handleDestinationNode(dataMap);
    }

    // Nếu node được copy chưa có data thì bỏ qua.
    if (dataMap) {
      // Cập nhật stateNode với dataMap đã xử lý
      stateNode = stateNode.set(nodes.nodeId, dataMap);
    }

    // Xử lý đệ quy các nhánh con
    const branches = safeParseArray(nodes.branchs);

    branches.forEach(branch => {
      stateNode = processNode(branch);
    });

    return stateNode;
  };

  /**
   * Thêm hậu tố "- Copy" vào tên nhánh
   * @param {Object} dataMap - Map dữ liệu của node
   * @returns {Object} - Map dữ liệu đã được cập nhật
   */
  const appendCopyToBranchName = dataMap => {
    const nameNode = dataMap.get('branchName');
    return dataMap.setIn(['branchName'], `${nameNode} - Copy`);
  };

  /**
   * Xử lý node kiểu SPLIT_NODE
   * @param {Object} node - Node cần xử lý
   * @param {Object} dataMap - Map dữ liệu của node
   * @returns {Object} - Map dữ liệu đã được cập nhật
   */
  const handleSplitNode = (node, dataMap) => {
    // Lấy tên nhánh từ branchName hoặc label
    const nameNode = dataMap.get('branchName') || node.label;

    // Cập nhật branches và weight trong stateNode nếu có nodeAB
    if (nodeAB.length > 0) {
      stateNode = stateNode.setIn([node.parentId, 'branches'], nodeAB);
      nodeAB.forEach(item => {
        stateNode = stateNode.setIn([item.id, 'weight'], item.value);
      });
    }

    // Cập nhật tên nhánh
    let updatedDataMap = dataMap.setIn(['branchName'], `${nameNode} - Copy`);

    // Cập nhật weight nếu có
    const weightItems = nodeAB.filter(item => item.id === node.nodeId);
    if (weightItems.length > 0) {
      updatedDataMap = updatedDataMap.setIn(['weight'], weightItems[0].value);
    }

    return updatedDataMap;
  };

  /**
   * Xử lý node kiểu DESTINATION
   * @param {Object} dataMap - Map dữ liệu của node
   * @returns {Object} - Map dữ liệu đã được cập nhật
   */
  const handleDestinationNode = dataMap => {
    const destination = dataMap.get('destination');
    const nameNode = destination.campaignName;

    return dataMap.setIn(['destination'], {
      ...destination,
      campaignName: `${nameNode} - Copy`,
      isCopyNode: true,
      data: {
        ...destination.data,
        campaignName: {
          ...destination.data.campaignName,
          value: `${nameNode} - Copy`,
        },
      },
    });
  };

  // Bắt đầu xử lý từ node gốc
  return processNode(copyNodes);
};

// Change Label Node
export const getTitleBranch = (nodes, title = {}) => {
  if ([NODE_TYPE.CONDITION_YES, NODE_TYPE.WFR_NODE].includes(nodes.type)) {
    const nameNode = nodes.map.get('branchName');

    title[nodes.nodeId] = `${nameNode} - Copy`;
  } else if (nodes.type === NODE_TYPE.SPLIT_NODE) {
    let nameNode;
    if (nodes.map.get('branchName')) {
      nameNode = nodes.map.get('branchName');
    } else {
      nameNode = nodes.label;
    }

    title[nodes.nodeId] = `${nameNode} - Copy`;
  }

  if (nodes.branchs && nodes.branchs.length > 0) {
    nodes.branchs.forEach(item => getTitleBranch(item, title));
  }

  return title;
};

export const findIndexTwoArray = (array1, array2) => {
  let isExistNode = false;
  array1.forEach(each => {
    const index = array2.findIndex(temp => temp.nodeId === each.nodeId);
    if (index !== -1) {
      isExistNode = true;
    }
  });

  return isExistNode;
};

export const deleteExistNodeIdInFlow = (array1, array2) => {
  const dataOut = [...array1];
  array2.forEach(each => {
    const index = dataOut.findIndex(temp => temp.nodeId === each.nodeId);
    dataOut.splice(index, 1);
  });
  return dataOut;
};
