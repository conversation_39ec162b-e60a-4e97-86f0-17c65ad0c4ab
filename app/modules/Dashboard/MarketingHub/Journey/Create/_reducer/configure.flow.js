/* eslint-disable indent */
/* eslint-disable dot-notation */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import cloneDeep from 'lodash/cloneDeep';
import ReduxTypes from '../../../../../../redux/constants';

import { NODE_TYPE } from '../Content/Nodes/constant';
import {
  createNodeAndMoveAfterNodeBranch,
  createNodeAndRemoveAllAfterNodeBranch,
  getBranchsByNode,
  moveNodeBranch,
  removeAllAfterNodeBranch,
} from '../utils.flow';
import { forEach, isEmpty } from 'lodash';

export const actionTypes = {
  SET_STATUS_GO_TO_OTHER_ACTION: 'SET_STATUS_GO_TO_OTHER_ACTION',
  SET_ARRAY_ACTION_GO_TO_OTHER_ACTION: 'SET_ARRAY_ACTION_GO_TO_OTHER_ACTION',
  SET_REF_OBJECT_ID_GO_TO_OTHER_ACTION: 'SET_REF_OBJECT_ID_GO_TO_OTHER_ACTION',
  ADD_NODE_GO_TO_OTHER_ACTION: 'ADD_NODE_GO_TO_OTHER_ACTION',
  SET_NODE_BRANCH: 'SET_NODE_BRANCH',
  SET_NODE_TREES: 'SET_NODE_TREES',
  SET_FLATTEN_NODES: 'SET_FLATTEN_NODES',
  SET_FLATTEN_NODES_WITH_REMOVE_NODE: 'SET_FLATTEN_NODES_WITH_REMOVE_ACTION',
  SET_FLATTEN_NODES_WITH_ADD_NODE: 'SET_FLATTEN_NODES_WITH_ADD_NODE',
  CANCEL_CHANGE_NODE_GO_TO_OTHER_ACTION:
    'CANCEL_CHANGE_NODE_GO_TO_OTHER_ACTION',
  CREATE_NODE: 'CREATE_NODE',
  REMOVE_NODE: 'REMOVE_NODE',
  SET_FLATTEN_NODE: 'SET_FLATTEN_NODE',
};

const initialStateConfigureFlow = {
  nodeBranch: [],
  nodeTrees: [],
  flattenNodes: [],
  disabledNodes: [],
  arrActionGoToOtherAction: [],
  statusGoToOtherAction: {
    isSelect: false,
    nodeIdToCome: null,
    nodeIdToGo: null,
    parentId: null,
  },
};

const configureFlowReducerFor = moduleConfig => {
  const PREFIX = moduleConfig.key;
  const configureReducer = (state = initialStateConfigureFlow, action) => {
    return produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}@@FLOW@@${actionTypes.ADD_NODE_GO_TO_OTHER_ACTION}${
          ReduxTypes.UPDATE_VALUE
        }`: {
          if (action.payload && action.payload.param) {
            const { param } = action.payload;
            const parentId = null;

            let result = [];
            const cloneDeepData = cloneDeep(state.flattenNodes);

            if (cloneDeepData.length > 1) {
              result = cloneDeepData.map(node => {
                if (node.parentId === param.parentId) {
                  node.parentId = param.nodeId;
                }
                node.active = false;
                return { ...node };
              });

              result = [...result, { ...param }];
            }

            draft.flattenNodes = result;

            const nodes = state.flattenNodes.reduce((objNode, currentNode) => {
              return {
                ...objNode,
                [currentNode.nodeId]: currentNode,
              };
            }, {});

            // eslint-disable-next-line no-shadow
            const disabledNodes = (nodes, nodeId, disabled) => {
              disabled = [...disabled, nodeId];
              if (nodes[nodeId] && nodes[nodeId].parentId) {
                disabled = disabledNodes(
                  nodes,
                  nodes[nodeId].parentId,
                  disabled,
                );
              }

              return disabled;
            };

            let disabled = [];

            disabled = disabledNodes(nodes, param.parentId, []);

            Object.values(nodes).forEach(item => {
              if (item.nodeType === NODE_TYPE.GO_TO) {
                disabled = [...disabled, item.nodeId];
              }
            });

            draft.statusGoToOtherAction.isSelect = true;
            draft.statusGoToOtherAction.nodeIdToGo = param.nodeId;
            draft.statusGoToOtherAction.parentId = parentId;
            draft.disabledNodes = disabled;
          }

          return;
        }
        case `${PREFIX}@@FLOW@@${
          actionTypes.SET_REF_OBJECT_ID_GO_TO_OTHER_ACTION
        }${ReduxTypes.UPDATE_VALUE}`: {
          if (action.payload.nodeId && action.payload.objectRefId) {
            const nodeIndex = state.flattenNodes.findIndex(
              node => node.nodeId === action.payload.nodeId,
            );

            draft.flattenNodes[nodeIndex].objectRefId =
              action.payload.objectRefId;
          }
          return;
        }
        case `${PREFIX}@@FLOW@@${
          actionTypes.CANCEL_CHANGE_NODE_GO_TO_OTHER_ACTION
        }${ReduxTypes.UPDATE_VALUE}`: {
          if (state.statusGoToOtherAction.nodeIdToGo) {
            draft.statusGoToOtherAction.isSelect = false;
            draft.statusGoToOtherAction.nodeIdToGo = null;
            draft.statusGoToOtherAction.parentId = null;
            draft.disabledNodes = [];
          }
          return;
        }
        // case actionTypes.SET_STATUS_GO_TO_OTHER_ACTION:
        case `${PREFIX}@@FLOW@@${actionTypes.SET_STATUS_GO_TO_OTHER_ACTION}${
          ReduxTypes.UPDATE_VALUE
        }`:
          // return {...state, count1: state.count1 + 1};
          draft.count1 = state.count1 + 1;
          return;
        case `${PREFIX}@@FLOW@@${
          actionTypes.SET_ARRAY_ACTION_GO_TO_OTHER_ACTION
        }${ReduxTypes.UPDATE_VALUE}`:
          draft.arrActionGoToOtherAction =
            action.payload.arrActionGoToOtherAction;
          return;
        // return {...state, arrActionGoToOtherAction: action.payload.arrActionGoToOtherAction};
        case `${PREFIX}@@FLOW@@${actionTypes.SET_NODE_BRANCH}${
          ReduxTypes.UPDATE_VALUE
        }`:
          draft.nodeBranch = action.payload.nodeBranch;
          return;
        // return {...state, nodeBranch: action.payload.nodeBranch};
        case `${PREFIX}@@FLOW@@${actionTypes.SET_NODE_TREES}${
          ReduxTypes.UPDATE_VALUE
        }`:
          draft.nodeTrees = action.payload.nodeTrees;
          return;
        // return {...state, nodeTrees: action.payload.nodeTrees};
        case `${PREFIX}@@FLOW@@${actionTypes.SET_FLATTEN_NODES}${
          ReduxTypes.UPDATE_VALUE
        }`:
          draft.flattenNodes = action.payload.flattenNodes;
          return;
        case `${PREFIX}@@FLOW@@${
          actionTypes.SET_FLATTEN_NODES_WITH_REMOVE_NODE
        }${ReduxTypes.UPDATE_VALUE}`: {
          // console.log('SET_FLATTEN_NODES_WITH_REMOVE_ACTION', action.payload);
          draft.flattenNodes = action.payload.flattenNodes;
          return;
        }
        case `${PREFIX}@@FLOW@@${actionTypes.SET_FLATTEN_NODES_WITH_ADD_NODE}${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.flattenNodes = action.payload.flattenNodes;
          return;
        }
        case `${PREFIX}@@FLOW@@${actionTypes.CREATE_NODE}${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { node } = action.payload;

          // Lấy nhánh từ node, nếu không có thì trả về mảng chứa chính node đó
          let newNodes = getBranchsByNode(node);

          if (isEmpty(newNodes)) {
            newNodes = [node];
          }

          const newFlattenNodes = [...state.flattenNodes];
          const rootNode = newNodes[0];

          if (newFlattenNodes.length >= 1) {
            forEach(newFlattenNodes, n => {
              if (n.parentId === rootNode.parentId) {
                n.parentId = rootNode.nodeId;
              }
            });

            draft.flattenNodes = [...newFlattenNodes, ...newNodes];
          } else {
            draft.flattenNodes = [...newNodes];
          }
          return;
        }
        case `${PREFIX}@@FLOW@@${actionTypes.REMOVE_NODE}${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const nodeRemove = action.payload;
          const array = [];
          if (
            nodeRemove.type === NODE_TYPE.EVENT_BASED ||
            nodeRemove.type === NODE_TYPE.SCHEDULED
          ) {
            // remove empty workflow
          } else {
            state.flattenNodes.forEach(node => {
              if (node.nodeId !== nodeRemove.nodeId) {
                if (node.parentId === nodeRemove.nodeId) {
                  node.parentId = nodeRemove.parentId;
                }
                array.push(node);
              }
            });
          }

          draft.flattenNodes = array;
          return;
        }
        case `${PREFIX}@@FLOW@@CONFIRM_CREATE_NODE${ReduxTypes.UPDATE_VALUE}`: {
          const { node, type, valueBranch } = action.payload;
          // finds node
          if (type === 'remove') {
            // find node after and remove

            draft.flattenNodes = createNodeAndRemoveAllAfterNodeBranch(
              state.flattenNodes,
              node,
            );
          } else if (type === 'move') {
            draft.flattenNodes = createNodeAndMoveAfterNodeBranch(
              state.flattenNodes,
              node,
              valueBranch,
            );
          }
          return;
        }
        case `${PREFIX}@@FLOW@@CONFIRM_REMOVE_NODE${ReduxTypes.UPDATE_VALUE}`: {
          const { node, type, valueBranch } = action.payload;
          // console.log('node, type, valueBranch', node, type, valueBranch);
          // finds node
          if (type === 'remove') {
            draft.flattenNodes = removeAllAfterNodeBranch(
              state.flattenNodes,
              node,
            );
          } else if (type === 'move') {
            draft.flattenNodes = moveNodeBranch(
              state.flattenNodes,
              node,
              valueBranch,
            );
          }
          return;
        }
        case `${PREFIX}@@FLOW@@CONFIRM_REMOVE_CONDITION_YES_NODE${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { node } = action.payload;

          draft.flattenNodes = removeAllAfterNodeBranch(
            state.flattenNodes,
            node,
          );

          return;
        }

        case `${PREFIX}@@FLOW@@CONFIRM_REMOVE_BASIC_NODE${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { node, type } = action.payload;
          // console.log('node, type', node, type);
          if (type === 'remove-one') {
            const array = [];
            state.flattenNodes.forEach(tmp => {
              if (tmp.nodeId !== node.nodeId) {
                if (tmp.parentId === node.nodeId) {
                  tmp.parentId = node.parentId;
                }
                array.push(tmp);
              }
            });
            // console.log('node, type', node, type, array);

            draft.flattenNodes = array;
          } else if (type === 'remove-all') {
            draft.flattenNodes = removeAllAfterNodeBranch(
              state.flattenNodes,
              node,
            );
          }

          return;
        }

        case `${PREFIX}@@FLOW@@CONFIRM_REMOVE_NODE_TRIGGER${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.flattenNodes = [];
          return;
        }
        case `${PREFIX}@@FLOW@@${actionTypes.SET_FLATTEN_NODE}${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { nodeId, data } = action.payload;
          const index = draft.flattenNodes.findIndex(
            node => node.nodeId === nodeId,
          );

          if (index > -1) {
            draft.flattenNodes[index] = {
              ...draft.flattenNodes[index],
              ...data,
            };
          }
          return;
        }
        default:
          return state;
      }
    });
  };
  return configureReducer;
};

export default configureFlowReducerFor;
