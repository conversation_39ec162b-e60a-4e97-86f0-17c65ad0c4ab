/* eslint-disable camelcase */
import { delay, put, select } from 'redux-saga/effects';
// import { push } from 'react-router-redux';
// import { delay } from 'redux-saga';
// import delay from '@redux-saga/delay-p';
import { Map, OrderedMap } from 'immutable';
import { updateValue } from '../../../../../redux/actions';
import { NODE_TYPE } from './Content/Nodes/constant';
import { getStoryRoleActions, STORY_SETUP_ACTION } from './utils.story.rules';
import { selectDomainMainCreateWorkflow } from './selectors';
import { MAP_DATA_OPTIONS_FILTER } from '../../../../../components/common/UINodeFilter/utils';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { buildFlattenNodeWithNumberBranchSplit } from './utils.flow';
import { getBranchesFromFlattenNodesFromUI } from './Content/Nodes/SplitBranch/utils';
import { safeParse } from '../../../../../utils/common';
import { updateNodeData } from './actions';

export function* handleUpdateNameNode(nodeActive, infoNode, args) {
  const prefix = args.moduleConfig.key;
  if (
    nodeActive.type === NODE_TYPE.CONDITION_YES ||
    nodeActive.type === NODE_TYPE.CONDITION_NO
  ) {
    if (
      nodeActive.label !== infoNode.get('branchName') &&
      infoNode.get('branchName')
    ) {
      yield put(
        updateValue(`${prefix}@@STORY_NAME_BRANCH@@`, {
          nodeId: nodeActive.nodeId,
          branchName: infoNode.get('branchName'),
        }),
      );
    }
  }
}

export function* handleUpdateDataSplitNodeParent(args, action) {
  // console.log({ args, action });
  try {
    const prefix = args.moduleConfig.key;
    const { activeNode, branches, oldBranches, id } = action.payload;
    // const reducerMainConfigure = yield select(
    //   makeSelectConfigureMainCreateWorkflow(),
    // );
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const reducerMainConfigure = reducer.configure.main;
    const { flattenNodes } = reducerMainConfigure;
    // console.log(reducerMainConfigure);
    const newFlatten = buildFlattenNodeWithNumberBranchSplit({
      flattenNodes,
      activeNode,
      branches,
      oldBranches,
      id,
    });

    yield put(
      updateValue(`${prefix}@@SPLIT_NODE_PARENT_FLATTEN@@`, {
        flattenNodes: newFlatten,
      }),
    );

    yield put(
      updateValue(`${prefix}@@SPLIT_NODE_DATA@@`, {
        activeNode,
        branches,
      }),
    );
    // console.log('newFlatten', newFlatten);
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.flow.js',
      func: 'handleUpdateDataSplitNodeParent',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.error(err);
  }
}

export function* handleUpdatePercentSplitBranchNode(args, action) {
  yield delay(250);
  const prefix = args.moduleConfig.key;
  const { activeNode } = action.payload;
  const reducer = yield select(state =>
    selectDomainMainCreateWorkflow(state, args),
  );
  const reducerMainConfigure = reducer.configure.main;
  const { flattenNodes, nodes } = reducerMainConfigure;
  let branches = [];

  if (activeNode) {
    // handle for case SPLIT_NODE_DATA UPDATE_VALUE
    branches = getBranchesFromFlattenNodesFromUI(
      nodes,
      activeNode,
      flattenNodes,
    );
  } else {
    // handle for case INIT FLOW_UPDATE
    flattenNodes
      .filter(node => node.type === NODE_TYPE.SPLIT_BRANCH)
      .forEach(ABSplit => {
        const branchesOfAbSplit = getBranchesFromFlattenNodesFromUI(
          nodes,
          ABSplit,
          flattenNodes,
        );
        branches.push(...branchesOfAbSplit);
      });
  }

  const newFlattenNodes = flattenNodes.map(node => {
    if (
      [NODE_TYPE.SPLIT_NODE, NODE_TYPE.AB_SPLIT_CONTROL_NODE].includes(
        node.type,
      )
    ) {
      const branchUpdateForNode = branches.find(
        branch => branch.id === node.nodeId,
      );
      if (branchUpdateForNode) {
        return {
          ...node,
          moreInfo: {
            ...(node.moreInfo || {}),
            groupPercent: `${branchUpdateForNode.value.toFixed(0)}%`,
          },
        };
      }
    }
    return node;
  });

  yield put(
    updateValue(`${prefix}@@SPLIT_NODE_PARENT_FLATTEN@@`, {
      flattenNodes: newFlattenNodes,
    }),
  );
}

export function* handleValidateRuleActionDeleteNode(channelId, args) {
  try {
    const prefix = args.moduleConfig.key;
    const channelIdTmp = safeParse(channelId, ''); // Check for using single channel
    // const reducerMain = yield select(makeSelectMainCreateWorkflow());
    // const reducerMainConfigure = yield select(
    //   makeSelectConfigureMainCreateWorkflow(),
    // );
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const reducerMain = reducer.main;
    const reducerMainConfigure = reducer.configure.main;
    const { activeRow } = reducerMain;
    const { flattenNodes } = reducerMainConfigure;

    const roleActions = getStoryRoleActions(activeRow.accepted_actions);
    const disabledRemoveNodes = {};
    // Nếu channel id khác 8 || 10 (Zalo) thì mình disable nút xóa đi
    if (
      channelIdTmp !== '' &&
      channelIdTmp !== 8 &&
      channelIdTmp !== 10 &&
      flattenNodes &&
      flattenNodes.length
    ) {
      disabledRemoveNodes[flattenNodes[0].nodeId] = true;
    }
    if (roleActions.has(STORY_SETUP_ACTION.REMOVE_NODE) === false) {
      flattenNodes.forEach(item => {
        disabledRemoveNodes[item.nodeId] = true;
      });
    }
    yield put(
      updateValue(
        `${prefix}@@STORY_DISABLED_REMOVE_NODES@@`,
        disabledRemoveNodes,
      ),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.flow.js',
      func: 'handleValidateRuleActionDeleteNode',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.error(err);
  }
}

export function* resetTriggetTargetAudience(args, action) {
  // console.log({ args, action });
  try {
    const prefix = args.moduleConfig.key;
    // const reducerMainConfigure = yield select(makeSelectConfigureMainCreateWorkflow());
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const reducerMainConfigure = reducer.configure.main;
    const { flattenNodes } = reducerMainConfigure;
    let { nodes } = reducerMainConfigure;
    const listNodeFilter = flattenNodes.filter(
      item =>
        item.type === NODE_TYPE.FILTER ||
        item.type === NODE_TYPE.CONDITION_YES ||
        item.type === NODE_TYPE.UPDATE_INFO ||
        item.type === NODE_TYPE.UPDATE_SEGMENT,
    );

    // console.log(
    //   'listNodeFilter====>',
    //   listNodeFilter,
    //   nodes.toJS(),
    //   action.payload,
    // );

    const errors = {};
    // bien doi cai data node nay lai data da dc reset
    // // nodes
    const user_attributes = { itemTypeId: action.payload, rules: {} };
    // console.log(user_attributes);
    nodes = nodes.withMutations(map => {
      // console.log('listNodeFilter', listNodeFilter);
      listNodeFilter.forEach(node => {
        // console.log('node', node);
        if (node.type === NODE_TYPE.UPDATE_INFO) {
          map.setIn(
            [node.nodeId],
            Map({ type: action.payload, rules: OrderedMap({}) }),
          );
        } else if (node.type === NODE_TYPE.UPDATE_SEGMENT) {
          map.setIn([node.nodeId, 'updateSegment'], {});
        } else {
          map.setIn([node.nodeId, 'filter'], {
            filterType: MAP_DATA_OPTIONS_FILTER.user_attributes,
            user_attributes,
          });
        }

        errors[node.nodeId] = true;
      });
    });

    // console.log('nodes', JSON.stringify(nodes));

    // console.log('nodes ===>', nodes);
    // put lai action, update nodes
    yield put(updateValue(`${prefix}@@RESET_NODE@@`, { nodes, errors }));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.flow.js',
      func: 'resetTriggetTargetAudience',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.error(err);
  }
  // console.log('action ===>', action.payload);
}

export function* resetTriggetPerformEvent(args, _action) {
  // console.log({ args, action });
  try {
    const prefix = args.moduleConfig.key;
    // const reducerMainConfigure = yield select(makeSelectConfigureMainCreateWorkflow());
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const reducerMainConfigure = reducer.configure.main;
    const { flattenNodes } = reducerMainConfigure;
    // console.log('flattenNodes', flattenNodes);
    let { nodes } = reducerMainConfigure;
    const listNodeFilter = flattenNodes.filter(
      item =>
        item.type === NODE_TYPE.FILTER || item.type === NODE_TYPE.CONDITION_YES,
    );

    const errors = {};

    nodes = nodes.withMutations(map => {
      listNodeFilter.forEach(node => {
        // console.log(
        //   'node filter',
        //   map.getIn([node.nodeId]),
        //   JSON.stringify(map.getIn([node.nodeId])),
        // );

        const filter = map.getIn([node.nodeId, 'filter']) || {};
        const { filterType = {} } = filter;

        // console.log({ filterType, isFetchInfoData });

        if (filterType.value === 'event_attribute') {
          errors[node.nodeId] = true;
          map.setIn([node.nodeId, 'filter'], {
            filterType: MAP_DATA_OPTIONS_FILTER.event_attribute,
            event_attribute: {
              rules: {}, // chỉ assgin = {} thì vào lại node filter vẫn load dc data.
            },
          });
        }
      });
    });

    // console.log('errors', errors);

    // put lai action, update nodes
    yield put(updateValue(`${prefix}@@RESET_NODE@@`, { nodes, errors }));
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.flow.js',
      func: 'resetTriggetPerformEvent',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.error(err);
  }
  // console.log('action ===>', action.payload);
}

export function* rebuildBranchSplitNodeName(activeNode, args) {
  // console.log({ activeNode, args });
  try {
    const prefix = args.moduleConfig.key;
    // const reducerMainConfigure = yield select(makeSelectConfigureMainCreateWorkflow());
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );
    const reducerMainConfigure = reducer.configure.main;
    const { flattenNodes, nodes } = reducerMainConfigure;
    const branches = getBranchesFromFlattenNodesFromUI(
      nodes,
      activeNode,
      flattenNodes,
    );
    // console.log('-----', branches);
    yield put(
      updateNodeData({
        prefix,
        nodeId: activeNode.nodeId,
        name: 'branches',
        data: branches,
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.flow.js',
      func: 'rebuildBranchSplitNodeName',
      data: err.stack,
    });
    // eslint-disable-next-line no-console
    console.log('error', err);
  }
}
