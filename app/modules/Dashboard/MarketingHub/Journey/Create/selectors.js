import { get, has, isEmpty } from 'lodash';
import { createSelector } from 'reselect';

import { initialStateBlastCampaign } from './BlastCampaign/constants';
import { initialStateConfigure } from './_reducer/configure';
import { initialStateMainCreate } from './_reducer/utils';
import { findNearestParentDestinationNode } from './utils.flow';

/**
 * Direct selector to the customer state domain
 */

const defaultValue = {
  configure: {
    main: initialStateConfigure(),
  },
  main: initialStateMainCreate(),
  mainReducer: initialStateBlastCampaign(),
};

const selectDomainMainCreateWorkflow = (state, props) =>
  state.get(props && props.moduleConfig && props.moduleConfig.key) ||
  defaultValue;

const selectDomainMainCreateWorkflowV2 = opts => state =>
  state.get(opts?.moduleConfig?.key) || defaultValue;

/**
 * Other specific selectors
 */

const makeSelectMainReducerCreateWorkflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.mainReducer,
  );

const makeSelectConfigureCreateWorkflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure,
  );

const makeSelectConfigureMainCreateWorkflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main,
  );

const makeSelectMainCreateWorkflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.main,
  );

const makeSelectCreateWorkflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate,
  );

const makeSelectConfigureFlow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.flow,
  );

const makeSelectConfigureRulesFlow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main.rules,
  );

const makeSelectActiveNodeData = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => {
      const { nodeId } = substate.configure.main.activeNode;
      return substate.configure.main.nodes.get(nodeId);
    },
  );

const makeSelectActiveNodeId = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => {
      const { nodeId } = substate.configure.main.activeNode;

      return nodeId;
    },
  );

const makeSelectActiveIdNodeData = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => {
      // const { nodeId } = substate.configure.main.activeNode;
      return substate.configure.main.activeNode;
    },
  );

const makeSelectOpenModalDestinationTesting = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => {
      const { isOpenModalTesting } = substate.configure.main.destinationNode;
      return isOpenModalTesting;
    },
  );

const makeSelectDesign = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.main.design,
  );

const makeSelectConfigureMainCreateWorkflowErrors = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main.errors,
  );

const makeSelectIsExpandNodeData = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.main.isExpand,
  );

const makeSelectIsCloseDataflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.main.isCloseDataflow,
  );

const makeSelectConfigureMainCreateFlattenNodes = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main.flattenNodes,
  );
const makeSelectConfigureMainCreateNodesTree = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main.treeNodes,
  );
const makeSelectConfigureMainCreateInitFlattenNodes = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main.flattenNodes,
  );

const makeSelectTriggerNode = moduleConfig =>
  createSelector(
    state => makeSelectConfigureMainCreateWorkflow()(state, { moduleConfig }),
    main => {
      let nodeTrigger;

      const triggerInfo = main.triggerNode;

      if (triggerInfo) {
        nodeTrigger = main.nodes.get(get(triggerInfo, 'nodeId'));
      }

      return {
        triggerInfo,
        nodeData: nodeTrigger,
      };
    },
  );

export const selectConfigureMain = opts =>
  createSelector(
    selectDomainMainCreateWorkflowV2(opts),
    substate => substate.configure.main,
  );

export const selectStoryNodeList = opts =>
  createSelector(
    selectConfigureMain(opts),
    main => main.node?.list || [],
  );

export const selectNodes = opts =>
  createSelector(
    selectConfigureMain(opts),
    main => main.nodes,
  );

export const selectNodeActive = opts =>
  createSelector(
    selectNodeActiveId(opts),
    selectFlattenNodes(opts),
    (activeId, flattenNodes) =>
      flattenNodes.find(node => node.nodeId === activeId),
  );

export const selectNodeActiveType = opts =>
  createSelector(
    selectNodeActive(opts),
    nodeActive => nodeActive?.type,
  );

export const selectNodeById = (nodeId, opts) =>
  createSelector(
    selectFlattenNodes(opts),
    flattenNodes => flattenNodes.find(n => n.nodeId === nodeId),
  );

export const selectNodeDataById = (nodeId, opts) =>
  createSelector(
    selectNodes(opts),
    nodes => nodes.get(nodeId),
  );

export const selectNodeActiveData = opts =>
  createSelector(
    selectNodes(opts),
    selectNodeActiveId(opts),
    (nodes, nodeActiveId) => nodes.get(nodeActiveId),
  );

export const selectIsExistNodeData = (nodeId, opts) =>
  createSelector(
    selectNodeDataById(nodeId, opts),
    nodeData => !isEmpty(nodeData),
  );

export const selectIsExistNodeActiveData = opts =>
  createSelector(
    selectNodeActiveData(opts),
    nodeActiveData => !isEmpty(nodeActiveData),
  );

export const selectConfigureNodeErrors = opts =>
  createSelector(
    selectConfigureMain(opts),
    main => main.errors,
  );

export const selectNodeActiveId = opts =>
  createSelector(
    selectConfigureMain(opts),
    main => main.activeNode?.nodeId,
  );

export const selectFlattenNodes = opts =>
  createSelector(
    selectConfigureMain(opts),
    main => main.flattenNodes,
  );

export const selectParentNodeActiveId = opts =>
  createSelector(
    selectFlattenNodes(opts),
    selectNodeActiveId(opts),
    (flattenNodes = [], nodeActiveId) =>
      flattenNodes.find(node => node.nodeId === nodeActiveId),
  );

export const selectNodeActiveParentData = opts =>
  createSelector(
    selectNodes(opts),
    selectParentNodeActiveId(opts),
    (nodes, nodeActiveParentId) => nodes.get(nodeActiveParentId),
  );

export const selectIsNodeExistError = (nodeId, opts) =>
  createSelector(
    selectConfigureNodeErrors(opts),
    errors => has(errors, nodeId),
  );

export const selectNodeActiveExistError = opts =>
  createSelector(
    selectNodeActiveId(opts),
    selectConfigureNodeErrors(opts),
    (nodeActiveId, errors) => has(errors, nodeActiveId),
  );

export const selectNearestParentDestinationNode = (nodeId, opts) =>
  createSelector(
    selectFlattenNodes(opts),
    (flattenNodes = []) =>
      findNearestParentDestinationNode(flattenNodes, nodeId),
  );

export const selectModalSaveAsTemplate = moduleConfig =>
  createSelector(
    state => makeSelectMainCreateWorkflow()(state, { moduleConfig }),
    main => main.modalSaveAsTemplate,
  );

export const makeSelectScheduledThirdParty = () => {
  return createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate?.mainReducer?.trigger?.scheduledThirdParty || {},
  );
};

// const makeSelectNeedConfirmActive = moduleConfig =>
//   createSelector(
//     makeSelectJourneyChannelActive(),
//     makeSelectTriggerNode(moduleConfig),
//     state => makeSelectMainCreateWorkflow()(state, { moduleConfig }),
//     (channelActive, triggerNode, main) => {
//       const nodeTriggerMetadata = get(
//         main,
//         'activeRow.workflow_setting.metadata',
//       );

//       return checkDataForActiveChanged({
//         channel: channelActive,
//         nodeTriggerInfo: triggerNode.triggerInfo,
//         nodeTriggerData: triggerNode.nodeData,
//         nodeTriggerMetadata,
//       });
//     },
//   );

/**
 * Default selector used by Customer
 */

export default makeSelectCreateWorkflow;

export {
  makeSelectActiveIdNodeData,
  makeSelectActiveNodeData,
  makeSelectConfigureCreateWorkflow,
  makeSelectConfigureFlow,
  makeSelectConfigureMainCreateFlattenNodes,
  makeSelectConfigureMainCreateInitFlattenNodes,
  makeSelectConfigureMainCreateNodesTree,
  makeSelectConfigureMainCreateWorkflow,
  makeSelectConfigureMainCreateWorkflowErrors,
  makeSelectConfigureRulesFlow,
  makeSelectDesign,
  makeSelectIsCloseDataflow,
  makeSelectIsExpandNodeData,
  makeSelectMainCreateWorkflow,
  makeSelectMainReducerCreateWorkflow,
  makeSelectOpenModalDestinationTesting,
  makeSelectTriggerNode,
  selectDomainMainCreateWorkflow,
  makeSelectActiveNodeId,
};
