/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import React, { useMemo } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getPrefixCreate } from '../../../Detail/VersionHistory2/Detail/utils';
import { makeSelectJourneyChannelActive } from '../../../selectors';
import HeaderNode from '../../Content/HeaderNode';
import { getStoryRoleActions } from '../../utils.story.rules';
import BlastCampaign from './BlastCampaign';
import {
  makeSelectConfigureMainCreateWorkflow,
  makeSelectMainReducerCreateWorkflow,
} from '../../selectors';
import { isShowFlowChartOnly } from '../../../Detail/utils';
import PreviewCapture from '../../Content/Nodes/Destination/PreviewCapture';
import { updateValue } from '../../../../../../../redux/actions';

const Content = props => {
  const {
    page,
    use = 'setting',
    hasEditRole,
    isViewMode,
    itemTypeId,
    initData,
    initDataMain,
    audienceTypeBlastCampaign,
    onChangeData = () => {},
    isShowDrawer,
    mainCreate,
    mainReducer,
  } = props;
  const { activeRow } = mainReducer;
  const { isCapturing } = mainCreate;

  const roleActions = useMemo(
    () => getStoryRoleActions(activeRow.accepted_actions),
    [activeRow.status, activeRow.accepted_actions],
  );

  const renderBlastCampaign = () => (
    <BlastCampaign
      initData={initData}
      initDataMain={initDataMain}
      onChangeData={onChangeData}
      callback={props.callback}
      moduleConfig={props.moduleConfig}
      roleActions={roleActions}
      activeRow={activeRow}
      validateKey={props.validateKey}
      validateKeyBlast={props.mainReducer.validateKeyBlast}
      errorKey={props.errorKey}
      design={props.design}
      isValidTimeScheduled={props.mainReducer.isValidTimeScheduled}
      audienceTypeBlastCampaign={audienceTypeBlastCampaign}
      // errorSendTo={props.errorSendTo}
      isViewMode={props.isViewMode}
      mainNodesBlast={props.mainNodesBlast}
      activeNode={props.activeNode}
      triggerType={props.triggerType}
      itemTypeId={itemTypeId}
      dataValidateSwitchTabAudiences={
        props.mainReducer.validateSwitchTabsAudiences
      }
      refreshKeyValidateAudiences={
        props.mainReducer.refreshKeyValidateAudiences
      }
      previewForecast={props.mainReducer.previewForecast}
      campaignList={props.campaignList || []}
      listNodes={props.listNodes || []}
      isFirstCampaign={props.isFirstCampaign}
      configure={props.configure}
      key={props.updateRefreshBlastCampaign}
      isLoading={props.isLoading}
    />
  );

  if (isShowFlowChartOnly(page)) {
    return renderBlastCampaign();
  }

  return (
    <>
      <HeaderNode
        isBlastCampaign
        roleActions={roleActions}
        activeRow={props.main.activeRow}
        design={props.main.design}
        moduleConfig={props.moduleConfig}
        isViewMode={props.isViewMode}
        use={use}
        hasEditRole={hasEditRole}
        cacheCopyId={props.cacheCopyId}
        versionId={props.versionId}
        callback={props.callback}
        handleChangeDesign={props.handleChangeDesign}
        onCancel={props.onCancel}
        blastCampaign={props.blastCampaign}
        isHasChange={props.isHasChange}
        isAbleToCheckChange={props.isAbleToCheckChange}
        channelId={props.channelId}
        isJourneyV2={props.isJourneyV2}
        isShowDrawer={isShowDrawer}
        // onChangeZoomInOut={onChangeZoomInOut}
        // zoomSize={zoomSize}
      />
      {renderBlastCampaign()}
      <PreviewCapture
        isCapturing={isCapturing}
        design={props.main.design}
        updateThumbnails={props.updateThumbnails}
        configure={props.configure}
        isBlastCampaign
      />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  mainCreate: makeSelectConfigureMainCreateWorkflow(),
  mainReducer: makeSelectMainReducerCreateWorkflow(),
  channelActive: makeSelectJourneyChannelActive(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig.key;
  return {
    updateThumbnails: thumbnails =>
      dispatch(updateValue(`${prefix}@@STORY_THUMBNAIL@@`, thumbnails)),
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Content);
