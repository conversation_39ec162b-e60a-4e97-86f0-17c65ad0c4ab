import { LIST_TYPE } from '../../../Destination/List/constants';
import { CHANNEL } from '../../constant';
import { NODE_TYPE } from '../Content/Nodes/constant';
import { initDataUIFilter } from './components/UINodeFilter/utils';
import { adjustTimeIfNeeded } from './Content/BlastCampaign/ThirdPartyScheduled/utils';

export const initialStateMainCreate = () => ({
  // action: 'create',
  // design: 'create',
  design: 'preview',
  itemTypeId: 0,
  isLoading: true,
  isDoing: false,
  activeRow: {},
  activeRowClone: {},
  disabled: true,
  actionDisabled: true,
  isCollapse: false,
  dataRunTest: {},
  // isExpand: false,
  // isCloseDataflow: false,
  isLoadingFlowChart: false,
});

export const initialSwitchTabsAudiences = () => ({
  canSwitch: false,
  use: [],
  switchToTab: {},
  itemTypeId: null,
});

const initScheduledThirdParty = () => {
  const initTime = new Date().getTime();
  const { timestamp } = adjustTimeIfNeeded(initTime);

  return {
    value: timestamp,
    catalogCode: '',
    sendMethod: null,
    error: '',
  };
};

const defaultScheduleTrigger = () => ({
  actionType: NODE_TYPE.SCHEDULED,
  endCondition: 'never',
  endDate: null,
  endTime: null,
  endTimeOfDay: null,
  frequency: 'once',
  frequencyCapping: null,
  repeatInterval: 1,
  startDate: '2023-03-27',
  startTime: 1679884920000,
  startTimeOfDay: {
    hour: 10,
    minute: 42,
  },
  triggerType: 'specific_date',
  scheduledThirdParty: initScheduledThirdParty(),
  itemTypeId: null,
  journeyGoals: {},
});

const defaultEventBasedTrigger = () => ({
  actionType: NODE_TYPE.EVENT_BASED,
  endCondition: 'never',
  startDate: '2023-03-27',
  startTimeOfDay: {
    hour: 10,
    minute: 42,
  },
  endDate: null,
  endTimeOfDay: null,
  startTime: 1679884920000,
  endTime: null,
  event: {},
  frequencyCapping: null,
  timeOfWeek: [],
  triggerType: 'all',
  journeyGoals: {},
});

export const initialStateBlastCampaign = (
  triggerType = NODE_TYPE.SCHEDULED,
) => ({
  design: 'preview',
  initDone: false,
  isViewMode: false,
  listNodes: [],
  isLoading: false,
  channelId: '',
  campaigns: [],
  initCampaignNode: null,
  refreshKeyValidateAudiences: 1,
  validateSwitchTabsAudiences: initialSwitchTabsAudiences(),
  previewForecast: {
    isValid: false,
    type: 'pie',
  },
  validateKeyBlast: 1,
  activeNode: {
    nodeId: '',
    value: '',
    channelCode: '',
    catalogCode: '',
    channelId: '',
  },
  isValidTimeScheduled: true,
  canActionValidate: false,
  canTriggerValidateSendTo: false,
  cachesNode: {},
  activeRow: {},
  trigger:
    triggerType === NODE_TYPE.SCHEDULED
      ? defaultScheduleTrigger()
      : defaultEventBasedTrigger(),
  templateDesign: '',
});

export const getInitCampaign = (nodeIdActive, nodeDes = {}, channelId) => {
  return {
    actionId: nodeIdActive,
    campaignId: null,
    campaignName: 'Campaign 1',
    catalogId: nodeDes.catalog_dest_id,
    catalogLabel: nodeDes.catalog_id,
    channelId: nodeDes.channel_code_id || channelId,
    customInputs: {},
    sendAs: nodeDes.destination_id || '',
    variantIds: [],
    variantInfo: [
      {
        contentSetting: {
          destinationInput: {
            content: '',
          },
          objectWidgetInput: {},
          variantExtraData: {},
        },
        custom_inputs: {},
        status: 1,
        variantId: '',
        variantKey: '',
        variantName: 'Variant 1',
      },
    ],

    // SCHEDULE fields
    audiences: {},
    deliveryTimeConfig: {},
    itemTypeId: -1003,

    // EVENT_BASED fields
    filter: initDataUIFilter(),
  };
};

export const getDataFilter = (
  channelId,
  appendColumns = [],
  extraData = {
    is3rdParty: false,
    sendMethod: 'sendCSKH',
  },
) => {
  const { is3rdParty, sendMethod } = extraData || {};
  const isChannelSms = +channelId === CHANNEL.SMS.id;
  const filterAND = [
    {
      type: 1,
      column: 'status',
      data_type: 'number',
      operator: 'matches',
      value: [1],
    },
    {
      type: 1,
      column: 'catalog_status',
      data_type: 'number',
      operator: 'matches',
      value: ['1'],
    },
    {
      column: 'channel_code_id',
      data_type: 'number',
      operator: 'matches',
      value: [channelId],
    },
  ];

  if (isChannelSms && sendMethod) {
    if (is3rdParty) {
      filterAND.push({
        column: 'send_method',
        data_type: 'string',
        operator: 'matches',
        value: [sendMethod],
      });
    } else {
      filterAND.push({
        column: 'send_method',
        data_type: 'string',
        operator: 'not_matches',
        value: [sendMethod],
      });
    }
  }

  return {
    page: 1,
    limit: 1000,
    search: '',
    sort: 'utime',
    sd: 'desc',
    // getListType: LIST_TYPE.OWNER,
    columns: [
      'status',
      'destination_name',
      'catalog_id',
      'channel_id',
      'destination_id',
      'channel_code_id',
      'logo_url',
      'catalog_code',
      'catalog_input',
      ...(appendColumns || []),
    ],
    perf_columns: [],
    filters: {
      OR: [
        {
          AND: filterAND,
        },
      ],
    },
  };
};
