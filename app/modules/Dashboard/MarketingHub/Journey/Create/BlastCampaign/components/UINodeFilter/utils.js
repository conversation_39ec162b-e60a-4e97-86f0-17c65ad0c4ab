/* eslint-disable dot-notation */
/* eslint-disable no-lonely-if */
/* eslint-disable no-empty */
/* eslint-disable prettier/prettier */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-useless-computed-key */
/* eslint-disable no-unneeded-ternary */
import {
	buildValueConditionFromUI,
	validateItemCondition,
} from 'containers/Filters/utils';
import { OrderedMap } from 'immutable';

import { MAP_SEGMENT_VALUES } from 'components/common/UIPerformEvent/constants';
import { toUITargetAudience } from 'components/common/UITargetAudience/utils';
import { toAPITargetAudience } from 'components/common/UITargetSelection/utils';
import TRANSLATE_KEY from 'messages/constant';
import { safeParse } from 'utils/common';
import { STATUS_ITEM_CODE } from 'utils/constants';
export const initDataByTypeFilter = {
  user_attributes: {},
  item_segment: {},
  event_attribute: {},
};
/* eslint-disable no-else-return */
export const DATA_OPTIONS_FILTER = [
  {
    label: 'Filter by audience attribute',
    value: 'user_attributes',
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_ATTR,
  },
  {
    label: 'Filter by audience segment',
    value: 'item_segment',
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_SEGT,
  },
  {
    label: 'Filter by trigger event attribute',
    value: 'event_attribute',
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_EVNT,
  },
];

export const DATA_OPTIONS_FILTER_NO_EVENT_ATTR = [
  {
    label: 'Filter by audience attribute',
    value: 'user_attributes',
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_ATTR,
  },
  {
    label: 'Filter by audience segment',
    value: 'item_segment',
    translateCode: TRANSLATE_KEY._STORY_FILTER_TYPE_SEGT,
  },
];

export const MAP_DATA_OPTIONS_FILTER = {
  user_attributes: DATA_OPTIONS_FILTER[0],
  user_attribute: DATA_OPTIONS_FILTER[0], // API lưu là user_attribute , FE thông tin là user_attributes :(
  customer_attribute: DATA_OPTIONS_FILTER[0],
  item_segment: DATA_OPTIONS_FILTER[1],
  event_attribute: DATA_OPTIONS_FILTER[2],
};

export const initDataUIFilter = () => ({
  filterType: {},
  user_attributes: {},
  item_segment: {},
  event_attribute: {},
  isFetchInfoData: false,
});

const MAP_CONDITION_TYPE = {
  '-1003': 'customer_attribute',
  '-1007': 'user_attribute',
};

function toConditionCompPropAPI(item) {
  if (validateItemCondition(item)) {
    const statusItemCode = safeParse(
      item.get('statusItemCode'),
      STATUS_ITEM_CODE.ACTIVE,
    );
    const objValue = buildValueConditionFromUI(item);

    let tempt = {};
    if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
      tempt = {
        conditionType: MAP_CONDITION_TYPE[item.get('property').itemTypeId],
        // property_name: item.get('property').propertyCode,
        column: item.get('property').propertyCode,
        itemTypeId: item.get('property').itemTypeId,
        dataType: item.get('property').itemDataType,
        operator: item.get('operator').value,
        ...objValue,
      };
    } else {
      tempt = item.get('backup');
      if (tempt !== null && tempt !== undefined) {
        tempt = { ...tempt, ...objValue };
        return tempt;
      }
    }
    return tempt;
  }
  return null;
}

export const toAPIAudienceAttributes = conditions => {
  if (
    Object.keys(safeParse(conditions, {})).length === 0 ||
    conditions.size === 0
  ) {
    return { OR: [{ AND: [] }] };
  }
  const isInit = conditions.first().get('isInit');
  if (isInit === true) {
    return conditions.first().get('backup');
  }

  const rulesOr = { OR: [] };
  conditions.forEach(condition => {
    const ruleAnd = { AND: [] };
    condition.forEach(item => {
      const conditionType = safeParse(item.get('conditionType'), {});
      if (conditionType.value === MAP_SEGMENT_VALUES.comp_attr) {
        const tempt = safeParse(toConditionCompPropAPI(item), null);

        if (tempt !== null) {
          ruleAnd.AND.push(tempt);
        }
      }
    });
    if (ruleAnd.AND.length > 0) {
      rulesOr.OR.push(ruleAnd);
    }
  });

  if (rulesOr.OR.length === 0) {
    return { OR: [{ AND: [] }] };
  }

  return rulesOr;
};

function toConditionEventAttributes(item) {
  if (validateItemCondition(item)) {
    const statusItemCode = safeParse(
      item.get('statusItemCode'),
      STATUS_ITEM_CODE.ACTIVE,
    );
    const objValue = buildValueConditionFromUI(item);

    let tempt = {};
    if (statusItemCode === STATUS_ITEM_CODE.ACTIVE) {
      const itemTypeId =
        item.get('property').itemTypeId === 0
          ? null
          : item.get('property').itemTypeId;

      const metaData = {
        itemTypeId,
        itemTypeName: item.get('property').itemTypeName,
        itemPropertyName: item.get('property').name,
        eventPropertySyntax: item.get('property').propertySyntax,
      };

      tempt = {
        conditionType: 'event_attribute',
        // property_name: item.get('property').propertyCode,
        column: item.get('property').name,
        itemTypeId,
        dataType: item.get('property').itemDataType,
        operator: item.get('operator').value,
        propertySyntax: item.get('property').propertySyntax,
        ...objValue,
        metadata: metaData,
      };
    } else {
      tempt = item.get('backup');
      if (tempt !== null && tempt !== undefined) {
        tempt = { ...tempt, ...objValue };
        return tempt;
      }
    }
    return tempt;
  }
  return null;
}

const toAPIEventAttributes = conditions => {
  // console.log('toAPIEventAttributes', conditions);
  if (Object.keys(safeParse(conditions, {})).length === 0) {
    return { OR: [{ AND: [] }] };
  }
  const isInit = conditions.first().get('isInit');
  if (isInit === true) {
    return conditions.first().get('backup');
  }

  const rulesOr = { OR: [] };
  conditions.forEach(condition => {
    const ruleAnd = { AND: [] };
    condition.forEach(item => {
      const tempt = safeParse(toConditionEventAttributes(item), null);

      if (tempt !== null) {
        ruleAnd.AND.push(tempt);
      }
    });
    if (ruleAnd.AND.length > 0) {
      rulesOr.OR.push(ruleAnd);
    }
  });

  if (rulesOr.OR.length === 0) {
    return { OR: [{ AND: [] }] };
  }

  return rulesOr;
};

const MAP_ITEM_TYPE_ID = {
  '-1003': 'customer_attribute',
  '-1007': 'user_attribute',
};

const wrapperToAPIAudienceAttributes = data => {
  const { itemTypeId = '-1003', rules } = data;
  const dataToAPI = {
    filterType: MAP_ITEM_TYPE_ID[itemTypeId],
    filters: {},
  };

  dataToAPI.filters = toAPIAudienceAttributes(rules);

  return dataToAPI;
};

const wrapperToAPIEventAttributes = data => {
  const { rules } = data;
  const dataToAPI = {
    filterType: 'event_attribute',
    filters: {},
  };

  dataToAPI.filters = toAPIEventAttributes(rules);

  return dataToAPI;
};

const wrapperToAPIAudienceSegment = data => {
  const dataToAPI = {
    filterType: 'item_segment',
    // filters: toAPIAudienceSegment(data),
    // ...toAPITargetAudience(data, 'includes'),
    ...toAPITargetAudience(data),
  };

  return dataToAPI;
};

export const toAPINodeFilter = data => {
  if (Object.keys(safeParse(data, {})).length === 0) {
    return {};
  }

  const filterType = safeParse(
    data.filterType,
    MAP_DATA_OPTIONS_FILTER.user_attributes,
  );

  if (filterType.value === 'user_attributes') {
    return wrapperToAPIAudienceAttributes(data[filterType.value]);
  } else if (filterType.value === 'item_segment') {
    return wrapperToAPIAudienceSegment(data[filterType.value]);
  } else if (filterType.value === 'event_attribute') {
    return wrapperToAPIEventAttributes(data[filterType.value]);
  }
};

export const toUINodeFilter = data => {
  const { filterType } = data;
  const dataToUI = initDataUIFilter();
  dataToUI.filterType = MAP_DATA_OPTIONS_FILTER[filterType];

  if (filterType === 'item_segment') {
    const { itemTypeId } = data;
    dataToUI.itemTypeId = itemTypeId;
    const dataTargetAudience = toUITargetAudience({
      itemTypeId,
      dataFilter: data,
    });
    dataToUI.item_segment = dataTargetAudience;
    // Object.keys(dataTargetAudience).forEach(key => {
    //   dataToUI[key] = dataTargetAudience[key];
    // });
    // dataToUI.filterType = MAP_DATA_OPTIONS_FILTER.item_segment;
  } else if (
    filterType === 'customer_attribute' ||
    filterType === 'user_attribute'
  ) {
    const userAttributes = {};
    userAttributes.itemTypeId =
      filterType === 'customer_attribute' ? '-1003' : '-1007';
    userAttributes.rules = OrderedMap({
      'data-init': OrderedMap({
        backup: data.filters,
        isInit: true,
      }),
    });

    dataToUI.user_attributes = userAttributes;
    dataToUI.isInitUserAttrs = true;
  } else if (filterType === 'event_attribute') {
    const eventAttributes = {};

    eventAttributes.rules = OrderedMap({
      'data-init': OrderedMap({
        backup: data.filters,
        isInit: true,
      }),
    });

    dataToUI.event_attribute = eventAttributes;
  }

  // console.log('dataToUI', { dataToUI, data });

  return dataToUI;
};
