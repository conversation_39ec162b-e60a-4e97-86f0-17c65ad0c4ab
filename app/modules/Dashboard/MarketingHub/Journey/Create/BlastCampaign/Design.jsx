/* eslint-disable no-param-reassign */
/* eslint-disable indent */
/* eslint-disable import/order */
/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
// Libraries
import React, { useMemo, useState, useEffect, useRef } from 'react';
import queryString from 'query-string';
import styled from 'styled-components';
import { connect } from 'react-redux';
import { useHistory, useParams, withRouter } from 'react-router-dom';
import { Map } from 'immutable';
import { compose } from 'redux';
import { cloneDeep, get, isEmpty, isEqual, isObject } from 'lodash';
import { createStructuredSelector } from 'reselect';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import ModalConfirmExit from '../../../../../../containers/modals/ModalConfirmExit';
import CustomHeader from '../../../../../../components/Organisms/CustomHeader';
import { UILoading } from '@xlab-team/ui-components';
import Content from './Content';

// Selectors
import {
  makeSelectJourneyChannelActive,
  makeSelectLoadingOnStepTwo,
} from '../../selectors';
import {
  makeSelectConfigureCreateWorkflow,
  makeSelectMainCreateWorkflow,
  makeSelectMainReducerCreateWorkflow,
} from '../selectors';

// Utils
import { STORY_SETUP_ACTION } from '../utils.story.rules';
import injectReducer, { useInjectReducer } from 'utils/injectReducer';
import { getActiveNode, handleGenDefaultName, isDisableActive } from './utils';
import { getInitCampaign } from './constants';
import { getBreadcrums, getStoryId, renderContentPopupConfirm } from '../utils';
import injectSaga, { useInjectSaga } from 'utils/injectSaga';
import saga from './saga';
import { safeParse, updateUrl } from '../../../../../../utils/common';
import {
  getCurrentAccessUserId,
  getPortalId,
} from '../../../../../../utils/web/cookie';
import {
  init,
  reset,
  update,
  updateValue,
  validate,
} from '../../../../../../redux/actions';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  MENU_CODE,
  checkingRoleScope,
  makeUrlPermisison,
  validateAction,
} from '../../../../../../utils/web/permission';
import reducer from '../reducer';
import { random } from '../../../../../../components/common/UIEditorPersonalization/utils.3rd';
import { CATALOG_CODE } from '../Content/Nodes/constant';

// Configs
import APP from '../../../../../../appConfig';
import { useImmer } from 'use-immer';
import useDebounce from '../../../../../../hooks/useDebounce';
import { makeSelectDashboard } from '../../../../selector';
import { useDeepCompareEffect, usePrevious } from '../../../../../../hooks';
import { Spin } from '@antscorp/antsomi-ui';
import { CHANNEL } from '../../constant';
import { MODULE_CONFIG as MODULE_CONFIG_COMMON } from '../../config';
import { updateNodeData } from '../actions';

const WrapperModalExit = styled.div`
  .modal {
    top: 30% !important;
  }
`;

const initState = () => ({
  targetAudience: {},
  destination: {},
});

const Design = props => {
  const {
    design,
    moduleConfig,
    isViewMode = false,
    main,
    mainReducer = {},
    configure,
    page,
    channelActive = {},
    isShowDrawer,
  } = props;
  const {
    trigger = {},
    initDone = false,
    activeNode = {},
    listNodes,
    initCampaignNode,
    campaigns = [],
    canActionValidate,
    // canTriggerValidateSendTo,
  } = mainReducer;

  const { activeRow = {} } = main;
  const { itemTypeId } = trigger;

  // ref
  const cacheInitDataDone = useRef();
  const timerRefInitDone = useRef();

  const params = useParams();
  const channelId = props.channelId || (channelActive && channelActive.value);
  const history = useHistory();
  const [loading, setIsLoading] = useState(true);
  const [initSettingDone, setInitSettingDone] = useState(false);

  const {
    main: {
      nodes,
      cacheNodes,
      mainNodesBlast,
      validateKey,
      errorKey,
      // sendToError,
      audienceTypeBlastCampaign,
      updateRefreshBlastCampaign,
      amountDestinations = 1,
    },
  } = configure;
  useInjectReducer({
    key: moduleConfig.key,
    reducer: reducer({ ...moduleConfig, channelActive: channelActive.code }),
  });
  useInjectSaga({
    key: moduleConfig.key,
    saga,
    args: {
      moduleConfig: { ...moduleConfig, channelActive: channelActive.code },
    },
  });

  const activeId = props.activeId || params.activeId;
  const cacheCopyId = useRef(null);
  const [disableSaveFirstTime, setDisableSaveFirstTime] = useState(false);

  const hasEditRole = validateAction(
    MENU_CODE.JOURNEY,
    APP_ACTION.UPDATE,
    props.activeRow ? props.activeRow.c_user_id : getCurrentAccessUserId(),
  );

  const numericChannelId = Number(channelId);

  useDeepCompareEffect(() => {
    if (design === 'create') {
      const { copyId } = queryString.parse(window.location.search);
      cacheCopyId.current = copyId;

      props.init({
        design,
        channelId: numericChannelId,
        channelActive: props.channelActive,
        copyId,
        isBlastCampaign: true,
      });

      const interval = setInterval(() => {
        const element = document.getElementById('main-compose');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'end' });
          clearInterval(interval);
        }
      }, 500);
    } else if (design === 'update' || design === 'preview') {
      const { activeRow } = props;
      // const splitUrl = window.location.href.split('?design=');
      const queryDesign = queryString.parse(window.location.search).design;
      let paramDesignUrl = 'preview';
      if (queryDesign) {
        paramDesignUrl = queryDesign;
      }
      // update không có design create
      if (paramDesignUrl === 'create' || props.versionId) {
        paramDesignUrl = 'update';
      }

      if (!hasEditRole) {
        paramDesignUrl = 'preview';
        const newUrl = `${
          APP.PREFIX
        }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
          activeRow.channel_id
        }/list?ui=detail-drawer&journeyId=${activeId}&channelId=${
          activeRow.channel_id
        }&tab=settings&design=preview`;

        updateUrl(makeUrlPermisison(newUrl));
      }
      props.init({
        itemTypeId: activeRow.itemTypeId,
        activeRow,
        design,
        channelId: numericChannelId,
        paramDesignUrl,
        channelActive: props.channelActive,
        rootJourneyDetail: props.rootJourneyDetail,
        blastCampaign: props.blastCampaign,
        isBlastCampaign: true,
      });
    }

    return () => {
      props.reset();
      props.flowUpdate({ cacheNodes: Map({}), mainNodesBlast: Map({}) });
    };
  }, [numericChannelId]);

  const [state, setState] = useImmer(initState());
  const debounceState = useDebounce(state, 500);
  const [isAbleToCheckChange, setIsAbleToCheckChange] = useState(false);
  const [isHasChange, setIsHasChange] = useState(false);
  const timerRef = useRef(null);

  useEffect(() => {
    if (debounceState) {
      Object.keys(debounceState).forEach(key => {
        const isValidObject =
          typeof debounceState[key] === 'object' &&
          Object.keys(debounceState[key]).length > 0;
        const isValidValue =
          typeof debounceState[key] !== 'object' &&
          debounceState[key] !== undefined;

        if (isValidObject || isValidValue) {
          if (key === 'targetAudience') {
            const { initDataDone = false, ...tempData } = debounceState[key];
            cacheInitDataDone.current = initDataDone;

            props.onChangeData({
              nodeId: activeNode.nodeId,
              name: key,
              data: tempData,
              isRealtime: true,
            });
          } else {
            props.onChangeData({
              nodeId: activeNode.nodeId,
              name: key,
              data: debounceState[key],
              isRealtime: true,
            });

            if (key === 'destination') {
              const dataDestination = get(
                debounceState,
                'destination.data',
                {},
              );
              const targetAudience = get(debounceState, 'targetAudience', {});
              const catalogCode = get(
                debounceState,
                'destination.catalogCode',
                '',
              );

              if (
                isEmpty(targetAudience) &&
                isObject(dataDestination) &&
                catalogCode === CATALOG_CODE.LINE_RICH_MENU
              ) {
                const {
                  destinationInput = {},
                  dynamicFields = [],
                } = dataDestination;

                const isUpdateDone = dynamicFields.every(
                  field =>
                    !isEmpty(get(destinationInput, `${field}.value`, '')),
                );
                cacheInitDataDone.current = isUpdateDone;
              }
            }
          }

          if (
            key === 'destination' &&
            !disableSaveFirstTime &&
            cacheInitDataDone.current &&
            main.design !== 'create'
          ) {
            setDisableSaveFirstTime(true);
            setTimeout(() => {
              props.disableOnSaveFirstTime(isDisableActive(mainNodesBlast));
            }, 300);
          } else {
            props.updateActionButtonFirstTime();
          }
        }
      });
      // check is has ready check changing setup
      if (
        !isHasChange &&
        debounceState &&
        debounceState.destination &&
        Object.keys(debounceState.destination).length
      ) {
        clearTimeout(timerRef.current);
        timerRef.current = setTimeout(() => {
          setIsAbleToCheckChange(true);
        }, 1500);
      }
    }
  }, [debounceState]);

  useEffect(
    () => () => {
      setState(draft => initState());
      props.onUpdateAmountDestination({
        amount: 0,
      });
    },
    [activeNode.nodeId],
  );

  const isFirstCampaign = useMemo(() => {
    let result = false;
    if (campaigns.length && activeNode.nodeId === campaigns[0].actionId) {
      result = true;
    }
    return result;
  }, [activeNode, campaigns.length]);

  useEffect(() => {
    // handle show loading to active button activate when mode update or preview
    if (main.design !== 'create') {
      clearTimeout(timerRefInitDone.current);
      timerRefInitDone.current = setTimeout(() => {
        if (!initSettingDone) {
          setInitSettingDone(true);
        }
      }, 10000);
    }

    return () => {
      clearTimeout(timerRefInitDone.current);
    };
  }, [campaigns]);
  useEffect(() => {
    if (initSettingDone) {
      // USE CASE DESIGN !== CREATE
      // update status wwhen init setting detail journey done
      props.initSettingBlastCampaignDone(true);
    }
  }, [initSettingDone]);

  const onChangeData = (name, data) => {
    if (design === 'create' || initDone) {
      switch (name) {
        case 'scheduled':
        case 'frequencyCapping':
        case 'customInput':
        case 'selectTime':
        case 'peformEvent':
        case 'selectDayOfWeek':
        case 'journeyGoals':
          props.onChangeData({
            nodeId: activeNode.nodeId,
            name,
            data,
            isRealtime: design !== 'preview',
            isMainNodeBlast: true,
          });

          if (name === 'scheduled' && canActionValidate) {
            const { triggerType = '', frequencyTime = {} } = data;
            if (triggerType === 'specific_date') {
              props.onValidateScheduledTime(frequencyTime);
            }
            if (triggerType === 'after_activate') {
              props.updateValidTime({ isValid: true });
            }
          }
          break;

        default:
          // props.onChangeData({
          //   nodeId: activeNode.nodeId,
          //   name,
          //   data,
          //   isRealtime: true,
          // });
          if (!isEqual(state[name], data)) {
            setState(draft => {
              draft[name] = data;
            });

            // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
            // if (name === 'targetAudience' && canTriggerValidateSendTo) {
            //   props.onValidateSendTo({
            //     isValidateBeforeAction: false,
            //     data,
            //   });
            // }
            break;
          }
      }
      if (isAbleToCheckChange) {
        setIsHasChange(true);
      }
    }
  };

  const onConfirm = () => {
    props.toggleModal(false);
    props.goToList();
  };

  const onToggleModal = () => {
    props.toggleModal(false);
  };

  const callback = (type, data) => {
    switch (type) {
      case 'SAVE_SEGMENT': {
        // console.log('data ====>', data);
        props.onSave(data);
        break;
      }
      case 'ADD_CAMPAIGN': {
        const newId = random(8);
        const newCampaignName = handleGenDefaultName(campaigns);
        let newInitCampaignNode = null;

        const newNode = {
          ...getInitCampaign(newId, listNodes[0], channelId),
          campaignName: newCampaignName,
        };

        if (initCampaignNode) {
          newInitCampaignNode = initCampaignNode
            .setIn(['destination', 'campaignName'], newCampaignName)
            .setIn(
              ['destination', 'data', 'campaignName', 'value'],
              newCampaignName,
            );
        }

        props.onAddNode({
          key: newId,
          initCampaignNode: newInitCampaignNode,
          isCreateCopy: Boolean(cacheCopyId.current),
        });
        props.onAddCampaign({ data: newNode });
        props.onUpdateAmountDestination({ amount: +amountDestinations + 1 });
        break;
      }

      case 'ON_REMOVE_CAMPAIGN': {
        const newCampaignList = campaigns.filter(
          campaign => campaign.actionId !== data,
        );
        const isSelected = activeNode.nodeId === data;
        if (isSelected) {
          const newActiveNode = getActiveNode(newCampaignList[0], listNodes);
          props.onChangeActiveNode(newActiveNode);
        }
        props.onRemoveNode({ key: data });
        props.onRemoveCampaign(newCampaignList);

        // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
        // props.onRemoveKeyErrorSendTo({
        //   errorKey: data,
        //   message: '',
        //   isEmpty: false,
        // });
        break;
      }
      case 'VALIDATE_CAMPAIGN_BEFORE_ACTION': {
        if (data.type === 'ACTIVE' && data.value.actionId === activeNode.nodeId)
          return;

        props.onLoading(true);

        if (data.type === 'ACTIVE') {
          props.onValidateBeforeAction({ type: 'ACTIVE', data: data.value });
        } else if (data.type === 'DUPLICATE') {
          const indexCampaign = campaigns.findIndex(
            item => item.actionId === data.value.actionId,
          );
          const campaignTemp = cloneDeep(campaigns[indexCampaign]);

          if (isEmpty(campaignTemp) || indexCampaign === -1) return;

          if (campaignTemp.moreInfo) {
            delete campaignTemp.moreInfo;
          }

          const randomId = random(8);
          const newName = `${campaignTemp.campaignName} - Copy`;
          const campaignCloned = {
            ...campaignTemp,
            actionId: randomId,
            campaignName: newName,
          };

          const tempNode = {
            newId: randomId,
            copyId: campaignTemp.actionId,
            campaignActive: campaignTemp,
            newCampaignName: newName,
          };
          const tempCampaign = {
            index: indexCampaign,
            data: campaignCloned,
          };

          props.onValidateBeforeAction({
            type: 'DUPLICATE',
            data: {
              node: tempNode,
              campaign: tempCampaign,
            },
          });
        }
        break;
      }
      case 'UPDATE_ACTIVE_NODE': {
        const newActiveNode = getActiveNode(data, listNodes);
        props.onChangeActiveNode(newActiveNode);
        break;
      }
      case 'ON_CHANGE_ITEM_TYPE_ID': {
        // khi thay đổi itemTypeId cho Campaign đầu tiên, thì sẽ cập nhật các audience của các Campaign phía dưới về dạng init (BlastCampaign)
        if (itemTypeId && itemTypeId !== data.itemTypeId) {
          const listActionId = [...nodes.keys()].filter(
            key => key !== campaigns[0].actionId,
          );
          const initAudience = {
            itemTypeId: data.itemTypeId,
            isInit: false,
            backup: {},
            currentData: {
              includeCluster: [],
              excludeCluster: [],
              mapCluster: {},
            },
          };
          if (listActionId.length) {
            let newCacheNodes = nodes;
            listActionId.forEach(actionId => {
              newCacheNodes = newCacheNodes.setIn(
                [actionId, 'targetAudience'],
                initAudience,
              );
            });

            props.flowUpdate({
              cacheNodes: newCacheNodes,
            });
          }
        }
        props.resetAudienceCampaign(data);
        props.onUpdateAudienceType(data);
        props.onChangeItemTypeId(data);
        break;
      }
      case 'UPDATE_DESIGN_BLAST': {
        if (mainReducer.design !== data) {
          props.onChangeDesignBlast(data);
        }
        break;
      }
      case 'ON_CANCEL': {
        history.push(
          makeUrlPermisison(
            `${
              APP.PREFIX
            }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${channelId}`,
          ),
        );
        break;
      }
      default: {
        break;
      }
    }
  };
  const isLoading = mainReducer && mainReducer.isLoading;
  const roleActions = new Set([STORY_SETUP_ACTION.EDIT_STORY_NAME]);

  const handleChangeDesign = (designMode = 'update') => {
    if (isShowDrawer) {
      const searchParams = new URLSearchParams(window.location.search);

      searchParams.set('design', 'update');

      history.push({ search: searchParams.toString() });
    } else {
      const newUrl = `${
        APP.PREFIX
      }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/journeys/${
        activeRow.channel_id
      }/detail/${activeId}/settings?design=${designMode}`;

      updateUrl(makeUrlPermisison(newUrl));
    }

    props.onChangeDesign(designMode);
  };

  const handleCancel = data => {
    if (props.main.disabled === false) {
      props.toggleModal(data);
    } else {
      props.goToList();
    }
  };

  const breadcrumbs = useMemo(() => {
    if (design !== 'create') return [];

    let listBreadcrumb = getBreadcrums(props.channelActive);

    const { ownerId, accounts, menuCodeActive } = props.dashboard;
    const isHasEveythingJourney = checkingRoleScope(
      menuCodeActive,
      APP_ACTION.CREATE,
      APP_ROLE_SCOPE.EVERYTHING,
    );

    const accountOwner = accounts.map[ownerId.value];
    const ownerName = accountOwner && accountOwner.full_name;

    if (isHasEveythingJourney) {
      listBreadcrumb[0].display = ownerName;
      listBreadcrumb[0].urlPath = {
        first: '',
        last: '',
      };
    } else {
      listBreadcrumb.splice(0, 1);
    }

    if (props.isJourneyV2 || props.isShowDrawer) {
      // chỉ giữ lại journey name
      listBreadcrumb = [...listBreadcrumb].filter(bread => !bread.urlPath);
    }

    return listBreadcrumb;
  }, [props.channelActive.label, props.dashboard.ownerId, design]);

  useEffect(() => {
    const mainContentEle = document.getElementById('main-content');
    if (mainContentEle && mainContentEle.style) {
      mainContentEle.style.backgroundColor = 'rgb(238, 245, 252)';
    }
  }, []);

  useEffect(() => {
    if (activeNode.nodeId) {
      setIsLoading(false);
      if (
        design === 'create' &&
        nodes &&
        nodes.get(activeNode.nodeId) &&
        !initCampaignNode
      ) {
        // sau khi vào trang create sẽ lưu lại 1 bộ data initCampaign dạng node dùng cho việc create campaign mới
        const dataNode = nodes.get(activeNode.nodeId);
        props.getInitCampaignNode({
          design,
          dataNode,
          isCreateCopy: Boolean(cacheCopyId.current),
        });
      }
    }
    return () => {
      setIsLoading(true);
    };
  }, [activeNode.nodeId, nodes]);

  useEffect(() => {
    // use case: khi portal chưa có destinaiton nào để select ở channel web và email thì đang bị ẩn đi và hiện loading
    if (
      design === 'create' &&
      activeNode.nodeId &&
      !activeNode.value &&
      [CHANNEL.WEB_PERSONALIZATION.id, CHANNEL.EMAIL.id].includes(+channelId) &&
      props.loadingOnStepTwo
    ) {
      props.updateLoadingOnStepTwo(false);
    }
  }, [props.loadingOnStepTwo, activeNode.nodeId]);

  const initData = safeParse(cacheNodes.get(activeNode.nodeId), Map({}));
  // console.log('insideee.dev - initData :>', initData);

  const initDataMain = safeParse(mainNodesBlast, Map({}));
  return (
    <>
      {!mainReducer ? (
        <UILoading isLoading />
      ) : (
        <ErrorBoundary>
          <Spin
            spinning={
              !['create', 'preview'].includes(main.design) && !props.versionId
                ? !initSettingDone
                : false
            }
            // style={{ height: '100vh' }}
          >
            <UILoading isLoading={loading} />
            {!loading && (
              <Content
                page={page}
                use={props.use}
                main={main}
                configure={configure}
                blastCampaign={props.blastCampaign}
                hasEditRole={hasEditRole}
                isLoading={isLoading}
                callback={callback}
                onChangeData={onChangeData}
                moduleConfig={moduleConfig}
                isViewMode={isViewMode}
                initData={initData}
                validateKey={validateKey}
                errorKey={errorKey}
                // errorSendTo={sendToError}
                audienceTypeBlastCampaign={audienceTypeBlastCampaign}
                cacheCopyId={cacheCopyId}
                initDataMain={initDataMain}
                activeNode={activeNode}
                triggerType={trigger.actionType}
                itemTypeId={itemTypeId}
                campaignList={campaigns}
                mainNodesBlast={mainNodesBlast}
                listNodes={listNodes}
                handleChangeDesign={handleChangeDesign}
                onCancel={handleCancel}
                design={design}
                isFirstCampaign={isFirstCampaign}
                updateRefreshBlastCampaign={updateRefreshBlastCampaign}
                isHasChange={isHasChange}
                isAbleToCheckChange={isAbleToCheckChange}
                isJourneyV2={props.isJourneyV2}
                activeId={activeId}
                isShowDrawer={isShowDrawer}
                channelId={channelId}
              />
            )}
          </Spin>
          <WrapperModalExit className="wrapper-modal-exit">
            <ModalConfirmExit
              isOpen={configure.main.isOpenModal}
              toggle={onToggleModal}
              design={props.design}
              showDiscard={getStoryId(props.main, props.design) > 0}
              title={renderContentPopupConfirm(props.main, props.design).title}
              onConfirm={onConfirm}
            >
              {renderContentPopupConfirm(props.main, props.design).content}
            </ModalConfirmExit>
          </WrapperModalExit>

          {/* <ModalSaveAsTemplate
            mode={JOURNEY_TEMPLATE_MODE.Save}
            moduleConfig={moduleConfig}
          /> */}
        </ErrorBoundary>
      )}
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  configure: makeSelectConfigureCreateWorkflow(),
  channelActive: makeSelectJourneyChannelActive(),
  main: makeSelectMainCreateWorkflow(),
  mainReducer: makeSelectMainReducerCreateWorkflow(),
  dashboard: makeSelectDashboard(),
  loadingOnStepTwo: makeSelectLoadingOnStepTwo(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig.key;
  return {
    init: params => {
      dispatch(init(prefix, params));
    },
    flowUpdate: params => {
      dispatch(init(`${prefix}@@FLOW_UPDATE@@`, params));
    },
    reset: params => {
      dispatch(reset(prefix, params));
    },
    updateTriggerSchedule: params => {
      dispatch(updateValue(`${prefix}@@TRIGGER_SCHEDULE@@`, params));
    },
    updateValueDestination: params => {
      dispatch(updateValue(`${prefix}@@UPDATE_VALUE_DESTINATION@@`, params));
    },
    onChangeData: (value = {}) =>
      dispatch(updateNodeData({ prefix, ...value })),
    onSave: data => dispatch(update(prefix, data)),
    onChangeActiveNode: value =>
      dispatch(updateValue(`${prefix}@@ACTIVE_NODE@@`, value)),
    onDuplicateCampaign: data => {
      dispatch(updateValue(`${prefix}@@DUPLICATE_CAMPAIGN@@`, data));
    },
    onDuplicateNode: data => {
      dispatch(updateValue(`${prefix}@@DUPLICATE_NODE@@`, data));
    },
    onRemoveCampaign: data => {
      dispatch(updateValue(`${prefix}@@REMOVE_CAMPAIGN@@`, data));
    },
    onRemoveNode: data => {
      dispatch(updateValue(`${prefix}@@REMOVE_NODE@@`, data));
    },
    onAddCampaign: data => {
      dispatch(updateValue(`${prefix}@@ADD_CAMPAIGN@@`, data));
    },
    onAddNode: data => {
      dispatch(updateValue(`${prefix}@@ADD_NODE@@`, data));
    },
    onValidateScheduledTime: data => {
      dispatch(validate(`${prefix}@@VALIDATE_SCHEDULED_TIME@@`, data));
    },
    updateValidTime: data => {
      dispatch(updateValue(`${prefix}@@UPDATE_VALID_TIME_SCHEDULED@@`, data));
    },
    // onRemoveKeyErrorSendTo: data => {
    //   dispatch(updateValue(`${prefix}@@SEND_TO_ERROR@@`, data));
    // },
    // onValidateSendTo: data => {
    //   dispatch(validate(`${prefix}@@SEND_TO@@`, data));
    // },
    onValidateBeforeAction: data => {
      dispatch(validate(prefix, data));
    },
    onChangeDesign: value =>
      dispatch(updateValue(`${prefix}@@DESIGN@@`, value)),
    toggleModal: data => dispatch(updateValue(`${prefix}@@TOGGLE_MODAL`, data)),
    goToList: () => dispatch(updateValue(`${prefix}@@GO_TO_LIST`)),
    onChangeItemTypeId: data => {
      dispatch(updateValue(`${prefix}@@CHANGE_ITEM_TYPE_ID@@`, data));
    },
    getInitCampaignNode: data => {
      dispatch(updateValue(`${prefix}@@GET_INIT_CAMPAIGN_NODE@@`, data));
    },
    onUpdateAudienceType: data => {
      dispatch(
        updateValue(`${prefix}@@UPDATE_AUDIENCE_TYPE_BLAST_CAMPAIGN@@`, data),
      );
    },
    disableOnSaveFirstTime: data => {
      dispatch(updateValue(`${prefix}@@DISABLED_HEADER_NODE@@`, data));
    },
    updateActionButtonFirstTime: data => {
      dispatch(updateValue(`${prefix}@@UPDATE_ACTION_BUTTON@@`, data));
    },
    onChangeDesignBlast: value =>
      dispatch(updateValue(`${prefix}@@DESIGN_BLAST@@`, value)),
    onLoading: value => dispatch(updateValue(`${prefix}@@LOADING@@`, value)),
    onUpdateAmountDestination: value =>
      dispatch(updateValue(`${prefix}@@UPDATE_AMOUNT_DESTINATIONS@@`, value)),
    resetAudienceCampaign: value =>
      dispatch(updateValue(`${prefix}@@RESET_AUDIENCE_CAMPAIGN@@`, value)),
    initSettingBlastCampaignDone: value =>
      dispatch(
        updateValue(`${prefix}@@INIT_SETTING_BLASTCAMPAIGN_DONE@@`, value),
      ),
    //   onChangeName: value =>
    //     dispatch(updateValue(`${prefix}@@SEGMENT_NAME@@`, value)),
    //   onValidate: data => dispatch(updateValue(`${prefix}@@VALIDATE`, data)),
    //   toggleModal: data => dispatch(updateValue(`${prefix}@@TOGGLE_MODAL`, data)),
    //   goToList: () => dispatch(updateValue(`${prefix}@@GO_TO_LIST`)),
    //   onChangeStoryName: value =>
    //     dispatch(updateValue(`${prefix}@@STORY_NAME@@`, value)),
    //   onChangeNameDesignUpdate: value =>
    //     dispatch(updateValue(`${prefix}@@STORY_NAME_DESIGN_UPDATE@@`, value)),
    // };
    updateLoadingOnStepTwo: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG_COMMON.key}@@LOADING_ON_STEP_TWO`, params),
      );
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withConnect,
  withRouter,
)(Design);
