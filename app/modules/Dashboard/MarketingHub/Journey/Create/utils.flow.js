/* eslint-disable no-restricted-syntax */
/* eslint-disable no-plusplus */
/* eslint-disable no-lonely-if */
/* eslint-disable no-else-return */
import { translate, translations } from '@antscorp/antsomi-locales';
import produce from 'immer';
import {
  cloneDeep,
  difference,
  differenceWith,
  findLastIndex,
  keyBy,
} from 'lodash';
import { generateKey } from '../../../../../utils/common';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { NODE_ICON, NODE_TYPE } from './Content/Nodes/constant';

export const MAP_BRANCH_ARRAY = {
  [NODE_TYPE.CLASSIC_LIST_BRANCH]: [
    {
      value: NODE_TYPE.CONDITION_YES,
      label: translate(translations._NODE_IF_THEN_TITL_YES),
    },
    {
      value: NODE_TYPE.CONDITION_NO,
      label: translate(translations._NODE_IF_THEN_TITL_NO),
    },
  ],
  [NODE_TYPE.PARALLEL_LIST_BRANCH]: [
    {
      value: NODE_TYPE.CONDITION_YES,
      label: translate(translations._NODE_IF_THEN_TITL_YES),
    },
    {
      value: NODE_TYPE.CONDITION_NO,
      label: translate(translations._NODE_IF_THEN_TITL_NO),
    },
  ],
  [NODE_TYPE.SPLIT_BRANCH]: [
    {
      value: 'BRANCH_1',
      label: 'Branch 1',
    },
    {
      value: 'BRANCH_2',
      label: 'Branch 2',
    },
  ],
  [NODE_TYPE.WAIT_EVENT]: [
    {
      value: NODE_TYPE.WAIT_EVENT_HAPPENED,
      label: translate(translations._NODE_EVENT_HAPPENED),
    },
    {
      value: NODE_TYPE.WAIT_EVENT_TIMEOUT,
      label: translate(translations._NODE_WAITING_TIMEOUT),
    },
  ],
  [NODE_TYPE.WFR_BRANCH]: [
    {
      value: NODE_TYPE.WFR_NODE,
      label: translate(translations._NODE_IF_THEN_TITL_YES),
    },
    {
      value: NODE_TYPE.WFR_NODE_NO,
      label: translate(translations._NODE_IF_THEN_TITL_NO),
    },
  ],
};

export function getBranchArrayWhenCreateMiddle(nodeType) {
  return MAP_BRANCH_ARRAY[nodeType] || [];
}

export function getBranchWhenCreateNodeCondition(currentNode) {
  const parentNodeIfThen = currentNode.nodeId;

  return [
    {
      icon: NODE_ICON[NODE_TYPE.CONDITION_YES],
      label: translate('_NODE_IF_THEN_TITL_YES', 'Yes'),
      nodeId: generateKey(),
      parentId: parentNodeIfThen,
      active: false,
      type: NODE_TYPE.CONDITION_YES,
    },
    {
      icon: NODE_ICON[NODE_TYPE.CONDITION_NO],
      label: translate('_NODE_IF_THEN_TITL_NO', 'No'),
      nodeId: generateKey(),
      parentId: parentNodeIfThen,
      active: false,
      type: NODE_TYPE.CONDITION_NO,
    },
    {
      label: currentNode.label,
      icon: currentNode.icon,
      nodeId: parentNodeIfThen,
      parentId: currentNode.parentId,
      active: true,
      type: currentNode.type,
    },
  ];
}

export function getBranchWhenCreateNodeWaitForEvent(currentNode) {
  const parentNodeIfThen = currentNode.nodeId;
  return [
    {
      icon: NODE_ICON[NODE_TYPE.WAIT_EVENT_HAPPENED],
      label: translate('_NODE_EVENT_HAPPENED', 'Event happened'),
      nodeId: generateKey(),
      parentId: parentNodeIfThen,
      active: false,
      type: NODE_TYPE.WAIT_EVENT_HAPPENED,
      // info: currentNode.info,
    },
    {
      icon: NODE_ICON[NODE_TYPE.WAIT_EVENT_TIMEOUT],
      label: translate('_NODE_WAITING_TIMEOUT', 'Waiting timeout'),
      nodeId: generateKey(),
      parentId: parentNodeIfThen,
      active: false,
      type: NODE_TYPE.WAIT_EVENT_TIMEOUT,
    },
    {
      label: currentNode.label,
      icon: NODE_ICON[NODE_TYPE.WAIT_EVENT],
      nodeId: parentNodeIfThen,
      parentId: currentNode.parentId,
      active: true,
      type: NODE_TYPE.WAIT_EVENT,
    },
  ];
}

export function getBranchWhenCreateNodeSplit(currentNode) {
  const parentNodeIfThen = currentNode.nodeId;

  return [
    {
      icon: NODE_ICON[NODE_TYPE.SPLIT_NODE],
      label: translate(translations._TITL_GROUP, 'Group', {
        number_group: 1,
      }),
      nodeId: generateKey(),
      parentId: parentNodeIfThen,
      active: false,
      type: NODE_TYPE.SPLIT_NODE,
      // info: currentNode.info,
    },
    {
      icon: NODE_ICON[NODE_TYPE.SPLIT_NODE],
      label: translate(translations._TITL_GROUP, 'Group', {
        number_group: 2,
      }),
      nodeId: generateKey(),
      parentId: parentNodeIfThen,
      active: false,
      type: NODE_TYPE.SPLIT_NODE,
      // info: node.info,
    },
    {
      label: currentNode.label,
      icon: NODE_ICON[NODE_TYPE.SPLIT_BRANCH],
      nodeId: parentNodeIfThen,
      parentId: currentNode.parentId,
      active: true,
      type: NODE_TYPE.SPLIT_BRANCH,
      // info: node.info,
    },
  ];
}

export function getBranchWhenCreateNodeWFR(currentNode) {
  const parentNodeIfThen = currentNode.nodeId;
  return [
    {
      icon: 'icon-antsomi-check',
      label: translate(translations._NODE_IF_THEN_TITL_YES),
      nodeId: generateKey(),
      parentId: parentNodeIfThen,
      active: false,
      type: NODE_TYPE.WFR_NODE,
    },
    {
      icon: 'icon-antsomi-close',
      label: translate(translations._NODE_IF_THEN_TITL_NO),
      nodeId: generateKey(),
      parentId: parentNodeIfThen,
      active: false,
      type: NODE_TYPE.WFR_NODE_NO,
    },
    {
      label: currentNode.label,
      icon: NODE_ICON[NODE_TYPE.WFR_BRANCH],
      nodeId: parentNodeIfThen,
      parentId: currentNode.parentId,
      active: true,
      type: NODE_TYPE.WFR_BRANCH,
    },
  ];
}

export function insertAt(array, index, ...elementsArray) {
  return array.splice(index, 0, ...elementsArray);
}

export function buildFlattenNodeWithNumberBranch({
  flattenNodes,
  activeNode,
  numberOfBranch,
  newBranchNodes = [
    {
      label: translate(translations._NODE_IF_THEN_TITL_YES, 'Yes'),
      icon: NODE_ICON[NODE_TYPE.CONDITION_YES],
      parentId: activeNode.nodeId,
      active: false,
      type: NODE_TYPE.CONDITION_YES,
    },
  ],
}) {
  const newFlattenNodes = cloneDeep(flattenNodes);

  const listChildren = [];

  newFlattenNodes.forEach(item => {
    if (item.parentId === activeNode.nodeId) listChildren.push(item);
  });

  if (numberOfBranch === listChildren.length) {
    return null;
  }

  // add thêm
  if (numberOfBranch > listChildren.length) {
    const lastChildrenIndex = findLastIndex(
      newFlattenNodes,
      item => item.parentId === activeNode.nodeId,
    );

    const arrayWillAdd = [];

    for (let i = 0; i < numberOfBranch - listChildren.length; i++) {
      const newNodes = newBranchNodes.map(node => ({
        ...node,
        nodeId: generateKey(),
      }));

      arrayWillAdd.push(...newNodes);
    }

    insertAt(newFlattenNodes, lastChildrenIndex, ...arrayWillAdd);

    return newFlattenNodes;
  }

  // remove
  const removedAmount = listChildren.length - numberOfBranch;

  const removedNodeIds = listChildren
    .slice(listChildren.length - removedAmount - 1, listChildren.length - 1)
    .map(({ nodeId }) => nodeId);

  return differenceWith(
    newFlattenNodes,
    removedNodeIds,
    ({ nodeId }, removedId) => nodeId === removedId,
  );
}

export function buildFlattenNodeWithNumberBranchSplit({
  flattenNodes,
  activeNode,
  branches,
  oldBranches,
  id,
}) {
  const numberOfBranch = branches.length;
  const listChildren = [];

  flattenNodes.forEach(item => {
    if (item.parentId === activeNode.nodeId) listChildren.push(item);
  });

  if (numberOfBranch === listChildren.length) {
    return null;
  }

  // add thêm
  if (numberOfBranch > listChildren.length) {
    const arrayWillAdd = [];
    const isAddControlGroup =
      branches.find(branch => branch.id === id).isControlGroup || false;
    // console.log(branches);

    if (isAddControlGroup) {
      arrayWillAdd.push({
        label: translate(translations._TITL_CONTROL_GROUP, 'Control Group'),
        icon: NODE_ICON[NODE_TYPE.AB_SPLIT_CONTROL_NODE],
        // nodeId: generateKey(),
        nodeId: id,
        parentId: activeNode.nodeId,
        active: false,
        type: NODE_TYPE.AB_SPLIT_CONTROL_NODE,
        isDisableActionBranch: true,
        isControlGroup: true,
      });
    } else {
      for (let i = 0; i < numberOfBranch - listChildren.length; i++) {
        arrayWillAdd.push({
          label: translate(translations._TITL_GROUP, 'Group', {
            number_group: listChildren.length + i + 1,
          }),
          icon: NODE_ICON[NODE_TYPE.SPLIT_NODE],
          // nodeId: generateKey(),
          nodeId: id,
          parentId: activeNode.nodeId,
          active: false,
          type: NODE_TYPE.SPLIT_NODE,
        });
      }
    }

    flattenNodes.push(...arrayWillAdd);

    // đưa control group node về nhánh dưới cùng
    if (flattenNodes.some(el => el.isDisableActionBranch)) {
      flattenNodes.sort((el1, el2) => {
        if (el1.isDisableActionBranch && !el2.isDisableActionBranch) {
          return 1;
        } else if (!el1.isDisableActionBranch && el2.isDisableActionBranch) {
          return -1;
        } else {
          return 0;
        }
      });
    }

    return flattenNodes;
  }

  // remove
  if (numberOfBranch < oldBranches.length) {
    const nodeIdWillRemove = [];

    oldBranches.forEach((obj, index) => {
      if (branches.find(obj2 => obj2.id === obj.id) === undefined) {
        if (listChildren[index]) {
          nodeIdWillRemove.push(listChildren[index].nodeId);
        }
      }
    });

    return flattenNodes.filter(
      item => nodeIdWillRemove.includes(item.nodeId) === false,
    );
  }

  return null;
}

function internalFindNodeRelate(nodes, parentId) {
  const relativeNode = nodes.filter(item => item.parentId === parentId);
  const arr = [];
  relativeNode.forEach(node => {
    arr.push(node);
    const tmp = internalFindNodeRelate(nodes, node.nodeId);
    if (tmp.length > 0) {
      arr.push(...internalFindNodeRelate(nodes, node.nodeId));
    }
  });
  return arr;
}

export function removeAllAfterNodeBranch(nodes, parentNode) {
  const nodeWillRemove = internalFindNodeRelate(nodes, parentNode.nodeId);
  const mapWillRemove = { [parentNode.nodeId]: true };
  nodeWillRemove.forEach(item => {
    mapWillRemove[item.nodeId] = true;
  });
  const array = nodes.filter(item => mapWillRemove[item.nodeId] === undefined);
  return array;
}

/**
 * Moves a branch node and its children to a new parent node in a tree structure while maintaining relationships.
 * This function handles the complex logic of:
 * 1. Preserving the moved node and its children
 * 2. Removing the old parent node and its unrelated children
 * 3. Updating parent relationships for the moved nodes
 *
 * @param {Array<Object>} nodes - Array of all nodes in the tree structure
 * @param {Object} parentNode - The parent node where the branch will be moved to
 * @param {Object} nodeWillMove - The node that will be moved along with its children
 * @returns {Array<Object>} - Updated array of nodes with the moved branch in its new position
 *
 * @example
 * const nodes = [
 *   { nodeId: '1', parentId: null },
 *   { nodeId: '2', parentId: '1' },
 *   { nodeId: '3', parentId: '2' }
 * ];
 * const result = moveNodeBranch(nodes, { nodeId: '1' }, { nodeId: '3' });
 */
export function moveNodeBranch(nodes, parentNode, nodeWillMove) {
  const movedNodeIds = internalFindNodeRelate(nodes, nodeWillMove.nodeId).map(
    ({ nodeId }) => nodeId,
  );

  let removedIds = internalFindNodeRelate(nodes, parentNode.nodeId).map(
    ({ nodeId }) => nodeId,
  );

  removedIds = [...difference(removedIds, movedNodeIds), parentNode.nodeId];

  let updatedNodes = differenceWith(
    nodes,
    removedIds,
    ({ nodeId }, removedId) => nodeId === removedId,
  );

  updatedNodes = updatedNodes.map(node =>
    produce(node, draft => {
      if (node.parentId === nodeWillMove.nodeId) {
        draft.parentId = parentNode.parentId;
      }
    }),
  );

  return updatedNodes;
}

export function getBranchsByNode(currentNode) {
  const { type: typeNode } = currentNode;

  switch (typeNode) {
    case NODE_TYPE.CLASSIC_LIST_BRANCH:
    case NODE_TYPE.PARALLEL_LIST_BRANCH:
      return getBranchWhenCreateNodeCondition(currentNode);

    case NODE_TYPE.SPLIT_BRANCH:
      return getBranchWhenCreateNodeSplit(currentNode);

    case NODE_TYPE.WAIT_EVENT:
      return getBranchWhenCreateNodeWaitForEvent(currentNode);

    case NODE_TYPE.WFR_BRANCH:
      return getBranchWhenCreateNodeWFR(currentNode);

    default:
      return [];
  }
}

export function createNodeAndRemoveAllAfterNodeBranch(nodes, currentNode) {
  const nodeWillRemove = internalFindNodeRelate(nodes, currentNode.parentId);
  const mapWillRemove = {};
  nodeWillRemove.forEach(item => {
    mapWillRemove[item.nodeId] = true;
  });

  const array = nodes.filter(item => mapWillRemove[item.nodeId] === undefined);
  const branchs = getBranchsByNode(currentNode);

  array.push(...branchs);

  return array;
}

export function createNodeAndMoveAfterNodeBranch(
  nodes,
  currentNode,
  valueBranch = {},
) {
  // detect node Type and generate branch by type
  const branchNodes = getBranchsByNode(currentNode);

  const newNodes = [];

  if (branchNodes.length === 0) {
    return newNodes;
  }

  nodes.forEach(node => {
    const updatedNode = { ...node };

    if (node.parentId === currentNode.parentId) {
      newNodes.push(...branchNodes);

      Object.entries(MAP_BRANCH_ARRAY).forEach(
        ([parentType, defaultBranchOpts]) => {
          if (parentType !== currentNode.type) {
            return;
          }

          defaultBranchOpts.forEach((branch, idx) => {
            if (branch.value === valueBranch.value) {
              updatedNode.parentId = branchNodes[idx].nodeId;
            }
          });
        },
      );
    }

    newNodes.push(updatedNode);
  });

  return newNodes;
}

/**
 * Tìm đường đi từ một node đến node gốc trong cấu trúc tree
 * @param {Array} nodes - Mảng các node trong tree
 * @param {string} nodeId - ID của node bắt đầu
 * @returns {Array} - Mảng các node trên đường đi từ node gốc đến node cụ thể
 */
export function findPathToRoot(nodes, nodeId) {
  const nodeMap = {};

  for (const node of nodes) {
    nodeMap[node.nodeId] = node;
  }

  const path = [];

  let currentNodeId = nodeId;

  while (currentNodeId) {
    const currentNode = nodeMap[currentNodeId];

    if (!currentNode) {
      // eslint-disable-next-line no-console
      console.error(`Node với ID ${currentNodeId} không tồn tại`);
      break;
    }

    path.push(currentNode);

    currentNodeId = currentNode.parentId;
  }

  return path.reverse();
}

/**
 * Tìm tất cả các node con (bao gồm các node con của con) từ một node cụ thể
 * @param {Array} nodes - Mảng các node trong tree
 * @param {string} nodeId - ID của node bắt đầu
 * @returns {Array} - Mảng các node con (bao gồm các node con của con)
 */
export function findAllChildren(nodes, nodeId) {
  const childrenMap = {};

  for (const node of nodes) {
    if (node.parentId !== null) {
      if (!childrenMap[node.parentId]) {
        childrenMap[node.parentId] = [];
      }
      childrenMap[node.parentId].push(node);
    }
  }

  const allChildren = [];

  function traverseChildren(currentNodeId) {
    const children = childrenMap[currentNodeId] || [];

    for (const child of children) {
      allChildren.push(child);

      traverseChildren(child.nodeId);
    }
  }

  traverseChildren(nodeId);

  return allChildren;
}

/**
 * Tìm node (destination) gần nhất trong đường đi từ node hiện tại đến gốc
 *
 * @param {Array} allNodes - Mảng chứa tất cả các node trong cây
 * @param {string} startNodeId - ID của node bắt đầu tìm kiếm
 * @returns {string|null} - Catalog code của node đích gần nhất, hoặc null nếu không tìm thấy
 */
export const findNearestParentDestinationNode = (allNodes, startNodeId) => {
  return (
    findPathToRoot(allNodes, startNodeId)
      .reverse()
      .find(node => node.type === NODE_TYPE.DESTINATION) || null
  );
};

export const shouldResetWFRFiltersAfterCreateNode = params => {
  try {
    const { flattenNodes = [], createdNodeId } = params;
    const nodeById = keyBy(flattenNodes, 'nodeId');

    const createdNode = nodeById[createdNodeId];
    const parentNode = nodeById[createdNode.parentId];
    const childNode = flattenNodes.find(
      ({ parentId }) => parentId === createdNode?.nodeId,
    );

    if (!createdNode || !childNode || !parentNode) return false;

    return (
      childNode.type === NODE_TYPE.WFR_BRANCH &&
      createdNode.catalogCode !== parentNode.catalogCode
    );
  } catch (error) {
    addMessageToQueue({
      args: params,
      func: 'shouldResetWFRFiltersAfterCreateNode',
    });

    return false;
  }
};
