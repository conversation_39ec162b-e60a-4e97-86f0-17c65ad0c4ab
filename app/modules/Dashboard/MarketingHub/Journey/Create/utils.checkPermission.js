// Libraries
import { cloneDeep } from 'lodash';
import {
  acceptablePatternChecking,
  DEFAULT_ACCEPT_TAGS,
  getCachedRegex,
  patternHandlers,
  PERSONALIZE_PTN,
  SHORT_LINK_V2,
} from '@antscorp/antsomi-ui';
import { translate, translations } from '@antscorp/antsomi-locales';

// Constants
import { DATA_ACCESS_OBJECT } from '../../../../../utils/constants';

// Sevices
import PermServices from 'services/Operate';
import LinkManagementServices from 'services/LinkManagement';
import { PROMOTION_CODE } from '../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/constants';

/**
 * Get permission tree nodes
 *
 * @param {Object} param0
 * @param {array} param0.treeNodes - Tree nodes
 * @param {array} param0.nodes - Nodes
 * @param {boolean} param0.isBlastCampaign - Is blast campaign
 * @param {Object} param0.nodeMap - nodeMap
 * @returns {Promise} - Object info
 */
export const getPermissionTreeNodes = async ({
  treeNodes,
  nodes,
  isBlastCampaign,
  nodeMap,
}) => {
  /** Get node settings */
  const [originSettings, nodeIds] = getNodeSettings({
    treeNodes,
    isBlastCampaign,
    nodeMap,
  });
  /** Get variants */
  const variants = getVariants({
    nodes,
    originSettings,
    isBlastCampaign,
    nodeIds,
  });

  /** Get tags Promotion */
  const { tags: tagPromotions, mapNodeTags: mapNodeTagPromotions } =
    variants.length > 0
      ? getAllTagOfVariants(variants, 'promotion_code', 'attributeName')
      : getAllTagOfVariants(
          Object.values(originSettings),
          'promotion_code',
          'attributeName',
        );
  const uniquePromotionTag = uniqueTags(tagPromotions, 'attributeName');
  const permInfoPromotions = await getObjectPermissionInfo(uniquePromotionTag);

  /** Get tags ShortLinks V2 */
  const { tags: tagShortLinks, mapNodeTags: mapNodeTagShortLinks } =
    variants.length > 0
      ? getAllTagOfVariants(variants, SHORT_LINK_V2, 'shortener')
      : getAllTagOfVariants(
          Object.values(originSettings),
          'shortlink_v2',
          'shortener',
        );
  const uniqueShortLinkTag = uniqueTags(tagShortLinks, 'shortener');
  const permInfoShortLinks = await getPermInfoShortLinks(uniqueShortLinkTag);

  /** Update mapErrorAttributes */
  const mapErrorAttributes = {
    [PROMOTION_CODE]: permInfoPromotions,
    [SHORT_LINK_V2]: permInfoShortLinks,
  };

  /** Update mapNodeError */
  const mapNodeErrorPromotions = getMapNodeErrorPromotions(
    mapNodeTagPromotions,
    permInfoPromotions,
  );
  const mapNodeErrorShortLinks = getMapNodeErrorShortLinks(
    mapNodeTagShortLinks,
    permInfoShortLinks,
  );
  const mapNodeError = combineMapNodeError([
    mapNodeErrorPromotions,
    mapNodeErrorShortLinks,
  ]);

  return { mapErrorAttributes, mapNodeError };
};

/**
 * Retrieves node settings and IDs from a given set of tree nodes.
 *
 * This function processes tree nodes to extract settings and IDs for nodes of type 'DESTINATION'.
 * It supports both regular and blast campaign nodes, collecting settings and IDs in separate arrays.
 *
 * @param {Object} param0 - The input parameters.
 * @param {Array} param0.treeNodes - The tree nodes to process.
 * @param {boolean} param0.isBlastCampaign - Flag indicating if the nodes belong to a blast campaign.
 * @returns {Array} - An array containing two elements: node settings and node IDs.
 */
const getNodeSettings = ({ treeNodes, isBlastCampaign, nodeMap }) => {
  const nodeSettings = {};
  const nodeIds = [];

  const readNodes = nodes => {
    if (!Array.isArray(nodes)) return;

    nodes.forEach(node => {
      if (
        node &&
        (node?.type === 'DESTINATION' || node?.actionType === 'DESTINATION')
      ) {
        nodeIds.push(node?.nodeId || node?.actionId);
        // arrNodeId.current.push(node.nodeId);

        if (node?.metadata?.variantInfo) {
          node.metadata.variantInfo.forEach(variant => {
            nodeSettings[variant.variant_id] = {
              nodeId: node?.nodeId || node?.actionId,
              variantId: +variant?.variant_id,
              campaignId: +node?.metadata?.campaignId,
              destinationInput: variant?.content_setting?.destinationInput,
              variantExtraData: variant?.content_setting?.variantExtraData,
              catalogCode:
                node?.catalogCode ||
                nodeMap?.[node?.metadata?.catalogId]?.catalogCode,
              channelCode:
                node?.channelCode ||
                nodeMap?.[node?.metadata?.catalogId]?.channelCode,
              channelId: node?.metadata?.channelId,
              catalogId: node?.metadata?.catalogId,
              destinationId: node?.metadata?.destinationId,
              // order: Object.keys(nodeSettings).length,
            };

            if (node?.channelCode === 'smart_inbox') {
              // fetchDestinationInfo(node.metadata.destinationId);
            }
          });
        }
      }
      readNodes(node?.branchs);
    });
  };
  readNodes(treeNodes);

  return [nodeSettings, nodeIds];
};

/**
 * Get variants of nodes.
 *
 * @param {Object} param0 - Parameters
 * @param {array} param0.nodes - Nodes
 * @param {Object} param0.originSettings - Origin settings
 * @param {boolean} param0.isBlastCampaign - Is blast campaign
 * @param {Object} param0.configure - Configure
 * @param {array} param0.nodeIds - Node ids
 * @returns {array} - Variants
 */
const getVariants = ({ nodes, originSettings, isBlastCampaign, nodeIds }) => {
  const arrVariants = [];

  if (isBlastCampaign) {
    // nodesOrderRef.current = [];
    nodes.forEach((node, id) => {
      const dest = node.get('destination') || {};
      if (!dest || !dest?.variants || !dest?.variants?.cacheInfo) return;

      // nodesOrderRef.current.push(id);
      Object.entries(dest?.variants?.cacheInfo).forEach(
        ([variantId, variant]) => {
          arrVariants.push({
            nodeId: id || originSettings?.[id]?.nodeId,
            variantId: variantId || originSettings?.[id]?.variantId,
            catalogCode: dest?.catalogCode || originSettings?.[id]?.catalogCode,
            channelCode: dest?.channelCode || originSettings?.[id]?.channelCode,
            channelId: dest?.channelId || originSettings?.[id]?.channelId,
            catalogId: dest?.catalogId || originSettings?.[id]?.catalogId,
            destinationId:
              dest?.destinationId || originSettings?.[id]?.destinationId,
            destinationInput:
              variant?.destinationInput ||
              originSettings?.[id]?.destinationInput ||
              {},
            variantExtraData:
              variant?.variantExtraData ||
              originSettings?.[id]?.variantExtraData ||
              {},
          });
        },
      );
    });

    return arrVariants;
  }

  function parseDestinationInfo(value, key) {
    try {
      const dest = value?.get('destination');
      if (!dest) return;
      if (dest?.variants) {
        Object.entries(dest?.variants?.cacheInfo).forEach(([id, variant]) => {
          if (!nodeIds.includes(key)) return;

          arrVariants.push({
            nodeId: key || originSettings?.[id]?.nodeId,
            variantId: id || originSettings?.[id]?.variantId,
            catalogCode: dest?.catalogCode || originSettings?.[id]?.catalogCode,
            channelCode: dest?.channelCode || originSettings?.[id]?.channelCode,
            channelId: dest?.channelId || originSettings?.[id]?.channelId,
            catalogId: dest?.catalogId || originSettings?.[id]?.catalogId,
            destinationId:
              dest?.destinationId || originSettings?.[id]?.destinationId,
            destinationInput:
              variant?.destinationInput ||
              originSettings?.[id]?.destinationInput ||
              {},
            variantExtraData:
              variant?.variantExtraData ||
              originSettings?.[id]?.variantExtraData ||
              {},
          });
        });
      } else {
        (dest?.variantIds || []).forEach(id => {
          if (originSettings[id]) {
            arrVariants.push({
              ...originSettings[id],
              channelId: dest?.channelId || originSettings?.[id]?.channelId,
            });
          }
        });
      }
    } catch (error) {
      console.error(error);
    }
  }

  (nodes || []).forEach(parseDestinationInfo);

  return arrVariants;
};

const getAllTagOfVariants = (variants, tagType, mapNodeTagKey = 'value') => {
  const stack = [...variants];
  const tags = [];
  const mapNodeTags = {};

  while (stack.length) {
    const currentVariant = stack.pop();
    const destinationInput = currentVariant?.destinationInput || {};

    Object.keys(destinationInput).forEach(inputKey => {
      const tagList = [];
      switch (true) {
        /** Case channel 2 - "web_personalization" */
        case currentVariant?.channelCode === 'web_personalization' &&
          currentVariant?.catalogCode === 'web_embedded': {
          (destinationInput?.[inputKey]?.viewPages || []).forEach(viewPage => {
            const tagData = tagifyGetAllTagData(
              viewPage?.html,
              tagType,
              extraPatternHandlers,
            );
            tagList.push(...tagData);
          });
          break;
        }

        /** Case channel 15 - "antsomi line"  */
        case currentVariant?.channelCode === 'line' &&
          currentVariant?.catalogCode === 'line_app': {
          (destinationInput?.[inputKey]?.data || []).forEach(data => {
            const tagData = tagifyGetAllTagData(data?.text, tagType);
            tagList.push(...tagData);
          });
          break;
        }

        /** Case channel 6 - "webhook" && catalog id 7662828 - "aeon_mall_in_app_message" */
        case currentVariant?.channelCode === 'webhook' &&
          currentVariant?.catalogCode === 'aeon_mall_in_app_message': {
          if (Array.isArray(destinationInput?.[inputKey])) {
            (destinationInput?.[inputKey] || []).forEach(item => {
              const tagData = tagifyGetAllTagData(item?.value, tagType);
              tagList.push(...tagData);
            });
          }
          break;
        }

        /** Case basic */
        case typeof destinationInput?.[inputKey] === 'string': {
          const tagData = tagifyGetAllTagData(
            destinationInput?.[inputKey],
            tagType,
          );
          tagList.push(...tagData);
          break;
        }
        case typeof destinationInput?.[inputKey] === 'object': {
          const dataStringify = Object.values(
            destinationInput?.[inputKey],
          ).join(' ');
          const tagData = tagifyGetAllTagData(dataStringify, tagType);
          tagList.push(...tagData);
          break;
        }
        default:
          break;
      }
      tags.push(...tagList);

      const tagIds = (tagList || []).map(tag => {
        return tag?.[mapNodeTagKey] || tag.label;
      });

      /** Update mapNodeTags */
      if (mapNodeTags[currentVariant.nodeId]) {
        if (mapNodeTags[currentVariant.nodeId]?.[inputKey]) {
          mapNodeTags[currentVariant.nodeId][inputKey].push(...tagIds);
        } else {
          mapNodeTags[currentVariant.nodeId][inputKey] = tagIds;
        }
      } else {
        mapNodeTags[currentVariant.nodeId] = {
          [inputKey]: tagIds,
        };
      }
    });

    if (currentVariant?.branchs && Array.isArray(currentVariant?.branchs)) {
      stack.push(...currentVariant.branchs);
    }
  }

  return { tags, mapNodeTags };
};

/**
 * Given a list of tags and a key to uniquely identify each tag, it will
 * return a new list of tags that only contains unique tags. The key is used
 * to identify duplicate tags.
 *
 * @param {array} tags - List of tags
 * @param {string} keyUnique - Key to uniquely identify each tag
 * @return {array} List of unique tags
 */
const uniqueTags = (tags, keyUnique) => {
  const seen = new Set();
  const _uniqueTags = tags.filter(tag => {
    const duplicate = seen.has(tag[keyUnique]);
    seen.add(tag[keyUnique]);
    return !duplicate;
  });
  return _uniqueTags;
};

/**
 * Given an input string and optional type, it will return an array of tag data
 * that matches the given type. If no type is given, it will return all tag data
 * that matches the given input string.
 *
 * @param {string} input - The input string to parse.
 * @param {string} [type] - Optional type to filter the result.
 *
 * @returns {TagData[]} An array of tag data.
 */
const tagifyGetAllTagData = (input, type, extraPattern) => {
  if (typeof input !== 'string') return [];

  const acceptableTagPattern = cloneDeep(DEFAULT_ACCEPT_TAGS);
  // Array to store all tag data
  const tags = [];

  // Collect all matches from each pattern
  Object.values({ ...patternHandlers, ...(extraPattern || {}) }).forEach(
    patternWrapper => {
      const {
        pattern,
        name: cachePatternName,
        acceptablePattern: acceptableType,
        handler,
      } = patternWrapper;

      const isAccepted = acceptablePatternChecking(
        acceptableType,
        acceptableTagPattern,
      );

      // No need to continue if pattern is not accepted
      if (!isAccepted) return;

      // Use the cached regex instead of creating a new one each time
      const regex = getCachedRegex(pattern, 'g', cachePatternName);
      let match;

      // Iterate over matches of the current pattern
      // eslint-disable-next-line no-cond-assign
      while ((match = regex.exec(input)) !== null) {
        const { isValid, tagData } = handler(match);
        if (isValid && tagData && (!type || (type && tagData.type === type))) {
          tags.push(tagData);
        }
      }
    },
  );

  return tags;
};

/**
 * Get permission info of a given list of objects.
 *
 * @param {array} objects - List of objects, each object is an object with
 *                          `objectType` and `objectCode` properties.
 * @return {object} An object, each key is an object code and the value is an
 *                  object with two properties: `isExist` and `isView`.
 */
const getObjectPermissionInfo = async tags => {
  try {
    if (!Array.isArray(tags) || tags.length === 0) return {};

    const objects = (tags || []).map(item => ({
      objectCode: item.attributeName,
      objectType: DATA_ACCESS_OBJECT.PROMOTION,
    }));

    const result = {};
    const permissionInfo = await PermServices.permission.getInfo({
      body: { objects },
    });

    /** Check if permission info is valid */
    if (
      !permissionInfo ||
      permissionInfo.code < 200 ||
      permissionInfo.code > 300 ||
      !permissionInfo.data
    ) {
      objects.forEach(item => {
        result[item.objectCode] = {
          isExist: false,
          isView: false,
        };
      });

      /** Check if permission info is array */
    } else if (Array.isArray(permissionInfo.data)) {
      permissionInfo.data.forEach(permission => {
        if (!permission?.isExist || !permission?.isView) {
          result[(permission?.objectCode)] = {
            isExist: permission?.isExist,
            isView: permission?.isView,
          };
        }
      });
    }
    return result;
  } catch (error) {
    return {};
  }
};
const getPermInfoShortLinks = async tags => {
  try {
    if (!Array.isArray(tags) || tags.length === 0) return {};

    const listShortenerId = tags.map(item => +item.shortener);

    const result = {};
    const permissionInfo = await LinkManagementServices.shortener.checkPermission(
      {
        listShortenerId,
      },
    );

    /** Check if permission info is valid */
    if (
      !permissionInfo ||
      permissionInfo.code < 200 ||
      permissionInfo.code > 300 ||
      !permissionInfo.data
    ) {
      listShortenerId.forEach(shortenerId => {
        result[shortenerId] = {
          isExist: false,
          isView: false,
        };
      });

      /** Check if permission info is array */
    } else if (Array.isArray(permissionInfo.data)) {
      permissionInfo.data.forEach(permission => {
        if (!permission?.isExist || !permission?.isView) {
          result[(permission?.link_shortener_id)] = {
            isExist: permission?.isExist,
            isView: permission?.isView,
          };
        }
      });
    }
    return result;
  } catch (error) {
    return {};
  }
};

export const hasPoolRemoved = mapErrorAttributes => {
  return Boolean(
    mapErrorAttributes &&
      mapErrorAttributes?.[PROMOTION_CODE] &&
      Object.keys(mapErrorAttributes?.[PROMOTION_CODE]).some(
        poolId => !mapErrorAttributes?.[PROMOTION_CODE]?.[poolId]?.isExist,
      ),
  );
};

const createTagPattern = tagInfo => `[[${JSON.stringify(tagInfo)}]]`;

const handleExtraPersonalizeTagPattern = match => {
  const [personalizeTag, personalizeContent] = match;
  const [tagCode] = personalizeContent.split('||');
  const [type, attributeName, subAttributeName] = tagCode.split('.');

  if (!tagCode || !type) {
    console.error('Invalid personalize pattern detected: ', tagCode);

    return {
      isValid: false,
      tag: '[[Invalid Personalize]]',
      tagData: undefined,
    };
  }

  const label = attributeName || type;

  const tagData = {
    label,
    type,
    value: personalizeTag,
    attributeName,
  };

  const tag = createTagPattern(tagData);

  return { isValid: true, tag, tagData };
};

const errorWrapper = handler => match => {
  try {
    return handler(match);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error(`Error in pattern handler: ${error.message}`, { match });
    // Return a placeholder or an empty string if an error occurs
    return {
      isValid: false,
      tag: `[[Error processing tag: ${match[0]}]]`,
      tagData: undefined,
    };
  }
};

const extraPatternHandlers = {
  extraPersonalizePattern: {
    pattern: '@\\{(?!shortlink|line|viber)([^}]+?)\\}',
    name: 'extraPersonalizePattern', // Used to cache the regex, please keep the name unique for each pattern
    acceptablePattern: PERSONALIZE_PTN,
    handler: errorWrapper(handleExtraPersonalizeTagPattern),
  },
};

const getMapNodeErrorPromotions = (mapNodeTags, permInfoPromotions) => {
  const mapNodeError = Object.fromEntries(
    Object.entries(mapNodeTags)
      .map(([nodeId, fields]) => [
        nodeId,
        Object.fromEntries(
          Object.entries(fields)
            .filter(([_fieldId, usedTags]) =>
              usedTags.some(
                tag => permInfoPromotions?.[tag]?.isExist === false,
              ),
            )
            .map(([fieldId, _tags]) => [
              fieldId,
              translate(
                translations._PERSONALIZATION_TAG_ERR_REMOVED,
                'This pool is removed',
              ), // Đặt thông báo vì các field này thỏa mãn điều kiện !isExist
            ]),
        ),
      ])
      .filter(([, fields]) => Object.keys(fields).length > 0), // Loại bỏ các node không còn field nào
  );

  return mapNodeError;
};

const getMapNodeErrorShortLinks = (mapNodeTags, permInfoPromotions) => {
  const mapNodeError = Object.fromEntries(
    Object.entries(mapNodeTags)
      .map(([nodeId, fields]) => [
        nodeId,
        Object.fromEntries(
          Object.entries(fields)
            .filter(([_fieldId, usedTags]) =>
              usedTags.some(
                tag => permInfoPromotions?.[tag]?.isExist === false,
              ),
            )
            .map(([fieldId, _tags]) => [
              fieldId,
              translate(
                translations._PER_TAG_LINK_ERR,
                'This shortener is inactivated or removed',
              ), // Đặt thông báo vì các field này thỏa mãn điều kiện !isExist
            ]),
        ),
      ])
      .filter(([, fields]) => Object.keys(fields).length > 0), // Loại bỏ các node không còn field nào
  );

  return mapNodeError;
};

const combineMapNodeError = mapNodeTagsList => {
  const combinedResult = {};

  mapNodeTagsList.forEach(mapNodeTags => {
    Object.entries(mapNodeTags).forEach(([nodeId, fields]) => {
      if (!combinedResult[nodeId]) {
        combinedResult[nodeId] = {};
      }

      Object.entries(fields).forEach(([fieldKey, value]) => {
        combinedResult[nodeId][fieldKey] = [value];
        // if (!combinedResult[nodeId][fieldKey]) {
        //   combinedResult[nodeId][fieldKey] = [value];
        // } else {
        //   combinedResult[nodeId][fieldKey].push(value);
        // }
      });
    });
  });

  return combinedResult;
};
