/* eslint-disable import/named */
/* eslint-disable react/prop-types */
// Libraries
import React from 'react';

// Components
import { UIButton } from '@xlab-team/ui-components';

// Styled
import { StyleFooter, UIButtonStyled, WapperStyleFooter } from './styles';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { SaveJTButton } from '../../_UI/JourneyTemplate';

// Translations

// Constants

const MAP_TITLE = {
  actBack: getTranslateMessage(TRANSLATE_KEY._ACT_BACK, 'Back'),
  actNext: getTranslateMessage(TRANSLATE_KEY._ACT_NEXT, 'Next'),
  actEdit: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT, 'Edit'),
  labelCancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
  labelSave: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
};

export function Footer(props) {
  const { STEP_NAME, activeStep, isLoadingSave } = props;

  const onAction = () => {
    // callback('ON_EDIT');
    props.callback('ON_CHANGE_STEP', activeStep === 0 ? 1 : 0);
  };

  const onSave = () => {
    props.callback('ON_SAVE');
  };

  return (
    <WapperStyleFooter className="p-x-4 p-y-4">
      <StyleFooter>
        <UIButtonStyled
          iconName={
            STEP_NAME.GENERAL === activeStep
              ? 'keyboard_arrow_right'
              : 'keyboard_arrow_left'
          }
          variant="contained"
          theme="outline"
          onClick={onAction}
        >
          {STEP_NAME.GENERAL === activeStep
            ? MAP_TITLE.actNext
            : MAP_TITLE.actBack}
        </UIButtonStyled>

        <SaveJTButton
          style={{
            marginLeft: '10px',
          }}
          isLoading={isLoadingSave}
          onClick={onSave}
        />
      </StyleFooter>
    </WapperStyleFooter>
  );
}

export default Footer;
