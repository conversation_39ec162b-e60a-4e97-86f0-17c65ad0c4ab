/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable react/prop-types */
import React, { useRef, useState, useMemo, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Hooks
import { useDeepCompareEffect } from '../../../../../../../hooks';

// Components
import {
  DivLoading,
  StyledTemplateSaveAs,
  WapperStyle,
  WapperStyleHeader,
  WrapperInner,
} from './styles';
import Footer from './Footer';
import { CircularProgress } from '@material-ui/core';
// Services
import { StepWrapper } from '../../../../../Profile/Segment/Create/styles';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import {
  camelCaseToSnakeCase,
  message,
  usePersistTemplate,
  useTemplateSave,
  useGetObjectTemplateDetail,
  snakeCaseToCamelCase,
  Steps,
  Drawer,
  DRAWER_DETAIL_DIMENSION,
  Button,
} from '@antscorp/antsomi-ui';
import { initAccessInfoDefault } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/utils';
import {
  GET_LIST_TYPE,
  OBJECT_TYPES,
  PUBLIC_LEVEL,
} from '@antscorp/antsomi-ui/es/constants';
import { useExternalServiceAuth } from '../../../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/JourneyTactic/constants';
import { MENU_PERMISSION } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/constants';
import { selectModalSaveAsTemplate } from '../../selectors';
import { JourneyTemplate } from '../../_UI/JourneyTemplate/Loadable';
import { format } from 'date-fns';
import { JourneyTemplateProvider } from '../../_UI/JourneyTemplate';
import { isEmpty, cloneDeep } from 'lodash';
import UILinearProgress from '../../../../../../../components/common/UILinearProgress';
import { handleUploadWorkflowThumbs } from '../../utils';

const MAP_TITLE = {
  titleSaveAsNew: getTranslateMessage(
    TRANSLATE_KEY._,
    'Save as a new journey template',
  ),
  titleSaveAsExis: getTranslateMessage(
    TRANSLATE_KEY._,
    'Save as an existing journey template',
  ),
  description: getTranslateMessage(
    TRANSLATE_KEY._,
    'Describe your journey template',
  ),
};
const STEP_NAME = {
  GENERAL: 0,
  VERIFICATION: 1,
};
// const STEPS = [
//   getTranslateMessage(TRANSLATE_KEY._, 'General Information'),
//   getTranslateMessage(TRANSLATE_KEY._, 'Verification'),
// ];
const STEPS = [
  {
    index: STEP_NAME.GENERAL,
    title: getTranslateMessage(TRANSLATE_KEY._, 'General Information'),
  },
  {
    index: STEP_NAME.VERIFICATION,
    title: getTranslateMessage(TRANSLATE_KEY._, 'Verification'),
  },
];

const MAP_STEP_NAME = {
  [STEP_NAME.GENERAL]: 'general_setting_step',
  [STEP_NAME.CATALOG]: 'verification',
};
function DrawerSaveAs(props) {
  const {
    moduleConfig,
    userInfo = {},
    saveAsGallery,
    channelId,
    isBlastCampaign,
  } = props;
  const serviceAuth = useExternalServiceAuth();

  // State
  const [visitedSteps, setVisitedSteps] = useState([
    MAP_STEP_NAME[STEP_NAME.GENERAL],
  ]);
  const [activeStep, setActiveStep] = useState(0);
  const [dataVerification, setdataVerification] = useState({
    isLoading: true,
  });
  const [completed, setCompleted] = useState({
    0: false,
    1: false,
  });

  const [isLoadingSave, setIsLoadingSave] = useState(false);
  const [dynamicThumbnails, setDynamicThumbnails] = useState([]);

  // Selector
  const { show, savedData } = useSelector(
    selectModalSaveAsTemplate(moduleConfig),
  );
  // Refs
  const stepRef = useRef(null);
  // console.log('savedData', savedData);
  const { thumbnails } = savedData || {};
  // const workflowThumbnail = getObjectPropSafely(() => thumbnails[0].url);

  const variantThumbnails = useMemo(() => {
    const thumbs = [];
    if (!Array.isArray(thumbnails)) {
      return thumbs;
    }

    return thumbnails.map(item => ({
      url: item.url,
      showSkeleton: item.type === 'media',
    }));
  }, [thumbnails]);

  useDeepCompareEffect(() => {
    setDynamicThumbnails([]);
  }, [thumbnails]);

  // callback
  const callback = (type, data) => {
    switch (type) {
      case 'ON_CHANGE_STEP':
        handleStep(data);
        break;
      case 'ON_SAVE':
        handleCreateTemplate();
        break;
      default:
        break;
    }
  };
  // dispact
  const dispatch = useDispatch();

  // action
  const handleCancelSaveAsTemplate = () => {
    setActiveStep(0);
    setdataVerification({
      ...dataVerification,
      isLoading: true,
    });
    setCompleted({
      0: false,
      1: false,
    });
    setVisitedSteps([MAP_STEP_NAME[STEP_NAME.GENERAL]]);
    const type = `${moduleConfig.key}@@SAVE_AS_TEMPLATE_CANCEL`;
    dispatch({ type });
    reset();
    form.resetFields();
    // setValue({
    //   ...templateValue,
    //   templateName: {
    //     label: `Untitled Journey Template#${format(
    //       new Date(),
    //       'yyyy-MM-dd HH:mm:ss',
    //     )}`,
    //     id: undefined,
    //   },
    // });
  };

  const handleStep = step => {
    const steps = Object.values(MAP_STEP_NAME).slice(0, step + 1);
    setActiveStep(step);
    setVisitedSteps(steps);
    setCompleted({ [activeStep]: true, [step]: false });
  };

  const onChangeVerification = data => {
    setdataVerification(prev => ({ ...prev, ...data }));
  };
  const [messageApi, contextHolder] = message.useMessage();
  // hook

  const {
    templateItems,
    isLoadingTemplateList,
    onLoadMore: onLoadMoreTemplates,
    value: templateValue,
    onSearchName,
    onChange,
    reset,
    categoryItems,
    form,
    setValue,
    searchNameProps,
  } = useTemplateSave({
    service: serviceAuth,
    config: {
      objectType: OBJECT_TYPES.JOURNEY_TEMPLATE,
      getListType: GET_LIST_TYPE.OWNER, // In case Public Level == Public -> don't need to use get_list_type,
      publicLevel:
        saveAsGallery === 'gallery'
          ? PUBLIC_LEVEL.PUBLIC
          : PUBLIC_LEVEL.RESTRICTED,
      channel: channelId,
      limitListPerPage: 20,
    },
    queriesOptions: {
      categoryList: {
        // enabled: isRichMenu,
      },
      templateList: {
        // enabled: isRichMenu,
      },
    },
    defaultValue: {
      // templateName: {
      //   label: `Untitled Journey Template#${format(
      //     new Date(),
      //     'yyyy-MM-dd HH:mm:ss',
      //   )}`,
      //   id: undefined,
      // },
      accessInfo:
        saveAsGallery === 'gallery'
          ? undefined
          : !isEmpty(
              (objectTemplateDetail && objectTemplateDetail.shareAccess) || {},
            )
          ? snakeCaseToCamelCase(
              {
                ...objectTemplateDetail.shareAccess,
                owner_id:
                  (objectTemplateDetail &&
                    objectTemplateDetail.shareAccess &&
                    objectTemplateDetail.shareAccess.owner_id) ||
                  userInfo.user_id,
              } || {},
              true,
            )
          : initAccessInfoDefault(userInfo),
      categories: {
        journey_type: [isBlastCampaign ? 24933 : 24930],
      },

      // saveOption: 'new',
    },
  });

  const { data: objectTemplateDetail } = useGetObjectTemplateDetail({
    args: {
      auth: serviceAuth,
      params: {
        object_type: OBJECT_TYPES.JOURNEY_TEMPLATE,
        public_level: PUBLIC_LEVEL.RESTRICTED,
        template_id:
          templateValue.templateName &&
          templateValue.templateName.id &&
          Number(templateValue.templateName.id),
      },
    },
  });

  useEffect(() => {
    setValue({ ...templateValue, accessInfo: initAccessInfoDefault(userInfo) });
  }, [userInfo]);
  // Mutations
  const { mutateAsync: persistTemplate /* , isLoading */ } = usePersistTemplate(
    {
      options: {
        onSuccess: (_, variables) => {
          const { persistType } = variables;

          messageApi.success('Save as new Gallery Template Success');
        },
        onError: (_, variables) => {
          const { persistType } = variables;

          messageApi.error('Save as new Gallery Template Error');
        },
      },
    },
  );

  const handleCreateTemplate = async () => {
    setIsLoadingSave(true);
    const {
      templateName = {
        label: '',
        id: undefined,
      },
      defaultThumbnail,
      accessInfo,
      description,
      saveOption = 'new',
      categories,
    } = templateValue;
    const persistType = saveOption === 'exist' ? 'update' : 'create';

    let saveThumbnails = cloneDeep(thumbnails);
    if (dynamicThumbnails && dynamicThumbnails.length) {
      dynamicThumbnails.forEach((thumb, idx) => {
        saveThumbnails[idx].url = typeof thumb === 'string' ? thumb : thumb.url;
      });
    }

    saveThumbnails = await handleUploadWorkflowThumbs({
      isBlastCampaign,
      createCopy: true,
      variantThumbnails: saveThumbnails,
      captureWorkspace: false,
    });

    try {
      const dataIn = {
        template_id: templateName && templateName.id && Number(templateName.id),
        // template_id: +templateName?.id!,
        // template_name:
        //   saveOption === 'new' &&
        //   (templateName.label ||
        //     `Untitled Template#${format(
        //       new Date(),
        //       'yyyy-MM-dd HH:mm:ss',
        //     )}`),
        // template_type: templateName.id,
        properties: {
          categories,
          ...(persistType === 'create' ? { isInitial: true } : {}),
          thumbnails: saveThumbnails,
          defaultThumbnail,
        },
        object_type: OBJECT_TYPES.JOURNEY_TEMPLATE,
        // public_level: PUBLIC_LEVEL.RESTRICTED,
        public_level:
          saveAsGallery === 'gallery'
            ? PUBLIC_LEVEL.PUBLIC
            : PUBLIC_LEVEL.RESTRICTED,
        thumbnail: saveThumbnails.length
          ? saveThumbnails[defaultThumbnail || 0].url
          : '',
        description,
        config: {
          journey: {
            ...dataVerification.journey,
            thumbnails: undefined,
          },
        },
        config_object: {
          objects:
            dataVerification &&
            dataVerification.verification &&
            dataVerification.verification.objects,
          templateObjSettings:
            dataVerification &&
            dataVerification.verification &&
            dataVerification.verification.templateObjSettings,
        },
        share_access:
          saveAsGallery === 'gallery'
            ? { ...camelCaseToSnakeCase(accessInfo, true), is_public: 1 }
            : camelCaseToSnakeCase(accessInfo, true),
        channel: channelId,
        ...categories,
      };
      if (saveOption === 'new') {
        dataIn.template_name =
          templateName.label ||
          `Untitled Template#${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}`;
      }
      await persistTemplate({
        persistType,
        params: {
          auth: serviceAuth,
          data: dataIn,
        },
      });

      dispatch({
        type: `${moduleConfig.key}@@SAVE_AS_TEMPLATE_SUCCESS`,
      });
    } catch (error) {
      setIsLoadingSave(false);
      return true;
      // const notification = {
      //   id: 'error',
      //   message: error,
      //   timeout: 2000,
      //   type: 'danger',
      // };
      // dispatch(addNotification(notification));
    }
    setIsLoadingSave(false);
    handleCancelSaveAsTemplate();
  };
  const renderVerification = () => {
    return (
      savedData && (
        <JourneyTemplate
          onChange={onChangeVerification}
          style={{ display: activeStep === 0 && 'none' }}
          onCancel={handleCancelSaveAsTemplate}
        />
      )
    );
  };

  const renderUISteps = step =>
    ({
      [STEP_NAME.GENERAL]: (
        <>
          {dataVerification.isLoading ? (
            <DivLoading>
              <CircularProgress size={24} />
            </DivLoading>
          ) : (
            <StyledTemplateSaveAs
              onSaveThumbnail={({
                thumbnails: outputThunmbs,
                thumbnailChange,
              }) => {
                setDynamicThumbnails(outputThunmbs);
              }}
              form={form}
              imageReview={{
                // hideDefaultButton: true,
                previewNavigation: false,
                skeleton: undefined,
                // label: 'Thumbnail',
                thumbnails: dynamicThumbnails.length
                  ? dynamicThumbnails
                  : variantThumbnails,
                infinity: false,
                // isLoading: saveAsState.isLoadingCapture,
                hideThumbnailsList: false,
                useFabric: true,
              }}
              categories={categoryItems}
              onChange={(newValue, errors) => {
                onChange(newValue, errors);
              }}
              saveOptions={{
                saveExistText: MAP_TITLE.titleSaveAsExis,
                saveExistValue: 'exist',
                saveNewText: MAP_TITLE.titleSaveAsNew,
                saveNewValue: 'new',
              }}
              templateNamesOptions={{
                ...searchNameProps,
                // defaultNewTemplateName: `Untitled Journey Template#${format(
                //   new Date(),
                //   'yyyy-MM-dd HH:mm:ss',
                // )}`,
                placeholder: 'Enter your journey template name',
              }}
              shareAccess={{
                getUserInfo: async search => {
                  const response = await props.DashboardServices.data.getUserInfo(
                    {
                      email: search,
                    },
                  );

                  return response?.data || [];
                },
                show: saveAsGallery !== 'gallery',
                userId: userInfo && +userInfo.user_id,
                userPermission: {
                  edit: MENU_PERMISSION.CREATED_BY_USER,
                  view: MENU_PERMISSION.CREATED_BY_USER,
                },
              }}
              descriptionOptions={{
                placeholder: MAP_TITLE.description,
              }}
              templateNames={templateItems}
              value={templateValue}
              //   value={omit(saveAsState, [
              //     'isOpenSaveAsModal',
              //     'userId',
              //     'isLoadingCapture',
              //     'saveAsType',
              //   ])}
            />
          )}

          {renderVerification()}
        </>
      ),
      [STEP_NAME.VERIFICATION]: renderVerification(),
    }[step]);
  const renderContent = () => {
    return (
      <WrapperInner style={{ marginBottom: 10, padding: 0 }}>
        <StepWrapper>
          <Steps
            items={STEPS}
            rootClassName="steps-container"
            current={activeStep}
            ref={stepRef}
            size="small"
            iconSize={24}
            onChange={handleStep}
          />
        </StepWrapper>

        <WrapperInner
          style={{
            height: `calc(100vh - 160px)`,
            paddingLeft: activeStep === 0 && '8px',
          }}
        >
          {renderUISteps(activeStep)}
        </WrapperInner>
        <Footer
          STEP_NAME={STEP_NAME}
          activeStep={activeStep}
          isLoadingSave={isLoadingSave}
          callback={callback}
          dataVerification={dataVerification}
        />
        <UILinearProgress isShow={isLoadingSave} />
      </WrapperInner>
    );
  };
  return (
    <Drawer
      destroyOnClose
      keyboard
      closable={false}
      placement="right"
      width={DRAWER_DETAIL_DIMENSION.LAYER_2.maxWidth}
      open={show}
      styles={{ body: { padding: 0 } }}
      onClose={handleCancelSaveAsTemplate}
    >
      <JourneyTemplateProvider
        moduleConfig={moduleConfig}
        mode={props.mode}
        data={savedData}
      >
        <WapperStyle width="100%">
          <WapperStyleHeader>
            {saveAsGallery === 'gallery'
              ? 'Save as template gallery'
              : 'Save as my template'}

            <Button theme="outline" onClick={handleCancelSaveAsTemplate}>
              Close
            </Button>
          </WapperStyleHeader>
          {renderContent()}
        </WapperStyle>
      </JourneyTemplateProvider>
    </Drawer>
  );
}

export default DrawerSaveAs;
