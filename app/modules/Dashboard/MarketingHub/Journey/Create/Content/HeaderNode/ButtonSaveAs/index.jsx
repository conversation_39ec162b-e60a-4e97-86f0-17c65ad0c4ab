/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import {
  ButtonExpend,
  ButtonSave,
  LabelSave,
  MenuItemStyled,
  StyleDivider,
  StyledPopover,
  WrapperButton,
} from './styles';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { Disabled } from 'components/Atoms/Disabled';
import { ExpandMore } from '@material-ui/icons';
import Popover from '@material-ui/core/Popover';
// eslint-disable-next-line import/no-unresolved
import {
  usePopupState,
  bindTrigger,
  bindMenu,
} from 'material-ui-popup-state/hooks';
import useToggle from '../../../../../../../../hooks/useToggle';
import DrawerSaveAs from '../../DrawerSaveAs';
import { MODE } from '../../../_UI/JourneyTemplate/constant';
import { useDispatch, useSelector } from 'react-redux';
import { update } from '../../../../../../../../redux/actions';
// services
import DashboardServices from 'services/Dashboard';
import { makeSelectUser } from '../../../../../../selector';
import { useCancellablePromise } from '../../../../../../../../utils/web/useHooks';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
import {
  Button,
  Dropdown,
  useGetSaveAsGalleryPermissionEmails,
} from '@antscorp/antsomi-ui';
import { useExternalServiceAuth } from '../../../../../../../../hooks';
import { getPortalId } from '../../../../../../../../utils/web/cookie';

function ButtonSaveAs(props) {
  const [isOpen, isToggleSaveAs] = useToggle(false);
  const [stateUser, setState] = useState({
    userInfo: {},
  });
  const [saveAsGallery, setSaveAsGallery] = useState('');
  const dispatch = useDispatch();
  const popupStateMenu = usePopupState({
    variant: 'popover',
    popupId: 'control-table-popup-popover',
  });
  const serviceAuth = useExternalServiceAuth();
  const isShowJourneyTemplate = true;
  // [
  //   561236459, // Demo
  //   564890637, // Retail
  //   564888929, // Staging
  //   564891336, // Staging 2
  //   33167, // Sandbox
  //   564890547, // Sandbox 2
  // ].includes(+getPortalId());

  // Hook
  const { cancellablePromise } = useCancellablePromise();
  const {
    data: saveAsGalleryPermissionEmails,
    // isLoading: isLoadingSaveAsGalleryPermissionEmails,
  } = useGetSaveAsGalleryPermissionEmails({
    args: {
      auth: serviceAuth,
    },
  });
  const userInfo = useSelector(state => makeSelectUser(state));
  const onClickSaveAs = data => {
    dispatch(
      update(props.moduleConfig.key, {
        saveAsTemplate: true,
      }),
    );
    popupStateMenu.close();
    isToggleSaveAs();
    setSaveAsGallery(data);
    fetUserInfo();
  };
  const fetUserInfo = async () => {
    try {
      // const encodePromotionCode = encodeURL(safeParse(promotionCode, ''));
      // const res = await PromotionServices.promotionCode.detail({
      //   poolId,
      //   promotionCode: encodePromotionCode,
      // });
      // console.log({ res });
      cancellablePromise(
        DashboardServices.data.getUserInfo({
          email: userInfo.email,
        }),
      ).then(res => {
        if (res.code === 200) {
          const data = res.data[0];
          setState({
            userInfo: {
              user_id: data.user_id,
              full_name: data.full_name,
              avatar: data.avatar,
              address: data.address,
              phone: data.phone,
              gender: data.gender,
              email: data.email,
              birthday: data.birthday,
              language: data.language,
              account_type: '1',
            },
          });
        }
      });
    } catch (err) {
      addMessageToQueue({
        path:
          'app/modules/Dashboard/MarketingHub/Journey/Create/Content/HeaderNode/ButtonSaveAs/index.jsx',
        func: 'useFetchData',
        data: err.stack,
      });
      console.warn('err', err);
    }
  };

  const renderMenuItem = () => {
    const menuItems = [
      {
        key: 'save_as',
        label: 'Save as',
        disabled: true,
      },
      {
        key: 'my_template',
        label: getTranslateMessage(
          TRANSLATE_KEY._ACT_MY_TEMPLATE,
          'My Template',
        ),
        onClick: () => onClickSaveAs('my_template'),
      },
    ];
    if (
      saveAsGalleryPermissionEmails &&
      saveAsGalleryPermissionEmails.includes(userInfo.email)
    ) {
      menuItems.push({
        key: 'gallery',
        label: getTranslateMessage(
          TRANSLATE_KEY._ACT_GALLERY_TEMPLATE,
          'Gallery Template',
        ),
        onClick: () => onClickSaveAs('gallery'),
      });
    }

    return menuItems;
  };

  return (
    <>
      {/* <Button
        type="primary"
        disabled={props.disabled}
        isLoading={props.isLoading}
        style={{
          gap: '4px',
        }}
      >
        <LabelSave onClick={props.onClick}>
          {getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save')}
        </LabelSave>

        {isShowJourneyTemplate && (
          <>
            <ButtonExpend
              style={{
                color: props.disabled
                  ? 'rgb(0,0,0,0.38)'
                  : 'rgb(255, 255, 255)',
              }}
              disabled={props.disabled}
              size="small"
              {...bindTrigger(popupStateMenu)}
            >
              <ExpandMore />
            </ButtonExpend>
            <StyledPopover
              {...bindMenu(popupStateMenu)}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
            >
              <div>
                <div
                  style={{ color: '#7F7F7F', fontSize: '12px', padding: '7px' }}
                  // onClick={() => onClickItem(item)}
                >
                  Save as...
                </div>
                <Disabled>
                  <MenuItemStyled
                    style={{ cursor: 'pointer' }}
                    onClick={() => onClickSaveAs('my_template')}
                  >
                    My template
                  </MenuItemStyled>
                </Disabled>
                {saveAsGalleryPermissionEmails &&
                  saveAsGalleryPermissionEmails.includes(userInfo.email) && (
                    <Disabled>
                      <MenuItemStyled
                        style={{ cursor: 'pointer' }}
                        onClick={() => onClickSaveAs('gallery')}
                      >
                        Template gallery
                      </MenuItemStyled>
                    </Disabled>
                  )}
              </div>
            </StyledPopover>
          </>
        )}
        </Button> */}
      {!isShowJourneyTemplate ? (
        <Button onClick={props.onClick} type="primary">
          {getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save')}
        </Button>
      ) : (
        <Dropdown.Button
          type="primary"
          icon={<ExpandMore />}
          trigger={['click']}
          menu={{
            items: renderMenuItem(),
          }}
          disabled={props.disabled}
          data-cy="btn-save-as"
        >
          <LabelSave onClick={props.onClick}>
            {getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save')}
          </LabelSave>
        </Dropdown.Button>
      )}

      <DrawerSaveAs
        moduleConfig={props.moduleConfig}
        isOpen={isOpen}
        isToggleSaveAs={isToggleSaveAs}
        mode={MODE.Save}
        saveAsGallery={saveAsGallery}
        userInfo={stateUser.userInfo}
        channelId={props.channelId}
        DashboardServices={DashboardServices}
        isBlastCampaign={props.isBlastCampaign}
      />
    </>
  );
}

export default ButtonSaveAs;
