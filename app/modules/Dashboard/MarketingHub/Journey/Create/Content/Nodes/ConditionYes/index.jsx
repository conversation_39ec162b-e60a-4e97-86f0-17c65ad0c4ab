/* eslint-disable react/no-unescaped-entities */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import Grid from '@material-ui/core/Grid';
import { UITextField, UITippy } from '@xlab-team/ui-components';
import styled from 'styled-components';
import ErrorBoundary from 'components/common/ErrorBoundary';

import { connect } from 'react-redux';

import { createStructuredSelector } from 'reselect';
import { makeSelectConfigureMainCreateWorkflow } from '../../../selectors';
import { updateValue } from '../../../../../../../../redux/actions';

import UINodeFilter from '../../../../../../../../components/common/UINodeFilter';
import { STORY_SETUP_ACTION } from '../../../utils.story.rules';
import { validateNameBranchIfThen } from './utils';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import { makeStyles } from '@material-ui/core';
import InputPreview from '../../../../../../../../components/Atoms/InputPreview';
import TargetAudienceV2 from '../../../../../../../../components/Templates/TargetAudienceMulti/TargetAudienceMultiV2';
import { useDebounceCallback } from '../../../../../../../../hooks';

const textFieldProps = {
  size: 'small',
  multiline: false,
  rowsMax: 1,
  // className: 'width-100',
  id: 'standard-basic',
  error: false,
};

export const WrapperTextField = styled.div`
  width: 100%;
  .MuiFormControl-root {
    width: 100%;
  }
`;

function ConditionYes(props) {
  const { isViewMode = false, isDisplayDiagramBlastCampaign = false } = props;
  const [error, setError] = useState([]);

  const [branchName, setBranchName] = useState(props.activeNode.label);

  const commonValidateName = name => {
    const { errors } = validateNameBranchIfThen(name);
    setError(errors);
  };

  if (branchName === undefined || branchName === null) {
    setBranchName(props.activeNode.label);
    // branchName = props.activeNode.label;
  }

  const { debounce: debounceOnChange } = useDebounceCallback(
    props.onChange,
    300,
  );

  const onChangeValue = value => {
    setBranchName(value);
    commonValidateName(value);
    debounceOnChange('branchName', value);
  };

  useEffect(() => {
    props.onChange('branchName', props.activeNode.label);

    return () => {
      setBranchName(null);
    };
  }, [props.componentId]);

  useEffect(() => {
    commonValidateName(branchName);
  }, [props.validateKey]);

  const onChangeFilter = data => {
    props.onChange('filter', data);
    // props.onChange('branchName', branchName);
  };

  const useStyles = makeStyles(() => ({
    labelText: {
      width: '100px',
      color: '#666',
      fontSize: '12px',
      flexBasis: 'unset',
      marginRight: '20px',
      textAlign: 'right',
    },
    w250: {
      width: '250px',
    },
  }));

  const classes = useStyles();

  if (isDisplayDiagramBlastCampaign) {
    return (
      <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/ConditionYes/index.jsx">
        <Grid container className="p-all-4" alignItems="center">
          <Grid
            item
            sm={2}
            className={classes.labelText}
            data-test="branch-name-label"
          >
            {getTranslateMessage(
              TRANSLATE_KEY._TITL_BRANCH_NAME,
              'Branch name',
            )}
          </Grid>
          <Grid container item sm={3} data-test="branch-name">
            <UITippy content={branchName} arrow distance={10}>
              <div className={classes.w250}>
                <WrapperTextField>
                  <InputPreview
                    isViewMode={isViewMode}
                    value={branchName}
                    type="input"
                  >
                    <UITextField
                      textFieldProps={textFieldProps}
                      value={branchName}
                      onChange={onChangeValue}
                      // textFieldProps={{ error: error.length > 0 }}
                      firstText={error[0]}
                      isViewMode={isViewMode}
                      inputProps={{
                        'data-test': 'branch-name-input',
                      }}
                    />
                  </InputPreview>
                </WrapperTextField>
              </div>
            </UITippy>
          </Grid>
          <Grid container>
            <Grid item sm={2} className={classes.labelText}>
              <div
                style={{
                  textAlign: 'right',
                  marginRight: '30px',
                  paddingTop: '10px',
                  color: '#666',
                  fontSize: '12px',
                }}
              >
                {!isViewMode && <span style={{ color: 'red' }}>* </span>}
                <span>
                  {getTranslateMessage(
                    TRANSLATE_KEY._BLAST_CAMPAIGN_SEND_TO,
                    'Send to',
                  )}
                </span>
              </div>
            </Grid>
            <Grid container item sm={3}>
              <TargetAudienceV2
                initData={props.initData.get('filter')}
                // onChange={data => props.onChange('targetAudience', data)}
                validateKey={props.validateKey}
                validateKeyBlast={props.validateKeyBlast}
                componentId={props.componentId}
                // callback={props.callback}
                errors={[]}
                disabled={false}
                isViewMode={isViewMode}
                errorsSchedule={[]}
                // hasOpenModalConfirm={props.hasOpenModalConfirm}
                // isCollapsed={false}
                itemTypeId={props.itemTypeId}
                isFirstCampaign={props.isFirstCampaign}
              />
            </Grid>
          </Grid>
        </Grid>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/ConditionYes/index.jsx">
      <Grid container className="p-all-4" alignItems="center">
        <Grid
          item
          sm={2}
          className={classes.labelText}
          data-test="branch-name-label"
        >
          {getTranslateMessage(TRANSLATE_KEY._TITL_BRANCH_NAME, 'Branch name')}
        </Grid>
        <Grid container item sm={3} data-test="branch-name">
          <UITippy content={branchName} arrow distance={10}>
            <div className={classes.w250}>
              <WrapperTextField>
                <InputPreview
                  isViewMode={isViewMode}
                  value={branchName}
                  type="input"
                >
                  <UITextField
                    textFieldProps={textFieldProps}
                    value={branchName}
                    onChange={onChangeValue}
                    textFieldProps={{ error: error.length > 0 }}
                    firstText={error[0]}
                    isViewMode={isViewMode}
                    inputProps={{
                      'data-test': 'branch-name-input',
                    }}
                  />
                </InputPreview>
              </WrapperTextField>
            </div>
          </UITippy>
        </Grid>
        {/* <Grid item sm={2} /> */}
        <Grid item sm={12}>
          <UINodeFilter
            moduleConfig={props.moduleConfig}
            onChange={onChangeFilter}
            initData={props.initData.get('filter')}
            componentId={props.componentId}
            validateKey={props.validateKey}
            eventValue={props.eventValue}
            itemTypeId={props.itemTypeId}
            triggerType={props.triggerType}
            disabled={
              !props.roleActions.has(STORY_SETUP_ACTION.EDIT_ACTION_NODE)
            }
            isViewMode={isViewMode}
          />
        </Grid>
      </Grid>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  mainConfigure: makeSelectConfigureMainCreateWorkflow(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig.key;
  return {
    onChangeNumberBranchNode: value =>
      dispatch(updateValue(`${prefix}@@NUMBER_OF_BRANCH@@`, value)),
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ConditionYes);
