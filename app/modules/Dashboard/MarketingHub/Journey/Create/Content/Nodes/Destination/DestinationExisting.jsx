/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
import React, { useEffect, useState } from 'react';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';

import Destination from './index';
import { makeSelectMainCreateWorkflow } from '../../../selectors';

function DestinationExisting(props) {
  // const { data } = props;
  // const { channel_id, catalog_id, campaign_id, destination_id } = data;
  // const [state, setstate] = useState({});
  // useEffect(() => {
  //   const dataTmp = {
  //     role: 'RESET',
  //     isFetchInfoData: false,
  //     channelId: channel_id,
  //     destinationId: destination_id,
  //     catalogId: catalog_id,
  //     campaignId: campaign_id,
  //     variantIds: [],
  //     lookup: { campaignId: campaign_id },
  //   };
  //   setstate(dataTmp);
  // }, [data]);

  return (
    <>
      <Destination
        {...props}
        isUseTemplateCampaign={props.isUseTemplateCampaign}
        // itemSquareSelected={props.itemSquareSelected}
      />
      {/* <UITemplate /> */}
    </>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectMainCreateWorkflow(),
});

export default connect(
  mapStateToProps,
  null,
)(DestinationExisting);
