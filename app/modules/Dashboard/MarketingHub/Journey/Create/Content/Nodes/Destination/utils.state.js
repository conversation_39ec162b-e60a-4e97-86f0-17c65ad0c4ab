/* eslint-disable no-loop-func */
/* eslint-disable eqeqeq */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-param-reassign */
import _, { cloneDeep, get, isFunction } from 'lodash';
import {
  MAP_VALIDATE,
  MAP_INPUT_TYPE,
  getTypeRender,
  // MAP_TYPE_RENDER,
} from './utils.form';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import {
  getVariantId,
  initInputElementDestination,
  toDesinationInputAPI,
  validateCustomInputVariant,
} from './utils';
import {
  generateKey,
  getObjectPropSafely,
  safeParse,
  safeParseInt,
} from '../../../../../../../../utils/common';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
import { loopVariantExtraData } from '../../../utils.map';
export const MAP_TRANSLATE = {
  _TITL_CAMPAIGN_NAME: getTranslateMessage(
    TRANSLATE_KEY._TITL_CAMPAIGN_NAME,
    'Campaign Name',
  ),
  _TITL_VARIANT_NAME: getTranslateMessage(
    TRANSLATE_KEY._TITL_VARIANT_NAME,
    'Variant Name',
  ),
  _TITL_WORKFLOW_DESTINATION: getTranslateMessage(
    TRANSLATE_KEY._TITL_WORKFLOW_DESTINATION,
    'Workflow Destination',
  ),
  _TITL_DELIVERY_ALGORITHM: getTranslateMessage(
    TRANSLATE_KEY._TITL_DELIVERY_ALGORITHM,
    'Delivery Algorithm',
  ),
  _DELIVERY_ALGORITHM_RANDOMIZATION: getTranslateMessage(
    TRANSLATE_KEY._DELIVERY_ALGORITHM_RANDOMIZATION,
    'Random',
  ),
};
export const initDefaultData = () => ({
  inputFields: {
    editorFields: [],
    htmlEditorFields: [],
    mediaTemplateFields: [],
  },
  campaignSetting: {},
  isFetchInfoData: false,
  // flag isOpened để biết destination đã mở ra lần nào chưa, ý nghĩa sẽ dùng để toAPI Full Data.
  // có những trường hợp isFetchInfoData = false nhưng isOpened = true, nên ko dùng isFetchInfoData để get data to API dc
  // cụ thể là khi update fe_config_id cho email template
  isCallback: true,
  useDesign: 'advance',
  status: 1,
  catalogId: '',
  // variantId: '',
  campaignId: '',
  channelId: '',
  channelCode: '',
  destinationId: '',
  isValidate: false,
  isInitDone: false,
  isExistedDestination: false,
  triggerFetchContent: 0,
  triggerFetchCatalogInfo: 0,
  inputConfig: {},
  dynamicFields: [],
  mixFields: ['workspaces'],
  defaultFields: ['campaignName', 'workflowDestination', 'algorithms'],
  // webChannelFields: ['zoneId', 'contentPlacement', 'isFitContent', 'priority'],
  webChannelFields: [], //
  variantHardFields: ['variantName'],
  testingAudienceIds: [],
  objectWidgetInput: {},
  timeTarget: {},
  variantExtraData: {}, // for other config not follow rule variant : fiel for config email template editor :(
  variants: {
    list: [], // list array for render
    cacheInfo: {}, // map info data by value
    activeId: null, // active variant id
    // mapVariantName: {}, // {"variantId": variantName} => for validate duplicate name
    // activeInfo: {}, // data info active, name variant and flexible data by destination config
  },
  // variantIds: [],
  destinationInput: {}, // object for dynamic field in catalog
  campaignCustomInput: {
    templateCustomInput: {},
    workspaces: [],
    customInputs: {},
    customInputAttributes: [],
  },
  variantName: {
    ...initInputElementDestination(
      'variantName',
      null,
      255,
      true,
      MAP_TRANSLATE._TITL_VARIANT_NAME,
      null,
      MAP_VALIDATE.variantName,
    ),
    componentEl: MAP_INPUT_TYPE.singleLineText,
    mapVariantName: {},
    activeId: '',
    ref: null,
  },
  campaignName: {
    ...initInputElementDestination(
      'campaignName',
      null,
      255,
      true,
      MAP_TRANSLATE._TITL_CAMPAIGN_NAME,
      null,
      MAP_VALIDATE.singleLineText,
    ),
    componentEl: MAP_INPUT_TYPE.singleLineText,
  },
  workflowDestination: {
    ...initInputElementDestination(
      'workflowDestination',
      {},
      null,
      true,
      MAP_TRANSLATE._TITL_WORKFLOW_DESTINATION,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_INPUT_TYPE.selectDropdown,
    options: [],
  },
  algorithms: {
    ...initInputElementDestination(
      'algorithms',
      {},
      null,
      true,
      MAP_TRANSLATE._TITL_DELIVERY_ALGORITHM || '',
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_INPUT_TYPE.selectDropdown,
    options: [
      {
        value: 'random',
        name: 'random',
        label: MAP_TRANSLATE._DELIVERY_ALGORITHM_RANDOMIZATION,
      },
    ],
  },
  workspaces: {
    ...initInputElementDestination(
      'workspaces',
      [],
      null,
      false,
      'Workspaces',
      null,
    ),
    componentEl: MAP_INPUT_TYPE.workspaces,
  },
  filter: {
    ...initInputElementDestination(
      'filter',
      {},
      null,
      false,
      'Target to',
      null,
    ),
    componentEl: MAP_INPUT_TYPE.filter,
  },
  cachedWorkflowDestinationInfo: [],
  // for web personalization
  // variantWebChannelHards: {},
});

export function setVariantCacheInfo(data, setData, params) {
  const {
    activeId,
    objectWidgetInput,
    variantExtraData,
    templateWorkspaces,
  } = params;
  const destinationInput = toDesinationInputAPI(
    data.dynamicFields,
    data.destinationInput,
  );
  // console.log('destinationInput', destinationInput);

  const tmpObjectWidgetInput =
    objectWidgetInput || data.objectWidgetInput || {};
  const tmpVariantExtraData = variantExtraData || data.variantExtraData || {};

  // console.log({ tmpVariantExtraData, tmpObjectWidgetInput });
  // console.log(
  //   'data.variantExtraData',
  //   data.variantExtraData,
  //   '--',
  //   data.variants.cacheInfo,
  // );
  // cache lại data hiện tại
  setData(draft => {
    draft.isCallback = true;
    draft.variants.cacheInfo[activeId] = {
      destinationInput,
      objectWidgetInput: tmpObjectWidgetInput,
      variantExtraData: tmpVariantExtraData,
      status: data.variants.cacheInfo[activeId]
        ? data.variants.cacheInfo[activeId].status
        : true,
      customInputVia: {
        workspaces: templateWorkspaces || [],
      },
    };
  });
}

export function validateVariantContent(data, setData, extra = {}) {
  let status = false;
  const { contentAccentType = '' } = extra;

  setData(draft => {
    draft.isCallback = true;
    [...draft.dynamicFields].forEach(each => {
      const { errors, isValidate } = draft.destinationInput[each].validate({
        ...(draft.destinationInput[each] || {}),
        destinationInput: data.destinationInput,
        channelCode: data.channelCode || '',
        catalogCode: data.catalogCode || '',
        contentAccentType,
      });
      draft.destinationInput[each].errors = errors;
      draft.destinationInput[each].isValidate = isValidate;
      return isValidate;
    });

    [...draft.variantHardFields].forEach(each => {
      const { errors, isValidate } = draft[each].validate(draft[each]);
      draft[each].errors = errors;
      draft[each].isValidate = isValidate;
      return isValidate;
    });

    const statusA = [...draft.dynamicFields].every(
      each => draft.destinationInput[each].isValidate,
    );
    const statusB = [...draft.variantHardFields].every(
      each => draft[each].isValidate,
    );

    const statusCustomAttribute = validateCustomInputVariant(draft);

    status = statusA && statusB && statusCustomAttribute;
    draft.isValidate = status;
  });
  return status;
}

export const validateVariantContentWithData = data => {
  const tmpData = cloneDeep(data);

  [...tmpData.dynamicFields].forEach(each => {
    const validateFn = get(tmpData.destinationInput, [each, 'validate']);

    if (isFunction(validateFn)) {
      const { errors, isValidate } = validateFn({
        ...(tmpData.destinationInput[each] || {}),
        destinationInput: data.destinationInput,
        channelCode: data.channelCode || '',
        catalogCode: data.catalogCode || '',
      });
      tmpData.destinationInput[each].errors = errors;
      tmpData.destinationInput[each].isValidate = isValidate;
    }
  });

  const statusA = [...tmpData.dynamicFields].every(
    each => tmpData.destinationInput[each].isValidate,
  );
  const statusB = [...tmpData.variantHardFields].every(
    each => tmpData[each].isValidate,
  );

  const statusCustomAttribute = validateCustomInputVariant(tmpData);

  return statusA && statusB && statusCustomAttribute;
};

export function createFeVarinatId() {
  return `fe_${generateKey()}`;
}

/**
 * activeVariantId
 * @param {*} variantsData
 * @param {*} activeVariantId : vẫn để lỡ sau này dùng, hiện tại luôn lấy thằng đầu tiên
 */
export function initVariants(
  role,
  variantsData = [],
  activeVariantId = 'ui-fix-first-child',
) {
  const variants = {
    list: [],
    cacheInfo: {},
    activeId: '',
    initVariantName: '',
    mapVariantName: {},
  };
  if (variantsData.length === 0) {
    const genId = createFeVarinatId();
    const tmp = {
      value: genId,
      label: 'Variant 1',
    };
    variants.list.push(tmp);
    variants.activeId = genId;
    variants.initVariantName = tmp.label;
    variants.mapVariantName[tmp.value] = tmp.label;
    variants.cacheInfo[genId] = { status: true };
  } else {
    /**
     * content_setting:
      destinationInput: {body: {…}}
      objectWidgetInput: {}
      __proto__: Object
      row_count: "2"
      status: "1"
      variant_id: 110962
      variant_name: "xfgsdger Variant 1"
     */
    variantsData.forEach(item => {
      const tmp = {
        value: role === 'RESET' ? createFeVarinatId() : item.variant_id,
        label: item.variant_name,
      };

      variants.list.push(tmp);

      variants.cacheInfo[tmp.value] = item.content_setting;

      variants.cacheInfo[tmp.value].status =
        role === 'RESET' ? true : safeParseInt(item.status) === 1;

      variants.cacheInfo[tmp.value].customInputs = item.custom_inputs;
      variants.mapVariantName[tmp.value] = tmp.label;
    });

    const indexOfActive = variants.list.findIndex(
      item => item.value == activeVariantId,
    );

    // console.log('indexOfActive', indexOfActive, '==', activeVariantId);
    if (indexOfActive > -1) {
      variants.activeId = activeVariantId;
      variants.initVariantName = variants.list[indexOfActive].label;
    } else {
      // gắn variant đầu tiên
      variants.activeId = variants.list[0].value;
      variants.initVariantName = variants.list[0].label;
    }
  }
  // console.log('initVariants---', variants);
  return variants;
}

// đối với variantExtraData,  cần detect email use template/ use advance.
// Vì trong component không nên detech và onChange  ra ngoài được.
export function reInitVariantExtraData(initDataVariants, inputFields = {}) {
  try {
    const {
      editorFields = [],
      htmlEditorFields = [],
      mediaTemplateFields = [],
    } = inputFields;
    // console.log({ inputFields, initDataVariants });
    if (
      editorFields.length === 0 &&
      htmlEditorFields.length === 0 &&
      mediaTemplateFields.length === 0
    ) {
      return;
    }
    Object.keys(initDataVariants.cacheInfo).forEach(key => {
      const variantExtraData = safeParse(
        initDataVariants.cacheInfo[key].variantExtraData,
        {},
      );
      // debugger;
      const destinationInput = safeParse(
        initDataVariants.cacheInfo[key].destinationInput,
        {},
      );
      if (Object.keys(variantExtraData).length === 0) {
        editorFields.forEach(field => {
          if (
            typeof destinationInput[field] === 'string' &&
            destinationInput[field].length > 0
          ) {
            variantExtraData[field] = {
              design: 'advance',
              emailConfig: {},
            };
          }
        });
        htmlEditorFields.forEach(field => {
          if (
            typeof destinationInput[field] === 'object' &&
            destinationInput[field].html.length > 0
          ) {
            variantExtraData[field] = {
              design: 'advance',
              type: 'htmlEditor',
            };
          } else {
            variantExtraData[field] = {
              design: 'template',
              type: 'htmlEditor',
            };
          }
        });
        mediaTemplateFields.forEach(field => {
          if (
            typeof destinationInput[field] === 'object' &&
            destinationInput[field].properties.id
          ) {
            if (variantExtraData[field]) {
              const { type } = variantExtraData[field];

              variantExtraData[field] = {
                design: type,
                type,
              };
            }
          }
        });
      }
      initDataVariants.cacheInfo[key].variantExtraData = variantExtraData;
    });
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.state.js',
      func: 'reInitVariantExtraData',
      data: err.stack,
    });
    console.log('error', err);
  }
}

export function buildValueFormVariantFromCache(
  setData,
  initDataVariant,
  destinationInput,
  newActiveItem,
) {
  setData(draft => {
    draft.isCallback = true;
    draft.variants.activeId = newActiveItem.value;
    draft.variantName.activeId = newActiveItem.value;
    // draft.variants.activeInfo.name = dataIn.label;
    draft.variantName.value = newActiveItem.label;
    draft.variantName.initValue = newActiveItem.label;
    draft.variantName.errors = [];
    // set data ui theo active value
    // console.log('destinationInput', destinationInput);
    Object.keys(destinationInput).forEach(key => {
      const tmpValue = safeParse(initDataVariant.destinationInput[key], '');
      if (destinationInput[key].inputType === 'select') {
        const mapOption = safeParse(destinationInput[key].mapOptions, {});
        // set value = map opttion
        draft.destinationInput[key].value = mapOption[tmpValue];
        draft.destinationInput[key].initValue = mapOption[tmpValue];
      } else {
        draft.destinationInput[key].value = tmpValue;
        draft.destinationInput[key].initValue = tmpValue;
      }
      draft.destinationInput[key].errors = [];
    });
    draft.objectWidgetInput = initDataVariant.objectWidgetInput || {};
    draft.variantExtraData = initDataVariant.variantExtraData || {};
  });
}

export function rebuildSettingDestinationInputs({ data, setData, dataIn }) {
  try {
    for (const [inputKey, inputSetting] of Object.entries(
      data.destinationInput,
    )) {
      const relatedOnChangeConfig = _.get(
        inputSetting,
        'relatedOnChange.config',
        null,
      );

      if (!_.isEmpty(relatedOnChangeConfig)) {
        for (const [optionValue, onChangeSettings] of Object.entries(
          relatedOnChangeConfig,
        )) {
          if (onChangeSettings?.fields && onChangeSettings?.inputs) {
            const currentValue = getObjectPropSafely(() => {
              const inputValue =
                data.variants.cacheInfo[dataIn.value].destinationInput[
                  inputKey
                ];
              return inputValue?.value || inputValue;
            });

            if (currentValue === optionValue) {
              setData(draft => {
                for (const relatedField of onChangeSettings.fields) {
                  draft.destinationInput[relatedField] = _.merge(
                    draft.destinationInput[relatedField],
                    onChangeSettings.inputs[relatedField],
                  );
                }
              });
            }
          }
        }
      }
    }
  } catch (error) {
    console.error(error);
  }
}

export function reBuildValueCampaignSetting({
  setData,
  dataVariantList,
  dataInit,
  type = 'RE_BUILD_VALUE', // RE_BUILD_VALUE: Đi build lại % data, KEEP_VALUE: giữ nguyên data
}) {
  // console.log('dataInit', dataInit, dataVariantList);
  const data = { list: [], priority: {} };
  let count = 0;
  let lastKey = '';

  dataVariantList.forEach(each => {
    // Tính phần trăm
    const percent = Math.round(100 / dataVariantList.length);
    const eachTmp = {
      ...each,
      status: dataInit.variants.cacheInfo[each.value]
        ? dataInit.variants.cacheInfo[each.value].status
        : true,
    };

    count += percent;
    lastKey = each.value;

    if (type === 'RE_BUILD_VALUE') {
      data.priority[each.value] = percent / 100;
    } else if (type === 'KEEP_VALUE') {
      data.priority = { ...dataInit.campaignSetting.priority };
    }
    data.list.push(eachTmp);
  });

  if (type === 'RE_BUILD_VALUE' && count < 100) {
    data.priority[lastKey] = (100 - count) / 100 + data.priority[lastKey];
  }

  // console.log('data', data);

  const variantIds = [];
  data.list.forEach(each => {
    if (getVariantId(each.value) !== null) {
      variantIds.push(each.value);
    }
  });

  setData(draft => {
    draft.campaignSetting = data;
    draft.variantIds = variantIds;
  });
}

export function initVariantExtraData(inputFields, extraData) {
  const {
    editorFields = [],
    htmlEditorFields = [],
    mediaTemplateFields = [],
  } = inputFields;

  const variantExtraData = {};
  editorFields.forEach(field => {
    variantExtraData[field] = { design: 'template', emailConfig: {} };
  });
  htmlEditorFields.forEach(field => {
    variantExtraData[field] = { design: 'template', type: 'htmlEditor' };
  });

  mediaTemplateFields.forEach(field => {
    if (extraData && extraData[field]) {
      const { type } = extraData[field];

      variantExtraData[field] = {
        design: type,
        type,
      };
    }
  });
  return variantExtraData;
}

export const getLookupConfig = ({
  lookupConfig = {},
  originalDestinationInput = {},
}) => {
  try {
    if (_.isEmpty(lookupConfig) || _.isEmpty(originalDestinationInput))
      return {};

    let isValidLookupData = false;
    const { fields = [], mapData = {}, config = {} } = lookupConfig;

    if (_.isArray(fields) && _.isObject(mapData)) {
      isValidLookupData = fields.every(field => {
        const originalInput = _.get(originalDestinationInput, field, {});
        let lookupValue = mapData[field] || '';

        if (_.isObject(originalInput) && lookupValue) {
          const inputValue =
            originalInput.value || originalInput.initValue || '';

          if (
            originalInput.inputType === 'select' &&
            _.isString(inputValue) &&
            _.isObject(lookupValue)
          ) {
            lookupValue = lookupValue.value;
          }

          return _.isEqual(inputValue, lookupValue);
        }

        return false;
      });
    }

    if (!isValidLookupData || !_.isObject(config)) return {};
    return config;
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.state.js',
      func: 'getLookupConfig',
      data: {
        error: error.stack,
        lookupConfig,
        originalDestinationInput,
      },
    });
    return {};
  }
};

/**
 * Generates new destination input configurations based on provided parameters.
 *
 * @param {Object} options - An object containing options for generating new destination input configurations.
 * @param {string} [options.use='onChange'] - The mode of operation. Can be either 'init' or 'onChange'. Defaults to 'onChange'.
 * @param {string} [options.name=''] - The name of the input field.
 * @param {*} [options.newValue=''] - The new value for the input field.
 * @param {Object} [options.relatedConfigs={}] - An object containing related configurations.
 * @param {Object} [options.allData={}] - An object containing all relevant data.
 * @returns {Object} An object containing the updated destination input and dynamic fields.
 * @returns {Object} destinationInput - An object representing the updated destination input configurations.
 * @returns {Array} dynamicFields - An array containing the updated dynamic fields.
 */
export const getNewDestinationInputConfigs = ({
  use = 'onChange', // two modes: 'init' | 'onChange'
  name = '',
  newValue = '',
  relatedConfigs = {},
  allData = {},
}) => {
  let [destinationInput, dynamicFields] = [{}, []];

  try {
    if (
      !name ||
      !newValue ||
      !_.isObject(allData) ||
      !_.isObject(relatedConfigs)
    )
      return { destinationInput, dynamicFields };

    const originalInput = _.get(allData, 'destinationInput', {});
    destinationInput = _.cloneDeep(originalInput);
    const fieldsTemp = _.get(allData, 'dynamicFields', []);
    dynamicFields = _.cloneDeep(fieldsTemp);
    const { globalAction = {}, config = {} } = relatedConfigs;

    // Handle individual field case
    if (!_.isEmpty(config)) {
      let individualName = _.cloneDeep(newValue);
      if (_.isObject(individualName)) individualName = individualName.value;

      const individualConfig = _.get(config, individualName, null);

      if (_.isObject(individualConfig)) {
        const { fields = [], inputs = {}, lookups = {} } = individualConfig;

        if (_.isArray(fields)) {
          fields.forEach(field => {
            if (
              (_.has(inputs, field) || _.has(lookups, field)) &&
              _.has(destinationInput, field)
            ) {
              const originalConfigField = _.get(destinationInput, field, {});
              const newConfig = _.get(inputs, field, {});
              let combineConfig = {
                ...originalConfigField,
                ...newConfig,
              };

              const hasLookups = _.has(lookups, field);
              if (hasLookups) {
                // Get lookup configuration depending on value of many fields
                const lookupConfig = getLookupConfig({
                  lookupConfig: _.get(lookups, field, {}),
                  originalDestinationInput: originalInput,
                });

                combineConfig = {
                  ...combineConfig,
                  ...lookupConfig,
                };
              }

              const originalTypeRender = getTypeRender(originalConfigField);
              const newTypeRender = getTypeRender(combineConfig);

              // If type render is different, update the type render
              if (!_.isEqual(originalTypeRender, newTypeRender)) {
                _.set(combineConfig, 'validate', MAP_VALIDATE[newTypeRender]);
                _.set(
                  combineConfig,
                  'componentEl',
                  MAP_INPUT_TYPE[newTypeRender],
                );
              }

              // Merge the new config to the original config
              _.set(destinationInput, field, combineConfig);
            }
          });
        }
      }
    }

    // Handle Global Action case
    if (!_.isEmpty(globalAction)) {
      const {
        isReset = false,
        ignoreResetFields = [],
        isSort = false,
        sortFields = [],
        isSlice = false,
        sliceFields = [],
      } = globalAction;

      if (isReset && _.isArray(fieldsTemp) && use !== 'init') {
        // Reset all field except coming field name
        const exceptFields = _.without(fieldsTemp, name);

        exceptFields.forEach(field => {
          const inputSetting = _.get(destinationInput, field, {});

          let fallbackValue = '';
          if (inputSetting) {
            if (inputSetting.inputType === 'select') fallbackValue = {};
          }

          let resetValue = _.get(
            destinationInput,
            [field, 'default'],
            fallbackValue,
          );

          // in case reset value has default property and it is null/undefined
          if (!resetValue) resetValue = fallbackValue;

          // Mutate all field to set value of null except coming field name & ignore reset fields
          if (
            _.isArray(ignoreResetFields) &&
            !ignoreResetFields.includes(field)
          ) {
            _.set(destinationInput, field, {
              ...inputSetting,
              initValue: resetValue,
              value: resetValue,
            });
          }
        });
      }

      // set value of coming field with new value
      _.set(destinationInput, name, {
        ..._.get(destinationInput, name, {}),
        initValue: newValue,
        value: newValue,
      });

      // Handle mutate fields
      if (isSort && _.isArray(sortFields)) {
        dynamicFields = sortFields;
      }

      // Handle slice fields
      if (isSlice && _.isArray(sliceFields)) {
        dynamicFields = sliceFields;
        destinationInput = _.pick(destinationInput, sliceFields);
      }
    }

    return {
      destinationInput,
      dynamicFields,
    };
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.state.js',
      func: 'getNewDestinationInputConfigs',
      data: {
        use,
        newValue,
        relatedConfigs,
        allData,
        error: err.stack,
      },
    });
    return {
      destinationInput: {},
      dynamicFields: [],
    };
  }
};

export function adjustDynamicDataIfRelatedChange({ dynamicData = {} }) {
  try {
    // Update dynamic data in case if it has relatedOnChange property
    if (_.isObject(dynamicData.destinationInput)) {
      const relatedValueFields = Object.entries(
        dynamicData.destinationInput,
      ).reduce((accumulator, [destInputKey, item]) => {
        if (!_.isEmpty(item.value) && !_.isEmpty(item.relatedOnChange)) {
          return {
            ...accumulator,
            [destInputKey]: item.value,
          };
        }

        return accumulator;
      }, {});

      if (!_.isEmpty(relatedValueFields)) {
        Object.entries(relatedValueFields).forEach(([name, newValue]) => {
          const relatedConfigs = _.get(dynamicData, [
            'destinationInput',
            name,
            'relatedOnChange',
          ]);

          const {
            destinationInput: newDestinationInput,
            dynamicFields: newDynamicFields,
          } = getNewDestinationInputConfigs({
            name,
            newValue,
            relatedConfigs,
            use: 'init',
            allData: dynamicData,
          });

          if (!_.isEmpty(newDynamicFields) && !_.isEmpty(newDestinationInput)) {
            _.set(dynamicData, 'destinationInput', newDestinationInput);
            _.set(dynamicData, 'dynamicFields', newDynamicFields);
          }
        });
      }
    }
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.state.js',
      func: 'adjustDynamicDataIfRelatedChange',
      data: {
        error: error.stack,
        args: {
          dynamicData,
        },
      },
    });
  }
}
