// Libraries
import React, { memo, useMemo } from 'react';
import PropTypes from 'prop-types';
import { get, isString } from 'lodash';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

// Components
import { Flex, Typography } from '@antscorp/antsomi-ui';
import { Container } from './styled';
import RowInput from '../RowInput';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import ErrorText from '../ErrorText';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

// Utils
import {
  extractUniquePlaceholderPatterns,
  getError,
  PathStructure,
} from '../../utils';

const {
  body: __bodyPath,
  genPath: __genPath,
  carousel: __carouselPath,
  indexKey: __indexKey,
} = PathStructure;

const MAP_TRANSLATE = {
  placeholder: translate(translations._, 'Placeholder'),
};

const BodyMessage = props => {
  const {
    body,
    setting,
    otherData,
    itemTypeId,
    isBlastCampaign,
    isForceHideBtnPersonalization,
    eventValue,
    groupCodes,
    componentKey,
    isViewMode,
    isCarousel,
    cardIndex,
    errors,
    onChangeOthers,
    onChange,
  } = props;
  const { text } = body || {};

  const placeholderList = useMemo(() => {
    if (!isString(text)) return [];

    return extractUniquePlaceholderPatterns(text);
  }, [text]);

  const renderPlaceholderList = list => {
    const content = list.map((placeholderPattern, placeholderIdx) => {
      // Path
      const path = __bodyPath(placeholderIdx);
      const idxPath = __indexKey(placeholderIdx);

      const label = `${MAP_TRANSLATE.placeholder} ${placeholderIdx + 1}`;

      // Get error
      let errPath = path;
      if (isCarousel) {
        errPath = __genPath(__carouselPath.body(cardIndex), idxPath, false);
      }
      const errMessage = getError(errors, errPath);

      return (
        <RowInput key={placeholderPattern} isRequired label={label}>
          <TinymceEditor
            hasEmoji={false}
            name="text"
            typeComponent="input"
            enableShortLink
            initValue={get(setting, idxPath, '') ?? ''}
            otherData={otherData}
            itemTypeId={itemTypeId}
            isBlastCampaign={isBlastCampaign}
            isForceHideBtnPersonalization={isForceHideBtnPersonalization}
            eventValue={eventValue}
            groupCodes={groupCodes}
            componentKey={componentKey}
            isViewMode={isViewMode}
            errors={errMessage}
            onChangeOthers={onChangeOthers}
            onChange={newValue => onChange(path, newValue)}
          />
          <ErrorText message={errMessage} />
        </RowInput>
      );
    });

    return content;
  };

  if (!placeholderList.length) return null;

  return (
    <Flex vertical gap={15} style={{ padding: '5px 0px' }}>
      <Typography.Text
        style={{
          color: globalToken?.colorText,
          fontSize: `${globalToken.fontSize}px`,
        }}
      >
        {translate(translations._, 'Template Data')}
      </Typography.Text>

      <Container>{renderPlaceholderList(placeholderList)}</Container>
    </Flex>
  );
};

BodyMessage.defaultProps = {
  body: {},
  setting: [],
  onChange: () => {},
};
BodyMessage.propTypes = {
  body: PropTypes.object,
  setting: PropTypes.array,
  otherData: PropTypes.object,
  itemTypeId: PropTypes.number,
  isBlastCampaign: PropTypes.bool,
  isForceHideBtnPersonalization: PropTypes.bool,
  eventValue: PropTypes.object,
  groupCodes: PropTypes.array,
  componentKey: PropTypes.string,
  isViewMode: PropTypes.bool,
  errors: PropTypes.array,
  cardIndex: PropTypes.number,
  isCarousel: PropTypes.bool,
  onChangeOthers: PropTypes.func,
  onChange: PropTypes.func,
};

export default memo(BodyMessage);
