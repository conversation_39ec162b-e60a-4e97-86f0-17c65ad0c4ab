/* eslint-disable react/no-array-index-key */
// Libraries
import React, { memo } from 'react';
import { get, isArray } from 'lodash';
import PropTypes from 'prop-types';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

// Components
import RowInput from '../RowInput';
import { Divider, Flex, Typography } from '@antscorp/antsomi-ui';
import { CollapseStyled } from './styled';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import ErrorText from '../ErrorText';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import { BUTTON_TYPE } from '../../constants';

// Utils
import {
  extractUniquePlaceholderPatterns,
  getError,
  PathStructure,
} from '../../utils';

const {
  buttons: __btnPath,
  genPath: __genPath,
  carousel: __carouselPath,
  indexKey: __indexKey,
} = PathStructure;

const { QUICK_REPLY, URL, COPY_CODE, CATALOG } = BUTTON_TYPE;

const MAP_TRANSLATE = {
  placeholder: translate(translations._, 'Placeholder'),
};

const ButtonMessage = props => {
  const {
    buttons,
    setting,
    otherData,
    itemTypeId,
    isBlastCampaign,
    isForceHideBtnPersonalization,
    eventValue,
    groupCodes,
    componentKey,
    isViewMode,
    isCarousel,
    cardIndex,
    errors,
    onChangeOthers,
    onChange,
  } = props;

  const renderButtonCollapse = buttonList => {
    const content = buttonList.map((button, btnIdx) => {
      const { type } = button;
      const isDefaultShow = btnIdx === 0;
      const path = __btnPath(btnIdx);
      const valueBtnPath = __indexKey(btnIdx);
      const initValue = get(setting, valueBtnPath, '');

      switch (type) {
        case QUICK_REPLY: {
          const { text } = button;

          return (
            <CollapseStyled
              key={btnIdx}
              defaultActiveKey={isDefaultShow ? type : undefined}
              items={[
                {
                  key: type,
                  label: text,
                  children: (
                    <RowInput label={translate(translations._, 'Content')}>
                      <TinymceEditor
                        enableShortLink
                        hasEmoji={false}
                        name="text"
                        typeComponent="input"
                        initValue={initValue ?? ''}
                        otherData={otherData}
                        itemTypeId={itemTypeId}
                        isBlastCampaign={isBlastCampaign}
                        isForceHideBtnPersonalization={
                          isForceHideBtnPersonalization
                        }
                        eventValue={eventValue}
                        groupCodes={groupCodes}
                        componentKey={componentKey}
                        isViewMode={isViewMode}
                        onChangeOthers={onChangeOthers}
                        onChange={newValue => onChange(path, newValue)}
                      />
                    </RowInput>
                  ),
                },
              ]}
            />
          );
        }
        case URL: {
          const { text, url } = button;

          const listPlaceholders = extractUniquePlaceholderPatterns(url);

          return (
            <CollapseStyled
              key={btnIdx}
              defaultActiveKey={isDefaultShow ? type : undefined}
              items={[
                {
                  key: type,
                  label: text,
                  children:
                    listPlaceholders.length === 0 ? (
                      <Typography.Text>{url}</Typography.Text>
                    ) : (
                      listPlaceholders.map(
                        (eachPlaceholder, placeholderIdx) => {
                          // Paths
                          const placeholderPath = __indexKey(placeholderIdx);
                          const innerPath = `${path}${placeholderPath}`;
                          const initValuePath = `${valueBtnPath}${placeholderPath}`;

                          const label = `${
                            MAP_TRANSLATE.placeholder
                          } ${placeholderIdx + 1}`;

                          let errPath = innerPath;
                          if (isCarousel) {
                            errPath = __genPath(
                              __carouselPath.buttons(cardIndex),
                              initValuePath,
                              false,
                            );
                          }

                          return (
                            <RowInput
                              isRequired
                              key={eachPlaceholder}
                              label={label}
                            >
                              <TinymceEditor
                                enableShortLink
                                hasEmoji={false}
                                name="text"
                                typeComponent="input"
                                initValue={
                                  get(setting, initValuePath, '') ?? ''
                                }
                                otherData={otherData}
                                itemTypeId={itemTypeId}
                                isBlastCampaign={isBlastCampaign}
                                isForceHideBtnPersonalization={
                                  isForceHideBtnPersonalization
                                }
                                eventValue={eventValue}
                                groupCodes={groupCodes}
                                componentKey={componentKey}
                                isViewMode={isViewMode}
                                errors={getError(errors, errPath)}
                                onChangeOthers={onChangeOthers}
                                onChange={newValue =>
                                  onChange(innerPath, newValue)
                                }
                              />
                              <ErrorText message={getError(errors, errPath)} />
                            </RowInput>
                          );
                        },
                      )
                    ),
                },
              ]}
            />
          );
        }
        case COPY_CODE: {
          const { text } = button;
          let errPath = path;
          if (isCarousel) {
            errPath = __genPath(
              __carouselPath.buttons(cardIndex),
              valueBtnPath,
              false,
            );
          }

          return (
            <CollapseStyled
              key={btnIdx}
              defaultActiveKey={isDefaultShow ? type : undefined}
              items={[
                {
                  key: type,
                  label: text,
                  children: (
                    <RowInput
                      isRequired
                      label={translate(translations._, 'Code')}
                    >
                      <TinymceEditor
                        hasEmoji={false}
                        name="text"
                        typeComponent="input"
                        enableShortLink={false}
                        initValue={initValue ?? ''}
                        otherData={otherData}
                        itemTypeId={itemTypeId}
                        isBlastCampaign={isBlastCampaign}
                        isForceHideBtnPersonalization={
                          isForceHideBtnPersonalization
                        }
                        eventValue={eventValue}
                        groupCodes={groupCodes}
                        componentKey={componentKey}
                        isViewMode={isViewMode}
                        errors={getError(errors, errPath)}
                        onChangeOthers={onChangeOthers}
                        onChange={newValue => onChange(path, newValue)}
                      />
                      <ErrorText message={getError(errors, errPath)} />
                    </RowInput>
                  ),
                },
              ]}
            />
          );
        }
        case CATALOG: {
          const { text } = button;

          return (
            <CollapseStyled
              key={btnIdx}
              defaultActiveKey={isDefaultShow ? type : undefined}
              items={[
                {
                  key: type,
                  label: text,
                  children: (
                    <RowInput
                      label={translate(translations._, 'Product Thumbnail ID')}
                    >
                      <TinymceEditor
                        hasEmoji={false}
                        name="text"
                        typeComponent="input"
                        enableShortLink={false}
                        initValue={initValue ?? ''}
                        otherData={otherData}
                        itemTypeId={itemTypeId}
                        isBlastCampaign={isBlastCampaign}
                        isForceHideBtnPersonalization={
                          isForceHideBtnPersonalization
                        }
                        eventValue={eventValue}
                        groupCodes={groupCodes}
                        componentKey={componentKey}
                        isViewMode={isViewMode}
                        onChangeOthers={onChangeOthers}
                        onChange={newValue => onChange(path, newValue)}
                      />
                    </RowInput>
                  ),
                },
              ]}
            />
          );
        }
        default: {
          return null;
        }
      }
    });

    return (
      <Flex vertical gap={15}>
        {content}
      </Flex>
    );
  };

  if (!isArray(buttons) || !buttons.length) return null;

  return (
    <>
      <Divider
        dashed
        style={{ borderColor: globalToken.bw4, margin: '20px 0px' }}
      />

      <Flex vertical gap={10}>
        <Typography.Text style={{ fontWeight: 500 }}>
          {translate(translations._, 'Button')}
        </Typography.Text>

        {renderButtonCollapse(buttons)}
      </Flex>
    </>
  );
};

ButtonMessage.defaultProps = {
  buttons: [],
  setting: [],
  onChange: () => {},
};
ButtonMessage.propTypes = {
  buttons: PropTypes.array,
  setting: PropTypes.array,
  otherData: PropTypes.object,
  itemTypeId: PropTypes.number,
  isBlastCampaign: PropTypes.bool,
  isForceHideBtnPersonalization: PropTypes.bool,
  eventValue: PropTypes.object,
  groupCodes: PropTypes.array,
  componentKey: PropTypes.string,
  isViewMode: PropTypes.bool,
  isCarousel: PropTypes.bool,
  cardIndex: PropTypes.number,
  errors: PropTypes.array,
  onChangeOthers: PropTypes.func,
  onChange: PropTypes.func,
};

export default memo(ButtonMessage);
