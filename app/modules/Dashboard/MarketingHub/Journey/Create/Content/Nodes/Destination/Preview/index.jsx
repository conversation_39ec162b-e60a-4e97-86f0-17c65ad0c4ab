/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { memo } from 'react';
import PropTypes from 'prop-types';
import get from 'lodash/get';
import UIWebPush from 'containers/UIPreview/WebPush';
import UIZaloOA from 'containers/UIPreview/Zalo';
import UIAppPush from 'containers/UIPreview/AppPushPreview/AppPushDefault';
import UIAppPushTemplate from 'containers/UIPreview/AppPushPreview/AppPushTemplate';
import UIWebhookAeonMall from 'containers/UIPreview/Webhook/AeonMall';
import UISmartInbox from 'containers/UIPreview/SmartInbox';
import UIWebHook from 'containers/UIPreview/WebHook';
import UIConversation from 'containers/UIPreview/Conversation';
import UISms from 'containers/UIPreview/Sms';
import UILineApp from 'containers/UIPreview/Line';
import UIEmail from 'containers/UIPreview/Email';
import UIViberPreview from 'containers/UIPreview/Viber/ViberPreview';
import UIWebPersonalization from 'containers/UIPreview/WebPersonalization';
import SimulateIphoneSMS from '../../../../../../../../../containers/UIPreview/SimulateIphoneSMS';
import UITelegramBlast from '../../../../../../../../../containers/UIPreview/TelegramBlast';
import UIViberBlast from '../../../../../../../../../containers/UIPreview/Viber/ViberBlast';
import ChannelSmsPreview from '../../../../../../../../../containers/UIPreview/ChannelSmsPreview';
import { Flex } from '@antscorp/antsomi-ui';

import { CATALOG_CODES } from '../../../../../../Destination/CreateV2/Design/Templates/constants';
import ConversationBlast from '../../../../../../../../../containers/UIPreview/ConversationBlast';
import UIZaloZNS from '../../../../../../../../../containers/UIPreview/ZaloZNS';
import UIZaloPayZNS from '../../../../../../../../../containers/UIPreview/ZaloPayZNS ';
import { useCapture } from './useCapture';
import ChannelWhatsappPreview from '../../../../../../../../../containers/UIPreview/ChannelWhatsappPreview';
import IosTemplate from '../../../../../../../../../containers/UIPreview/AppPushPreview/AppPushTemplate/IosTemplate';
import { IOS_TEMPLATE_CF_HOUSE_COMPONENT } from '../../../../../../../../../containers/UIPreview/AppPushPreview/AppPushTemplate/IosTemplate/constants';

// Constants
import { CATALOG_CODE } from '../../constant';
import { TEMPLATE_CF_HOUSE_KEYS } from '../../../../../../../../../containers/UIPreview/AppPushPreview/AppPushTemplate/constants';

// Utils
import {
  getAeonMallInAppMessageSettings,
  getTemplateSettings,
  getTheCoffeeHouseSetting,
} from './utils';

// Libraries
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';

// Actions
import {
  initPersonalizations,
  startPersonalizations,
} from '../../../../../../../../../components/common/UIEditorPersonalization/WrapperPersonalization/libs/action';

// Selectors
import { makeSelectPersonalizations } from '../../../../../../../selector';

// 1 -	"email"
// 2 -	"web_personalization"
// 3 -	"web_push"
// 4 -	"app_push"
// 5 -	"conversation"
// 6 -	"webhook"
const MAP_CHANNEL = {
  email: props => <UIEmail data={props.dataRealtime} />,
  // web_personalization: props => <UIWebPersonalization data={props.data} />,
  web_push: props => {
    return <UIWebPush data={props.dataRealtime} />;
  },
  app_push: props => {
    let Preview;

    switch (props.catalogCode) {
      case CATALOG_CODE.ANTSOMI_APP_PUSH:
      case CATALOG_CODE.ACFC_APP_PUSH:
      case CATALOG_CODE.FIRE_BASE_APP_PUSH: {
        const template = get(props, 'dataRealtime.template.value.value', null);

        Preview = (
          <UIAppPushTemplate
            {...getTemplateSettings(props, template)}
            catalogCode={props.catalogCode}
            template={template}
            isRecommendation={props.isRecommendation}
          />
        );
        break;
      }
      case CATALOG_CODE.CF_HOUSE_APP_PUSH: {
        Preview = (
          <Flex vertical align="center">
            <IosTemplate
              {...getTheCoffeeHouseSetting(props)}
              template={TEMPLATE_CF_HOUSE_KEYS.DEFAULT}
              componentStrategy={IOS_TEMPLATE_CF_HOUSE_COMPONENT}
            />
          </Flex>
        );

        break;
      }
      default: {
        Preview = (
          <UIAppPush
            data={props.dataRealtime}
            catalogCode={props.catalogCode}
            isRecommendation={props.isRecommendation}
            isBlastCampaign={props.isBlastCampaign}
            isPreviewAppPushV2={props.catalogCode === 'antsomi_app_push'}
            observeTypeDevicePreview={props.observeTypeDevicePreview}
          />
        );
      }
    }

    return Preview;
  },
  smart_inbox: props => (
    <UISmartInbox
      data={props.dataRealtime}
      dataSmartInbox={props.dataSmartInbox}
      deviceType={props.deviceType}
      selectedTemplate={props.selectedTemplate}
    />
  ),
  conversation: props => {
    return <ConversationBlast data={props.dataRealtime} />;
  },
  zalo: props => {
    switch (props.catalogCode) {
      case CATALOG_CODES.ZALO_OA:
        return <UIZaloOA data={props.dataRealtime} />;
      case CATALOG_CODES.ZNS:
        return (
          <UIZaloZNS
            destinationId={props.destinationId}
            data={props.dataRealtime}
            isCapture={props.isCapture}
          />
        );
      case CATALOG_CODES.ZALO_PAY_ZNS: {
        return <UIZaloPayZNS data={props.dataRealtime} />;
      }
      default:
        return null;
    }
  },
  webhook: props => {
    let Preview;

    switch (props.catalogCode) {
      case CATALOG_CODE.AEON_MALL_IN_APP_MESSAGE: {
        Preview = (
          <UIWebhookAeonMall {...getAeonMallInAppMessageSettings(props)} />
        );
        break;
      }
      default: {
        Preview = <UIWebHook data={props.dataRealtime} />;
      }
    }

    return Preview;
  },
  sms: props => <ChannelSmsPreview {...props} />,
  telegram: props => {
    return <UITelegramBlast data={props.dataRealtime} />;
  },
  whatsapp: props => <ChannelWhatsappPreview {...props} />,
  line: props => (
    <UILineApp
      data={props.dataRealtime}
      channelCode={props.channelCode}
      setObserverIndex={props.setObserverIndex}
    />
  ),
  viber: props => {
    // if (props.isBlastCampaign) {
    //   return <UIViberBlast data={props.dataViber} />;
    // }
    return (
      <UIViberBlast
        data={props.dataRealtime}
        channelCode={props.channelCode}
        catalogCode={props.catalogCode}
        dynamicFields={props.dynamicFields}
        isViewMode={props.isViewMode}
        isBlastCampaign={props.isBlastCampaign}
      />
    );
  },
};

const SELECTOR_MAP = {
  antsomi_app_push: ' >div:last-child',
};

function PreviewPage(props) {
  const { capture } = useCapture({
    selector: `#__destination_preview_wrapper > div${SELECTOR_MAP[
      props.catalogCode
    ] || ''}`,
    backgroundColor: props.channelCode === 'sms' ? 'transparent' : '#fff',
  });

  // useDeepCompareEffect(() => {
  //   const handleCapture = async () => {
  //     try {
  //       const thumbnail = await capture();

  //       if (thumbnail && props.onChangeThumbnail) {
  //         props.onChangeThumbnail(thumbnail);
  //       }
  //     } catch (e) {
  //       // console.log(e);
  //     }
  //   };

  //   handleCapture();
  // }, [props.data, props.dataSmartInbox, props.dataRealtime]);

  return (
    <div id="__destination_preview_wrapper">
      {(MAP_CHANNEL[props.channelCode] || (() => {}))({
        ...props,
        key: props.variantActiveId,
      }) || <div />}
    </div>
  );
}
PreviewPage.propTypes = {
  onChange: PropTypes.func,
  onChangeThumbnail: PropTypes.func,
};
PreviewPage.defaultProps = {
  onChange: () => {},
};

const mapStateToProps = createStructuredSelector({
  personalizations: makeSelectPersonalizations(),
});

function mapDispatchToProps(dispatch, props) {
  return {
    initPersonalizations: () => dispatch(initPersonalizations()),
    startPersonalizations: params => dispatch(startPersonalizations(params)),
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(memo(PreviewPage));
