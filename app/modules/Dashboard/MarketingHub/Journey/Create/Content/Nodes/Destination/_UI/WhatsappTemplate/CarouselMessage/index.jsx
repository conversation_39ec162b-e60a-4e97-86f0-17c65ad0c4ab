// Libraries
import React, { memo, useCallback, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import {
  get,
  isArray,
  toString as toStringLD,
  isFunction,
  isMap,
} from 'lodash';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

// Components
import { Flex, SlideBar } from '@antscorp/antsomi-ui';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import {
  BodyMessage,
  ButtonMessage,
  ErrorText,
  HeaderMessage,
  RowInput,
  TemplateStructure,
} from '../UI';
import ErrorBoundary from 'components/common/ErrorBoundary';

// Utils
import {
  extractUniquePlaceholderPatterns,
  filterSupportedButton,
  getError,
  PathStructure,
} from '../utils';

const PATH =
  'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/_UI/WhatsappTemplate/CarouselMessage/index.jsx';

const MAP_TRANSLATE = {
  placeholder: translate(translations._, 'Placeholder'),
};

const prefix = 'settings.carousel';
const { body: __bodyPath, indexKey: __indexKeyPath } = PathStructure;

const CarouselMessage = props => {
  // Props
  const {
    cards,
    body,
    setting,
    settingBody,
    otherData,
    itemTypeId,
    isBlastCampaign,
    isForceHideBtnPersonalization,
    eventValue,
    groupCodes,
    componentKey,
    isViewMode,
    errors,
    onChangeOthers,
    onChange,
  } = props;

  // State
  const [currentCardIdx, setCurrentCardIdx] = useState('0'); // Use string to prevent react beautiful dnd warning

  const cardOpts = useMemo(() => {
    if (!isArray(cards)) return [];
    return cards.map((_card, cardIdx) => ({
      label: toStringLD(cardIdx),
      value: toStringLD(cardIdx),
    }));
  }, [cards]);

  const slideBarErrors = useMemo(() => {
    if (!isMap(errors[0])) return [];
    const errorKeys = Array.from(errors[0].keys());

    return cardOpts.map((card, idx) => {
      const errCardKey = `carousel.cards[${idx}]`;
      if (errorKeys.some(eachErrKey => eachErrKey.includes(errCardKey)))
        return card.value;
      return undefined;
    });
  }, [errors, cardOpts]);

  const cardSetting = useMemo(
    () => get(setting, `cards[${currentCardIdx}]`, {}),
    [setting, currentCardIdx],
  );
  const cardItem = useMemo(() => get(cards, `[${currentCardIdx}]`, {}), [
    cards,
    currentCardIdx,
  ]);

  const handleCallbackSlideBar = useCallback(
    (type, newData) => {
      if (type === 'ACTIVE_SLIDE') {
        setCurrentCardIdx(newData?.value);

        if (isFunction(onChange)) {
          onChange(`${prefix}.cardIndex`, newData?.value);
        }
      }
    },
    [onChange],
  );

  const handleChangeStructure = useCallback(
    (path, newValue) => {
      if (isFunction(onChange)) {
        onChange(`${prefix}.cards[${currentCardIdx}].${path}`, newValue);
      }
    },
    [currentCardIdx, onChange],
  );

  const renderPlaceholders = () => {
    const placeholderList = extractUniquePlaceholderPatterns(body?.text || '');
    if (!placeholderList.length) return null;

    const content = placeholderList.map((eachPlaceholder, placeholderIdx) => {
      const path = `settings.body[${placeholderIdx}].value`;
      const label = `${MAP_TRANSLATE.placeholder} ${placeholderIdx + 1}`;
      const idxBodyErrPath = __bodyPath(placeholderIdx);
      const idxBodyPath = __indexKeyPath(placeholderIdx);
      const initValue = get(settingBody, idxBodyPath, '');

      return (
        <RowInput key={eachPlaceholder} isRequired label={label}>
          <TinymceEditor
            hasEmoji={false}
            name="text"
            typeComponent="input"
            enableShortLink
            initValue={initValue}
            otherData={otherData}
            itemTypeId={itemTypeId}
            isBlastCampaign={isBlastCampaign}
            isForceHideBtnPersonalization={isForceHideBtnPersonalization}
            eventValue={eventValue}
            groupCodes={groupCodes}
            componentKey={componentKey}
            errors={getError(errors, idxBodyErrPath)}
            isViewMode={isViewMode}
            onChangeOthers={onChangeOthers}
            onChange={newValue => onChange(path, newValue)}
          />
          <ErrorText message={getError(errors, idxBodyErrPath)} />
        </RowInput>
      );
    });

    return <Flex vertical>{content}</Flex>;
  };

  const renderCardCarousel = () => {
    if (!isArray(cards)) return null;

    return (
      <Flex vertical gap={10} style={{ padding: '5px 0px' }}>
        <SlideBar
          isDragDisabled
          borderless
          badgeNumber
          prefix="Slide"
          activeId={currentCardIdx}
          options={cardOpts}
          isShowAdd={false}
          size="medium"
          isMore={false}
          errors={slideBarErrors}
          callback={handleCallbackSlideBar}
        />

        <TemplateStructure
          key={`${prefix}.cards[${currentCardIdx}]`}
          Header={
            <ErrorBoundary path={PATH}>
              <HeaderMessage
                isCarousel
                cardIndex={currentCardIdx}
                setting={cardSetting?.header}
                header={cardItem?.header}
                otherData={otherData}
                itemTypeId={itemTypeId}
                isBlastCampaign={isBlastCampaign}
                isForceHideBtnPersonalization={isForceHideBtnPersonalization}
                eventValue={eventValue}
                groupCodes={groupCodes}
                componentKey={componentKey}
                isViewMode={isViewMode}
                errors={errors}
                onChangeOthers={onChangeOthers}
                onChange={handleChangeStructure}
              />
            </ErrorBoundary>
          }
          Body={
            <ErrorBoundary path={PATH}>
              <BodyMessage
                isCarousel
                cardIndex={currentCardIdx}
                setting={cardSetting?.body}
                body={cardItem?.body}
                otherData={otherData}
                itemTypeId={itemTypeId}
                isBlastCampaign={isBlastCampaign}
                isForceHideBtnPersonalization={isForceHideBtnPersonalization}
                eventValue={eventValue}
                groupCodes={groupCodes}
                componentKey={componentKey}
                isViewMode={isViewMode}
                errors={errors}
                onChangeOthers={onChangeOthers}
                onChange={handleChangeStructure}
              />
            </ErrorBoundary>
          }
          Buttons={
            <ErrorBoundary path={PATH}>
              <ButtonMessage
                isCarousel
                cardIndex={currentCardIdx}
                setting={cardSetting?.buttons}
                buttons={filterSupportedButton(cardItem?.buttons)}
                otherData={otherData}
                itemTypeId={itemTypeId}
                isBlastCampaign={isBlastCampaign}
                isForceHideBtnPersonalization={isForceHideBtnPersonalization}
                eventValue={eventValue}
                groupCodes={groupCodes}
                componentKey={componentKey}
                isViewMode={isViewMode}
                errors={errors}
                onChangeOthers={onChangeOthers}
                onChange={handleChangeStructure}
              />
            </ErrorBoundary>
          }
        />
      </Flex>
    );
  };

  return (
    <Flex vertical>
      {renderPlaceholders()}
      {renderCardCarousel()}
    </Flex>
  );
};

CarouselMessage.defaultProps = {
  cards: [],
  body: {},
  setting: { body: [], cards: [] },
  onChange: () => {},
};
CarouselMessage.propTypes = {
  cards: PropTypes.array,
  body: PropTypes.object,
  setting: PropTypes.object,
  settingBody: PropTypes.array,
  otherData: PropTypes.object,
  itemTypeId: PropTypes.number,
  isBlastCampaign: PropTypes.bool,
  isForceHideBtnPersonalization: PropTypes.bool,
  eventValue: PropTypes.object,
  groupCodes: PropTypes.array,
  componentKey: PropTypes.string,
  errors: PropTypes.array,
  isViewMode: PropTypes.bool,
  onChangeOthers: PropTypes.func,
  onChange: PropTypes.func,
};

export default memo(CarouselMessage);
