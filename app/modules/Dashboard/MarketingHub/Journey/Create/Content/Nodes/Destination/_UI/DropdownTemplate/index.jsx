/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */
/* eslint-disable react/prop-types */
// Libraries
import React, { useEffect } from 'react';
import { Flex } from '@antscorp/antsomi-ui';
import { useImmer } from 'use-immer';
import { isBoolean, isEmpty } from 'lodash';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import { GroupItem } from './GroupItem';

// Styled
import { WrapperUISquare } from '../../styles';
import { WrapperDropdown } from './styled';

// Services
import JourneyServices from '../../../../../../../../../../services/Journey';

// Utils
import { getTranslateMessage } from '../../../../../../../../../../containers/Translate/util';
import { mapDataToFe } from './utils';

export default function PopoverGroup(props) {
  const { callback, key, storyId, catalogId, isOpenPopover } = props;

  const [state, setState] = useImmer({
    searchData: [],
    txtSearch: '',
    dataCampaignSameStory: {},
    dataCampaignOtherStory: {},
    isOpenGroupCampaignSameStory: false,
    isOpenGroupCampaignOtherStory: false,
    isLoadingCampaignSameStory: true,
    isLoadingCampaignOtherStory: true,
    currentPageCampaignSameStory: 1,
    currentPageCampaignOtherStory: 1,
    isHasLoadMoreCampaignSameStory: true,
    isHasLoadMoreCampaignOtherStory: true,
  });

  const setLoadingCampaignSameStory = loading => {
    setState(draft => {
      draft.isLoadingCampaignSameStory = loading;
    });
  };

  const setLoadingCampaignOtherStory = loading => {
    setState(draft => {
      draft.isLoadingCampaignOtherStory = loading;
    });
  };

  const setOpenGroupCampaignSameStory = value => {
    setState(draft => {
      draft.isOpenGroupCampaignSameStory = isBoolean(value)
        ? value
        : !state.isOpenGroupCampaignSameStory;
    });
  };

  const setOpenGroupCampaignOtherStory = value => {
    setState(draft => {
      draft.isOpenGroupCampaignOtherStory = isBoolean(value)
        ? value
        : !state.isOpenGroupCampaignOtherStory;
    });
  };

  const setIsLoadMoreCampaignSameStory = value => {
    setState(draft => {
      draft.isHasLoadMoreCampaignSameStory = value;
    });
  };

  const setIsLoadMoreCampaignOtherStory = value => {
    setState(draft => {
      draft.isHasLoadMoreCampaignOtherStory = value;
    });
  };
  const fetchDataCampaignSameStoryAPI = async ({
    search = '',
    page = 1,
    isLoadMore = false,
  }) => {
    const params = {
      data: {
        catalogId,
        storyId: storyId || 0, // case create journey === 0
        isGroupingByStory: 1,
        sort: 'u_user_id',
        sd: 'asc',
        columns: [
          'campaign_id',
          'campaign_name',
          'campaign_name_multilang',
          'portal_id',
          'story_id',
          'channel_id',
          'destination_id',
          'zone_id',
          'type',
          'trigger_type',
          'start_date',
          'end_date',
          'campaign_setting',
          'status',
          'description',
          'description_multilang',
          'properties',
          'c_user_id',
          'u_user_id',
          'ctime',
          'utime',
          'api_temp_id',
          'catalog_id',
          'catalog_name',
          'story_name',
        ],
        isGetZoneInfo: 1,
        limit: 50,
        page,
        isSameStory: true,
        search,
      },
    };

    setLoadingCampaignSameStory(true);
    const res = await JourneyServices.campaigns.listingCampaignSameStoryById(
      params,
    );

    const dataCampaignSameStory = mapDataToFe(res.data[0]);

    // check load more
    if (isEmpty(dataCampaignSameStory.options)) {
      setIsLoadMoreCampaignSameStory(false);
    }

    if (!isLoadMore) {
      setState(draft => {
        draft.dataCampaignSameStory = dataCampaignSameStory;
      });
    } else {
      setState(draft => {
        draft.dataCampaignSameStory.list = [
          ...state.dataCampaignSameStory.list,
          ...dataCampaignSameStory.list,
        ];
        draft.dataCampaignSameStory.map = {
          ...state.dataCampaignSameStory.map,
          ...dataCampaignSameStory.map,
        };
      });
    }

    setLoadingCampaignSameStory(false);
  };
  const fetchDataCampaignOtherStoryAPI = async ({
    search = '',
    page = 1,
    isLoadMore = false,
  }) => {
    setLoadingCampaignOtherStory(true);

    const params = {
      data: {
        catalogId,
        storyId: storyId || 0, // case create journey === 0
        isGroupingByStory: 1,
        sort: 'u_user_id',
        sd: 'asc',
        columns: [
          'campaign_id',
          'campaign_name',
          'campaign_name_multilang',
          'portal_id',
          'story_id',
          'channel_id',
          'destination_id',
          'zone_id',
          'type',
          'trigger_type',
          'start_date',
          'end_date',
          'campaign_setting',
          'status',
          'description',
          'description_multilang',
          'properties',
          'c_user_id',
          'u_user_id',
          'ctime',
          'utime',
          'api_temp_id',
          'catalog_id',
          'catalog_name',
          'story_name',
        ],
        isGetZoneInfo: 1,
        limit: 50,
        page,
        isSameStory: false,
        search,
      },
    };

    const res = await JourneyServices.campaigns.listingCampaignOtherStoryById(
      params,
    );

    const dataCampaignOtherStory = mapDataToFe(res.data[0]);

    if (dataCampaignOtherStory.option.length === 0) {
      setIsLoadMoreCampaignOtherStory(false);
    }

    if (!isLoadMore) {
      setState(draft => {
        draft.dataCampaignOtherStory = dataCampaignOtherStory;
      });
    } else {
      setState(draft => {
        draft.dataCampaignOtherStory = {
          ...state.dataCampaignOtherStory,
          option: [
            ...state.dataCampaignOtherStory.option,
            ...dataCampaignOtherStory.option,
          ],
        };
      });
    }

    setLoadingCampaignOtherStory(false);
  };

  useEffect(() => {
    if (isOpenPopover) {
      fetchDataCampaignSameStoryAPI({});
      fetchDataCampaignOtherStoryAPI({});
      setIsLoadMoreCampaignOtherStory(true);
      setIsLoadMoreCampaignSameStory(true);
    }

    return () => {
      setState(draft => {
        draft.txtSearch = '';
        draft.currentPageCampaignSameStory = 1;
        draft.currentPageCampaignOtherStory = 1;
      });
    };
  }, [catalogId, isOpenPopover]);

  const onLoadMoreCampaignSameStory = () => {
    fetchDataCampaignSameStoryAPI({
      page: state.currentPageCampaignSameStory + 1,
      search: state.txtSearch,
      isLoadMore: true,
    });

    setState(draft => {
      draft.currentPageCampaignSameStory =
        state.currentPageCampaignSameStory + 1;
    });
  };

  const onLoadMoreCampaignOtherStory = () => {
    fetchDataCampaignOtherStoryAPI({
      page: state.currentPageCampaignOtherStory + 1,
      search: state.txtSearch,
      isLoadMore: true,
    });

    setState(draft => {
      draft.currentPageCampaignOtherStory =
        state.currentPageCampaignOtherStory + 1;
    });
  };

  const [anchorEl, setAnchorEl] = React.useState(null);

  const onSearch = txtVal => {
    setIsLoadMoreCampaignSameStory(true);
    setIsLoadMoreCampaignOtherStory(true);

    setState(draft => {
      draft.txtSearch = txtVal;
      draft.currentPageCampaignSameStory = 1;
      draft.currentPageCampaignOtherStory = 1;
    });

    fetchDataCampaignSameStoryAPI({ search: txtVal });
    fetchDataCampaignOtherStoryAPI({ search: txtVal });

    setOpenGroupCampaignOtherStory(true);
    setOpenGroupCampaignSameStory(true);
  };

  const onClickItem = item => {
    callback('ON_SELECT_VALUE_TEMPLATE', {
      itemSelected: item,
      itemSquare: props.item,
    });
    setAnchorEl(null);
  };

  const handleClick = (item, event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const id = open ? 'simple-popover' : undefined;

  useEffect(() => {
    callback('CHANGE_OPEN_POPOVER', open);
  }, [open]);

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/_UI/DropdownTemplate/index.jsx">
      <WrapperUISquare
        {...props}
        aria-describedby={id}
        variant="contained"
        color="primary"
        onClick={handleClick}
        key={key}
      />
      <WrapperDropdown
        popoverProps={{
          id,
          open,
          anchorEl,
          onClose: handleClose,
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'right',
          },
          transformOrigin: {
            vertical: 'top',
            horizontal: 'left',
          },
          PaperProps: {
            style: {
              minHeight: 15,
              overflowY: 'hidden',
            },
          },
        }}
        searchProps={{
          onChange: onSearch,
          value: state.searchValue,
        }}
      >
        <Flex vertical>
          <GroupItem
            onClickItem={onClickItem}
            toggle={setOpenGroupCampaignSameStory}
            item={state.dataCampaignSameStory}
            open={state.isOpenGroupCampaignSameStory}
            isLoading={state.isLoadingCampaignSameStory}
            label={getTranslateMessage(
              '_TITL_SAME_JOURNEY_CAMPAIGN',
              'Campaigns from this journeys',
            )}
            isHasLoadMore={state.isHasLoadMoreCampaignSameStory}
            loadMoreFunc={onLoadMoreCampaignSameStory}
          />
          <GroupItem
            onClickItem={onClickItem}
            toggle={setOpenGroupCampaignOtherStory}
            item={state.dataCampaignOtherStory}
            open={state.isOpenGroupCampaignOtherStory}
            isLoading={state.isLoadingCampaignOtherStory}
            label={getTranslateMessage(
              '_TITL_ALL_CAMPAIGN_IN_OTHER_JOURNEYS',
              'All campaigns in other journeys',
            )}
            isHasLoadMore={state.isHasLoadMoreCampaignOtherStory}
            loadMoreFunc={onLoadMoreCampaignOtherStory}
          />
        </Flex>
      </WrapperDropdown>
    </ErrorBoundary>
  );
}
