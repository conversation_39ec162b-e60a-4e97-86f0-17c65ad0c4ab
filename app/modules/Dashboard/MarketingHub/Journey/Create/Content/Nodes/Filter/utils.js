/* eslint-disable no-else-return */
import { Map } from 'immutable';

import {
  toAPINodeFilter,
  toUINodeFilter,
} from '../../../../../../../../components/common/UINodeFilter/utils';
import { validateRulesAudienceAttributes } from '../../../../../../../../components/common/UINodeFilter/_UI/AudienceAttributes/utils';
import { validateRulesEventAttributes } from '../../../../../../../../components/common/UINodeFilter/_UI/EventAttributes/utils';
import { validateDataAudienceSegment } from '../../../../../../../../components/Templates/TargetAudience/utils';
import { safeParse } from '../../../../../../../../utils/common';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';

export function toNodeFilterAPI(nodeInfo) {
  const dataNodeFilter = nodeInfo.get('filter');
  return toAPINodeFilter(dataNodeFilter);
}

export const validateNodeFilter = data => {
  try {
    if (data && typeof data.get === 'function') {
      const dataNodeFilter = safeParse(data.get('filter'), {});
      const { filterType = {} } = dataNodeFilter;

      if (filterType.value === 'user_attributes') {
        const dataNodeAudienceAttrs = dataNodeFilter[filterType.value];
        return validateRulesAudienceAttributes(dataNodeAudienceAttrs.rules);
      } else if (filterType.value === 'item_segment') {
        return validateDataAudienceSegment(dataNodeFilter[filterType.value]);
      } else if (filterType.value === 'event_attribute') {
        const dataNodeEventAttributes = dataNodeFilter[filterType.value];
        return validateRulesEventAttributes(dataNodeEventAttributes.rules);
      }
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Filter/utils.js',
      func: 'validateNodeFilter',
      data: err.stack,
    });
    // console.log(err);
  }
  return { status: true };
  // const res = validateDataAudienceSegment(dataTargetAudience);
  // return res;
};

export function toNodeFilterUI(nodeInfo) {
  const data = Map({
    filter: toUINodeFilter(nodeInfo),
  });
  return data;
}
