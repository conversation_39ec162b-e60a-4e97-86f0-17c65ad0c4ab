/* eslint-disable react/prop-types */
/* eslint-disable eqeqeq */
import React from 'react';
import Grid from '@material-ui/core/Grid';
import ErrorBoundary from 'components/common/ErrorBoundary';

import UIPerformEventV2 from '../../../../../../../../components/common/UIPerformEventV2';
import {
  ContainerNodeContent,
  useStyles,
} from '../../../../../../../../components/common/UISchedulerTrigger/styled';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import DelayAmountOfTime from '../../../../../../../../components/common/UIDelay/_UI/DelayAmountOfTime';

function NodeWaitEvent(props) {
  const classes = useStyles();
  // const { roleActions } = props;
  const { isViewMode } = props;

  // const info

  const onChange = data => {
    // console.log('onChange UIPerformEvent ===>', toAPIPerformEvent(data));
    props.onChange('peformEvent', data);
  };

  const onChangeTimeout = (type, data) => {
    props.onChange(type, data);
  };

  const callback = (type, data) => {
    // console.log(type, data);
    if (type === 'COMP_PROP_CONDITION_CHANGE_PROPERTY') {
      props.onChange(type, data);
    } else if (type === 'RESET_TRIGGER_PERFORM_EVENT') {
      props.callback('RESET_TRIGGER_PERFORM_EVENT', data);
    }
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/NodeWaitEvent/index.jsx">
      <UIPerformEventV2
        translateLabelTitle="_TITL_WAITING_EVENT"
        moduleConfig={props.moduleConfig}
        validateKey={props.validateKey}
        onChange={onChange}
        callback={callback}
        initData={props.initData.get('peformEvent')}
        componentId={props.componentId}
        showCustomTitle
        showTitle={false}
        // disabledEvent={!roleActions.has(STORY_SETUP_ACTION.EDIT_TRIGGER_NODE_EVENT)}
        // disabledEventConditions={
        //   !roleActions.has(STORY_SETUP_ACTION.EDIT_TRIGGER_EVENT_CONDITIONS)
        // }
        disabledEvent={false}
        disabledEventConditions={false}
        paramsFetchEvent={
          props.channelActive.value == 2
            ? { objectType: 'STORIES', insightPropertyTypes: 1 }
            : { objectType: 'STORIES' }
        }
        isMultiple
        isViewMode={isViewMode}
      />

      <ContainerNodeContent className={classes.root}>
        <Grid container className={classes.padding}>
          <Grid
            item
            sm={2}
            style={{
              textAlign: 'left',
              // marginRight: '1.25rem',
              marginTop: !isViewMode && '10px',
              maxWidth: '150px',
            }}
          >
            {getTranslateMessage(
              TRANSLATE_KEY._TITL_WAITING_FOR,
              'Waiting for',
            )}
          </Grid>
          <Grid item sm={9} lg={10}>
            <DelayAmountOfTime
              onChange={onChangeTimeout}
              initData={props.initData.get('timeout')}
              type="timeout"
              componentId={props.componentId}
              validateKey={props.validateKey}
              disabled={props.disabled}
              isViewMode={isViewMode}
            />
          </Grid>
        </Grid>
      </ContainerNodeContent>
    </ErrorBoundary>
  );
}

export default NodeWaitEvent;
