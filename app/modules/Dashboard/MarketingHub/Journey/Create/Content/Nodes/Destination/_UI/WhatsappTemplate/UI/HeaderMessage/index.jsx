// Libraries
import React, { useMemo, useCallback, memo } from 'react';
import PropTypes from 'prop-types';
import { partial } from 'lodash';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

// Components
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import RowInput from '../RowInput';
import { Flex, UploadImage } from '@antscorp/antsomi-ui';
import ErrorText from '../ErrorText';
import Hint from '../Hint';

// Utils
import { isProduction } from '../../../../../../../../../../../../utils/common';
import {
  getCurrentUserId,
  getToken,
} from '../../../../../../../../../../../../utils/web/cookie';
import {
  extractUniquePlaceholderPatterns,
  getError,
  PathStructure,
} from '../../utils';

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';

const { header: __headerPath } = PathStructure.carousel;

function HeaderMessage(props) {
  const {
    header,
    setting,
    otherData,
    itemTypeId,
    isBlastCampaign,
    isForceHideBtnPersonalization,
    eventValue,
    groupCodes,
    componentKey,
    isViewMode,
    isCarousel,
    cardIndex,
    errors,
    onChangeOthers,
    onChange,
  } = props;
  const { format } = header || {};

  const userId = getCurrentUserId();
  const token = getToken();

  const paramMemoized = useMemo(
    () => ({
      token,
      userId,
      accountId: userId,
    }),
    [token, userId],
  );
  const domainMedia = isProduction() ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX;

  const handleChangeText = useCallback(partial(onChange, 'header.headerData'), [
    onChange,
  ]);

  const handleChangeImage = useCallback(partial(onChange, 'header.imageURL'), [
    onChange,
  ]);

  const handleChangeVideo = useCallback(partial(onChange, 'header.videoURL'), [
    onChange,
  ]);

  const handleChangeDocName = useCallback(
    partial(onChange, 'header.documentName'),
    [onChange],
  );

  const handleChangeDoc = useCallback(partial(onChange, 'header.documentURL'), [
    onChange,
  ]);

  const handleChangeLong = useCallback(partial(onChange, 'header.longitude'), [
    onChange,
  ]);

  const handleChangeLat = useCallback(partial(onChange, 'header.latitude'), [
    onChange,
  ]);

  switch (format) {
    case 'TEXT': {
      const placeholders = extractUniquePlaceholderPatterns(header.text);

      if (!placeholders.length) return null;
      let errPath = 'header.headerData';
      if (isCarousel) {
        errPath = __headerPath(cardIndex, 'headerData');
      }

      return (
        <RowInput isRequired label={translate(translations._, 'Header data')}>
          <TinymceEditor
            hasEmoji={false}
            name="text"
            typeComponent="input"
            enableShortLink={false}
            initValue={setting?.headerData ?? ''}
            otherData={otherData}
            itemTypeId={itemTypeId}
            isBlastCampaign={isBlastCampaign}
            isForceHideBtnPersonalization={isForceHideBtnPersonalization}
            eventValue={eventValue}
            errors={getError(errors, errPath)}
            groupCodes={groupCodes}
            componentKey={componentKey}
            isViewMode={isViewMode}
            onChangeOthers={onChangeOthers}
            onChange={handleChangeText}
          />
          <ErrorText message={getError(errors, errPath)} />
        </RowInput>
      );
    }
    case 'IMAGE': {
      let errPath = 'header.imageURL';
      if (isCarousel) {
        errPath = __headerPath(cardIndex, 'imageURL');
      }

      return (
        <RowInput
          isRequired
          offsetTop={4}
          label={translate(translations._, 'Image URL')}
        >
          <Flex vertical gap={4}>
            <UploadImage
              isInputMode
              disabled={isViewMode}
              domainMedia={domainMedia}
              slug="api/v1"
              width="100%"
              paramConfigs={paramMemoized}
              selectedImage={{
                url: setting?.imageURL,
              }}
              placeholder={translate(translations._, 'Upload or input URL')}
              onChangeImage={newImage => {
                handleChangeImage(newImage?.url ?? '');
              }}
              onRemoveImage={() => {
                handleChangeImage('');
              }}
              maxSize={5}
              errors={getError(errors, errPath)}
            />
            <Hint
              description={translate(
                translations._,
                'Maximum of 5MB, type JPG, JPEG, PNG',
              )}
            />
          </Flex>
        </RowInput>
      );
    }
    case 'VIDEO': {
      let errPath = 'header.videoURL';
      if (isCarousel) {
        errPath = __headerPath(cardIndex, 'videoURL');
      }

      return (
        <RowInput
          isRequired
          offsetTop={4}
          label={translate(translations._, 'Video URL')}
        >
          <Flex vertical gap={4}>
            <UploadImage
              isInputMode
              domainMedia={domainMedia}
              disabled={isViewMode}
              slug="api/v1"
              mode="video"
              width="100%"
              paramConfigs={paramMemoized}
              selectedImage={{
                url: setting?.videoURL,
              }}
              placeholder={translate(translations._, 'Upload or input URL')}
              extensions={['.mp4', '.3gp']}
              onChangeImage={newVideo => {
                handleChangeVideo(newVideo?.url ?? '');
              }}
              onRemoveImage={() => {
                handleChangeVideo('');
              }}
              errors={getError(errors, errPath)}
              maxSize={16}
            />
            <Hint
              description={translate(
                translations._,
                'Maximum of 16MB, type MP4, 3GPP',
              )}
            />
          </Flex>
        </RowInput>
      );
    }
    case 'DOCUMENT': {
      let [errDocURLPath, errDocNamePath] = [
        'header.documentURL',
        'header.documentName',
      ];
      if (isCarousel) {
        errDocURLPath = __headerPath(cardIndex, 'documentURL');
        errDocNamePath = __headerPath(cardIndex, 'documentName');
      }

      return (
        <Flex vertical>
          <RowInput
            isRequired
            offsetTop={4}
            label={translate(translations._, 'Document URL')}
          >
            <TinymceEditor
              hasEmoji={false}
              name="text"
              typeComponent="input"
              enableShortLink
              initValue={setting?.documentURL ?? ''}
              otherData={otherData}
              itemTypeId={itemTypeId}
              isBlastCampaign={isBlastCampaign}
              isForceHideBtnPersonalization={isForceHideBtnPersonalization}
              eventValue={eventValue}
              groupCodes={groupCodes}
              componentKey={componentKey}
              errors={getError(errors, errDocURLPath)}
              isViewMode={isViewMode}
              onChangeOthers={onChangeOthers}
              onChange={handleChangeDoc}
            />
            <ErrorText message={getError(errors, errDocURLPath)} />
            <Hint
              description={translate(
                translations._,
                'Maximum of 100MB, type PDF',
              )}
            />
          </RowInput>
          <RowInput
            isRequired
            label={translate(translations._, 'Document Name')}
          >
            <TinymceEditor
              hasEmoji
              name="text"
              typeComponent="input"
              enableShortLink
              initValue={setting?.documentName ?? ''}
              otherData={otherData}
              itemTypeId={itemTypeId}
              isBlastCampaign={isBlastCampaign}
              isForceHideBtnPersonalization={isForceHideBtnPersonalization}
              eventValue={eventValue}
              groupCodes={groupCodes}
              componentKey={componentKey}
              isViewMode={isViewMode}
              errors={getError(errors, errDocNamePath)}
              onChangeOthers={onChangeOthers}
              onChange={handleChangeDocName}
            />
            <ErrorText message={getError(errors, errDocNamePath)} />
            <Hint
              description={translate(
                translations._,
                'Maximum of 240 character',
              )}
            />
          </RowInput>
        </Flex>
      );
    }

    case 'LOCATION': {
      let [errLatPath, errLongPath] = ['header.latitude', 'header.longitude'];
      if (isCarousel) {
        errLatPath = __headerPath(cardIndex, 'latitude');
        errLongPath = __headerPath(cardIndex, 'longitude');
      }

      return (
        <Flex vertical>
          <RowInput isRequired label={translate(translations._, 'Latitude')}>
            <TinymceEditor
              hasEmoji={false}
              name="text"
              typeComponent="input"
              enableShortLink={false}
              initValue={setting?.latitude ?? ''}
              otherData={otherData}
              itemTypeId={itemTypeId}
              isBlastCampaign={isBlastCampaign}
              isForceHideBtnPersonalization={isForceHideBtnPersonalization}
              eventValue={eventValue}
              groupCodes={groupCodes}
              componentKey={componentKey}
              errors={getError(errors, errLatPath)}
              isViewMode={isViewMode}
              onChangeOthers={onChangeOthers}
              onChange={handleChangeLat}
            />
            <ErrorText message={getError(errors, errLatPath)} />
          </RowInput>
          <RowInput isRequired label={translate(translations._, 'Longitude')}>
            <TinymceEditor
              hasEmoji={false}
              name="text"
              typeComponent="input"
              enableShortLink={false}
              initValue={setting?.longitude ?? ''}
              otherData={otherData}
              itemTypeId={itemTypeId}
              isBlastCampaign={isBlastCampaign}
              isForceHideBtnPersonalization={isForceHideBtnPersonalization}
              eventValue={eventValue}
              groupCodes={groupCodes}
              componentKey={componentKey}
              errors={getError(errors, errLongPath)}
              isViewMode={isViewMode}
              onChangeOthers={onChangeOthers}
              onChange={handleChangeLong}
            />
            <ErrorText message={getError(errors, errLongPath)} />
          </RowInput>
        </Flex>
      );
    }

    default: {
      return null;
    }
  }
}

HeaderMessage.propTypes = {
  header: PropTypes.any,
  setting: PropTypes.object,
  otherData: PropTypes.object,
  itemTypeId: PropTypes.number,
  isBlastCampaign: PropTypes.bool,
  isForceHideBtnPersonalization: PropTypes.bool,
  eventValue: PropTypes.object,
  groupCodes: PropTypes.array,
  componentKey: PropTypes.string,
  isViewMode: PropTypes.bool,
  isCarousel: PropTypes.bool,
  cardIndex: PropTypes.number,
  errors: PropTypes.array,
  onChangeOthers: PropTypes.func,
  onChange: PropTypes.func,
};
HeaderMessage.defaultProps = {
  header: {},
  setting: {},
  onChange: () => {},
};

export default memo(HeaderMessage);
