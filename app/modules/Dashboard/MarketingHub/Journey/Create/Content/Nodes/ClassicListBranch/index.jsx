/* eslint-disable react/prop-types */
import React from 'react';
import Grid from '@material-ui/core/Grid';
import { UINumber } from '@xlab-team/ui-components';
import ErrorBoundary from 'components/common/ErrorBoundary';
import parse from 'html-react-parser';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { makeStyles } from '@material-ui/core';
import { NODE_TYPE } from '../constant';
import { makeSelectConfigureMainCreateWorkflow } from '../../../selectors';
import { updateValue } from '../../../../../../../../redux/actions';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import InputPreview from '../../../../../../../../components/Atoms/InputPreview';

const labelNumofBr = getTranslateMessage(
  TRANSLATE_KEY._INFO_STORY_NUM_BRANCHES,
  ' Number of branches',
);
const labelNodeIntro = getTranslateMessage(
  TRANSLATE_KEY._USER_GUIDE_PARALLEL_FILTER,
  '<b>Node intro:</b> An audience can go through any of the Yes-branches whose condition is met.<br>Otherwise, the audience pass through the No-branch',
);

function ClassicListBranch(props) {
  const { isViewMode = false } = props;
  const number =
    props.mainConfigure.nodes.getIn([props.activeNode.nodeId, 'numBranchs']) ||
    2;
  const onChangeTimes = value => {
    props.onChange('numBranchs', value);
    props.onChangeNumberBranchNode({
      activeNode: props.activeNode,
      number: value,
    });
  };

  const useStyles = makeStyles(() => ({
    textLabel: {
      width: '150px',
      height: '15px',
      color: '#666',
      fontSize: '12px',
      textAlign: 'right',
    },
    styleUINumber: {
      display: 'inline',
      flexBasic: 'unset',
      marginLeft: '20px',
      color: '#000',
      fontSize: '13px',
    },
  }));
  const classes = useStyles();

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/ClassicListBranch/index.jsx">
      <Grid
        container
        className="p-x-4 p-bottom-4 p-top-2"
        style={{ minWidth: 'unset' }}
      >
        {props.type === NODE_TYPE.PARALLEL_LIST_BRANCH && (
          <Grid item xs={12} className="m-top-0 m-bottom-4">
            {parse(labelNodeIntro)}
          </Grid>
        )}
        <Grid item container alignItems="center">
          <div
            className={classes.textLabel}
            data-test="number-of-branches-label"
          >
            {labelNumofBr}
          </div>
          <div className={classes.styleUINumber} data-test="number-of-branches">
            <InputPreview
              value={number}
              isViewMode={isViewMode}
              type="input"
              style={{ fontWeight: 500 }}
            >
              <UINumber
                onChange={onChangeTimes}
                value={number}
                min={2}
                max={20}
                defaultValue={2}
                width="4rem"
                inputProps={{
                  style: {
                    paddingLeft: 14,
                    textAlign: 'center',
                  },
                  'data-test': 'number-of-branches-input',
                }}
              />
            </InputPreview>
          </div>
        </Grid>
      </Grid>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  mainConfigure: makeSelectConfigureMainCreateWorkflow(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig.key;
  return {
    onChangeNumberBranchNode: value =>
      dispatch(updateValue(`${prefix}@@NUMBER_OF_BRANCH@@`, value)),
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ClassicListBranch);
