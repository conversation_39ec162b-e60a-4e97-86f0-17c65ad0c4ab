/* eslint-disable no-restricted-syntax */
/* eslint-disable camelcase */
/* eslint-disable default-case */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable indent */
/* eslint-disable arrow-body-style */
/* eslint-disable react/prop-types */
import { generateKey } from '../../../../../../../utils/common';

export const CHANNEL_TYPES = {
  WEB_PER: 'web_personalization',
  EMAIL: 'email',
  SMS: 'sms',
  WEB_PUSH: 'web_push',
  CONVERSATION: 'conversation',
  APP_PUSH: 'app_push',
  VIBER: 'viber',
  WEBHOOK: 'webhook',
  JOURNEY: 'journey',
  FILE_TRANSFER: 'file_transfer',
  SMART_INBOX: 'smart_inbox',
  WHAT_SAPP: 'whatsapp',
  TELEGRAM: 'telegram',
  LINE: 'line',
  ZALO: 'zalo',
};

export const NODE_TYPE = {
  EVENT_BASED: 'EVENT_BASED',
  SCHEDULED: 'SCHEDULED',
  DELAY: 'DELAY',
  CLASSIC_LIST_BRANCH: 'CLASSIC_LIST_BRANCH',
  PARALLEL_LIST_BRANCH: 'PARALLEL_LIST_BRANCH',
  FILTER: 'FILTER',
  GO_TO: 'GO_TO',
  DESTINATION: 'DESTINATION',
  END: 'END',
  CONDITION_YES: 'CONDITION_YES',
  CONDITION_NO: 'CONDITION_NO',
  SPLIT_BRANCH: 'AB_SPLIT',
  SPLIT_NODE: 'AB_SPLIT_NODE',
  WAIT_EVENT: 'WAIT_EVENT',
  WAIT_EVENT_HAPPENED: 'WAIT_EVENT_HAPPENED',
  WAIT_EVENT_TIMEOUT: 'WAIT_EVENT_TIMEOUT',
  UPDATE_INFO: 'UPDATE_INFO',
  UPDATE_SEGMENT: 'UPDATE_SEGMENT',
  AB_SPLIT_CONTROL_NODE: 'AB_SPLIT_CONTROL_NODE',
  WFR_BRANCH: 'WAIT_FOR_RESPONSE',
  WFR_NODE: 'WAIT_FOR_RESPONSE_NODE',
  WFR_NODE_NO: 'WAIT_FOR_RESPONSE_NODE_NO',
};

export const NODE_DESTINATION_TYPE = {
  WEB_EMBEDDED: 'web_embedded',
};

export const NODE_ICON = {
  [NODE_TYPE.EVENT_BASED]: 'icon-antsomi-touch',
  [NODE_TYPE.SCHEDULED]: 'icon-antsomi-alarm',
  [NODE_TYPE.DELAY]: 'icon-antsomi-more-time',
  [NODE_TYPE.CLASSIC_LIST_BRANCH]: 'icon-antsomi-classic-branch',
  [NODE_TYPE.PARALLEL_LIST_BRANCH]: 'icon-antsomi-parallel-filter',
  [NODE_TYPE.FILTER]: 'icon-antsomi-filter-list',
  [NODE_TYPE.GO_TO]: '',
  [NODE_TYPE.DESTINATION]: '',
  [NODE_TYPE.END]: 'icon-antsomi-flag',
  [NODE_TYPE.CONDITION_YES]: 'icon-antsomi-check',
  [NODE_TYPE.CONDITION_NO]: 'icon-antsomi-close',
  // [NODE_TYPE.SPLIT_BRANCH]: 'icon-antsomi-arrow-forward',
  [NODE_TYPE.SPLIT_BRANCH]: 'icon-antsomi-call-split',
  [NODE_TYPE.SPLIT_NODE]: 'icon-antsomi-arrow-forward',
  [NODE_TYPE.WAIT_EVENT]: 'icon-antsomi-hourglass-top',
  [NODE_TYPE.WAIT_EVENT_HAPPENED]: 'icon-antsomi-check',
  [NODE_TYPE.WAIT_EVENT_TIMEOUT]: 'icon-antsomi-close',
  [NODE_TYPE.UPDATE_INFO]: 'icon-antsomi-edit-note',
  [NODE_TYPE.UPDATE_SEGMENT]: 'icon-antsomi-update-segment',
  [NODE_TYPE.AB_SPLIT_CONTROL_NODE]: 'icon-antsomi-arrow-forward',
  [NODE_TYPE.WFR_BRANCH]: 'icon-antsomi-response-routing',
  [NODE_TYPE.WFR_NODE]: 'icon-antsomi-check',
  [NODE_TYPE.WFR_NODE_NO]: 'icon-antsomi-close',
};

export const NODE_DESTINATION_ICON = {
  [NODE_DESTINATION_TYPE.WEB_EMBEDDED]: 'icon-antsomi-branding-watermark',
};

export const NODE_LABEL = {
  EVENT_BASED: 'Action-Based Trigger',
  WAIT_FOR_RESPONSE: 'Wait for response',
  SCHEDULED: 'Scheduled Trigger',
  CLASSIC_LIST_BRANCH: 'If/then branches',
  CONDITION_YES: 'Yes',
  CONDITION_NO: 'No',
  END: 'END',
};

export const DEFAULT_TRIGGER_TYPE = {
  [CHANNEL_TYPES.WEB_PER]: NODE_TYPE.EVENT_BASED,
  [CHANNEL_TYPES.EMAIL]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.SMS]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.WEB_PUSH]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.APP_PUSH]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.CONVERSATION]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.VIBER]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.WEBHOOK]: NODE_TYPE.SCHEDULED,
  // [CHANNEL_TYPES.JOURNEY]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.FILE_TRANSFER]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.SMART_INBOX]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.WHAT_SAPP]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.TELEGRAM]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.LINE]: NODE_TYPE.SCHEDULED,
  [CHANNEL_TYPES.ZALO]: NODE_TYPE.SCHEDULED,
};

export const TRIGGER_NODE = {
  [NODE_TYPE.EVENT_BASED]: {
    icon: NODE_ICON[NODE_TYPE.EVENT_BASED],
    iconUrl: '',
    label: NODE_LABEL.EVENT_BASED,
    nodeId: generateKey(),
    parentId: null,
    type: NODE_TYPE.EVENT_BASED,
  },
  [NODE_TYPE.SCHEDULED]: {
    icon: NODE_ICON[NODE_TYPE.SCHEDULED],
    iconUrl: '',
    label: NODE_LABEL.SCHEDULED,
    nodeId: generateKey(),
    parentId: null,
    type: NODE_TYPE.SCHEDULED,
  },
};

export const MAP_ITEMTYPE_ATTRS = {
  SCHEDULED: '-1009',
  EVENT_BASED: '-1009',
  DESTINATION: '-1010',
};

export const DATA_CATALOG_CODE = [
  'trello',
  'pnj_workplace_message',
  'caresoft',
  'antbuddy_order_ticket_pnj',
  'promotion_center',
];

export const DATA_CHANNEL_SMS = [
  'sms',
  'app_push',
  'web_push',
  'viber',
  'webhook',
];

export const CATALOG_CODE = {
  ONE_SIGNAL_APP_PUSH: 'onesignal_app_push',
  FIRE_BASE_APP_PUSH: 'firebase_app_push',
  ONEWAY_SMS: 'oneway_sms',
  LINE: 'line_app',
  LINE_RICH_MENU: 'line_rich_menu',
  ZALO_OA: 'zalo_official_account',
  ANTSOMI_APP_PUSH: 'antsomi_app_push',
  SMART_INBOX: 'smart_inbox_app',
  ZNS: 'zalo_notification_service',
  SMS_IMEDIA: 'sms_imedia',
  ZALO_IMEDIA: 'imedia_zns',
  SMS_FPT: 'sms_fpt',
  SMS_FPT_NEW: 'sms_fpt_new',
  WEBHOOK_GOLDEN_GATE: 'webhook_golden_gate',
  AEON_MALL_IN_APP_MESSAGE: 'aeon_mall_in_app_message',
  CARESOFT: 'caresoft',
  CARESOFT_ZNS: 'caresoft_zns',
  VIETTEL: 'sms_viettel',
  ACFC_APP_PUSH: 'acfc_app_push',
  INFOBIP_WHATSAPP_TEMPLATE: 'infobip_whatsapp_template',
  CF_HOUSE_APP_PUSH: 'the_coffee_house_app_push',
  ZALO_PAY_ZNS: 'zalo_pay_zns',
  PNJ_ZNS_V2: 'pnj_zns_v2',
};

export const CHANNEL_CODE = {
  WEBHOOK: 'webhook',
  CONVERSATION: 'conversation',
  EMAIL: 'email',
  VIBER: 'viber',
  SMS: 'sms',
  APP_PUSH: 'app_push',
  SMART_INBOX: 'smart_inbox',
  LINE: 'line',
  TELEGRAM: 'telegram',
  ZALO: 'zalo',
  WEB_PER: 'web_personalization',
};

export const MAP_DATA_DATE_TIME = {
  DD_MM_YYYY_HH_MM_SS: 'DD/MM/YYYY HH:mm:ss',
  DD_MM_YYYY: 'DD/MM/YYYY',
  MM_YYYY: 'MM/YYYY',
  YYYY: 'YYYY',
  '': '',
};

export const WFR_CONFIG = {
  MIN_BRANCHES: 2,
  MAX_BRANCHES: 20,
};
