/* eslint-disable no-param-reassign */
// Libraries
import React, { useState, memo, useCallback } from 'react';
import PropTypes from 'prop-types';
import produce from 'immer';
import isEqual from 'react-fast-compare';
import { isEmpty, isFunction, partial, set } from 'lodash';
import { useSelector } from 'react-redux';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

// Selectors
import { makeSelectCacheListWhatsappTemplateByKey } from '../../../../../../selectors';

// Hooks
import {
  buildCacheKeyWhatsappTemplate,
  useGetSavedWhatsappTemplate,
} from './hooks/useSavedWhatsappTemplate';

// Components
import {
  Flex,
  Spin,
  useDeepCompareEffect,
  useUpdateEffect,
} from '@antscorp/antsomi-ui';
import {
  BodyMessage,
  ButtonMessage,
  ErrorText,
  HeaderMessage,
  RowInput,
  TemplateStructure,
} from './UI';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import SelectTree from 'components/form/UISelectCondition';
import ErrorBoundary from 'components/common/ErrorBoundary';
import ExpirationTime from './ExpirationTime';
import CarouselMessage from './CarouselMessage';
import { GalleryTemplateIcon } from '@antscorp/antsomi-ui/es/components/icons';

// Utils
import {
  defaultState,
  filterSupportedButton,
  getError,
  initialState,
} from './utils';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

const PATH =
  'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/_UI/WhatsappTemplate/index.jsx';

function WhatsappTemplate(props) {
  // Props
  const {
    destinationId,
    initValue,
    otherData,
    itemTypeId,
    isBlastCampaign,
    isForceHideBtnPersonalization,
    eventValue,
    groupCodes,
    componentKey,
    isViewMode,
    errors,
    templateList,
    onChange,
    onChangeOthers,
    callbackWhatsappTemplate,
  } = props;

  const { data: newWhatsappTemplateList, status } = useSelector(
    makeSelectCacheListWhatsappTemplateByKey(
      buildCacheKeyWhatsappTemplate(destinationId),
    ),
  );

  // State
  const [state, setState] = useState(() => initialState(initValue));
  const { trackingId, template = {}, settings = {} } = state;

  const handleChangeTemplate = useCallback(
    newTemplate => {
      setState(prevState => {
        const nextState = produce(prevState, draft => {
          if (draft.template?.value !== newTemplate?.value) {
            return defaultState(newTemplate);
          }
        });

        onChange(nextState);
        return nextState;
      });
    },
    [onChange],
  );

  useGetSavedWhatsappTemplate({
    destinationId,
  });

  const produceNextState = useCallback(
    (prevState, path, newValue) =>
      produce(prevState, draft => {
        set(draft, path, newValue);
      }),
    [],
  );

  const handleBaseChange = useCallback(
    (path, newValue) => {
      setState(prevState => {
        const nextState = produceNextState(prevState, path, newValue);
        onChange(nextState);
        return nextState;
      });
    },
    [produceNextState, onChange],
  );

  const handleChangeSettings = useCallback(
    (path, newValue) => {
      setState(prevState => {
        const nextState = produceNextState(
          prevState,
          `settings.${path}`,
          newValue,
        );
        onChange(nextState);
        return nextState;
      });
    },
    [produceNextState, onChange],
  );

  const handleChangeTrackingId = useCallback(
    partial(handleBaseChange, 'trackingId'),
    [handleBaseChange],
  );
  // console.log('template :>>>', template);
  // console.log('WhatsappTemplate state :>>', state);

  // Clean up
  useUpdateEffect(() => {
    if (componentKey) {
      setState(initialState(initValue));

      return () => {
        setState(initialState());
      };
    }
  }, [initValue, componentKey]);

  useDeepCompareEffect(() => {
    if (!isFunction(callbackWhatsappTemplate)) return;

    if (
      newWhatsappTemplateList &&
      !isEqual(templateList, newWhatsappTemplateList)
    ) {
      callbackWhatsappTemplate(newWhatsappTemplateList, newValue => {
        setState(() => initialState(newValue));
      });
    }
  }, [templateList, newWhatsappTemplateList, callbackWhatsappTemplate]);

  const renderStructure = structure => {
    if (isEmpty(structure)) return null;

    const { header, body, buttons, limitedTimeOffer, carousel } = structure;

    // Template carousel
    if (carousel && carousel.cards) {
      return (
        <CarouselMessage
          settingBody={settings?.body}
          body={body}
          setting={settings?.carousel}
          cards={carousel.cards}
          otherData={otherData}
          itemTypeId={itemTypeId}
          isBlastCampaign={isBlastCampaign}
          isForceHideBtnPersonalization={isForceHideBtnPersonalization}
          eventValue={eventValue}
          groupCodes={groupCodes}
          componentKey={componentKey}
          isViewMode={isViewMode}
          errors={errors}
          onChangeOthers={onChangeOthers}
          onChange={handleBaseChange}
        />
      );
    }

    return (
      <TemplateStructure
        Header={
          <ErrorBoundary path={PATH}>
            <HeaderMessage
              setting={settings?.header}
              header={header}
              otherData={otherData}
              itemTypeId={itemTypeId}
              isCarousel={false}
              isBlastCampaign={isBlastCampaign}
              isForceHideBtnPersonalization={isForceHideBtnPersonalization}
              eventValue={eventValue}
              groupCodes={groupCodes}
              componentKey={componentKey}
              isViewMode={isViewMode}
              errors={errors}
              onChangeOthers={onChangeOthers}
              onChange={handleChangeSettings}
            />
          </ErrorBoundary>
        }
        ExpirationTime={
          limitedTimeOffer ? (
            <ErrorBoundary path={PATH}>
              <ExpirationTime
                setting={settings?.limitedTimeOffer}
                isViewMode={isViewMode}
                errors={errors}
                onChange={handleChangeSettings}
              />
            </ErrorBoundary>
          ) : null
        }
        Body={
          <ErrorBoundary path={PATH}>
            <BodyMessage
              setting={settings?.body}
              body={body}
              errors={errors}
              otherData={otherData}
              itemTypeId={itemTypeId}
              isCarousel={false}
              isBlastCampaign={isBlastCampaign}
              isForceHideBtnPersonalization={isForceHideBtnPersonalization}
              eventValue={eventValue}
              groupCodes={groupCodes}
              componentKey={componentKey}
              isViewMode={isViewMode}
              onChangeOthers={onChangeOthers}
              onChange={handleChangeSettings}
            />
          </ErrorBoundary>
        }
        Buttons={
          <ErrorBoundary path={PATH}>
            <ButtonMessage
              setting={settings?.buttons}
              buttons={filterSupportedButton(buttons)}
              otherData={otherData}
              itemTypeId={itemTypeId}
              isBlastCampaign={isBlastCampaign}
              isCarousel={false}
              isForceHideBtnPersonalization={isForceHideBtnPersonalization}
              eventValue={eventValue}
              groupCodes={groupCodes}
              componentKey={componentKey}
              isViewMode={isViewMode}
              errors={errors}
              onChangeOthers={onChangeOthers}
              onChange={handleChangeSettings}
            />
          </ErrorBoundary>
        }
      />
    );
  };

  return (
    <ErrorBoundary path={PATH}>
      <Flex vertical style={{ width: '100%' }}>
        <Spin spinning={status === 'loading'}>
          {/* Template row */}
          <RowInput
            offsetTop={4}
            isRequired={props.isRequired}
            label={props.label}
          >
            <SelectTree
              onlyParent
              options={templateList}
              style={{ width: '100%' }}
              use="tree"
              displayFormat
              isSearchable
              value={template}
              errors={getError(errors, 'template')}
              isViewMode={isViewMode}
              onChange={handleChangeTemplate}
              placeholder={translate(
                translations._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover
              searchNodataLabel={translate(
                translations._,
                'No available templates. Please check your destination.',
              )}
              searchNodataIcon={
                <GalleryTemplateIcon style={{ color: globalToken.bw5 }} />
              }
            />
            <ErrorText message={getError(errors, 'template', true)} />
          </RowInput>
          {/* Tracking id */}
          <RowInput label={translate(translations._, 'Tracking ID')}>
            <TinymceEditor
              hasEmoji={false}
              name="text"
              typeComponent="input"
              enableShortLink={false}
              initValue={trackingId}
              otherData={otherData}
              itemTypeId={itemTypeId}
              isBlastCampaign={isBlastCampaign}
              isForceHideBtnPersonalization={isForceHideBtnPersonalization}
              eventValue={eventValue}
              groupCodes={groupCodes}
              componentKey={componentKey}
              isViewMode={isViewMode}
              onChangeOthers={onChangeOthers}
              onChange={handleChangeTrackingId}
            />
          </RowInput>

          {/* Structure */}
          {renderStructure(template.structure)}
        </Spin>
      </Flex>
    </ErrorBoundary>
  );
}

WhatsappTemplate.defaultProps = {
  isRequired: true,
  label: translate(translations._WHATSAPP_TEMPLATE, 'Template'),
  onChange: () => {},
};
WhatsappTemplate.propTypes = {
  destinationId: PropTypes.oneOfType([PropTypes.number, PropTypes.string])
    .isRequired,
  isRequired: PropTypes.bool,
  label: PropTypes.string,
  initValue: PropTypes.object,
  otherData: PropTypes.object,
  itemTypeId: PropTypes.number,
  isBlastCampaign: PropTypes.bool,
  isForceHideBtnPersonalization: PropTypes.bool,
  eventValue: PropTypes.object,
  groupCodes: PropTypes.array,
  componentKey: PropTypes.string,
  isViewMode: PropTypes.bool,
  errors: PropTypes.array,
  templateList: PropTypes.array,
  onChangeOthers: PropTypes.func,
  onChange: PropTypes.func,
  callbackWhatsappTemplate: PropTypes.func,
};

export default memo(WhatsappTemplate);
