/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable no-restricted-syntax */
/* eslint-disable camelcase */
/* eslint-disable react/prop-types */
import React, { memo, useEffect, useState } from 'react';
import Grid from '@material-ui/core/Grid';
import UIFrequencyCapping from 'components/common/UIFrequencyCapping';
import ErrorBoundary from 'components/common/ErrorBoundary';

import { useImmer } from 'use-immer';
import _, { assignWith, isArray, keyBy } from 'lodash';
import { CellWifi } from '@material-ui/icons';
import UIPerformEvent from '../../../../../../../../components/common/UIPerformEvent';
import {
  ContainerNodeContent,
  useStyles,
} from '../../../../../../../../components/common/UISchedulerTrigger/styled';
import SelectTime from '../../../../../../../../components/common/UISchedulerTrigger/_UI/SelectTime';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { STORY_SETUP_ACTION } from '../../../utils.story.rules';
import WrapperSelectDayOfWeek from '../../../../../../../../containers/UIDev/SelectDayOfWeek/WrapperSelectDayOfWeek';
import { Title } from '../Destination/styles';
import { validateCustomInputJourney } from './utils';
import ConditionValueDropdownSelect from '../../../../../../../../containers/Filters/AddFilter/FormCondition/ConditionValue/ConditionValueDropdownSelect';
import { OPTIONS_SELECT_SEGMENTS } from '../../../../../../../../components/common/UITargetAudience/utils';
import { filterCustomInputAttributes } from '../utils';
import ConversionGoal from '../../../../../../../../containers/UIDev/ConversionGoal';

// const initData = {
//   // endCondition: 'ondate',
//   // endDate: '2020-10-05',
//   // endTime: 1601894493000,
//   // endTimeOfDay: { hour: 17, minute: 41 },
//   // startDate: '2020-10-25',
//   // startTime: 1601894493000,
//   // startTimeOfDay: { hour: 17, minute: 41 },
// };

// const initDataFrequency = {
//   timeUnit: 'week',
//   value: 12,
// };

const InputComponent = memo(props => (
  <Grid container className={props.classes.item} style={props.style}>
    {props.componentEl(props)}
  </Grid>
));
// const initDataPerformEvent = {};

function TriggerEventBased(props) {
  // console.log('props___11', props);
  const classes = useStyles();
  const {
    roleActions,
    dataInitInputViaUI = [],
    isLoadingInputViaUI,
    customInputAttributes,
  } = props;

  const [hiddenEndDate, setHiddenEndDate] = useState(false);

  const [state, setState] = useImmer({
    frequencyCapping: {
      timeUnit: 'week',
      value: 12,
    },
    dataInitInputViaUI: _.cloneDeep(dataInitInputViaUI),
  });

  useEffect(() => {
    if (!isLoadingInputViaUI) {
      const workspacesTmp = _.cloneDeep(dataInitInputViaUI);

      // workspacesTmp = filterAssociateOptions(workspacesTmp);

      // props.callback('SET_IS_LOADING', { isValidate });
      setState(draft => {
        draft.dataInitInputViaUI = _.cloneDeep(dataInitInputViaUI);
        // draft.dataInitInputViaUI = workspacesTmp;
      });
      props.onChange('customInput', workspacesTmp);
    }
    return () => {};
  }, [isLoadingInputViaUI]);

  // const info
  const onChange = data => {
    // console.log('onChange UIPerformEvent ===>', toAPIPerformEvent(data));
    props.onChange('peformEvent', data);
  };

  const onChangeSelectTime = data => {
    setHiddenEndDate(
      data.selectEndDate.value.value === 'never' && props.isViewMode,
    );
    // console.log('CHANGE_SCHEDULE ===>', toAPISelectTime(data));
    props.onChange('selectTime', data);
  };

  useEffect(() => {
    if (!props.isViewMode) {
      setHiddenEndDate(false);
    }
  }, [props.isViewMode]);

  const onChangeFrequencyCapping = data => {
    // const toAPI = {
    //   frequencyCapping: output,
    // };
    props.onChange('frequencyCapping', data);
    // console.log('toAPIFrequencyCapping', toAPIFrequencyCapping(data));
  };

  const callback = (type, data) => {
    // console.log(type, data);
    if (type === 'COMP_PROP_CONDITION_CHANGE_PROPERTY') {
      props.onChange(type, { ...data, type: 'eventBased' });
    } else if (type === 'RESET_TRIGGER_PERFORM_EVENT') {
      props.callback('RESET_TRIGGER_PERFORM_EVENT', data);
    }
  };

  const onChangeDataCustomInputVia = ({ value, type, workspaces }) => {
    const { infor, isValidate } = value;
    const { rowId, cellId } = infor;
    const workspacesTmp = [...workspaces];
    // console.log(value);
    // let dataCustomInputVia = _.cloneDeep(
    //   data.variants.cacheInfo[data.variants.activeId].customInputVia.workspaces,
    // );
    const indexRow = workspacesTmp.findIndex(each => each.id === rowId);
    const indexCell = workspacesTmp[indexRow].sections.findIndex(
      each => each.id === cellId,
    );

    workspacesTmp[indexRow].sections[indexCell].properties.value = value.value;
    workspacesTmp[indexRow].sections[
      indexCell
    ].properties.isValidate = isValidate;
    workspacesTmp[indexRow].sections[indexCell].errors = [];

    const updateValueCustomInputAttributes = customInputAttributes.map(attr => {
      const res = { ...attr };
      workspacesTmp.forEach(row => {
        row.sections.forEach(cell => {
          if (cell.id === attr.itemPropertyName) {
            res.propertiesValue = cell.properties.value;
          }
        });
      });
      return res;
    });

    const newCustomInputAttributes = filterCustomInputAttributes(
      updateValueCustomInputAttributes,
    );
    const mapNewCustomInputAttributes = keyBy(
      newCustomInputAttributes,
      'itemPropertyName',
    );

    workspacesTmp.forEach((row, rowIndex) => {
      row.sections.forEach((cell, cellIndex) => {
        if (cell.inputViaUiValue.isAssociateDropdown) {
          const { value: options, isMultiSelect } = mapNewCustomInputAttributes[
            cell.id
          ].inputViaUiValue;
          workspacesTmp[rowIndex].sections[cellIndex].option = options || [];
          // reset value
          const isExist = options.find(
            option =>
              !option.isHidden &&
              option.value ===
                workspacesTmp[rowIndex].sections[cellIndex].properties.value,
          );
          if (!isExist && !isMultiSelect) {
            workspacesTmp[rowIndex].sections[
              cellIndex
            ].properties.value = undefined;
          }

          // const isReset =
          //   newCustomInputAttributes.findIndex(
          //     item =>
          //       item.itemPropertyName === cellId &&
          //       item.inputViaUiValue.isPreSelectDropdown &&
          //       item.associateWiths.includes(cell.id),
          //   ) > -1;

          // if (isReset && cell.id !== cellId) {
          //   // reset all
          //   workspacesTmp[rowIndex].sections[
          //     cellIndex
          //   ].properties.value = undefined;
          // }
        }
      });
    });

    setState(draft => {
      draft.dataInitInputViaUI = workspacesTmp;
    });
    // props.callback('SET_IS_LOADING', { isValidate });
    props.onChange('customInput', workspacesTmp);
  };
  const onChangeSelectDayOfWeek = data => {
    props.onChange('selectDayOfWeek', data);
  };
  const onChaneInput = data => {
    props.onChange('customInput', data.value);
  };
  const onChangeConversion = data => {
    props.onChange('journeyGoals', data);
  };
  // console.log('roleActions', roleActions, '---', !roleActions.has(STORY_SETUP_ACTION.EDIT_TRIGGER_NODE_EVENT));
  // console.log('dataInitInputViaUI', dataInitInputViaUI, isLoadingInputViaUI);

  useEffect(() => {
    if (dataInitInputViaUI.length) {
      // console.log('is validating');

      // validate customInput

      // const isValidateCustomInputCampaign = validateCustomInputJourney(
      //   dataInitInputViaUI,
      // );
      const { isValidate, dataWorkspace } = validateCustomInputJourney(
        _.cloneDeep(state.dataInitInputViaUI),
      );

      // console.log('dataWorkspace', dataWorkspace);

      setState(draft => {
        draft.dataInitInputViaUI = _.cloneDeep(dataWorkspace);
      });
    }
  }, [props.validateKey]);

  // console.log('state.dataInitInputViaUI', state.dataInitInputViaUI);
  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/TriggerEventBased/index.jsx">
      <ConversionGoal
        initData={props.initData.get('journeyGoals')}
        onChange={onChangeConversion}
        validateKey={props.validateKey}
        isViewMode={props.isViewMode}
        prefixKey={props.moduleConfig.key}
        componentId={props.componentId}

      />
      <ContainerNodeContent
        data-test="scheduler-trigger"
        className={`{${classes.root} ${classes.rootNoBottom}`}
      >
        <Grid container className={`${classes.padding} ${classes.flexNoWrap}`}>
          <Grid
            item
            sm={2}
            className={`${classes.customView} ${classes.mdText}`}
          >
            {getTranslateMessage(
              TRANSLATE_KEY._INFO_STORY_SCHEDULE,
              'Scheduled',
            )}
          </Grid>
          <Grid container item sm={10} md={8}>
            <SelectTime
              isViewMode={props.isViewMode}
              design={props.design}
              onChange={onChangeSelectTime}
              initData={props.initData.get('selectTime')}
              componentId={props.componentId}
              validateKey={props.validateKey}
              isShowAt
              disabledEndDate={
                !roleActions.has(STORY_SETUP_ACTION.EDIT_END_DATE)
              }
              disabledStartDate={
                !roleActions.has(STORY_SETUP_ACTION.EDIT_START_DATE)
              }
              hiddenEndDate={hiddenEndDate}
            />
          </Grid>
        </Grid>
      </ContainerNodeContent>
      <ContainerNodeContent
        className={!hiddenEndDate && !props.isViewMode && classes.root}
      >
        <Grid
          container
          className={`${
            props.isViewMode ? classes.unsetPaddingTop : classes.paddingX
          } ${classes.flexNoWrap}`}
        >
          <Grid item sm={2} className={classes.customView} />
          <Grid container item sm={10} md={8}>
            <WrapperSelectDayOfWeek
              isViewMode={props.isViewMode}
              onChange={onChangeSelectDayOfWeek}
              initData={props.initData.get('selectDayOfWeek')}
              validateKey={props.validateKey}
            />
          </Grid>
        </Grid>
      </ContainerNodeContent>

      <UIPerformEvent
        moduleConfig={props.moduleConfig}
        validateKey={props.validateKey}
        onChange={onChange}
        callback={callback}
        initData={props.initData.get('peformEvent')}
        componentId={props.componentId}
        showCustomTitle
        showTitle={false}
        isViewMode={props.isViewMode}
        disabledEvent={
          !roleActions.has(STORY_SETUP_ACTION.EDIT_TRIGGER_NODE_EVENT)
        }
        disabledEventConditions={
          !roleActions.has(STORY_SETUP_ACTION.EDIT_TRIGGER_EVENT_CONDITIONS)
        }
        hasOpenModalConfirm={props.hasOpenModalConfirm}
        paramsFetchEvent={
          props.channelActive.value == 2
            ? { objectType: 'STORIES', insightPropertyTypes: '1,2' }
            : { objectType: 'STORIES' }
        }
      />
      <UIFrequencyCapping
        onChange={onChangeFrequencyCapping}
        initData={props.initData.get('frequencyCapping')}
        componentId={props.componentId}
        isViewMode={props.isViewMode}
        channelId={props.channelActive.value}
        disabled={!roleActions.has(STORY_SETUP_ACTION.EDIT_FREQUENCY_CAPPING)}
        isHiddenRecordEvent
      />
      {!isLoadingInputViaUI && state.dataInitInputViaUI.length ? (
        <ContainerNodeContent className={classes.root}>
          <Grid
            container
            className={`${classes.paddingX} ${classes.flexNoWrap}`}
          >
            <Grid item sm={2} className={classes.customView}>
              <span style={{ color: 'red' }}>* </span>Custom Input
            </Grid>
            <Grid item sm={10} md={9}>
              {state.dataInitInputViaUI.map(row => (
                <Grid
                  container
                  key={row.id}
                  spacing={3}
                  style={{ paddingBottom: '8px' }}
                >
                  {row.sections.map(cell => (
                    <Grid item xs key={cell.id}>
                      <Title className={props.classNameTitle}>
                        {cell.properties.label}
                        {!!cell.properties.isRequired && ' *'}
                      </Title>
                      <InputComponent
                        {...cell}
                        option={cell.option}
                        onChange={value => {
                          onChangeDataCustomInputVia({
                            value,
                            type: 'customInputs',
                            workspaces: [...state.dataInitInputViaUI],
                          });
                          onChangeDataCustomInputVia({
                            value,
                            type: 'customInputs',
                            workspaces: [...state.dataInitInputViaUI],
                          });
                        }}
                        value={cell.properties.value}
                        isViewMode={props.isViewMode}
                        infor={{
                          rowId: row.id,
                          cellId: cell.id,
                        }}
                        classes={{ item: '' }}
                        customInputs={state.dataInitInputViaUI}
                      />
                    </Grid>
                  ))}
                </Grid>
              ))}
            </Grid>
          </Grid>

          {/* <Grid container className={classes.padding}>
              <Grid item sm={2}>
                Custom Input
              </Grid>
              <Grid container item sm={10}>
                <RenderWorkspace
                  workspaces={
                    props.initData.get('customInput')
                      ? mapCustomInputToFe(
                          props.initData.get('customInput'),
                          dataInitInputViaUI,
                        )
                      : dataInitInputViaUI
                  }
                  callback={(type, value) => {
                    onChaneInput({ type, value });
                  }}
                />
              </Grid>
            </Grid> */}
        </ContainerNodeContent>
      ) : (
        <></>
      )}
    </ErrorBoundary>
  );
}

export default TriggerEventBased;
