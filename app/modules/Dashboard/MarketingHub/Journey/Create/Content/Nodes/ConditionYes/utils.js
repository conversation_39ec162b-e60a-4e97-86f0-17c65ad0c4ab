import { Map } from 'immutable';
import { addMessageToQueue } from 'utils/web/queue';
import {
  toAPINodeFilter,
  toUINodeFilter,
} from '../../../../../../../../components/common/UINodeFilter/utils';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { safeParse } from '../../../../../../../../utils/common';
import { validateNodeFilter } from '../Filter/utils';
import { wrapperToUITargetAudience } from '../TriggerScheduled/utils';
import { convertDeliveryFilterToBlastFilter } from '../../../BlastCampaign/utils';

const PATH =
  '/home/<USER>/Projects/fe-app.cdp.asia/app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/ConditionYes/utils.js';

export function toNodeConditionYesAPI(nodeInfo, activeNode) {
  const filters = toAPINodeFilter(nodeInfo.get('filter'));
  const meta = {
    branchName: nodeInfo.get('branchName') || activeNode.label,
    ...filters,
  };

  return meta;
}

export function toNodeConditionYesUI(metadata) {
  const filter = toUINodeFilter(metadata);
  const data = Map({
    branchName: metadata.branchName,
    filter,
  });

  return data;
}

export function toNodeBlastCampaignAudiences(metadata) {
  const state = convertDeliveryFilterToBlastFilter(metadata.filters);
  const newFilter = wrapperToUITargetAudience(state);
  const data = Map({
    branchName: metadata.branchName,
    filter: newFilter,
  });

  return data;
}

export const validateNameBranchIfThen = name => {
  const res = { status: true, errors: [] };
  const nameBranch = safeParse(name, '');

  if (nameBranch.length === 0) {
    res.status = false;
    res.errors = [
      getTranslateMessage(
        TRANSLATE_KEY._NOTI_EMPTY_NAME,
        "Name can't be empty",
      ),
    ];
  } else if (nameBranch.length > 255) {
    res.status = false;
    res.errors = [
      getTranslateMessage(
        TRANSLATE_KEY._INVALID_NAME_MAX_LENGTH,
        'Invalid name: maximum 255 characters',
      ),
    ];
  }

  return res;
};

export const validateNodeYes = nodeInfo => {
  const res = { status: true, errors: [] };
  try {
    const validateFilter = validateNodeFilter(nodeInfo);

    const name = nodeInfo.get('branchName') || '';
    if (validateFilter.status === false) {
      res.status = false;
    }

    const dataError = validateNameBranchIfThen(name);

    if (dataError.status === false) {
      res.status = false;
      res.errors = dataError.errors;
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/ConditionYes/utils.js',
      func: 'validateNodeYes',
      data: err.stack,
    });
    console.log(err);
  }
  return res;
};
