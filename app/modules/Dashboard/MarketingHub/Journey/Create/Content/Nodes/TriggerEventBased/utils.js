import { Map } from 'immutable';
import {
  toAPIFrequencyCapping,
  toUIFrequencyCapping,
} from '../../../../../../../../components/common/UIFrequencyCapping/utils';
import {
  toAPISelectTime,
  toUISelectTime,
  validateSelectTime,
} from '../../../../../../../../components/common/UISchedulerTrigger/utils';
import { safeParse } from '../../../../../../../../utils/common';
import {
  toAPIPerformEvent,
  toUIPerformEvent,
} from '../../../../../../../../components/common/UIPerformEvent/utils';
import { validateRulesConditionPerformEvent } from '../../../../../../../../components/common/UIPerformEvent/utils.validate';
import {
  toAPISelectDayOfWeek,
  toUISelectDayOfWeek,
  validateSelectDayOfWeek,
} from '../../../../../../../../containers/UIDev/SelectDayOfWeek/utils';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
import moment from 'moment';
import _ from 'lodash';
import { validateConversion } from '../../../../../../../../containers/UIDev/ConversionGoal/utils';

export function toNodeTriggerEventBasedAPI(nodeInfo, channelActive) {
  const peformEvent = safeParse(nodeInfo.get('peformEvent'), {});
  const selectTime = safeParse(nodeInfo.get('selectTime'), {});
  const journeyGoals = safeParse(nodeInfo.get('journeyGoals'), {});
  const frequencyCapping = safeParse(nodeInfo.get('frequencyCapping'), {});
  //   const frequencyCapping = nodeInfo.get('frequencyCapping');
  const selectDayOfWeek = safeParse(nodeInfo.get('selectDayOfWeek'), {});
  const dataApiSelectTime = toAPISelectTime(selectTime).frequencyTime;
  const meta = {
    ...dataApiSelectTime,
    event: toAPIPerformEvent(peformEvent),
    frequencyCapping: toAPIFrequencyCapping(frequencyCapping, channelActive),
    ...toAPISelectDayOfWeek(selectDayOfWeek),
    journeyGoals: toConversonAPI(journeyGoals),
  };
  // metadata: {
  //   endCondition: "never" || "onDate"
  //   startDate: "2020-09-30"
  //   startTimeOfDay: {
  //     hour: 13
  //     minute: 10
  //   }
  //   endDate: "2020-10-30"
  //   endTimeOfDay: {
  //     hour: 13
  //     minute: 10
  //   }
  //   startTime: timestamp
  //   endTime: timestamp
  //   event: {}
  //   frequencyCapping: {}
  // }
  return meta;
}
export function toConversionUI(data) {
  const dataOut = {
    journeyGoal: 'none',

    goal: [],
  };
  if (Object.keys(data).length > 0) {
    dataOut.journeyGoal = 'specific';
  } else {
    dataOut.goal.push({
      title: 'First goal',
      value: null,
      error: [],
    });
  }
  Object.keys(data).forEach((key, index) => {
    dataOut.goal.push({
      title: STTUI[index],
      value: data[key].conversionId,
      error: [],
    });
  });
  return dataOut;
}
export function toConversonAPI(data) {
  const dataOut = {};
  if (data.journeyGoal === 'specific') {
    data.goal.map((each, index) => {
      dataOut[STTFE[index]] = { conversionId: each.value };
    });
  }
  return dataOut;
}
const STTFE = ['1st_goal', '2nd_goal'];
const STTUI = ['First goal', 'Second goal'];

export function toNodeTriggerEventBasedUI(metadata, customInput) {
  // const peformEvent = safeParse(nodeInfo.get('peformEvent'), {});
  // const selectTime = safeParse(nodeInfo.get('selectTime'), {});
  // const frequencyCapping = safeParse(nodeInfo.get('frequencyCapping'), {});
  //   const frequencyCapping = nodeInfo.get('frequencyCapping');
  const {
    event,
    timeOfWeek = [],
    triggerType = 'all',
    frequencyCapping,
    journeyGoals = {},
    ...dataApiSelectTime
  } = metadata;
  // const uiDataApiSelectTime = {};
  const uiDataApiSelectTime = toUISelectTime(dataApiSelectTime).frequencyTime;
  // const uiPeformEvent = undefined;
  const uiPeformEvent = toUIPerformEvent(event);
  const uiFrequencyCapping = toUIFrequencyCapping(frequencyCapping);
  const uiSelectDayOfWeek = toUISelectDayOfWeek({ timeOfWeek, triggerType });
  const uiConversion = toConversionUI(journeyGoals);
  // const meta = {
  //   ...dataApiSelectTime,
  //   event: toAPIPerformEvent(peformEvent),
  //   frequencyCapping: toAPIFrequencyCapping(frequencyCapping),
  // };

  const data = Map({
    customInput,
    selectTime: uiDataApiSelectTime,
    peformEvent: uiPeformEvent,
    frequencyCapping: uiFrequencyCapping,
    selectDayOfWeek: uiSelectDayOfWeek,
    journeyGoals: uiConversion,
  });

  return data;
}

export function validateNodeTriggerEventBased(
  nodeInfo,
  isValidateFormatDateTime,
) {
  const res = { status: true, errors: [] };

  try {
    const peformEvent = safeParse(nodeInfo.get('peformEvent'), {});
    const dataScheduled = nodeInfo.get('selectTime');
    const dataSelectDayOfWeek = nodeInfo.get('selectDayOfWeek');
    const customInputs = nodeInfo.get('customInput');
    const journeyGoals = safeParse(nodeInfo.get('journeyGoals'), {});
    // console.log(customInputs);
    const { isValidate, dataWorkspace } = validateCustomInputJourney(
      customInputs,
    );

    const dataValidatePerformEvent = validateRulesConditionPerformEvent(
      peformEvent,
    );
    const dataValidateSelectTime = validateSelectTime({
      frequencyTime: dataScheduled,
    });
    const dataValidateSelectDayOfWeek = validateSelectDayOfWeek(
      dataSelectDayOfWeek,
    );
    const { dataValidateConversion } = validateConversion(journeyGoals);
    if (isValidate === false) {
      res.status = false;
      res.errors = '';
    }
    if (isValidateFormatDateTime === false) {
      res.status = false;
      res.errors = '';
    }
    if (dataValidatePerformEvent.status === false) {
      res.status = false;
      res.errors = dataValidatePerformEvent.errors;
    }
    if (dataValidateSelectTime.status === false) {
      res.status = false;
      res.errors = dataValidateSelectTime.errors;
    }
    if (dataValidateSelectDayOfWeek.status === false) {
      res.status = false;
      res.errors = dataValidateSelectDayOfWeek.errors;
    }
    if (dataValidateConversion === false) {
      res.status = false;
      res.errors = '';
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/TriggerEventBased/utils.js',
      func: 'validateNodeTriggerEventBased',
      data: err.stack,
    });
    console.log(err);
  }

  return res;
}
export const mapCustomInputToFe = (customInput, dataInitInputViaUI) => {
  // console.log('dataInitInputViaUI', dataInitInputViaUI);
  const dataClone = _.cloneDeep(dataInitInputViaUI);
  dataInitInputViaUI.forEach((item, key) => {
    item.sections.forEach((each, index) => {
      if (customInput && customInput[each.id]) {
        let value = customInput[each.id];
        if (
          dataClone[key].sections[index].properties.input_type ===
          'singleCheckbox'
        ) {
          value = !!customInput[each.id];
        }
        // else if (
        //   dataClone[key].sections[index].properties.input_type === 'datePicker'
        // ) {
        //   dataClone[key].sections[index].properties.isValidate = true;
        // }

        dataClone[key].sections[index].properties = {
          ...dataClone[key].sections[index].properties,
          value,
        };
      }

      if (
        !_.isEmpty(dataClone[key].sections) &&
        dataClone[key].sections[index].properties.input_type === 'datePicker'
      ) {
        dataClone[key].sections[index].properties.isValidate = true;
      }
    });
  });
  // console.log('dataClone___', dataClone);
  return dataClone;
};
export const mapCustomInputToFeActive = customInput => {
  const dataClone = _.cloneDeep(customInput);

  return dataClone;
};
export const validateCustomInputJourney = data => {
  // validate customInput
  const isValidateCustomCampaignArr = [];
  const dataWorkspace = _.cloneDeep(data);
  // console.log(dataWorkspace);
  if (dataWorkspace && Object.keys(dataWorkspace).length > 0) {
    dataWorkspace.forEach(each => {
      if (each.sections.length > 0) {
        each.sections.forEach(item => {
          // console.log('item', item);
          if (item.validate && item.name !== 'datePicker') {
            const { errors, isValidate } = item.validate({
              ...item,
              ...item.properties,
              errors: [],
            });
            item.errors = errors;
            item.isValidate = isValidate;
            isValidateCustomCampaignArr.push(isValidate);
          }
          if (item.name === 'datePicker') {
            item.isValidate = item.properties.isValidate;
            isValidateCustomCampaignArr.push(item.properties.isValidate);
          }
        });
      }
    });
  }

  // console.log('isValidateCustomCampaignArr', isValidateCustomCampaignArr);
  const isValidateCustomCampaign = isValidateCustomCampaignArr.every(
    each => each === true,
  );

  // console.log('isValidateCustomAttributeArr', isValidateCustomAttribute);

  return { isValidate: isValidateCustomCampaign, dataWorkspace };
};
