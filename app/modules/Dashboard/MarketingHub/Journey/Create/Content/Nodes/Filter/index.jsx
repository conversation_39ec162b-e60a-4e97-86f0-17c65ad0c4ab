/* eslint-disable react/prop-types */
import React from 'react';
import Grid from '@material-ui/core/Grid';
import ErrorBoundary from 'components/common/ErrorBoundary';

import UINodeFilter from '../../../../../../../../components/common/UINodeFilter';
import { STORY_SETUP_ACTION } from '../../../utils.story.rules';
// import { toAPIUIDelay } from '../../../../../../../../components/common/UIDelay/utils';

// const dataEvent = {
//   eventActionId: -102,
//   eventCategoryId: -20,
// };

function NodeFilter(props) {
  const onChange = data => {
    props.onChange('filter', data);
  };
  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Filter/index.jsx">
      <Grid container className="p-all-4">
        <Grid item sm={12}>
          <UINodeFilter
            design={props.design}
            moduleConfig={props.moduleConfig}
            onChange={onChange}
            initData={props.initData.get('filter')}
            validateKey={props.validateKey}
            componentId={props.componentId}
            eventValue={props.eventValue}
            triggerType={props.triggerType}
            isViewMode={props.isViewMode}
            itemTypeId={props.itemTypeId}
            disabled={
              !props.roleActions.has(STORY_SETUP_ACTION.EDIT_ACTION_NODE)
            }
            // hasOpenModalConfirm={props.hasOpenModalConfirm}
          />
        </Grid>
      </Grid>
    </ErrorBoundary>
    // <UINodeFilter onChange={onChange} initData={props.initData.get('filter')} />
  );
}

export default NodeFilter;
