/* eslint-disable no-param-reassign */
/* eslint-disable no-else-return */
/* eslint-disable react/prop-types */
import { IconButton } from '@material-ui/core';
import PlayArrowIcon from '@material-ui/icons/PlayArrow';
import { UIButton } from '@xlab-team/ui-components';
import clx from 'classnames';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { Map } from 'immutable';
import _, { get, isEmpty } from 'lodash';
import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import JourneyServices from 'services/Journey';
import { useImmer } from 'use-immer';
import UIIconXlab from '../../../../../../../components/common/UIIconXlab';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { update, updateValue } from '../../../../../../../redux/actions';
import { safeParse } from '../../../../../../../utils/common';
import { addMessageToQueue } from '../../../../../../../utils/web/queue';
import { MODULE_CONFIG } from '../../../config';
import { makeSelectJourneyChannelActive } from '../../../selectors';
import {
  makeSelectConfigureCreateWorkflow,
  makeSelectMainCreateWorkflow,
} from '../../selectors';
import { NodeProcess } from '../NodeProcess';
import {
  StyledNameNode,
  StyleToggleButton,
  UnderConstruction,
  WrapperResize,
} from '../styles';
import ClassicListBranch from './ClassicListBranch';
import ConditionNo from './ConditionNo';
import ConditionYes from './ConditionYes';
import Delay from './Delay';
import Destination from './Destination/LayoutDestination';
import Filter from './Filter';
import SplitBranch from './SplitBranch';
import SplitNode from './SplitNode';
import TriggerEventBased from './TriggerEventBased';
import {
  mapCustomInputToFe,
  mapCustomInputToFeActive,
} from './TriggerEventBased/utils';
import TriggerScheduled from './TriggerScheduled';
import NodeUpdateInfo from './UpdateInfo';
import NodeUpdateSegment from './UpdateSegment';
import NodeWaitEvent from './WaitEvent';
import { NODE_TYPE } from './constant';
import { serializeDataInputAttrs } from './utils';
import { SYSTEM_BO } from '../../../../../../../utils/constants';
import { WFRBranch } from './WFRBranch';
import { WFRNode } from './WFRNode';
import { updateNodeData } from '../../actions';

const PATH =
  'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/index.jsx';

const MAP_TITLE = {
  resumeFromTriggerNode: getTranslateMessage(TRANSLATE_KEY._, 'Resume'),
};

function WrapperNode(props) {
  const {
    activeNode,
    triggerNode,
    configure,
    roleActions,
    channelActive,
    isExpand,
    onChangeExpand,
    // isCloseDataflow,
    onChangeCloseDataflow,
    moduleConfig,
    isViewMode,
    main,
    typeResume,
    isDisplayDiagramBlastCampaign = false,
    keyResume,
  } = props;
  const {
    validateKey,
    updateFreshNodesKey,
    cacheNodes,
    nodes,
    previousNodes,
    triggerEvent,
    triggerEventBased,
    itemTypeId = '-1007',
    hasOpenModalConfirm,
    destinationNode: { isEnableTesting },
    errorKey,
    errors,
    errorsSchedule,
  } = configure.main;

  const { type } = activeNode;
  const [state, setState] = useImmer({
    inputStories: [],
    customInputAttributes: [],
    isLoadingInputViaUI: true,
    isDisableResume: false,
  });
  const params = useParams();
  const searchParams = new URLSearchParams(window.location.search);

  const scheduleId = searchParams.get('scheduleId') || params.scheduleId;
  // const nodeStatus = nodes.getIn([activeNode.nodeId, 'status']) || 1;
  const nodeStatus =
    // đọc sau khi onChange
    nodes.getIn([activeNode.nodeId, 'status']) ||
    // đọc cho lần đầu
    // safeParse(nodes.getIn([activeNode.nodeId, 'destination']), {}).status ||
    // -1;
    safeParse(nodes.getIn([activeNode.nodeId, 'destination']), {}).status ||
    1;

  const nodeStatusUpdateSegment =
    // đọc sau khi onChange
    nodes.getIn([activeNode.nodeId, 'status']) ||
    // đọc cho lần đầu
    // safeParse(nodes.getIn([activeNode.nodeId, 'destination']), {}).status ||
    // -1;
    safeParse(nodes.getIn([activeNode.nodeId, 'updateSegment']), {}).status ||
    1;

  const onChangeStatus = () => {
    props.onChangeData({
      nodeId: activeNode.nodeId,
      name: 'status',
      data: nodeStatus === 1 ? 2 : 1,
    });
  };
  const onChangeStatusNodeUpdateSegment = () => {
    props.onChangeData({
      nodeId: activeNode.nodeId,
      name: 'status',
      data: nodeStatusUpdateSegment === 1 ? 2 : 1,
    });
  };

  const onChangeData = (name, data) => {
    props.onChangeData({ nodeId: activeNode.nodeId, name, data });
  };
  const onResume = () => {
    setState(draft => {
      draft.isDisableResume = true;
    });
    props.onChangeTypeResume({
      storyType: scheduleId ? 'schedule' : 'action',
      keyResume,
    });
    props.updateOpenFullScreen(false);
  };
  const onExpand = () => {
    onChangeExpand({
      isExpand: !isExpand,
      isClose: !!isExpand,
    });
    onChangeCloseDataflow(false);
  };

  // useEffect(() => {
  //   if (type === NODE_TYPE.DESTINATION) {
  //     onChangeStatus();
  //   }
  // }, [activeNode.nodeId]);
  // const labelButtonQuickTest = getTranslateMessage(
  //   TRANSLATE_KEY._ACT_QUICK_TEST,
  //   'Quick Test',
  // );

  useEffect(() => {
    const initData = safeParse(cacheNodes.get(activeNode.nodeId), Map({}));
    // console.log(initData);

    setState(draft => {
      draft.isLoadingInputViaUI = true;
    });

    JourneyServices.data
      .getAttrsCustomInput({
        itemTypeId: SYSTEM_BO.Journey.itemTypeId,
      })
      .then(res => {
        if (res.code !== 200 || isEmpty(res.data)) {
          return null;
        }

        setState(draft => {
          const customInputs =
            props.design === 'create'
              ? get(main, 'activeRowClone.custom_inputs', {}) || {}
              : get(main, 'activeRow.custom_inputs', {}) || {};
          const inputAttributes = get(
            res,
            'data[0].customInputAttributes',
            [],
          ).map(item => {
            return {
              ...item,
              propertiesValue: get(customInputs, `[${item.itemPropertyName}]`),
            };
          });

          if (initData.get('customInput')) {
            // console.log(initData);
            const tmp = initData.get('customInput');
            // setState(draft => {

            const { dataOut, customInputAttributes } = serializeDataInputAttrs({
              customInputAttributes: inputAttributes,
              positions: res.data[0].positions,
            });
            draft.inputStories = Array.isArray(tmp)
              ? mapCustomInputToFeActive(tmp)
              : _.cloneDeep(mapCustomInputToFe(tmp, dataOut));
            draft.customInputAttributes = customInputAttributes;
            // });
          } else {
            const { dataOut, customInputAttributes } = serializeDataInputAttrs({
              customInputAttributes: inputAttributes,
              positions: res.data[0].positions,
            });
            draft.inputStories = dataOut;
            draft.customInputAttributes = customInputAttributes;
          }

          // console.log(initData.get('customInput'));
          // console.log(draft.inputStories, initData);
          // props.initDataCustomInput({
          //   nodeId: activeNode.nodeId,
          //   data: draft.inputStories,
          // });
          if (type === NODE_TYPE.SCHEDULED || type === NODE_TYPE.EVENT_BASED) {
            props.initDataCustomInput({
              nodeId: activeNode.nodeId,
              data: draft.inputStories,
            });
          }

          draft.isLoadingInputViaUI = false;
        });
        return () => {
          setState(draft => {
            draft.isLoadingInputViaUI = false;
          });
        };
      })
      .catch(err => {
        if (!err.isCanceled) {
          addMessageToQueue({
            path: PATH,
            func: 'cancellablePromise',
            data: err.stack,
          });

          // eslint-disable-next-line no-console
          console.warn('err', err);
        }
      });

    // }
  }, [type, activeNode.nodeId]);
  const callback = (typeCallback, data) => {
    // console.log('typeCallback, data', typeCallback, data);
    if (typeCallback === 'RESET_TRIGGER_TARGET_AUDIENCE') {
      props.resetTriggetTargetAudience(data);
    } else if (typeCallback === 'RESET_TRIGGER_PERFORM_EVENT') {
      props.resetTriggetPerformEvent(data);
    } else if (typeCallback === 'UPDATE_VALUE_TESTING') {
      props.updateValueTesting(data);
    } else if (typeCallback === 'SET_IS_LOADING') {
      props.validateFormatDateTiem(data);
      // setState(draft => {
      //   draft.isLoadingInputViaUI = true;
      // });
    }
  };
  const showContent = () => {
    const initData = safeParse(cacheNodes.get(activeNode.nodeId), Map({}));
    // console.log('triggerNode', triggerNode, JSON.stringify(triggerNode));
    if (type === NODE_TYPE.SCHEDULED) {
      return (
        <TriggerScheduled
          moduleConfig={moduleConfig}
          roleActions={roleActions}
          errorsSchedule={errorsSchedule}
          onChange={onChangeData}
          errors={errors}
          validateKey={validateKey}
          initData={initData}
          componentId={activeNode.nodeId}
          itemTypeId={itemTypeId}
          isViewMode={isViewMode}
          channelActive={channelActive}
          design={props.design}
          callback={callback}
          hasOpenModalConfirm={hasOpenModalConfirm}
          dataInitInputViaUI={state.inputStories}
          isLoadingInputViaUI={state.isLoadingInputViaUI}
          customInputAttributes={state.customInputAttributes}
          isDisplayDiagramBlastCampaign={isDisplayDiagramBlastCampaign}
        />
      );
    } else if (type === NODE_TYPE.EVENT_BASED) {
      return (
        <TriggerEventBased
          moduleConfig={moduleConfig}
          roleActions={roleActions}
          validateKey={validateKey}
          onChange={onChangeData}
          initData={initData}
          componentId={activeNode.nodeId}
          design={props.design}
          isViewMode={isViewMode}
          callback={callback}
          hasOpenModalConfirm={hasOpenModalConfirm}
          channelActive={channelActive}
          dataInitInputViaUI={state.inputStories}
          customInputAttributes={state.customInputAttributes}
          isLoadingInputViaUI={state.isLoadingInputViaUI}
        />
      );
    } else if (type === NODE_TYPE.DESTINATION) {
      // console.log('showContent - initData', initData.toJS());

      return (
        <Destination
          moduleConfig={moduleConfig}
          roleActions={roleActions}
          previousNodes={previousNodes}
          validateKey={validateKey}
          updateFreshNodesKey={updateFreshNodesKey}
          onChange={onChangeData}
          activeNode={activeNode}
          initData={cacheNodes.getIn([activeNode.nodeId, 'destination'])}
          triggerType={triggerNode.type}
          eventValue={triggerEvent}
          itemTypeId={itemTypeId}
          callback={callback}
          isEnableTesting={isEnableTesting}
          errorKey={errorKey}
          componentId={activeNode.nodeId}
          isViewMode={isViewMode}
          isDisplayDiagramBlastCampaign={isDisplayDiagramBlastCampaign}
          // templateInputViaUI={state.inputStories}
          // isLoadingInputViaUI={state.isLoadingInputViaUI}
        />
      );
    } else if (
      type === NODE_TYPE.CLASSIC_LIST_BRANCH ||
      type === NODE_TYPE.PARALLEL_LIST_BRANCH
    ) {
      return (
        <ClassicListBranch
          moduleConfig={moduleConfig}
          validateKey={validateKey}
          activeNode={activeNode}
          onChange={onChangeData}
          isViewMode={isViewMode}
          type={type} // param dung de render node type tương ứng
        />
      );
    } else if (type === NODE_TYPE.CONDITION_NO) {
      return (
        <ConditionNo
          moduleConfig={moduleConfig}
          validateKey={validateKey}
          activeNode={activeNode}
          onChange={onChangeData}
          isViewMode={isViewMode}
        />
      );
    } else if (type === NODE_TYPE.CONDITION_YES) {
      return (
        <ConditionYes
          moduleConfig={moduleConfig}
          roleActions={roleActions}
          validateKey={validateKey}
          activeNode={activeNode}
          onChange={onChangeData}
          initData={initData}
          componentId={activeNode.nodeId}
          itemTypeId={itemTypeId}
          eventValue={triggerEvent}
          triggerType={triggerNode.type}
          isViewMode={isViewMode}
          isDisplayDiagramBlastCampaign={isDisplayDiagramBlastCampaign}
        />
      );
    } else if (type === NODE_TYPE.DELAY) {
      return (
        <Delay
          moduleConfig={moduleConfig}
          roleActions={roleActions}
          validateKey={validateKey}
          activeNode={activeNode}
          onChange={onChangeData}
          initData={initData}
          componentId={activeNode.nodeId}
          isViewMode={isViewMode}
          channelActive={props.channelActive}
        />
      );
    } else if (type === NODE_TYPE.FILTER) {
      return (
        <Filter
          moduleConfig={moduleConfig}
          roleActions={roleActions}
          validateKey={validateKey}
          activeNode={activeNode}
          triggerType={triggerNode.type}
          eventValue={triggerEventBased}
          onChange={onChangeData}
          design={props.design}
          initData={initData}
          componentId={activeNode.nodeId}
          itemTypeId={itemTypeId}
          isViewMode={isViewMode}
          // onChange={onChangeDataDebug}
          // hasOpenModalConfirm={hasOpenModalConfirm}
        />
      );
    } else if (type === NODE_TYPE.SPLIT_BRANCH) {
      return (
        <SplitBranch
          moduleConfig={moduleConfig}
          validateKey={validateKey}
          activeNode={activeNode}
          onChange={onChangeData}
          isViewMode={isViewMode}
        />
      );
    } else if (
      type === NODE_TYPE.SPLIT_NODE ||
      type === NODE_TYPE.AB_SPLIT_CONTROL_NODE
    ) {
      return (
        <SplitNode
          moduleConfig={moduleConfig}
          initData={initData}
          validateKey={validateKey}
          activeNode={activeNode}
          onChange={onChangeData}
          componentId={activeNode.nodeId}
          isViewMode={isViewMode}
        />
      );
    } else if (type === NODE_TYPE.WAIT_EVENT) {
      return (
        <NodeWaitEvent
          roleActions={roleActions}
          validateKey={validateKey}
          onChange={onChangeData}
          initData={initData}
          componentId={activeNode.nodeId}
          design={props.design}
          callback={callback}
          channelActive={channelActive}
          isViewMode={isViewMode}
        />
      );
    } else if (type === NODE_TYPE.UPDATE_INFO) {
      // console.log('triggerNode', triggerNode, JSON.stringify(triggerNode));
      const triggerData = safeParse(
        cacheNodes.get(triggerNode.nodeId),
        Map({}),
      );

      return (
        <NodeUpdateInfo
          moduleConfig={moduleConfig}
          triggerData={triggerData}
          triggerType={triggerNode.type}
          roleActions={roleActions}
          validateKey={validateKey}
          onChange={onChangeData}
          initData={initData}
          componentId={activeNode.nodeId}
          design={props.design}
          callback={callback}
          channelActive={channelActive}
          isViewMode={isViewMode}
        />
      );
    } else if (type === NODE_TYPE.UPDATE_SEGMENT) {
      return (
        <NodeUpdateSegment
          moduleConfig={moduleConfig}
          triggerType={triggerNode.type}
          roleActions={roleActions}
          validateKey={validateKey}
          onChange={onChangeData}
          initData={initData}
          componentId={activeNode.nodeId}
          design={props.design}
          callback={callback}
          channelActive={channelActive}
          previousNodes={previousNodes}
          updateFreshNodesKey={updateFreshNodesKey}
          activeNode={activeNode}
          eventValue={triggerEvent}
          itemTypeId={itemTypeId}
          isEnableTesting={isEnableTesting}
          errorKey={errorKey}
          isViewMode={isViewMode}
        />
      );
    } else if (type === NODE_TYPE.WFR_BRANCH) {
      return (
        <WFRBranch
          moduleConfig={moduleConfig}
          isViewMode={isViewMode}
          validateKey={validateKey}
        />
      );
    } else if (type === NODE_TYPE.WFR_NODE || type === NODE_TYPE.WFR_NODE_NO) {
      return (
        <WFRNode
          moduleConfig={moduleConfig}
          isViewMode={isViewMode}
          validateKey={validateKey}
        />
      );
    }

    return (
      <UnderConstruction>
        <p>This node under construction</p>
      </UnderConstruction>
    );
  };

  return (
    <>
      <div className="popup">
        <div className="popup-title d-flex justify-content-between">
          {props.activeNodeMulti.length > 0 ? (
            <StyledNameNode>
              {`Selected node(s): ${props.activeNodeMulti.length}`}
            </StyledNameNode>
          ) : (
            <StyledNameNode>{activeNode.label}</StyledNameNode>
          )}

          {type === NODE_TYPE.DESTINATION && !isViewMode && (
            <div className="d-flex justify-content-end align-items-center">
              <StyleToggleButton
                name="destination-status"
                className="node-status"
                isToggle={nodeStatus === 1}
                handleClick={onChangeStatus}
                handleChange={() => {}}
              />
            </div>
          )}

          {type === NODE_TYPE.UPDATE_SEGMENT && (
            <div className="d-flex width-100 justify-content-end align-items-center">
              <StyleToggleButton
                name="update-segment-status"
                className="node-status"
                isToggle={nodeStatusUpdateSegment === 1}
                handleClick={onChangeStatusNodeUpdateSegment}
                handleChange={() => {}}
              />
            </div>
          )}

          <div style={{ display: 'flex', gap: '10px' }}>
            {typeResume && (
              <UIButton
                theme="outline"
                onClick={onResume}
                className="btn-trigger"
                disabled={state.isDisableResume}
                // style={{ display: 'none' }}
              >
                <PlayArrowIcon />
                {MAP_TITLE.resumeFromTriggerNode}
              </UIButton>
            )}

            <IconButton size="small" onClick={onExpand}>
              <UIIconXlab
                name={isExpand ? 'fullscreen-exit' : 'fullscreen'}
                className="setting-icon"
                style={{ fontSize: 24, color: '#005eb8' }}
              />
            </IconButton>

            <IconButton size="small">
              <UIIconXlab
                name="close"
                className="setting-icon"
                style={{ fontSize: 24, color: '#005eb8' }}
                onClick={() => props.onChangeCloseDataflow(true)}
              />
            </IconButton>
          </div>
        </div>

        {/* Force render when change active node with key */}
        <div className="popup-main" key={activeNode.nodeId}>
          {!props.activeNodeMulti.length > 0 && (
            <div
              className={clx('popup-wrapper', {
                'height-100 overflow-hidden': type === NODE_TYPE.SCHEDULED,
              })}
            >
              <WrapperResize isViewMode={props.isViewMode}>
                <ErrorBoundary
                  path={PATH}
                  debug={window.location.href.includes('debug-error')}
                >
                  {showContent()}
                </ErrorBoundary>
              </WrapperResize>

              {activeNode.nodeProcessInfo ? (
                <NodeProcess nodeProcessInfo={activeNode.nodeProcessInfo} />
              ) : null}
            </div>
          )}
        </div>
      </div>
    </>
  );
}

const mapStateToProps = createStructuredSelector({
  configure: makeSelectConfigureCreateWorkflow(),
  channelActive: makeSelectJourneyChannelActive(),
  main: makeSelectMainCreateWorkflow(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig.key;

  return {
    validateFormatDateTiem: value =>
      dispatch(updateValue(`${prefix}@@VALIDATE_FORMAT@@`, value)),
    initDataCustomInput: value =>
      dispatch(updateValue(`${prefix}@@GET_DATA_INIT_INPUT@@`, value)),
    onChangeData: (value = {}) =>
      dispatch(updateNodeData({ prefix, ...value })),
    resetTriggetTargetAudience: value =>
      dispatch(
        updateValue(`${prefix}@@RESET_TRIGGER_TARGET_AUDIENCE@@`, value),
      ),
    resetTriggetPerformEvent: value =>
      dispatch(updateValue(`${prefix}@@RESET_TRIGGER_PERFORM_EVENT@@`, value)),
    toggleDestinationModalTesting: value =>
      dispatch(
        updateValue(`${prefix}@@DESTINATION_TOGGLE_MODAL_TESTING@@`, value),
      ),
    updateValueTesting: value =>
      dispatch(update(`${prefix}@@DATA_RUN_TEST@@`, value)),
    onChangeTypeResume: params => {
      dispatch(update(`${prefix}@@RESUME_TRIGGER`, params));
    },
    updateOpenFullScreen: params => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@TOGGLE_FULL_SCREEN@@`, params),
      );
    },
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(WrapperNode);
