/* eslint-disable no-nested-ternary */
/* eslint-disable camelcase */
/* eslint-disable dot-notation */
/* eslint-disable indent */
/* eslint-disable consistent-return */
/* eslint-disable import/no-cycle */
/* eslint-disable operator-assignment */
import { Map } from 'immutable';
import moment from 'moment';
import React from 'react';

// Assets
import BrushOutlinedIcon from '@material-ui/icons/BrushOutlined';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import MailOutlineIcon from '@material-ui/icons/MailOutline';
import _, { cloneDeep, get, keyBy, map } from 'lodash';
import PhotoLibraryIcon from '@material-ui/icons/PhotoLibrary';
import ButtonMessage from 'assets/images/LineTemplate/ButtonMessage.png';
import CarouselMessage from 'assets/images/LineTemplate/CarouselMessage.png';
import ConfirmationMessage from 'assets/images/LineTemplate/ConfirmationMessage.png';
import ImageCarouselMessage from 'assets/images/LineTemplate/ImageCarousel.png';
import ImageMessage from 'assets/images/LineTemplate/ImageMessage.png';
import ImageMapMessage from 'assets/images/LineTemplate/MapMessage.png';
import ImageMapMessageAdvanced from 'assets/images/LineTemplate/MapMessageAdvanced.png';
import StickerMessage from 'assets/images/LineTemplate/StickerMessage.png';
import TextMessage from 'assets/images/LineTemplate/TextMessage.png';
import MediaZaloMessage from 'assets/images/ZaloTemplates/media.png';
import RequestInfoZaloMessage from 'assets/images/ZaloTemplates/request-info.png';
import RichMediaZaloMessage from 'assets/images/ZaloTemplates/rich-media.png';
import StickerZaloMessage from 'assets/images/ZaloTemplates/sticker.png';
import TextZaloMessage from 'assets/images/ZaloTemplates/text.png';
import TransactionZaloMessage from 'assets/images/ZaloTemplates/transaction.png';
import BasicNotificationAppPush from 'assets/images/AppPushTemplates/thumbnail-basic-notification.png';
import StylizedBasicAppPush from 'assets/images/AppPushTemplates/thumbnail-stylized-basic.png';
import SimpleImageCarouselAppPush from 'assets/images/AppPushTemplates/thumbnail-simple-image-carousel.png';
import SmallImageAppPush from 'assets/images/AppPushTemplates/thumbnail-small-image.png';
import RatingAppPush from 'assets/images/AppPushTemplates/thumbnail-rating.png';
import _isEmpty from 'lodash/isEmpty';
import _keys from 'lodash/keys';
import _pickBy from 'lodash/pickBy';

import MediaJsonIcon from 'images/icons/media-json.svg';
import {
  getObjectPropSafely,
  isProduction,
  isStaging,
  random,
  safeParse,
  safeParseInt,
} from '../../../../../../../../utils/common';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
import {
  MAP_INPUT_TYPE,
  MAP_VALIDATE,
  // MAP_TYPE_RENDER,
  getTypeRender,
} from './utils.form';
// import { FileCopyOutlinedIcon, BrushOutlinedIcon } from '@material-ui/icons';
import {
  EMAIL_TEMPLATE,
  JSON_TEMPLATE,
  MEDIA_TEMPLATE,
} from '../../../../../../../../components/common/UIEditorPersonalization/utils';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { CONFIG_OBJECT_STATUS } from '../../../../../../../../utils/constants';
import {
  parseObjectNumeric,
  serializeLabelToCodeAttr,
} from '../../../../../../../../utils/web/utils';
import {
  CATALOG_CODES,
  TEMPLATE_TAG,
  TEMPLATE_TYPES,
  TEMPLATE_ZALO_OA_TYPES,
} from '../../../../../Destination/CreateV2/Design/Templates/constants';
import { mapInputToAPI } from '../../../_reducer/utils';
import { CATALOG_CODE, CHANNEL_CODE, NODE_TYPE } from '../constant';
import {
  DEFAULT_ACTION_KEYS,
  DEFAULT_BASIC_INPUTS,
  DESTINATION_TYPE,
  GEN_RATING_TEXT,
  ICON_TYPE_KEY,
  KEYS_ORDER_TEMPLATE_FIRST,
  KEYS_ORDER_TEMPLATE_SECOND,
  LINE_ALL_FIELD,
  LINE_FIELDS,
  LIST_FIELD_HIDE_WHEN_UNCHECKED_URL,
  LIST_HIDE_WHEN_CHECKED_URL,
  LIST_ID_TEMPLATE_SMART_INBOX,
  MODIFIED_FIELDS,
  PREVIEW_FIELDS,
  RATING_SETTING_KEYS,
  RECOMMEND_FIELDS,
  initPlaceholder,
} from './constants';
import {
  INIT_INPUT_SETTINGS,
  initialInputElementRatingSettings,
} from './variant.config';
import {
  TEMPLATES as TEMPLATE_ANTSOMI_APP_PUSH,
  TEMPLATE_KEYS as TEMPLATE_ANTSOMI_APP_PUSH_KEYS,
} from 'containers/UIPreview/AppPushPreview/AppPushTemplate/constants';
import { initVariants } from './utils.state';
import { loopVariantExtraData } from '../../../utils.map';
import { patternHandlers } from '@antscorp/antsomi-ui';

const MAP_TITLE = {
  customDesign: {
    label: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Design from scratch'),
    desc: getTranslateMessage(
      TRANSLATE_KEY._DESTINATION_DESIGN_OPTION_SCRATCH_INTRO,
      'Custom design your campaign',
    ),
  },
  chooseTemplate: {
    label: getTranslateMessage(TRANSLATE_KEY.DATLE, 'Choose a Template'),
    desc: getTranslateMessage(
      TRANSLATE_KEY._DESTINATION_DESIGN_OPTION_SCRATCH_INTRO,
      'Use a message template for your campaign',
    ),
  },
  copyDesign: {
    label: getTranslateMessage(
      TRANSLATE_KEY._DESTINATION_DESIGN_OPTION_EXIST,
      'Load Existing Design',
    ),
    desc: getTranslateMessage(
      TRANSLATE_KEY._DESTINATION_DESIGN_OPTION_EXIST_INTRO,
      'Copy design of another campaign',
    ),
  },
  optinDesign: {
    label: getTranslateMessage(
      TRANSLATE_KEY._TITL_DESIGN_FROM_TEMPLATE,
      'Design From Template',
    ),
    desc: getTranslateMessage(
      TRANSLATE_KEY._TITL_DESIGN_YOUR_CAMP,
      'Design your campaign using template',
    ),
  },
  emailDesign: {
    label: getTranslateMessage(
      TRANSLATE_KEY.DATLE,
      'Design from Email template',
    ),
    desc: getTranslateMessage(
      TRANSLATE_KEY.DATLE,
      'Design your email using template',
    ),
  },
  jsonDesign: {
    label: getTranslateMessage(TRANSLATE_KEY._004, 'Design From JSON'),
    desc: getTranslateMessage(
      TRANSLATE_KEY._004,
      'Copy design your campaign data using JSON',
    ),
  },
};

const PATH =
  'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js';
export const THUMBNAIL_TYPE = {
  VARIANT: 'variant',
  MEDIA: 'media',
  EMAIL: 'email',
};

export const initInputElementDestination = (
  name,
  value,
  maxLength,
  isRequired,
  label,
  errors,
  validate,
) => ({
  name: name || '',
  value: value || '',
  maxLength: maxLength || null,
  isRequired,
  label: label || '',
  errors: errors || [],
  isValidate: true,
  validate: validate || (() => ({ errors: [], isValidate: true })),
  initValue: value || '',
});

export const mapDestinationToUI = dataIn => {
  let data = new Map();
  if (dataIn.length === 0) return data;
  const map = {};
  dataIn.forEach(each => {
    const temp = {
      ...each,
      value: each.destinationId,
      name: each.destinationId,
      label: each.destinationName,
      method: (each.destinationSetting || {}).method || 'send',
    };
    map[temp.name] = temp;
  });
  data = new Map(Object.entries(map));
  return data;
};

export const mapDynamicDataOptionsToUI = (
  key,
  item,
  destinationInput = {},
  itemTypeId,
  eventValue,
  triggerType,
  activeNode,
) => {
  try {
    let initValue = destinationInput[key] || item.default || '';
    if (item.inputType === 'checkbox' || item.inputFormat === 'boolean') {
      initValue = parseBoolean(initValue);
    }

    const typeRender = getTypeRender(item);
    const temp = {
      ...item,
      ...initInputElementDestination(
        key,
        initValue,
        item.maxLength,
        item.isRequired,
        item.label,
        null,
        MAP_VALIDATE[typeRender],
      ),
      isValidate: !item.isRequired,
      componentEl: MAP_INPUT_TYPE[typeRender],
      fullWidthPopover: key !== 'content',
    };
    if (typeRender === 'selectDropdown') {
      temp.options =
        Array.isArray(item.options) && item.appendOptions ? item.options : [];
    } else if (
      [
        'editor',
        'htmlEditor',
        'customFields',
        'singleLineAddPersonalize',
        'keyvalue',
        'multiLineText',
      ].includes(typeRender)
    ) {
      let groupCodes = ['visitor', 'customer', 'event'];
      if (triggerType === 'SCHEDULED') {
        if (parseInt(itemTypeId) === -1003) {
          groupCodes = ['customer'];
        } else if (parseInt(itemTypeId) === -1007) {
          groupCodes = ['customer', 'visitor'];
        }
      }
      // for all channels
      // groupCodes.push('promotion_code');
      if (
        activeNode.channelCode === 'email' ||
        activeNode.channelCode === 'web_personalization'
      ) {
        groupCodes.push('promotion_code');
        groupCodes.push('productTemplate');
        // groupCodes.push('objectWidget');
      } else if (activeNode.channelCode === 'sms') {
        groupCodes.push('promotion_code');
        // groupCodes.push('objectWidget');
      } else if (
        [
          'webhook',
          'web_push',
          'app_push',
          'file_transfer',
          'conversation',
          'viber',
        ].includes(activeNode.channelCode)
      ) {
        groupCodes.push('promotion_code');
      }

      groupCodes.push('story', 'campaign', 'variant');

      temp.groupCodes = groupCodes;
      temp.itemTypeId = itemTypeId;
      temp.eventValue = eventValue;
      temp.triggerType = triggerType;
    }

    return temp;
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'mapDynamicDataOptionsToUI',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};

export const mapDynamicDataToUI = (
  basicInputs,
  inputs,
  destinationInput = {},
  itemTypeId,
  eventValue,
  triggerType,
  activeNode,
  isBlastCampaign = false,
) => {
  const data = {
    dynamicFields: basicInputs || [],
    destinationInput: {},
  };

  // console.log('basicInputs', {basicInputs, destinationInput})
  basicInputs.forEach(each => {
    const item = inputs[each];
    let initValue = destinationInput[each] || item.default || '';
    if (item.inputType === 'checkbox' || item.inputFormat === 'boolean') {
      initValue = parseBoolean(initValue);
    }

    let optionGroups = [];
    if (item.inputType === 'group') {
      const { options = [] } = item;
      optionGroups = _.cloneDeep(options);

      if (Array.isArray(optionGroups)) {
        optionGroups.forEach(option => {
          const {
            basicInputs: tempBasicInputs = [],
            inputs: tempInputs = {},
          } = option;

          if (Array.isArray(tempBasicInputs) && !_isEmpty(tempInputs)) {
            tempBasicInputs.forEach(_each => {
              const temp = tempInputs[_each];
              const tempMapped = mapDynamicDataOptionsToUI(
                each,
                temp,
                destinationInput,
                itemTypeId,
                eventValue,
                triggerType,
                activeNode,
              );

              tempInputs[_each] = tempMapped;
            });
          }
        });
      }
    }

    const typeRender = getTypeRender(item);
    const temp = {
      ...item,
      ...initInputElementDestination(
        each,
        initValue,
        item.maxLength,
        item.isRequired,
        item.label,
        null,
        MAP_VALIDATE[typeRender],
      ),
      isValidate: !item.isRequired,
      componentEl: MAP_INPUT_TYPE[typeRender],
      fullWidthPopover: each !== 'content',
    };
    if (typeRender === 'selectDropdown') {
      temp.options = [];

      if (isBlastCampaign) {
        const tempOptions = getObjectPropSafely(() => item.options || []);
        const mapOptions = {};

        if (Array.isArray(tempOptions)) {
          tempOptions.forEach(({ label = '', value = '' }) => {
            mapOptions[value] = { label, value };
          });
        }

        temp.options = tempOptions;
        temp.mapOptions = mapOptions;
      }

      if (item.useOptions) {
        temp.options = item.options;
        temp.mapOptions = keyBy(item.options, 'value');
      }
    } else if (typeRender === 'group') {
      temp.options = optionGroups;
    } else if (
      [
        'editor',
        'customFields',
        'htmlEditor',
        'singleLineAddPersonalize',
        'keyvalue',
        'multiLineText',
        'multiLangPersonalize',
        'whatsappTemplate',
      ].includes(typeRender)
    ) {
      let groupCodes = ['visitor', 'customer', 'event'];
      if (triggerType === 'SCHEDULED') {
        if (parseInt(itemTypeId) === -1003) {
          groupCodes = ['customer'];
        } else if (parseInt(itemTypeId) === -1007) {
          groupCodes = ['customer', 'visitor'];
        }
      }
      // for all channels
      // groupCodes.push('promotion_code');
      if (
        activeNode.channelCode === 'email' ||
        activeNode.channelCode === 'web_personalization'
      ) {
        groupCodes.push('promotion_code');
        groupCodes.push('productTemplate');
        // groupCodes.push('objectWidget');
      } else if (activeNode.channelCode === 'sms') {
        groupCodes.push('promotion_code');
        // Do not support object widget anymore
        // groupCodes.push('objectWidget');
      } else if (
        [
          'webhook',
          'web_push',
          'app_push',
          'file_transfer',
          'conversation',
          'viber',
          'line',
          'zalo',
          'whatsapp',
          'telegram',
          'smart_inbox',
        ].includes(activeNode.channelCode)
      ) {
        groupCodes.push('promotion_code');
      }

      groupCodes.push('story', 'campaign', 'variant');

      temp.groupCodes = groupCodes;
      temp.itemTypeId = itemTypeId;
      temp.eventValue = eventValue;
      temp.triggerType = triggerType;
    }
    // console.log('initInputElementDestination', temp);
    data.destinationInput[each] = temp;
  });
  return data;
};

export const getDataMappingGroupConfigs = (configFields = {}) => {
  try {
    const { basicInputs = [], inputs = {} } = configFields;
    const result = [];

    if (_isEmpty(basicInputs) || _isEmpty(inputs)) {
      return result;
    }
    const selectedTemplate = getObjectPropSafely(
      () => inputs.template.value.value,
      null,
    );

    basicInputs.forEach(each => {
      const item = inputs[each] || null;
      const hasGroup = getObjectPropSafely(
        () => item && item.groupConfigs,
        false,
      );

      if (hasGroup) {
        const {
          panelCode = '',
          panelOrder = 0,
          groupName = '',
          groupCode = '',
          groupOrder = 0,
        } = item.groupConfigs;
        let { panelName = '' } = item.groupConfigs;

        const indexExistedGroup = result.findIndex(
          group => group && group.value === panelCode,
        );

        // For case Init first Group & Option
        if (indexExistedGroup === -1 && panelCode) {
          const options = [];
          const inputsTemp = [];

          if (groupCode) {
            options.push({
              label: groupName,
              value: groupCode,
              order: groupOrder,
              basicInputs: [each],
            });
          } else {
            inputsTemp.push(each);
          }

          if (
            selectedTemplate === TEMPLATE_ANTSOMI_APP_PUSH_KEYS.RATING &&
            panelName === 'Buttons'
          ) {
            panelName = 'Button';
          }

          result.push({
            label: panelName,
            key: panelCode,
            value: panelCode,
            order: panelOrder,
            options,
            basicInputs: inputsTemp,
          });
        } else {
          // For case Group | Option existed
          const { options = [], basicInputs: basicInputsTemp = [] } =
            result[indexExistedGroup] || {};
          const indexExistedOption = options.findIndex(
            option => option && option.value === groupCode,
          );

          // For case init first option of group
          if (indexExistedOption === -1 && panelCode && groupCode) {
            options.push({
              label: groupName,
              value: groupCode,
              order: groupOrder,
              basicInputs: [each],
            });
          } else {
            const indexGroupCode = options.findIndex(
              option => option.value === groupCode,
            );

            if (indexGroupCode !== -1 && groupCode) {
              const optionTemp = getObjectPropSafely(
                () => options[indexGroupCode],
                {},
              );
              const { basicInputs: bsInputs = [] } = optionTemp;

              if (optionTemp && optionTemp.value === groupCode) {
                bsInputs.push(each);
              }
            } else if (basicInputsTemp && Array.isArray(basicInputsTemp)) {
              basicInputsTemp.push(each);
            }
          }
        }
      }
    });

    return result;
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'getDataMappingGroupConfigs',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};

export const toEntryReducer = dataIn => {
  try {
    const {
      role,
      isFetchInfoData,
      campaignName,
      workflowDestination,
      // dynamicFields,
      catalogId,
      campaignId,
      channelId,
      status,
      isValidate,
      channelCode,
      catalogCode,
      testingAudienceIds,
      variants,
      variantIds,
      campaignSetting,
      itemSquareSelected,
      isCopy = false,
      lookup = {},
      // invalidFields = [],
    } = dataIn;
    const data = {
      role,
      isFetchInfoData,
      isValidate,
      status,
      channelId,
      channelCode,
      catalogCode,
      catalogId,
      isCopy,
      lookup,
      // campaignId: campaignId || initData.campaignId,
      // variantIds: variantIds || initData.variantIds,
      campaignId,
      variantIds,
      destinationId: (workflowDestination.value || {}).value || '',
      campaignName: campaignName.value,
      variants,
      data: dataIn,
      testingAudienceIds,
      campaignSetting: campaignSetting.priority,
      itemSquareSelected,
    };
    // const dataInDesInput = dataIn.destinationInput || {};
    // data.destinationInput = toDesinationInputAPI(dynamicFields, dataInDesInput);

    if (channelCode === 'web_personalization') {
      if (['web_embedded'].includes(catalogCode)) {
        data.zoneId =
          typeof dataIn.zoneId.value === 'object'
            ? dataIn.zoneId.value.value
            : 0;
        data.priority = dataIn.priority.value;
        data.isFitContent = dataIn.isFitContent.value;
        data.contentPlacement =
          typeof dataIn.contentPlacement.value === 'object'
            ? dataIn.contentPlacement.value.value
            : '';
      } else {
        data.priority = dataIn.priority ? dataIn.priority.value : 0;
      }
    }

    return data;
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'toEntryReducer',
      data: err.stack,
    });
    console.log(err);
  }
};

export function toDesinationInputAPI(
  dynamicFields,
  destinationInput,
  channelCode,
) {
  const data = {};
  dynamicFields.forEach(each => {
    data[each] = toFormValue(
      destinationInput[each] || {},
      destinationInput[each] && destinationInput[each].value,
      destinationInput,
      channelCode,
    );
  });
  // console.log('data', { data, dynamicFields, destinationInput });
  return data;
}

export function toFormValue(element, value, destinationInput, channelCode) {
  let data = value;
  // console.log('each', each, dataIn[each]);
  if (element.inputType === 'select') {
    data = typeof value === 'object' ? (value || {}).value : value;
  } else if (
    element.inputType === 'checkbox' ||
    element.inputFormat === 'boolean'
  ) {
    data = parseBoolean(value);
  } else if ([MEDIA_TEMPLATE, JSON_TEMPLATE].includes(element.inputType)) {
    if (value.properties && value.properties.id) {
      // const placement = mapPlacementToMT[_.get(allFields, 'contentPlacement.value.value')]
      // data = {
      //   js: `(function (d, story_id, zone_id, variant_id, zone_selector, query_str, isPreview = false, env = 'production') {
      //   var s = d.createElement("script");
      //   s.type = "text/javascript";
      //   s.src = ${
      //     isProduction()
      //       ? '"//st-media-template.antsomi.com/js/media.cdp.min.js"'
      //       : '"//sandbox-template.ants.vn/khanhhv/media.cdp.min.js"'
      //   };
      //   s.id="antsomi-cdp-optin";
      //   s.async = true;
      //   s.dataset.storyId = story_id;
      //   s.dataset.zoneId = zone_id;
      //   s.dataset.variantId = variant_id;
      //   s.dataset.zoneSelector = zone_selector;
      //   s.dataset.queryStr = query_str;
      //   s.dataset.zoneRenderType = ${placement};
      //   d.getElementsByTagName("head")[0].appendChild(s);
      // })(document, #STORY_ID#, #ZONE_ID#, #VARIANT_ID#, #ZONE_SELECTOR#, #QUERY_STRING#);`,
      //   css: '',
      //   html: '',
      // };
    } else {
      // case reset template
      data = '';
    }
  } else if (element.inputType === EMAIL_TEMPLATE) {
    const isSettingTemplate = value.properties && value.properties.id;
    if (isSettingTemplate) {
      const viewPages = getViewPagesDefault(value);
      const { html } = Object.values(viewPages).find(v => v.is_default);

      data = html;
    }
    // else {
    //   data = '';
    // }
  } else {
    data = value;
  }

  if (channelCode === 'email') {
    data = addPreheaderToBody({
      body: data,
      preheader: _.get(destinationInput, 'preheader.value', ''),
    });
  }

  return data;
}

export function getVariantId(id, isUseKeyFE) {
  if (isUseKeyFE) {
    return id;
  }
  if (`${id}`.startsWith('fe_')) {
    return null;
  }
  return safeParseInt(id, null);
}

export function getVariantThumbnail(variantInfo) {
  if (!variantInfo) {
    return {};
  }
  const variantThumbnail = variantInfo.thumbnail;
  const mediaThumbnail = getObjectPropSafely(
    () => variantInfo.variantExtraData.content.properties.thumbnail,
  );
  const viewPages = getObjectPropSafely(
    () => variantInfo.variantExtraData.content.properties.viewPages,
  );
  const viewThumbnails = variantInfo.thumbnails;
  const emailThumbnail = getObjectPropSafely(
    () => variantInfo.variantExtraData.body.properties.thumbnail,
  );
  const thumbnail = variantThumbnail || mediaThumbnail || emailThumbnail;
  const type = mediaThumbnail
    ? THUMBNAIL_TYPE.MEDIA
    : emailThumbnail
    ? THUMBNAIL_TYPE.EMAIL
    : THUMBNAIL_TYPE.VARIANT;

  if (viewPages && viewPages.length) {
    return {
      thumbnail,
      type,
      viewThumbnails: viewPages
        .filter(
          view => view.thumbnail && (!view.settings || view.settings.isActive),
        )
        .map(view => ({
          viewId: view.id,
          thumbnail: view.thumbnail,
        })),
    };
  }

  return {
    thumbnail,
    type,
    storedViewThumbnails: viewThumbnails,
    viewId: type === THUMBNAIL_TYPE.EMAIL ? 'optin' : undefined,
  };
}

export const getTemplateInfoFromVariantExtraData = variantExtraData => {
  let templateId = null;
  let templateName = null;

  loopVariantExtraData({
    variantExtraData,
    handler: ({ info, value }) => {
      const { isMediaTemplate, isEmailTemplate } = info;

      if (isMediaTemplate || isEmailTemplate) {
        const { id, name } = value.template_settings || {};

        templateName = name;
        templateId = +`${id}`.split('-').at(0);

        if (Number.isNaN(templateId)) {
          templateId = null;
        }
      }
    },
  });

  return { templateId, templateName };
};

export function toVariantAPI({
  design,
  variants = {},
  variantIdArray,
  destination,
  copyId,
  versionMT,
  thumbnails,
  campaignName,
}) {
  let data = [];
  const { channelCode, catalogCode = 'web_embedded', channelId } = destination; // catalogCode = 'web_embedded' for safe parse data zone,placement, vv

  variants.list.forEach(item => {
    let variantId = getVariantId(item.value);

    const cacheInfo = _.cloneDeep(variants.cacheInfo[item.value] || {});

    if (variantId === null && variantIdArray.length > 0) {
      variantIdArray.forEach(each => {
        if (each.feKeys === item.value) {
          variantId = each.variant_id;
        }
      });
    }

    const cachedMissDataVariantId = {};

    const variantExtraData = safeParse(cacheInfo.variantExtraData, {});

    if (typeof variantExtraData === 'object' && design === 'create') {
      Object.keys(variantExtraData).forEach(key => {
        if (key !== 'customFunction' && key !== 'formatAttributes') {
          if (
            copyId &&
            !variantExtraData[key].properties &&
            variantExtraData[key].template_settings &&
            !Object.keys(cachedMissDataVariantId).includes(
              variantExtraData[key].template_settings.id,
            )
          ) {
            cachedMissDataVariantId[
              variantExtraData[key].template_settings.id
            ] = variantExtraData[key].fe_config_id;
          }

          variantExtraData[key].fe_config_id = null;
        }
      });
    }

    const destinationInput = safeParse(cacheInfo.destinationInput, {});

    if (channelCode === 'email') {
      destinationInput.body = addPreheaderToBody({
        body: destinationInput.body,
        preheader: destinationInput.preheader,
      });
    }

    let variantThumbnails = [];

    if (thumbnails) {
      const {
        thumbnail,
        type,
        viewThumbnails,
        storedViewThumbnails,
        viewId,
      } = getVariantThumbnail(cacheInfo);

      const addThumb = thumbObj => {
        variantThumbnails.push({
          id: `v_thumb_${random(10)}`,
          url: thumbObj.thumbnail,
          name: item.label,
          variantId: String(variantId || item.value || ''),
          campaignName,
          type,
          viewId: thumbObj.viewId,
        });
      };

      if (storedViewThumbnails && storedViewThumbnails.length) {
        variantThumbnails = storedViewThumbnails.map(thumb => ({
          ...thumb,
          name: item.label,
          variantId: String(variantId || item.value || ''),
          campaignName,
        }));
      } else if (viewThumbnails && viewThumbnails.length) {
        viewThumbnails.forEach(addThumb);
      } else if (thumbnail) {
        addThumb({ thumbnail, viewId });
      }

      variantThumbnails.forEach(thumb => thumbnails.push(thumb));
    }

    // console.log('variantId', variantId, getVariantId(item.value), item);
    const tmp = {
      variantId: variantId || getVariantId(item.value),
      cachedIds: cachedMissDataVariantId,
      variantKey: variantId || getVariantId(item.value, true),
      variantName: item.label,
      contentSetting: {
        objectWidgetInput: cacheInfo.objectWidgetInput,
        destinationInput,
        variantExtraData,
        // thumbnails: variantThumbnails,
        // customFunction: mapToAPICustomFunction(customFunction),
      },
      status: variants.cacheInfo[item.value].status ? 1 : 2,
      custom_inputs: mapInputToAPI(cacheInfo?.customInputVia?.workspaces || []),

      ...getTemplateInfoFromVariantExtraData(variantExtraData),
    };

    data.push(tmp);
  });

  if (
    catalogCode === 'web_embedded' &&
    (channelCode === 'web_personalization' || +channelId === 2)
  ) {
    data = updateContentMediaTemplate({
      destination,
      variants: data,
      version: versionMT,
    });
  }

  return data;
}

export const getIsUpdatedDestination = (destination = {}) => {
  let isUpdated = destination.isFetchInfoData === true ? 1 : 0;
  if (Object.keys(destination.data || {}).length > 0) {
    isUpdated = 1;
  }
  return isUpdated;
};

export function toNodeDestinationAPI({
  destination,
  status,
  design,
  variantIdArray,
  copyId,
  thumbnails,
}) {
  try {
    const parseDestinationStatus =
      Number(destination.status) === 0 ? 1 : destination.status;

    const destStatus = status || parseDestinationStatus;

    const isUpdated = getIsUpdatedDestination(destination);

    const {
      channelCode,
      campaignId = null,
      catalogCode = 'web_embedded',
      data = {},
      lookup = {},
    } = destination; // catalogCode = 'web_embedded' for safe parse data zone,placement, vv

    const meta = {
      isUpdated,
      channelId: destination.channelId || 2,
      destinationId: destination.destinationId,
      catalogId: Number(destination.catalogId),
      campaignId,
      variantIds: safeParse(destination.variantIds, []),
      campaign: undefined,
      variants: undefined,
    };

    if (channelCode === 'web_personalization' || +destination.channelId === 2) {
      meta.priority = destination.priority;

      if (catalogCode === 'web_embedded') {
        meta.zoneId = destination.zoneId;
        meta.isFitContent = destination.isFitContent;
        meta.contentPlacement = destination.contentPlacement;
      }
    }

    // Handler when destination exist but user not click on it.
    // * lookup.[campaign || variants] only exist when user init journey from another existed journey || journey template.
    if (
      isUpdated === 0 &&
      Object.keys(lookup?.campaign || {}).length > 0 &&
      (lookup?.variants || []).length > 0
    ) {
      const { campaign } = lookup;

      const initializedVariants = initVariants('RESET', lookup.variants);

      const campaignMappingInfo = mapCampaignSettingToFe({
        variant: initializedVariants,
        campaign,
        role: 'RESET',
      });

      meta.campaignId = null;
      meta.isUpdated = 1;

      meta.campaign = {
        campaignId: null,
        campaignName: campaign.campaign_name,
        status: CONFIG_OBJECT_STATUS.ENABLE,
        campaignSetting: {
          ...campaign.campaign_setting,
          random: campaignMappingInfo?.priority,
        },
        custom_inputs: campaign.custom_inputs,
      };

      meta.variants = Object.entries(initializedVariants.cacheInfo).map(
        ([variantKey, contentSetting]) => {
          // mutate thumbnails in params
          if (thumbnails && Array.isArray(contentSetting.thumbnails)) {
            contentSetting.thumbnails.forEach(item => thumbnails.push(item));
          }

          const variantName = initializedVariants?.mapVariantName?.[variantKey];

          return {
            variantId: null,
            variantKey,
            variantName,
            contentSetting,
            status: CONFIG_OBJECT_STATUS.ENABLE,
            custom_inputs: contentSetting.customInputs,

            ...getTemplateInfoFromVariantExtraData(
              contentSetting.variantExtraData,
            ),
          };
        },
      );
    }

    if (isUpdated === 1) {
      // set lại ở luồng update chưa reload
      const priority = _.cloneDeep(data.campaignSetting.priority);

      if (variantIdArray && variantIdArray.length > 0) {
        Object.keys(priority).forEach(key => {
          variantIdArray.forEach(item => {
            if (item.feKeys === key) {
              priority[item.variant_id] = priority[key];
              delete priority[key];
            }
          });
        });
      }

      meta.campaign = {
        status: destStatus,
        campaignName: destination.campaignName,
        campaignId,
        campaignSetting: {
          algoMethod: 'random',
          random: { ...priority },
        },
        custom_inputs: mapInputToAPI(
          safeParse(
            data.campaignCustomInput && data.campaignCustomInput.workspaces,
            [],
          ),
        ),
      };

      if (channelCode !== 'web_personalization') {
        const timeTarget = destination?.data?.timeTarget;

        meta.campaign.campaignSetting.deliveryTimeConfig = {
          type: timeTarget.triggerType || 'all_time',

          range:
            timeTarget.triggerType === 'specific_hours'
              ? timeTarget.dataSelected
              : [],

          mode:
            timeTarget.triggerType === 'specific_hours'
              ? timeTarget.hoursType
              : 'delay',
        };
      }

      meta.variants = toVariantAPI({
        design,
        variants: destination.variants,
        variantIdArray,
        destination,
        copyId,
        thumbnails,
        campaignName: destination.campaignName,
      });
    }

    return meta;
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'toNodeDestinationAPI',
      data: err.stack,
    });
    console.log(err);
  }
  return {};
}

const updateContentMediaTemplate = ({ destination, variants, version }) => {
  const clonedVariants = _.cloneDeep(variants);
  const placement = mapPlacementToMT[destination.contentPlacement];
  const js = `(function (b,e,f,n,g,h,i,j="",k=!1,l="production"){let m=a=>new Promise(b=>setTimeout(b,a)),d=()=>{"function"==typeof window.mt_run_campaign&&window.mt_run_campaign({campaignId:n,variantId:g,storyId:e,zoneId:f,zoneSelector:h,zoneRenderType:j,queryStr:i,env:l,isPreview:k})},c=async()=>{for(;window.mt_run_campaign_waiting;)await m(200);d()};if("function"==typeof window.mt_run_campaign)d();else if(window.mt_run_campaign_waiting)c();else{window.mt_run_campaign_waiting=!0;var a=b.createElement("script");a.src=${
    isStaging()
      ? `"//st-media-template.antsomi.com/staging/js/media.cdp.min.js?v=#MT_SCRIPT_VERSION#"`
      : isProduction()
      ? `"//st-media-template.antsomi.com/js/media.cdp.min.js?v=#MT_SCRIPT_VERSION#"`
      : `"//sandbox-template.ants.vn/khanhhv/media.cdp.min.js?v=#MT_SCRIPT_VERSION#"`
  },a.id="antsomi-cdp-optin",a.dataset.singleton="1",a.async=true,b.getElementsByTagName("head")[0].appendChild(a),c()}})(
    document,  #STORY_ID#, #ZONE_ID#, #CAMPAIGN_ID#, #VARIANT_ID#, #ZONE_SELECTOR#, #QUERY_STRING#, '${placement}')`;

  const contentMediaTemplate = {
    js,
    css: '',
    html: '',
    template: '',
  };

  if (Array.isArray(clonedVariants)) {
    clonedVariants.forEach(item => {
      const variantExtraData = safeParse(
        item.contentSetting.variantExtraData,
        {},
      );
      const variantType = getObjectPropSafely(() =>
        _.get(variantExtraData, 'content.type', ''),
      );
      if ([MEDIA_TEMPLATE, JSON_TEMPLATE].includes(variantType)) {
        const templateType = safeParse(
          getObjectPropSafely(
            () => variantExtraData.content.template_settings.type,
          ),
          '',
        );
        // destination Web Personalization: set MediaTemplate content
        // eslint-disable-next-line no-param-reassign
        item.contentSetting.destinationInput.content = {
          ...contentMediaTemplate,
          template: templateType,
        };
      }
    });
  }

  return clonedVariants;
};

export function toNodeDestinationUI(metadata, role) {
  // const destination = safeParse(nodeInfo.get('destination'), {});
  const destination = {
    role,
    isFetchInfoData: false,
    channelId: metadata.channelId,
    destinationId: metadata.destinationId || metadata.sendAs,
    catalogId: metadata.catalogId,
    // campaignName: metadata.campaignName,
    campaignId: metadata.campaignId || null,
    // variantId: metadata.variantId || null,
    variantIds: metadata.variantIds || [],
    zoneId: metadata.zoneId,
    priority: metadata.priority,
    isFitContent: metadata.isFitContent,
    contentPlacement: metadata.contentPlacement,
  };

  if (role === 'RESET') {
    // v2 phải RESET ở bên trong, khi mở node lên, vì lúc đó cần id để get lại data.
    destination.lookup = {
      campaignId: metadata.campaignId || null,
      variantIds: metadata.variantIds || [],
      campaign: metadata.campaignInfo,
      variants: metadata.variantInfo,
    };
  }

  const data = Map({
    // status: metadata.status,
    destination,
  });

  return data;

  // const meta = {
  //   status: safeParse(nodeInfo.get('status', 1)),
  //   channelId: destination.channelId,
  //   destinationId: destination.destinationId,
  //   catalogId: destination.catalogId,
  //   campaignName: destination.campaignName,
  //   campaignId: destination.campaignId || null,
  //   variantId: destination.variantId || null,
  //   destinationInput: destination.destinationInput,
  // };

  // return meta;
}

export const validateNodeDestination = nodeInfo => {
  let [
    isValidateAll1,
    isValidateAll2,
    isValidateCustomInputCanpaign,
    isValidateCustomInputVariant,
  ] = [false, false, false, false];
  try {
    const destination = safeParse(nodeInfo.get('destination'), {});
    const pathContentAccent = [
      'destination',
      'data',
      'workflowDestination',
      'value',
      'destinationSetting',
      'contentType',
    ];
    const contentAccentType = safeParse(nodeInfo.getIn(pathContentAccent, ''));
    // console.log(destination);
    if (_isEmpty(destination)) return { status: false };
    // const {
    //   data,
    //   data: { defaultFields, dynamicFields },
    // } = destination;
    const data = safeParse(destination.data, {});
    const defaultFields = safeParse(data.defaultFields, []);
    const dynamicFields = safeParse(data.dynamicFields, []);
    const variantHardFields = safeParse(data.variantHardFields, []);
    const webChannelFields = safeParse(data.webChannelFields, []);
    // const campaignCustomInput = safeParse(data.campaignCustomInput, []);
    // const webChannelFields = safeParse(data.webChannelFields, []);
    if (
      destination.data.timeTarget.triggerType &&
      destination.data.timeTarget.dataSelected.length === 0 &&
      destination.data.timeTarget.triggerType === 'specific_hours'
    ) {
      return { status: false };
    }

    isValidateCustomInputCanpaign = !!validateCustomInputCampaign(
      _.cloneDeep(data),
    );
    isValidateCustomInputVariant = !!validateCustomInputVariant(
      _.cloneDeep(data),
    );

    // console.log(
    //   'isValidateCustomInputCanpaign',
    //   isValidateCustomInputCanpaign,
    //   isValidateCustomInputVariant,
    // );

    isValidateAll1 = [
      ...defaultFields,
      ...webChannelFields,
      ...variantHardFields,
    ].every(each => {
      const { isValidate } = data[each].validate(data[each]);
      return isValidate;
    });

    isValidateAll2 = [...dynamicFields].every(each => {
      // console.log('object', data.destinationInput[each]);
      // console.log('data', data);
      const { isValidate } = data.destinationInput[each].validate({
        ...data.destinationInput[each],
        contentAccentType,
        destinationInput: data.destinationInput,
        channelCode: destination.channelCode,
        catalogCode: destination.catalogCode,
      });
      return isValidate;
    });
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'validateNodeDestination',
      data: err.stack,
    });
    console.log(err);
  }
  return {
    status:
      isValidateAll1 &&
      isValidateAll2 &&
      isValidateCustomInputCanpaign &&
      isValidateCustomInputVariant,
  };
  // return { status: isValidateAll1 && isValidateAll2 };
};

export const listToMap = (list = []) =>
  list.reduce((acc, current) => {
    acc[current.value] = current;
    return acc;
  }, {});
export const dataEntrieToList = (list = []) => {
  const array = [];
  list.forEach(each => {
    const item = { label: '', value: '', zoneSetting: {} };
    item.value = each.zoneId;
    item.label = each.zoneName;
    item.zoneSetting = each.zoneSetting;
    array.push(item);
  });
  return array;
};
export const DATA_CONTENT_PLACEMENT = {
  map: {
    content_placement_replace: {
      label: MAP_TITLE.replaceContent,
      value: 'content_placement_replace',
    },
    content_placement_insert_before: {
      label: MAP_TITLE.insertContentBefore,
      value: 'content_placement_insert_before',
    },
    content_placement_insert_after: {
      label: MAP_TITLE.insertContentAfter,
      value: 'content_placement_insert_after',
    },
  },
};
const parseBoolean = value => (value === true || value === 1 ? 1 : 0);

export const serializeData = data => {
  // const DATA_DEFAULT = [
  //   { value: 1, label: 'Variant 1', isError: false, isActive: false },
  // ];
  const dataOut = {
    list: [],
    map: {},
  };
  if (data) {
    data.forEach(each => {
      dataOut.map[each.value] = each;
      const itemTmp = { ...each, isError: false, isActive: false };
      dataOut.list.push(itemTmp);
    });
  }
  //  else {
  //   DATA_DEFAULT.forEach(each => {
  //     DATA_DEFAULT.map[each.value] = each;
  //   });
  //   dataOut.list = [...DATA_DEFAULT];
  // }
  return dataOut;
};

// dung cho trương hơp placement không co value
export const codeItemToValue = data =>
  data.map(item => ({ ...item, value: item.value || item.code }));

export const DATA_DESIGN_NODE_DESTINATION = [
  {
    icon: <BrushOutlinedIcon />,
    label: MAP_TITLE.customDesign.label,
    value: DESTINATION_TYPE.CUSTOM,
    disabled: false,
    description: MAP_TITLE.customDesign.desc,
  },
  {
    icon: <FileCopyOutlinedIcon color="primary" />,
    label: MAP_TITLE.copyDesign.label,
    value: DESTINATION_TYPE.COPY,
    disabled: false,
    description: MAP_TITLE.copyDesign.desc,
    iconBackgroundColor: 'rgba(0, 94, 184, 0.05)',
  },
];

export const DATA_DESIGN_NODE_DESTINATION_LINE = [
  {
    icon: <BrushOutlinedIcon />,
    label: MAP_TITLE.chooseTemplate.label,
    value: DESTINATION_TYPE.CUSTOM,
    disabled: false,
    description: MAP_TITLE.chooseTemplate.desc,
  },
  {
    icon: <FileCopyOutlinedIcon color="primary" />,
    label: MAP_TITLE.copyDesign.label,
    value: DESTINATION_TYPE.COPY,
    disabled: false,
    description: MAP_TITLE.copyDesign.desc,
    iconBackgroundColor: 'rgba(0, 94, 184, 0.05)',
  },
];

export const MAP_TEMPLATE_LINE = {
  [TEMPLATE_TYPES.TEXT]: {
    icon: <BrushOutlinedIcon />,
    image: TextMessage,
  },
  [TEMPLATE_TYPES.IMAGE]: {
    icon: <BrushOutlinedIcon />,
    image: ImageMessage,
  },
  [TEMPLATE_TYPES.CAROUSEL]: {
    icon: <BrushOutlinedIcon />,
    image: CarouselMessage,
  },
  [TEMPLATE_TYPES.BUTTONS]: {
    icon: <BrushOutlinedIcon />,
    image: ButtonMessage,
  },
  [TEMPLATE_TYPES.CONFIRM]: {
    icon: <BrushOutlinedIcon />,
    image: ConfirmationMessage,
  },
  [TEMPLATE_TYPES.STICKER]: {
    icon: <BrushOutlinedIcon />,
    image: StickerMessage,
  },
  [TEMPLATE_TYPES.IMAGE_CAROUSE]: {
    icon: <BrushOutlinedIcon />,
    image: ImageCarouselMessage,
  },
  [TEMPLATE_TYPES.IMAGE_MAP_STANDARD]: {
    icon: <BrushOutlinedIcon />,
    image: ImageMapMessage,
  },
  [TEMPLATE_TYPES.IMAGE_MAP_ADVANCED]: {
    icon: <BrushOutlinedIcon />,
    image: ImageMapMessageAdvanced,
  },
};

export const MAP_TEMPLATE_ZALO_OA = {
  [TEMPLATE_ZALO_OA_TYPES.TEXT]: {
    icon: <BrushOutlinedIcon />,
    image: TextZaloMessage,
    label: getTranslateMessage(TRANSLATE_KEY._ZALO_TEMP_TYPE_1, 'Text Message'),
    value: TEMPLATE_ZALO_OA_TYPES.TEXT,
    hasImage: true,
    isShowCover: false,
  },
  [TEMPLATE_ZALO_OA_TYPES.IMAGE]: {
    icon: <BrushOutlinedIcon />,
    image: MediaZaloMessage,
    label: getTranslateMessage(
      TRANSLATE_KEY._ZALO_TEMP_TYPE_2,
      'Image Message',
    ),
    value: TEMPLATE_ZALO_OA_TYPES.IMAGE,
    hasImage: true,
    isShowCover: false,
  },
  [TEMPLATE_ZALO_OA_TYPES.REQUEST_INFO]: {
    icon: <BrushOutlinedIcon />,
    image: RequestInfoZaloMessage,
    label: getTranslateMessage(
      TRANSLATE_KEY._ZALO_TEMP_TYPE_5,
      'Request Information',
    ),
    value: TEMPLATE_ZALO_OA_TYPES.REQUEST_INFO,
    hasImage: true,
    isShowCover: false,
  },
  [TEMPLATE_ZALO_OA_TYPES.TRANSACTION]: {
    icon: <BrushOutlinedIcon />,
    image: TransactionZaloMessage,
    label: getTranslateMessage(
      TRANSLATE_KEY._ZALO_TEMP_TYPE_4,
      'Transactional Message',
    ),
    value: TEMPLATE_ZALO_OA_TYPES.TRANSACTION,
    hasImage: true,
    isShowCover: false,
  },
  [TEMPLATE_ZALO_OA_TYPES.STICKER]: {
    icon: <BrushOutlinedIcon />,
    image: StickerZaloMessage,
    label: getTranslateMessage(
      TRANSLATE_KEY._ZALO_TEMP_TYPE_3,
      'Sticker Message',
    ),
    value: TEMPLATE_ZALO_OA_TYPES.STICKER,
    hasImage: true,
    isShowCover: false,
  },
  [TEMPLATE_ZALO_OA_TYPES.RICH_MEDIA]: {
    icon: <BrushOutlinedIcon />,
    image: RichMediaZaloMessage,
    label: getTranslateMessage(
      TRANSLATE_KEY._ZALO_TEMP_TYPE_6,
      'Rich Media Message',
    ),
    value: TEMPLATE_ZALO_OA_TYPES.RICH_MEDIA,
    hasImage: true,
    isShowCover: false,
  },
};

export const MAP_TEMPLATE_ANTSOMI_APP_PUSH = {
  [TEMPLATE_ANTSOMI_APP_PUSH_KEYS.BASIC_NOTIFICATION]: {
    ...TEMPLATE_ANTSOMI_APP_PUSH[
      TEMPLATE_ANTSOMI_APP_PUSH_KEYS.BASIC_NOTIFICATION
    ],
    image: BasicNotificationAppPush,
  },
  [TEMPLATE_ANTSOMI_APP_PUSH_KEYS.STYLIZED_BASIC]: {
    ...TEMPLATE_ANTSOMI_APP_PUSH[TEMPLATE_ANTSOMI_APP_PUSH_KEYS.STYLIZED_BASIC],
    image: StylizedBasicAppPush,
  },
  [TEMPLATE_ANTSOMI_APP_PUSH_KEYS.RATING]: {
    ...TEMPLATE_ANTSOMI_APP_PUSH[TEMPLATE_ANTSOMI_APP_PUSH_KEYS.RATING],
    image: RatingAppPush,
  },
  [TEMPLATE_ANTSOMI_APP_PUSH_KEYS.SMALL_IMAGE]: {
    ...TEMPLATE_ANTSOMI_APP_PUSH[TEMPLATE_ANTSOMI_APP_PUSH_KEYS.SMALL_IMAGE],
    image: SmallImageAppPush,
  },
  [TEMPLATE_ANTSOMI_APP_PUSH_KEYS.SIMPLE_IMAGE_CAROUSEL]: {
    ...TEMPLATE_ANTSOMI_APP_PUSH[
      TEMPLATE_ANTSOMI_APP_PUSH_KEYS.SIMPLE_IMAGE_CAROUSEL
    ],
    image: SimpleImageCarouselAppPush,
  },
};

export const MAP_TEMPLATE_TAG_ZNS = {
  [TEMPLATE_TAG.IN_TRANSACTION]: {
    icon: 'in-transaction',
  },
  [TEMPLATE_TAG.OTP]: {
    icon: 'otp',
  },
  [TEMPLATE_TAG.POST_TRANSACTION]: {
    icon: 'post-transaction',
  },
  [TEMPLATE_TAG.ACCOUNT_UPDATE]: {
    icon: 'account-update',
  },
  [TEMPLATE_TAG.GENERAL_UPDATE]: {
    icon: 'in-transaction',
  },
  [TEMPLATE_TAG.FOLLOW_UP]: {
    icon: 'follow-up',
  },
  [TEMPLATE_TAG.TRANSACTION]: {
    icon: 'in-transaction',
  },
  [TEMPLATE_TAG.CUSTOMER_CARE]: {
    icon: 'account-update',
  },
  [TEMPLATE_TAG.PROMOTION]: {
    icon: 'follow-up',
  },
};

export const MAP_TEMPLATE_CATALOG = {
  [CATALOG_CODES.LINE_APP]: MAP_TEMPLATE_LINE,
  [CATALOG_CODES.ZALO_OA]: MAP_TEMPLATE_ZALO_OA,
  [CATALOG_CODES.ANTSOMI_APP_PUSH]: MAP_TEMPLATE_ANTSOMI_APP_PUSH,
};

export const DATA_DESIGN_NODE_DESTINATION_WEB_EMBEDDED = [
  {
    icon: <PhotoLibraryIcon color="primary" />,
    label: MAP_TITLE.optinDesign.label,
    value: DESTINATION_TYPE.MEDIA_TEMPLATE,
    disabled: false,
    description: MAP_TITLE.optinDesign.desc,
    iconBackgroundColor: 'rgba(0, 94, 184, 0.05)',
  },
  { ...DATA_DESIGN_NODE_DESTINATION[1] },
  {
    icon: <img src={MediaJsonIcon} alt="media-json" />,
    label: MAP_TITLE.jsonDesign.label,
    value: DESTINATION_TYPE.JSON_TEMPLATE,
    disabled: false,
    description: MAP_TITLE.jsonDesign.desc,
    iconBackgroundColor: 'rgba(0, 94, 184, 0.05)',
  },
];

export const DATA_DESIGN_NODE_DESTINATION_EMAIL = [
  {
    icon: <MailOutlineIcon color="primary" />,
    label: MAP_TITLE.emailDesign.label,
    value: DESTINATION_TYPE.EMAIL_TEMPLATE,
    disabled: false,
    description: MAP_TITLE.emailDesign.desc,
    iconBackgroundColor: 'rgba(0, 94, 184, 0.05)',
  },
  { ...DATA_DESIGN_NODE_DESTINATION[1] },
];

export const mapValueToAPIUseTemplateDestination = params => {
  const { catalogId, storyId } = params;
  const data = {
    catalogId,
    storyId,
    isGroupingByStory: 0,
    sort: 'u_user_id',
    sd: 'asc',
    columns: [
      'campaign_id',
      'campaign_name',
      'campaign_name_multilang',
      'portal_id',
      'story_id',
      'channel_id',
      'destination_id',
      'zone_id',
      'type',
      'trigger_type',
      'start_date',
      'end_date',
      'campaign_setting',
      'status',
      'description',
      'description_multilang',
      'properties',
      'c_user_id',
      'u_user_id',
      'ctime',
      'utime',
      'api_temp_id',
      'catalog_id',
      'catalog_name',
      'story_name',
    ],
  };
  return data;
};

export const mapDataToFe = data => {
  const dataOut = { list: [], map: {} };

  Object.keys(data).forEach(key => {
    const tmp = {
      ...data[key],
      label: getTranslateMessage(
        TRANSLATE_KEY[data[key].translateCode],
        data[key].groupName,
      ),
      value: key,
      option: data[key].list,
    };
    // if (key === 'other_story') {
    //   tmp.label = 'All Campaign in other journey';
    //   tmp.groupId = key;
    //   tmp.value = key;
    //   tmp.option = data.other_story.list;
    // } else if (key === 'same_story') {
    //   tmp.label = 'Same journey campagin';
    //   tmp.groupId = key;
    //   tmp.value = key;
    //   tmp.option = data.same_story.list;
    // }
    dataOut.map[key] = tmp;
    dataOut.list.push(tmp);
  });
  // console.log('dataOut', dataOut);
  return dataOut;
};

export const mapCampaignSettingToFe = ({
  variant,
  campaign,
  role, // Nếu sử dụng template thì đi build lại toàn bộ phần trăm
}) => {
  // campaign: lấy từ data từ API lên
  let data = { list: [], priority: {} };

  if (role === 'RESET') {
    data = mapNewPriority({ list: variant.list });
  } else if (_.isEmpty(campaign) && variant.list.length === 1) {
    // Trường hợp get API không có campaign setting và list variant trên màn hình === 1
    data.priority[variant.list[0].value] = 1;
  } else if (_.isEmpty(campaign) && variant.list.length > 1) {
    // Trường hợp get API không có campaign setting và list varaint trên màn hình > 1
    data = mapNewPriority({ list: variant.list });
  } else {
    data.priority = campaign[campaign.algoMethod];
  }
  data.list.reverse();
  return data;
};

export const mapNewPriority = ({ list }) => {
  const data = { list: [], priority: {} };
  let count = 0;
  let lastKey = '';
  list.forEach(each => {
    // Tính phần trăm
    const percent = Math.round(100 / list.length);

    const eachTmp = {
      ...each,
      status: true,
    };

    count += percent;
    lastKey = each.value;

    data.priority[each.value] = percent / 100;
    data.list.push(eachTmp);
  });

  // Check còn thừa thì push thêm phần trăm còn lại vô phần từ cuối cùng
  if (count < 100) {
    data.priority[lastKey] = (100 - count) / 100 + data.priority[lastKey];
  }

  return data;
};

export const isValidateFields = ({ fields, data }) =>
  fields.every(
    field => data[field].errors.filter(err => !_.isEmpty(err)).length === 0,
  );

// map placement to media template placement
const mapPlacementToMT = {
  content_placement_replace: 'replace',
  content_placement_insert_before: 'insertBefore',
  content_placement_insert_after: 'insertAfter',
};

// eslint-disable-next-line no-control-regex
// export const deleteSymbolCharacters = text => text.replace(/[^\x00-\x7F]/g, '');
export const deleteSymbolCharacters = text => text;

// text.replace(
//   /[^a-z0-9A-Z`\-=~!@#$%^&*()_+,./;'[\]<>?:"{}\\|\s\nàáãạảăắằẳẵặâấầẩẫậèéẹẻẽêềếểễệđìíĩỉịòóõọỏôốồổỗộơớờởỡợùúũụủưứừửữựỳỵỷỹýÀÁÃẠẢĂẮẰẲẴẶÂẤẦẨẪẬÈÉẸẺẼÊỀẾỂỄỆĐÌÍĨỈỊÒÓÕỌỎÔỐỒỔỖỘƠỚỜỞỠỢÙÚŨỤỦƯỨỪỬỮỰỲỴỶỸÝ\u00a9\u00ae\u2000-\u3300\ud83c\ud000-\udfff\ud83d\ud000-\udfff\ud83e\ud000-\udfff]/g,
//   '',
// );

export const getViewPagesDefault = settings => {
  const views = settings.viewPages.reduce((acc, cur) => {
    acc[cur.id] = { html: cur.html, styles: cur.styles };
    return acc;
  }, {});

  const defaultRuleset =
    settings.template_setting.rulesets.find(
      item => item.name === 'Default Ruleset',
    ) || {};
  if (defaultRuleset.actions) {
    const showCampaign = defaultRuleset.actions.find(
      item => item.type === 'show-campaign',
    );
    if (showCampaign && showCampaign.value) {
      views[showCampaign.value].is_default = true;
    }
  }

  return views;
};

// const PREHEADER_SPACES =
//   '&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;';

const addPreheaderToBody = ({ body, preheader }) => {
  if (!body || !preheader) {
    return body;
  }

  // const span = `<span id="antsomi-email-preheader" style="font-size:0px;line-height:1px;mso-line-height-rule:exactly;display:none;max-width:0px;max-height:0px;opacity:0;overflow:hidden;mso-hide:all;">${preheader} ${PREHEADER_SPACES}</span>`;
  // eslint-disable-next-line no-irregular-whitespace
  const span = `<div id="antsomi-email-preheader" style="display: none; overflow: hidden; line-height: 1px; opacity: 0; max-height: 0px; max-width: 0px;">${preheader}<div>&nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ &nbsp;‌​‍‎‏ </div></div>`;

  const regexPreheader = /<div.*id="antsomi-email-preheader".*?>.+?<\/div>/;
  const matchPreheader = body.match(regexPreheader);

  let newBody = body;
  // if exist preheader => replace
  if (matchPreheader && matchPreheader[0]) {
    const text = matchPreheader[0];
    newBody = body.replace(text, span);
  } else {
    // else => add new preheader
    const regex = /<body[^>]*>/;
    const match = body.match(regex);

    if (match && match[0]) {
      const text = match[0];
      const { index } = match;

      // end of body tag
      const addPosition = index + text.length;

      newBody = body.slice(0, addPosition) + span + body.slice(addPosition);
    }
  }

  return newBody;
};

export const getWidthCustomInput = (channelCode, step) => {
  let widthOf = '100%';
  if (step === 0) {
    return '30%';
  }
  switch (channelCode) {
    case 'email':
    case 'file_transfer':
      widthOf = '50%';
      break;

    case 'web_push':
    case 'sms':
    case 'viber':
    case 'app_push':
    case 'webhook':
      widthOf = '75%';
      break;

    case 'conversation':
      widthOf = '100%';
      break;

    case 'web_personalization':
      widthOf = '100%';
      break;

    default:
      break;
  }

  return widthOf;
};

export const getMaxLength = (name = '', catalogCode = '') => {
  try {
    switch (catalogCode) {
      case CATALOG_CODE.SMART_INBOX: {
        if (['couponButton', 'orderButton1', 'orderButton2'].includes(name))
          return 120;

        return undefined;
      }
      default: {
        return undefined;
      }
    }
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'getMaxLength',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};

export const RESET_DEFAULT_URL_SETTINGS = Object.freeze({
  announceCheckUrl: ['announceLaunchUrl', 'announceWebUrl', 'announceAppUrl'],
  couponCheckUrl: ['couponLaunchUrl', 'couponWebUrl', 'couponAppUrl'],
  orderCheckUrl1: ['orderLaunchUrl1', 'orderWebUrl1', 'orderAppUrl1'],
  orderCheckUrl2: ['orderLaunchUrl2', 'orderWebUrl2', 'orderAppUrl2'],
});

export const handleSetDefaultURLSettingSmartInbox = (
  name = '',
  status = false,
  data = {},
  setData = () => {},
) => {
  try {
    if (
      _isEmpty(data) ||
      _isEmpty(name) ||
      typeof setData !== 'function' ||
      typeof status !== 'boolean'
    )
      return;

    let destinationInput = getObjectPropSafely(
      () =>
        data.variants.cacheInfo[data.variants.activeId] &&
        data.variants.cacheInfo[data.variants.activeId].destinationInput,
      {},
    );
    const resetArr = RESET_DEFAULT_URL_SETTINGS[name] || [];

    if (
      !_isEmpty(resetArr) &&
      Array.isArray(resetArr) &&
      !_isEmpty(destinationInput)
    ) {
      const resetObj = _.zipObject(
        _.cloneDeep(resetArr),
        _.fill(_.cloneDeep(resetArr), ''),
      );

      if (!_isEmpty(resetObj)) {
        destinationInput = {
          ...(destinationInput || {}),
          ...(resetObj || {}),
          [name]: status ? 1 : 0,
        };
      }

      resetArr.forEach(each => {
        setData(draft => {
          draft.destinationInput[each].value = '';
          draft.destinationInput[each].initValue = '';
          draft.variants.cacheInfo[data.variants.activeId] = {
            ...data.variants.cacheInfo[data.variants.activeId],
            destinationInput: {
              ...(destinationInput || {}),
            },
          };
        });
      });
    }
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'handleSetDefaultURLSettingSmartInbox',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};

export const getSmsLimit = (name = '', catalogCode = '', inputInfo = {}) => {
  // Get max length by content accent type config
  if (inputInfo?.contentAccentType) {
    const type = inputInfo?.contentAccentType;

    const isUnaccented = type === 'unaccented_text';
    const isAccented = type === 'accented_text';

    if (isAccented) {
      return inputInfo?.maxLengthAccented;
    }
    if (isUnaccented) {
      return inputInfo?.maxLengthUnaccented;
    }
  }

  if (inputInfo?.baseHintInfo?.isCountCharacter) {
    return inputInfo?.maxLength;
  }

  switch (catalogCode) {
    case CATALOG_CODE.ANTSOMI_APP_PUSH: {
      if (name === 'title' || name === 'subTitle') {
        return 80;
      }

      if (name === 'androidContentSummary') {
        return 128;
      }

      if (GEN_RATING_TEXT.includes(name)) {
        return 25;
      }

      return 150;
    }
    case CATALOG_CODE.ONE_SIGNAL_APP_PUSH: {
      if (inputInfo) {
        if (inputInfo.isRequired && inputInfo.maxLength) {
          return inputInfo.maxLength;
        }
      }
      break;
    }
    case CATALOG_CODE.SMART_INBOX: {
      if (
        [
          'inboxHeading',
          'appTitle',
          'appSubtitle',
          'webTitle',
          'couponButton',
          'orderButton1',
          'orderButton2',
        ].includes(name)
      )
        return 120;

      if (['appContent', 'webContent'].includes(name)) return 400;

      if (name === 'inboxContent') return 1000;

      break;
    }
    case CATALOG_CODE.ONEWAY_SMS: {
      return 200;
    }
    case CATALOG_CODE.LINE: {
      if (name === 'text') return 5000;
      if (PREVIEW_FIELDS.includes(name) || name === 'imagemapMessage')
        return 400;

      break;
    }
    case CATALOG_CODE.ZALO_OA: {
      if (name === 'requestSubtitle') return 500;
      if (['text', 'imgContent'].includes(name)) return 2000;
      if (['mediaSubtitle', 'mediaMessage'].includes(name)) return 1000;
      if (['requestTitle', 'mediaTitle', 'transactionTitle'].includes(name))
        return 100;
      if (['transactionSubtitle', 'transactionMessage'].includes(name))
        return 250;

      break;
    }
    case CATALOG_CODE.ZNS: {
      if (name === 'trackingId') return 48;
      break;
    }
    default: {
      return 459;
    }
  }
};

export const checkIsHiddenInputComponent = ({
  name = '',
  data = {},
  tabKey = '',
  selectedTemplate = {},
  catalogCode = '',
  activeTabDisplay = '',
  extraData = {},
}) => {
  try {
    let isHidden = false;

    switch (catalogCode) {
      case CATALOG_CODE.ZALO_OA: {
        const { templateId = '' } = data;

        if (templateId && templateId !== selectedTemplate) {
          isHidden = true;
        }
        break;
      }
      case 'smart_inbox_app': {
        // Hidden when different tabs
        if (tabKey !== activeTabDisplay) {
          isHidden = true;
        }
        const { templateId = '' } = data;
        let selectedTemplateId = _.cloneDeep(selectedTemplate);

        if (typeof selectedTemplateId === 'object') {
          selectedTemplateId = getObjectPropSafely(
            () => selectedTemplate.value,
            '',
          );
        }

        // Hidden when different template selected
        if (
          templateId &&
          selectedTemplate &&
          templateId !== selectedTemplateId
        ) {
          isHidden = true;
        }

        // In case the same template
        if (templateId === selectedTemplateId) {
          const { data: dataState = {} } = extraData || {};

          if (
            LIST_ID_TEMPLATE_SMART_INBOX.includes(selectedTemplateId) &&
            !_isEmpty(dataState)
          ) {
            let destinationInputKey;

            // Hard ID list template
            if (selectedTemplateId === 'order_verified') {
              if (KEYS_ORDER_TEMPLATE_FIRST.includes(name)) {
                destinationInputKey = 'orderCheckUrl1'; // Key hard from API
              } else if (KEYS_ORDER_TEMPLATE_SECOND.includes(name)) {
                destinationInputKey = 'orderCheckUrl2'; // Key hard from API
              }
            } else if (selectedTemplateId === 'announcements') {
              destinationInputKey = 'announceCheckUrl'; // Key hard from API
            } else {
              destinationInputKey = 'couponCheckUrl';
            }

            const isCheckedDifferentURL = getObjectPropSafely(
              () => dataState.destinationInput[destinationInputKey].value,
              false,
            );

            if (
              (isCheckedDifferentURL &&
                LIST_HIDE_WHEN_CHECKED_URL.includes(name)) ||
              (!isCheckedDifferentURL &&
                LIST_FIELD_HIDE_WHEN_UNCHECKED_URL.includes(name))
            ) {
              isHidden = true;
            }
          }
        }

        // Special case for "inboxButton" not hide
        if (name === 'inboxButton') {
          isHidden = false;
        }

        break;
      }
      case CATALOG_CODE.LINE: {
        const { templateId = '' } = data;

        if (templateId && templateId !== selectedTemplate) {
          isHidden = true;
        }

        if (['imagemapLandingPageUrl', 'imagemapMessage'].includes(name)) {
          const { destinationInput = {} } = getObjectPropSafely(
            () => extraData.data || {},
          );
          let { value: valueImageMapType = '' } = getObjectPropSafely(
            () => destinationInput.imagemapType || {},
            '',
          );
          if (typeof valueImageMapType === 'object') {
            valueImageMapType = valueImageMapType.value;
          }

          if (
            (valueImageMapType === 'message_action' &&
              name === 'imagemapLandingPageUrl') ||
            (valueImageMapType === 'url' && name === 'imagemapMessage')
          ) {
            isHidden = true;
          }
        }
        break;
      }

      case CATALOG_CODE.ZNS:
        // ẩn trường templateId đi, do đã tự input value giá trị này rồi
        if (name === 'templateId') {
          isHidden = true;
        }

        break;
      case CATALOG_CODE.ANTSOMI_APP_PUSH: {
        const { templateId = '' } = data;
        const { destinationInput = {} } = getObjectPropSafely(
          () => (extraData && extraData.data) || {},
          {},
        );
        let ratingIconType = getObjectPropSafely(
          () => (destinationInput.ratingIconType || {}).value,
          '',
        );
        if (_.isObject(ratingIconType)) {
          ratingIconType = ratingIconType.value;
        }
        let ratingScale = getObjectPropSafely(
          () => (destinationInput.ratingScale || {}).value,
          '',
        );
        if (_.isObject(ratingScale)) {
          ratingScale = ratingScale.value;
        }
        let currentDefaultAction = getObjectPropSafely(
          () =>
            (destinationInput.defaultAction &&
              destinationInput.defaultAction.value) ||
            '',
          '',
        );
        if (_.isObject(currentDefaultAction)) {
          currentDefaultAction = currentDefaultAction.value;
        }

        // Check if different template
        if (
          templateId &&
          ((typeof templateId === 'string' &&
            templateId !== selectedTemplate) ||
            (Array.isArray(templateId) &&
              !templateId.includes(selectedTemplate)))
        ) {
          isHidden = true;
        }

        // Check if different default action
        if (
          Object.values(DEFAULT_ACTION_KEYS).includes(name) &&
          currentDefaultAction !== name
        ) {
          isHidden = true;
        }

        // Check if different rating icon type
        if (templateId === selectedTemplate) {
          if (GEN_RATING_TEXT.includes(name)) {
            if (ratingIconType !== ICON_TYPE_KEY.TEXT_EMOJI) isHidden = true;

            if (ratingIconType === ICON_TYPE_KEY.TEXT_EMOJI) {
              const rateNumber = Number(name.replace(/[^0-9]/g, ''));

              if (rateNumber > +ratingScale) isHidden = true;
            }
          }

          if (
            ratingIconType === ICON_TYPE_KEY.TEXT_EMOJI &&
            name === RATING_SETTING_KEYS.UNSELECTED
          ) {
            isHidden = true;
          }
        }

        break;
      }
      default: {
        isHidden = false;
      }
    }

    return isHidden;
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'checkIsHiddenInputComponent',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};

export const getDataReUpdateByTemplateLineMessage = (templateType = '') => {
  try {
    const resetList = {};
    const refillList = {};

    Object.keys(LINE_FIELDS).forEach(each => {
      resetList[each] = _.difference(LINE_ALL_FIELD, LINE_FIELDS[each]);
      refillList[each] = LINE_FIELDS[each];
    });

    return {
      resetList: resetList[templateType],
      refillList: refillList[templateType],
    };
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'getDataReUpdateByTemplateLineMessage',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};

export const validateActionButton = (data = {}) => {
  try {
    const errKeys = _pickBy(data, _isEmpty);
    const listKeys = _keys(data);

    listKeys.forEach(eachKey => {
      const itemValue = data[eachKey];

      if (typeof itemValue === 'string') {
        if (
          (eachKey === 'btnLabel' && itemValue.length > 35) ||
          (eachKey === 'contentSms' && itemValue.length > 160) ||
          (['contentSms', 'contentMessage', 'url'].includes(eachKey) &&
            itemValue.length > 1000)
        ) {
          errKeys[eachKey] = 'error';
        } else if (eachKey === 'contentSms') {
          const removePattern = /#{.*?}|[\n\t]/gm; // tags || newlines
          // remove personalization tags and new lines to validate
          const valueValidate = itemValue.replace(removePattern, '');
          const isNotASCII = /[^ -~]/gi.test(valueValidate);

          if (isNotASCII) {
            errKeys[eachKey] = 'error';
          }
        }
      }
    });

    return errKeys;
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'validateActionButton',
      data: err.stack,
    });
  }
};

const getReUpdateDestinationInputConfigs = ({
  templateType = '',
  destinationInput = {},
}) => {
  try {
    const result = {
      resetList: [],
      refillList: [],
    };

    if (_isEmpty(templateType) || _isEmpty(destinationInput)) return result;

    const inputKeys = Object.keys(destinationInput);

    inputKeys.forEach(eachKey => {
      const templateId = getObjectPropSafely(
        () => destinationInput[eachKey].templateId,
        '',
      );

      if (!_isEmpty(templateId)) {
        if (templateId !== templateType) {
          result.resetList.push(eachKey);
        } else {
          result.refillList.push(eachKey);
        }
      }
    });

    return result;
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'getReUpdateFieldsZaloOA',
      data: { err, templateType, destinationInput },
    });
  }
};

export const reUpdateDataByCatalog = ({
  catalogCode = '',
  templateType = '',
  destinationInput = {},
}) => {
  try {
    switch (catalogCode) {
      case CATALOG_CODE.LINE:
      case CATALOG_CODE.ZALO_OA:
        return getReUpdateDestinationInputConfigs({
          templateType,
          destinationInput,
        });
      default: {
        return {
          resetList: [],
          refillList: [],
        };
      }
    }
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'reUpdateDataByCatalog',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};

export const getSelectedTemplate = (
  catalogCode = '',
  dataDestinationInput = {},
) => {
  try {
    switch (catalogCode) {
      case CATALOG_CODE.ZALO_OA:
      case CATALOG_CODE.LINE: {
        const name = 'template';

        const tmp = getObjectPropSafely(
          () => dataDestinationInput[name].value,
          {},
        );

        if (typeof tmp === 'object') return tmp.value;

        return tmp;
      }
      default: {
        return '';
      }
    }
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'g',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};
export const mapToAPICustomFunction = data => {
  const dataOut = {};
  if (data && Object.keys(data).length > 0) {
    Object.keys(data).forEach(key => {
      const { displayFormat, personalizationName, dataType, formular } = data[
        key
      ];
      dataOut[key] = {
        templateCode: serializeLabelToCodeAttr(personalizationName),
        templateType: 'custom',
        templateName: personalizationName,
        customFunction: formular,
        outputDataType: dataType,
        outputFormat: {
          ...displayFormat,
          type: displayFormat.type.toLowerCase(),
        },
      };
      if (dataType == 'datetime') {
        const hasDateFormat =
          displayFormat.config.date && displayFormat.config.date.check;
        const hasTimeFormat =
          displayFormat.config.time && displayFormat.config.time.check;
        const dateParseFormat = displayFormat.config.format;
        const dateParseOption =
          displayFormat.config.date && displayFormat.config.date.value;
        const timeParseOption =
          displayFormat.config.time && displayFormat.config.time.value;
        const timeParseFormat =
          displayFormat.config.time && displayFormat.config.time.timeFormat;

        dataOut[key].outputFormat.config = {
          ...displayFormat.config,
          hasDateFormat,
          hasTimeFormat,
          dateParseFormat,
          dateParseOption,
          timeParseOption,
          timeParseFormat,
          dateFormatString: getStringFormatDateDF({
            hasDateFormat,
            hasTimeFormat,
            dateParseFormat,
            dateParseOption,
            timeParseOption,
            timeParseFormat,
          }),
        };
      } else {
        dataOut[key].outputFormat.config = {
          ...displayFormat.config,
          decimal: displayFormat.config.decimal || '.',
          grouping:
            displayFormat.config.group === 'space'
              ? ' '
              : displayFormat.config.group || ',',
          isCompact: displayFormat.config.isCompactNumber || false,
          decimalPlaces: displayFormat.config.decimalPlace || 0,
          currencyCode: displayFormat.config.currency,
        };
      }
    });
  }
  return dataOut;
};

export function getStringFormatDateDF(formatSettings) {
  const {
    hasDateFormat,
    hasTimeFormat,
    timeParseFormat,
    dateParseFormat,
    timeParseOption,
    dateParseOption,
  } = formatSettings;

  let dateFormat = '';
  let timeFormat = '';

  if (hasDateFormat) {
    switch (dateParseFormat) {
      case 'MM/DD/YYYY':
        if (dateParseOption === 'short') dateFormat = 'MM/DD/YYYY';
        if (dateParseOption === 'medium') dateFormat = 'MMM DD YYYY';
        if (dateParseOption === 'long') dateFormat = 'MMMM DD YYYY';
        break;
      case 'DD/MM/YYYY':
        if (dateParseOption === 'short') dateFormat = 'DD/MM/YYYY';
        if (dateParseOption === 'medium') dateFormat = 'DD MMM YYYY';
        if (dateParseOption === 'long') dateFormat = 'DD MMMM YYYY';
        break;
      case 'YYYY/MM/DD':
        if (dateParseOption === 'short') dateFormat = 'YYYY/MM/DD';
        if (dateParseOption === 'medium') dateFormat = 'YYYY MMM DD';
        if (dateParseOption === 'long') dateFormat = 'YYYY MMMM DD';
        break;
      default:
        break;
    }
  }
  if (hasTimeFormat) {
    if (timeParseFormat === '12hour') {
      if (timeParseOption === 'short') timeFormat = 'h:mm A';
      if (timeParseOption === 'medium') timeFormat = 'h:mm:ss A';
      if (timeParseOption === 'long') timeFormat = 'h:mm:ss A [GMT]';
    } else {
      if (timeParseOption === 'short') timeFormat = 'H:mm';
      if (timeParseOption === 'medium') timeFormat = 'H:mm:ss';
      if (timeParseOption === 'long') timeFormat = 'H:mm:ss [GMT]';
    }
  }

  return `${dateFormat} ${timeFormat}`;
}
export const mapCustomFunctionTemplate = dataIn => {
  const dataOut = {};
  const { template_setting = {} } = dataIn;
  const { actions = [] } = template_setting;
  if (actions && actions.length > 0) {
    actions.forEach(each => {
      const { dynamic = {} } = each;
      const { textData = {} } = dynamic;
      if (textData && Object.keys(textData).length > 0) {
        Object.keys(textData).forEach(key => {
          if (textData[key].customFunction) {
            dataOut[textData[key].customFunction.templateCode] = {
              ...textData[key].customFunction,
            };
          }
        });
      }
    });
  }
  return dataOut;
};

export const validateCustomInputCampaign = draft => {
  // validate customInput
  const isValidateCustomCampaignArr = [];

  draft.campaignCustomInput.workspaces.forEach(each => {
    if (each.sections.length > 0) {
      each.sections.forEach(campaign => {
        if (campaign.validate && campaign.name !== 'datePicker') {
          const { errors, isValidate } = campaign.validate({
            ...campaign,
            ...campaign.properties,
            errors: [],
          });
          campaign.errors = errors;
          campaign.isValidate = isValidate;
          isValidateCustomCampaignArr.push(isValidate);
        }
        if (campaign.name === 'datePicker') {
          campaign.isValidate = campaign.properties.isValidate;
          isValidateCustomCampaignArr.push(campaign.properties.isValidate);
        }
      });
    }
  });

  const isValidateCustomCampaign = isValidateCustomCampaignArr.every(
    each => each === true,
  );

  // console.log('isValidateCustomAttributeArr', isValidateCustomAttribute);

  return isValidateCustomCampaign;
};

export const validateCustomInputVariant = draft => {
  let statusCustomAttribute = false;
  // Validate Custom Input
  const { variants } = draft;
  const { activeId } = variants;
  const isValidateCustomAttributeArr = [];
  (
    draft?.variants?.cacheInfo[activeId]?.customInputVia?.workspaces || []
  ).forEach(each => {
    if (each.sections.length > 0) {
      each.sections.forEach(attribute => {
        // console.log('attribute---', attribute);
        if (attribute.validate && attribute.name !== 'datePicker') {
          const { errors, isValidate } = attribute.validate({
            ...attribute,
            ...attribute.properties,
            errors: [],
          });
          attribute.errors = errors;
          attribute.isValidate = isValidate;
          isValidateCustomAttributeArr.push(isValidate);
        }
        if (attribute.name === 'datePicker') {
          attribute.isValidate = attribute.properties.isValidate;
          isValidateCustomAttributeArr.push(attribute.properties.isValidate);
        }
      });
    }
  });

  // console.log('isValidateCustomAttributeArr', isValidateCustomAttributeArr);

  statusCustomAttribute = isValidateCustomAttributeArr.every(
    each => each === true,
  );

  return statusCustomAttribute;
};

export const colGridWrapper = ({
  isBlastCampaign,
  isViewMode,
  defaultCol = 12,
}) => (isBlastCampaign ? (isViewMode ? 6 : 'auto') : defaultCol);

export const getStyleEmojiIcon = ({
  channelCode = '',
  catalogCode = '',
  name = '',
  extraData = {},
}) => {
  let iconName = 'aststickers';
  let iconStyle = {};
  let position = {};
  let alignment = '';
  const { isBlastCampaign = false } = extraData;

  switch (channelCode) {
    case CHANNEL_CODE.LINE: {
      iconName = 'bear-emoji';
      alignment = 'right';
      break;
    }
    case CHANNEL_CODE.APP_PUSH: {
      if (catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH) {
        iconName = 'smiley';
        iconStyle = { color: 'rgba(0, 95, 184, 1)', fontSize: '20px' };
        position = { top: '8px', right: '36px', left: 'unset' };
        alignment = 'right';

        if (GEN_RATING_TEXT.includes(name)) {
          position = {
            ...position,
            right: '10px',
          };
        }
      }
      break;
    }
    case CHANNEL_CODE.ZALO: {
      if (catalogCode === CATALOG_CODE.ZALO_OA) {
        iconName = 'smiley';
        iconStyle = { color: 'rgba(0, 95, 184, 1)', fontSize: '20px' };
        position = { top: '8px', right: '36px', left: 'unset' };
        alignment = 'right';
      }
      break;
    }
    case CHANNEL_CODE.TELEGRAM: {
      iconName = 'smiley';
      position = { top: '8px', right: '36px', left: 'unset' };

      if (isBlastCampaign) {
        alignment = 'right';
        iconStyle = { color: 'rgba(0, 95, 184, 1)', fontSize: '20px' };
      }
      break;
    }
    default: {
      break;
    }
  }

  return {
    iconName,
    iconStyle,
    position,
    alignment,
  };
};

export const checkEnableShortLink = ({
  defaultEnabled = false,
  channelCode = '',
  catalogCode = '',
  name = '',
  isHideShortLink = false,
}) => {
  let enableShortLink = defaultEnabled;

  switch (channelCode) {
    case CHANNEL_CODE.SMS:
    case CHANNEL_CODE.VIBER:
    case CHANNEL_CODE.LINE:
    case CHANNEL_CODE.TELEGRAM:
    case CHANNEL_CODE.CONVERSATION: {
      enableShortLink = true;
      break;
    }
    case CHANNEL_CODE.APP_PUSH: {
      if (
        (catalogCode !== CATALOG_CODE.ANTSOMI_APP_PUSH && !isHideShortLink) ||
        (catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH &&
          ['deeplink', 'url'].includes(name))
      ) {
        enableShortLink = true;
      }

      break;
    }
    case CHANNEL_CODE.ZALO: {
      if (catalogCode === CATALOG_CODE.ZALO_OA) {
        enableShortLink = ![
          'transactionTitle',
          'transactionMessage',
          'requestTitle',
          'mediaTitle',
          'mediaSubtitle',
          'mediaMessage',
          'transactionSubtitle',
        ].includes(name);
      }
      break;
    }
    default: {
      break;
    }
  }

  return enableShortLink;
};

export const getEditorWidth = (channelCode, isBlastCampaign) => {
  let width = '100%';
  if (!isBlastCampaign) {
    switch (channelCode) {
      case CHANNEL_CODE.TELEGRAM:
        width = '75%';
        break;

      default:
        break;
    }
  }

  return width;
};

export const isValidateWithCondition = info => {
  const { step, catalogCode, data: destinationInput } = info;
  let result = true;
  switch (catalogCode) {
    case CATALOG_CODE.ZNS: {
      if (step === 0) {
        const { template } = destinationInput;
        result =
          template && template.value && Object.keys(template.value).length;
      }
      break;
    }

    default:
      break;
  }

  return result;
};

export const EXPIRE_TIME_OPTIONS = [
  {
    value: 'none',
    label: 'None',
  },
  {
    value: 'specific',
    label: 'Message expires on a specific date',
  },
  {
    value: 'after',
    label: 'Message expires after',
    options: [
      {
        value: 'since',
        label: 'day(s) since the journey activated',
      },
      {
        value: 'from',
        label: 'day(s) from sending',
      },
    ],
  },
];

export const initDefaultValueByChannelCode = channelCode => {
  switch (channelCode) {
    case 'smart_inbox': {
      const dateFormat = 'DD/MM/YYYY';

      const initValue = {
        type: 'none',
        specific: {
          date: moment()
            .add({ month: 1 })
            .format(dateFormat),
          time: '23:59',
        },
        after: {
          type: 'since',
          since: 30,
          from: 30,
        },
      };

      return {
        inboxExpireTime: {
          options: EXPIRE_TIME_OPTIONS,
          mapOptions: keyBy(EXPIRE_TIME_OPTIONS, 'value'),
          default: initValue,
        },
      };
    }
    default:
      return {};
  }
};

export const handleFetchInfoDestination = async data => {
  const { type = '', serviceFn = () => {}, initData } = data;

  if (!type) return { data: [] };

  switch (type) {
    case 'CAMPAIGN': {
      const { lookup, role } = initData;

      if (lookup?.campaign) {
        return {
          data: [
            parseObjectNumeric(lookup.campaign, {
              keyFormat: 'snake',
              ignoreKeys: ['campaign_name'],
            }),
          ],
        };
      }

      return serviceFn({
        data: {
          campaign_ids: [
            role === 'RESET'
              ? safeParseInt(lookup.campaignId, 0)
              : safeParseInt(initData.campaignId, 0),
          ],
          columns: [
            'campaign_id',
            'campaign_name',
            'status',
            'campaign_setting',
            'custom_inputs',
          ],
        },
      });
    }

    case 'VARIANT': {
      const { lookup, role, variantIds } = initData;

      if (lookup?.variants?.length) {
        return {
          data: map(lookup.variants, variant =>
            parseObjectNumeric(variant, {
              keyFormat: 'snake',
              ignoreKeys: ['custom_inputs'],
            }),
          ),
        };
      }

      return serviceFn({
        data: {
          variant_ids: role === 'RESET' ? lookup.variantIds : variantIds,
          columns: [
            'variant_id',
            'variant_name',
            'content_setting',
            'status',
            'custom_inputs',
          ],
        },
      });
    }
    default:
      return { data: [] };
  }
};

/**
 * Updates the input configuration specifically for Antsomi App Push channel, assuming API usage.
 *
 * @param {object} inputConfig - The input configuration object to be modified.
 *
 * @remarks
 * This function modifies the input configuration for the Antsomi App Push channel, adding
 * a 'template' and more..., input field and adjusting other properties relevant for API usage.
 */
const updateInputConfigAntsomiAppPush = (inputConfig = {}) => {
  try {
    // Override the default basic inputs
    _.set(inputConfig, 'send.basicInputs', DEFAULT_BASIC_INPUTS);

    // Remove property unused
    ['androidLargeIconUrl', 'landingPageUrl'].forEach(eachKey => {
      if (_.has(inputConfig, `send.inputs.${eachKey}`)) {
        delete inputConfig.send.inputs[eachKey];
      }
    });

    // Add new fields
    _.set(inputConfig, 'send.inputs', {
      ..._.get(inputConfig, 'send.inputs', {}),
      ...initialInputElementRatingSettings(), // add setting for rating panels
    });

    // Modify the input fields
    MODIFIED_FIELDS.forEach(eachKey => {
      const modifyPath = `send.inputs.${eachKey}`;
      const originalConfig = _.get(inputConfig, modifyPath, {});

      switch (eachKey) {
        case RECOMMEND_FIELDS.TYPE:
        case RECOMMEND_FIELDS.CONTENT_SOURCES: {
          const initConfigs = cloneDeep(INIT_INPUT_SETTINGS[eachKey]);
          _.set(inputConfig, modifyPath, initConfigs);
          break;
        }
        case 'url':
        case 'bgColor':
        case 'deeplink':
        case 'template':
        case 'imageCarousel':
        case 'defaultAction':
        case 'imageAlignment':
        case 'androidContentSummary': {
          const initConfigs = cloneDeep(INIT_INPUT_SETTINGS[eachKey]);

          if (
            [
              'defaultAction',
              'url',
              'deeplink',
              'imageCarousel',
              'bgColor',
              'imageAlignment',
            ].includes(eachKey)
          ) {
            _.set(initConfigs, 'groupConfigs', {
              groupCode: null,
              groupName: null,
              panelCode: 'basic_setting',
              panelName: 'Basic Setting',
              groupOrder: null,
              panelOrder: 1,
            });
          }

          _.set(inputConfig, modifyPath, initConfigs);
          break;
        }
        case 'androidSound': {
          _.set(inputConfig, modifyPath, {
            ...originalConfig,
            placeholder: `Default is device's default`,
          });
          break;
        }
        case 'advPriority': {
          _.set(inputConfig, modifyPath, {
            ...originalConfig,
            alignment: 'horizontal',
          });
          break;
        }
        case 'advButton': {
          _.set(inputConfig, modifyPath, {
            ...originalConfig,
            groupConfigs: {
              panelCode: 'button',
              panelName: 'Buttons',
              groupOrder: null,
              panelOrder: 1,
            },
            options: [
              {
                label: 'Button 1',
                value: 'button1',
                inputs: {
                  advBtnLabel: INIT_INPUT_SETTINGS.advBtnLabel,
                  advBtnClick: {
                    ...INIT_INPUT_SETTINGS.defaultAction,
                    label: 'Click Action',
                    description: 'Click action of setting',
                  },
                  [DEFAULT_ACTION_KEYS.URL]:
                    INIT_INPUT_SETTINGS[DEFAULT_ACTION_KEYS.URL],
                  [DEFAULT_ACTION_KEYS.DEEP_LINK]:
                    INIT_INPUT_SETTINGS[DEFAULT_ACTION_KEYS.DEEP_LINK],
                },
                basicInputs: [
                  'advBtnLabel',
                  'advBtnClick',
                  ...Object.values(DEFAULT_ACTION_KEYS),
                ],
              },
            ],
          });
          break;
        }
        case 'advField': {
          _.set(inputConfig, modifyPath, {
            ...originalConfig,
            canAddPersonalization: true,
            label: 'Data',
            isInitFirstRow: true,
            isAppendBOContentSource: true,
            groupConfigs: {
              panelCode: 'adv_setting',
              panelName: 'Advanced Setting',
              groupOrder: 0,
              panelOrder: 1,
            },
          });
          break;
        }
        case 'advCollapseId': {
          _.set(inputConfig, modifyPath, {
            ...originalConfig,
            placeholder: 'Collapse ID',
          });
          break;
        }
        case 'subTitle': {
          _.set(inputConfig, modifyPath, {
            ...originalConfig,
            isRequired: false,
            inputType: 'text',
            maxLength: null,
            canAddPersonalization: false,
            groupConfigs: {
              groupCode: 'ios',
              groupName: 'Apple IOS',
              panelCode: 'platform_setting',
              panelName: 'Platform Setting',
              groupOrder: 0,
              panelOrder: 0,
            },
          });
          break;
        }
        case 'imageUrl': {
          _.set(inputConfig, modifyPath, {
            ...originalConfig,
            templateId: Object.values(TEMPLATE_ANTSOMI_APP_PUSH_KEYS).filter(
              key =>
                key !== TEMPLATE_ANTSOMI_APP_PUSH_KEYS.SIMPLE_IMAGE_CAROUSEL,
            ),
            maxSize: 25 / 1024,
            isAppendBOContentSource: true,
            groupConfigs: {
              groupCode: null,
              groupName: null,
              panelCode: 'basic_setting',
              panelName: 'Basic Setting',
              groupOrder: null,
              panelOrder: 1,
            },
          });
          break;
        }
        default: {
          const placeholder = _.get(initPlaceholder, eachKey, '');

          _.set(inputConfig, modifyPath, {
            ...originalConfig,
            placeholder: placeholder || originalConfig.placeholder,
            isAppendBOContentSource: ['title', 'content'].includes(eachKey),
            groupConfigs: {
              groupCode: null,
              groupName: null,
              panelCode: 'basic_setting',
              panelName: 'Basic Setting',
              groupOrder: null,
              panelOrder: 1,
            },
          });
          break;
        }
      }
    });
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'updateInputConfigAntsomiAppPush',
      data: {
        err: err.stack,
      },
    });
  }
};

/**
 * Injects hard-coded input configuration properties specifically for API usage,
 * based on the provided channel code and catalog code.
 *
 * @param {object} options - An object containing the following properties:
 * @param {string} options.channelCode - The code representing the communication channel.
 * @param {string} options.catalogCode - The code representing the catalog.
 * @param {object} options.inputConfig - The input configuration object to be modified.
 *
 * @remarks
 * This function is intended to be used for hard-coding specific input configuration settings that are required for API usage,
 * as opposed to configurations that can be dynamically generated or retrieved from other sources.
 */
export const injectInputConfigHardForAPIByChannel = ({
  channelCode = '',
  catalogCode = '',
  inputConfig = {},
}) => {
  if (!_.isObject(inputConfig)) return;

  switch (channelCode) {
    case CHANNEL_CODE.EMAIL: {
      // hard for API
      _.set(inputConfig, 'send.basicInputs', ['subject', 'preheader', 'body']);
      _.set(inputConfig, 'send.inputs.preheader', {
        label: 'Preheader',
        dataType: 'string',
        inputType: 'text',
        labelCode: 'LABEL_TEMP',
        maxLength: 100,
        isRequired: false,
        validators: {
          type: 'string',
          length: {
            maximum: 100,
          },
          presence: {
            allowEmpty: false,
          },
        },
        description: 'Preheader of email',
        inputFormat: 'string',
        previewFieldCode: 'LABEL_TEMP',
        canAddPersonalization: true,
        canImportFromMediaLibrary: false,
      });
      break;
    }
    case CHANNEL_CODE.APP_PUSH: {
      if (catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH) {
        updateInputConfigAntsomiAppPush(inputConfig);
      }

      break;
    }
    default: {
      break;
    }
  }
};

/**
 * Gets the current template input for a given channel and catalog code.
 *
 * @param {object} destinationInput - The destination input object.
 * @param {string} destinationInput.template.value - The value of the template property in the destination input object.
 * @param {string} channelCode - The channel code (e.g., "app_push").
 * @param {string} catalogCode - The catalog code (e.g., "antsomi_app_push").
 *
 * @returns {string|null} The current template input, or null if not found.
 */
export const getCurrentTemplateInputByChannel = ({
  destinationInput = {},
  channelCode = '',
  catalogCode = '',
}) => {
  try {
    if (_.isEmpty(destinationInput)) return null;

    switch (channelCode) {
      case CHANNEL_CODE.APP_PUSH: {
        if (catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH) {
          return getObjectPropSafely(
            () => destinationInput.template.value,
            null,
          );
        }
        return null;
      }
      default: {
        return null;
      }
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'getCurrentTemplateInputByChannel',
      data: {
        err: err.stack,
        destinationInput,
        channelCode,
        catalogCode,
      },
    });
  }
};

/**
 * Handles changes to effect extra settings for a specific channel and catalog.
 *
 * @param {Object} options
 * @param {string} options.channelCode The channel code (e.g., `CHANNEL_CODE.APP_PUSH`).
 * @param {string} options.catalogCode The catalog code (e.g., `CATALOG_CODE.ANTSOMI_APP_PUSH`).
 * @param {Object} options.data The effect extra data (structure and types depend on context).
 * @param {string} options.name The name of the setting being changed.
 * @param {Function} options.callback Function to call with updated configuration or error.
 * @returns {void}
 *
 * @throws {Error} If the `callback` is not a function.
 * @throws {Error} If any other errors occur during processing.
 */
export const handleChangeEffectExtra = ({
  channelCode = '',
  catalogCode = '',
  data = {},
  name = '',
  callback = () => {},
}) => {
  try {
    if (typeof callback !== 'function') return null;

    switch (channelCode) {
      case CHANNEL_CODE.APP_PUSH: {
        if (catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH) {
          if (name === RATING_SETTING_KEYS.ICON_TYPE) {
            callback('UPDATE_CONFIG_RATING', { name, data });
          }
          if (['template', RATING_SETTING_KEYS.SCALE].includes(name)) {
            callback('UPDATE_ANTSOMI_APP_PUSH_EFFECT_EXTRA', {
              name,
              data,
            });
          }
        }

        break;
      }
      case CHANNEL_CODE.ZALO: {
        if (catalogCode === CATALOG_CODE.ZALO_OA && name === 'template') {
          callback('UPDATE_ZALO_OA_EFFECT_EXTRA', { name, data });
        }
        break;
      }
      default: {
        break;
      }
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleChangeEffectExtra',
      data: {
        err: err.stack,
        data,
        callback,
        channelCode,
        catalogCode,
      },
    });
  }
};

/**
 * Retrieves journey settings based on the provided parameters.
 *
 * This function is responsible for constructing and returning journey settings
 * based on the provided parameters such as trigger node, trigger event, trigger type,
 * blast campaign flag, and item type ID.
 *
 * @param {Object} params - The parameters object.
 * @param {Map} [params.triggerNode=new Map()] - The trigger node map, which contains information
 * about the trigger node, such as target audience and perform event details.
 * @param {Object} [params.triggerEvent={}] - The trigger event object, representing the event
 * details that trigger the journey, including event action ID and category ID.
 * @param {string} [params.triggerType=''] - The type of trigger for the journey, which can be
 * 'scheduled' or 'event_based'.
 * @param {boolean} [params.isBlastCampaign=false] - A flag indicating whether the journey is a blast campaign.
 * @param {number|null} [params.itemTypeId=null] - The item type ID associated with the journey.
 *
 * @returns {Object} - The journey settings object containing configuration parameters for the journey.
 * - unsubscribeSegmentType {string}: The type of segment to unsubscribe from ('customer' or 'visitor').
 * - triggerType {string}: The type of trigger for the journey ('scheduled' or 'event_based').
 * - triggerEvent {Object}: The trigger event details.
 *   - eventActionId {string}: The ID of the event action triggering the journey.
 *   - eventCategoryId {string}: The ID of the event category triggering the journey.
 *   - insightPropertyIds {Array<number>}: An array of insight property IDs related to the event.
 *
 * @throws {Error} If an unexpected error occurs during processing, an error is logged and the default
 * journey settings object is returned.
 */
export const getJourneySettings = ({
  triggerNode = new Map(),
  triggerEvent = {},
  triggerType = '',
  isBlastCampaign = false,
  itemTypeId = null,
}) => {
  const journeySettings = {
    unsubscribeSegmentType: 'visitor',
    triggerType: 'event_based',
    triggerEvent: {
      eventActionId: '',
      eventCategoryId: '',
      insightPropertyIds: [],
    },
  };

  try {
    if (
      !_.isObject(triggerEvent) ||
      ![NODE_TYPE.SCHEDULED, NODE_TYPE.EVENT_BASED].includes(triggerType)
    )
      return journeySettings;

    if (triggerType === NODE_TYPE.SCHEDULED) {
      _.set(journeySettings, 'triggerType', 'scheduled');
      let nodeItemTypeId = null;

      if (isBlastCampaign && itemTypeId) {
        nodeItemTypeId = itemTypeId;
      } else if (triggerNode.has('targetAudience')) {
        nodeItemTypeId = triggerNode.get('targetAudience').itemTypeId || null;
      }

      const unsubscribeSegmentType =
        +nodeItemTypeId === -1003 ? 'customer' : 'visitor';

      _.set(journeySettings, 'unsubscribeSegmentType', unsubscribeSegmentType);
    }

    if (triggerType === NODE_TYPE.EVENT_BASED) {
      const { eventActionId = '', eventCategoryId = '' } = triggerEvent;

      let { insightPropertyIds = [] } = triggerEvent;

      if (!isBlastCampaign && triggerNode.has('peformEvent')) {
        const performEvent = triggerNode.get('peformEvent');
        const isInit = performEvent.has('data-init'); // In case is not fetch node trigger yet => isInit = true

        if (isInit) {
          const backup = performEvent.get('data-init').get('backup');
          insightPropertyIds = _.get(backup, 'insightPropertyIds', []);
        } else if (
          performEvent &&
          performEvent.size === 1 &&
          performEvent.first() &&
          performEvent.first().size === 1
        ) {
          const performEventNode = performEvent.first().first();
          const dataSources = performEventNode.get('dataSources');

          if (_.isArray(dataSources)) {
            insightPropertyIds = dataSources.map(ds => Number(ds));
          }
        }
      }

      _.set(journeySettings, 'triggerEvent', {
        ...journeySettings.triggerEvent,
        eventActionId,
        eventCategoryId,
        insightPropertyIds,
      });
    }

    return journeySettings;
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/utils.js',
      func: 'getJourneySettings',
      data: {
        err: err.stack,
        isBlastCampaign,
        itemTypeId,
        triggerNode,
        triggerEvent,
        triggerType,
      },
    });
    return journeySettings;
  }
};

export const hasTags = input => {
  let hasTag = false;
  Object.values(patternHandlers).forEach(patternWrapper => {
    const {
      pattern,
      name: cachePatternName,
      acceptablePattern: acceptableType,
      handler,
    } = patternWrapper;
    let match;

    // eslint-disable-next-line no-cond-assign
    while ((match = new RegExp(pattern, 'g').exec(input)) !== null) {
      hasTag = true;
      break;
    }
  });
  return hasTag;
};
