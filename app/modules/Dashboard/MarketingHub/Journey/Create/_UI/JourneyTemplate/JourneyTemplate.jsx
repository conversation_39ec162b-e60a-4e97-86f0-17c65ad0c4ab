/* eslint-disable react/prop-types */
/* eslint-disable consistent-return */
import React, { memo } from 'react';
import { Verification, EmptyListing } from './UI/Loadable';
import { CircularProgress } from '@material-ui/core';
import { JourneyTemplateRoot } from './styled';
import classNames from 'classnames';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { isEmpty, isFunction } from 'lodash';
import isEqual from 'react-fast-compare';
import {
  useDebounceCallback,
  useDeepCompareEffect,
} from '../../../../../../../hooks';
import { selectSlice } from './selector';
import { useSelector } from 'react-redux';
import { formatDataChange } from './utils/helper';
import { useApplyTemplate } from './hooks/useApplyJT';
import { JOURNEY_TEMPLATE_ID } from './constant';

const PATH =
  'app/modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/JourneyTemplate';

export const JourneyTemplate = memo(props => {
  const { onChange, onApply, onCancel } = props;

  const state = useSelector(selectSlice);

  const { isLoading, initialized, verification } = state;

  const { debounce: debounceOnChange } = useDebounceCallback(onChange, 700);

  const isEmptyObjects = initialized && isEmpty(verification.objects);

  useApplyTemplate({ onApply });

  useDeepCompareEffect(() => {
    if (!state.initialized) return;

    if (isFunction(onChange)) {
      const dataChange = formatDataChange(state);

      debounceOnChange(dataChange);
    }
  }, [state]);

  const renderContent = () => {
    if (isLoading) {
      return <CircularProgress size={24} />;
    }

    if (isEmptyObjects) {
      return <EmptyListing onCancel={onCancel} />;
    }

    return <Verification onCancel={onCancel} />;
  };

  return (
    <ErrorBoundary path={PATH}>
      <JourneyTemplateRoot
        id={JOURNEY_TEMPLATE_ID}
        style={props.style}
        className={classNames({
          isLoading,
          isEmptyObjects,
        })}
      >
        {renderContent()}
      </JourneyTemplateRoot>
    </ErrorBoundary>
  );
}, isEqual);
