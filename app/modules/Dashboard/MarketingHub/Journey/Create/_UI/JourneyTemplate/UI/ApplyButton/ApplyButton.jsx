import React from 'react';
import { Button } from '@antscorp/antsomi-ui';
import { useDispatch, useSelector } from 'react-redux';
import { selectAllowApplyJT, selectIsApplying } from '../../selector';
import { applyJT } from '../../actions';

export const ApplyButton = () => {
  const dispath = useDispatch();

  const allowApply = useSelector(selectAllowApplyJT);
  const isApplying = useSelector(selectIsApplying);

  const handleApplyJT = () => {
    if (!allowApply) return;

    dispath(applyJT());
  };

  return (
    <Button loading={isApplying} onClick={handleApplyJT} disabled={!allowApply}>
      Apply
    </Button>
  );
};
