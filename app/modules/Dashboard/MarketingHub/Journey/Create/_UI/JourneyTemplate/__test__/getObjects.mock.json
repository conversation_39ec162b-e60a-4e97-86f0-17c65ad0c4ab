{"GET_OBJECTS_PER_NODE": [["Basic", "EVENT_BASED", {"branch": {"label": "Action-based Trigger", "actionId": "ag7tm", "actionType": "EVENT_BASED", "metadata": {"endCondition": "never", "startDate": "2024-03-05", "startTime": 1709601240000, "startTimeOfDay": {"hour": 9, "minute": 14}, "endDate": null, "endTime": null, "endTimeOfDay": null, "event": {"condition_type": "perf_event", "conditionType": "event_attribute", "eventCategoryId": -20, "eventActionId": -102, "eventTrackingName": "view pageview", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********], "filters": {"OR": [{"AND": [{"type": "item", "column": "name", "dataType": "string", "data_type": "string", "item_type_id": -3, "operator": "matches", "syntax": "browser_id", "conditionType": "event_attribute", "value": ["<PERSON><PERSON><PERSON><PERSON>", "Chrome", "Chrome Mobile"], "metadata": {"itemTypeId": -3, "itemTypeName": "browser", "itemPropertyName": "name", "eventPropertySyntax": "browser_id"}}, {"type": "event", "column": "atm_source", "dataType": "string", "data_type": "string", "item_type_id": null, "operator": "matches", "syntax": "extra.atm_source", "conditionType": "event_attribute", "value": ["123", "check", "check_pro_shey", "google.com", "tc_source", "trello.com", "test_delivery_utm_source"], "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "atm_source", "eventPropertySyntax": "extra.atm_source"}}]}]}}, "frequencyCapping": null, "timeOfWeek": [], "triggerType": "all", "journeyGoals": {}, "custom_inputs": {"type_of_external_partner": "Trade Partner", "tnh_thnh": "<PERSON><PERSON> <PERSON>", "thanh_country": "", "trade_partner": "Precision Marketing"}}}}, [{"pathConfig": [], "actionId": "ag7tm", "actionType": "EVENT_BASED", "itemTypeId": -3, "type": "business_object"}, {"pathConfig": [], "actionId": "ag7tm", "actionType": "EVENT_BASED", "itemTypeId": -1009, "type": "business_object"}, {"pathConfig": [], "actionId": "ag7tm", "actionType": "EVENT_BASED", "itemTypeId": -1009, "itemPropertyName": "type_of_external_partner", "type": "bo_attribute"}, {"pathConfig": [], "actionId": "ag7tm", "actionType": "EVENT_BASED", "itemTypeId": -1009, "itemPropertyName": "tnh_thnh", "type": "bo_attribute"}, {"pathConfig": [], "actionId": "ag7tm", "actionType": "EVENT_BASED", "itemTypeId": -1009, "itemPropertyName": "thanh_country", "type": "bo_attribute"}, {"pathConfig": [], "actionId": "ag7tm", "actionType": "EVENT_BASED", "itemTypeId": -1009, "itemPropertyName": "trade_partner", "type": "bo_attribute"}, {"pathConfig": [], "actionId": "ag7tm", "actionType": "EVENT_BASED", "itemPropertyName": "name", "itemTypeId": -3, "type": "bo_attribute"}, {"pathConfig": [{"path": "metadata.event.eventActionId", "value": "eventActionId"}, {"path": "metadata.event.eventCategoryId", "value": "eventCategoryId"}, {"path": "metadata.event.eventTrackingName", "value": "eventTrackingName"}, {"path": "metadata.event.insightPropertyIds", "value": "insightPropertyIds"}], "actionId": "ag7tm", "actionType": "EVENT_BASED", "eventActionId": -102, "eventCategoryId": -20, "type": "event"}, {"pathConfig": [{"path": "metadata.event.filters.OR.0.AND.0.column", "value": "itemPropertyName"}, {"path": "metadata.event.filters.OR.0.AND.0.item_type_id", "value": "itemTypeId"}, {"path": "metadata.event.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.event.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "metadata.event.filters.OR.0.AND.0.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.event.filters.OR.0.AND.0.metadata.itemTypeName", "value": "itemTypeName"}, {"path": "metadata.event.filters.OR.0.AND.0.syntax", "value": "eventPropertySyntax"}], "actionId": "ag7tm", "actionType": "EVENT_BASED", "eventActionId": -102, "eventCategoryId": -20, "itemTypeId": -3, "itemPropertyName": "name", "type": "event_attribute"}, {"pathConfig": [{"path": "metadata.event.filters.OR.0.AND.1.column", "value": "eventPropertyName"}, {"path": "metadata.event.filters.OR.0.AND.1.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.event.filters.OR.0.AND.1.metadata.itemPropertyName", "value": "eventPropertyName"}, {"path": "metadata.event.filters.OR.0.AND.1.syntax", "value": "eventPropertySyntax"}], "actionId": "ag7tm", "actionType": "EVENT_BASED", "eventActionId": -102, "eventCategoryId": -20, "eventPropertyName": "atm_source", "type": "event_attribute"}]], ["Basic", "CONDITION_YES", {"branch": {"label": "Yes", "actionId": "ltdqsdpp", "actionType": "CONDITION_YES", "metadata": {"branchName": "Yes", "filterType": "event_attribute", "filters": {"OR": [{"AND": [{"conditionType": "event_attribute", "column": "duration", "itemTypeId": null, "dataType": "number", "operator": "greater_than", "propertySyntax": "duration_time", "value": 12, "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "duration", "eventPropertySyntax": "duration_time"}}, {"conditionType": "event_attribute", "column": "name", "itemTypeId": -5, "dataType": "string", "operator": "matches", "propertySyntax": "country_id", "value": ["Việt Nam"], "metadata": {"itemTypeId": -5, "itemTypeName": "country", "itemPropertyName": "name", "eventPropertySyntax": "country_id"}}]}, {"AND": [{"conditionType": "event_attribute", "column": "name", "itemTypeId": -3, "dataType": "string", "operator": "matches", "propertySyntax": "browser_id", "value": ["Chrome Mobile", "Chrome"], "metadata": {"itemTypeId": -3, "itemTypeName": "browser", "itemPropertyName": "name", "eventPropertySyntax": "browser_id"}}]}]}}}}, [{"itemTypeId": -5, "pathConfig": [], "type": "business_object", "actionId": "ltdqsdpp", "actionType": "CONDITION_YES"}, {"itemTypeId": -3, "pathConfig": [], "type": "business_object", "actionId": "ltdqsdpp", "actionType": "CONDITION_YES"}, {"pathConfig": [], "itemTypeId": -5, "itemPropertyName": "name", "type": "bo_attribute", "actionId": "ltdqsdpp", "actionType": "CONDITION_YES"}, {"pathConfig": [], "itemTypeId": -3, "itemPropertyName": "name", "type": "bo_attribute", "actionId": "ltdqsdpp", "actionType": "CONDITION_YES"}, {"eventPropertyName": "duration", "pathConfig": [{"path": "metadata.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.0.AND.0.column", "value": "eventPropertyName"}, {"path": "metadata.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "eventPropertyName"}, {"path": "metadata.filters.OR.0.AND.0.propertySyntax", "value": "eventPropertySyntax"}], "eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "actionId": "ltdqsdpp", "actionType": "CONDITION_YES"}, {"itemPropertyName": "name", "pathConfig": [{"path": "metadata.filters.OR.0.AND.1.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.filters.OR.0.AND.1.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.0.AND.1.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.filters.OR.0.AND.1.metadata.itemTypeName", "value": "itemTypeName"}, {"path": "metadata.filters.OR.0.AND.1.column", "value": "itemPropertyName"}, {"path": "metadata.filters.OR.0.AND.1.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "metadata.filters.OR.0.AND.1.propertySyntax", "value": "eventPropertySyntax"}], "itemTypeId": -5, "eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "actionId": "ltdqsdpp", "actionType": "CONDITION_YES"}, {"itemPropertyName": "name", "pathConfig": [{"path": "metadata.filters.OR.1.AND.0.column", "value": "itemPropertyName"}, {"path": "metadata.filters.OR.1.AND.0.metadata.itemTypeName", "value": "itemTypeName"}, {"path": "metadata.filters.OR.1.AND.0.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.filters.OR.1.AND.0.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "metadata.filters.OR.1.AND.0.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.filters.OR.1.AND.0.propertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.1.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}], "itemTypeId": -3, "eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "actionId": "ltdqsdpp", "actionType": "CONDITION_YES"}]], ["Basic", "FILTER", {"branch": {"label": "Filter", "actionId": "x52ko", "actionType": "FILTER", "metadata": {"filterType": "event_attribute", "filters": {"OR": [{"AND": [{"value": ["1804_c"], "column": "atm_campaign", "dataType": "string", "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "atm_campaign", "eventPropertySyntax": "extra.atm_campaign"}, "operator": "matches", "itemTypeId": null, "conditionType": "event_attribute", "propertySyntax": "extra.atm_campaign"}]}, {"AND": [{"value": ["Affilate", "Direct", "Native Search", "Display", "SMS"], "column": "name", "dataType": "string", "metadata": {"itemTypeId": -8, "itemTypeName": "channel", "itemPropertyName": "name", "eventPropertySyntax": "channel_id"}, "operator": "matches", "itemTypeId": -8, "conditionType": "event_attribute", "propertySyntax": "channel_id"}, {"value": 1, "column": "virtual_custom_function", "dataType": "number", "metadata": {"itemTypeId": 1156616, "itemTypeName": "bo_test_rfm", "itemPropertyName": "virtual_custom_function", "eventPropertySyntax": "dims.bo_test_rfm_id"}, "operator": "greater_than", "itemTypeId": 1156616, "conditionType": "event_attribute", "propertySyntax": "dims.bo_test_rfm_id"}]}, {"AND": [{"value": ["check_pro_t", "check_pro_they"], "column": "atm_term", "dataType": "string", "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "atm_term", "eventPropertySyntax": "extra.atm_term"}, "operator": "matches", "itemTypeId": null, "conditionType": "event_attribute", "propertySyntax": "extra.atm_term"}]}]}}}}, [{"itemTypeId": -8, "pathConfig": [], "type": "business_object", "actionId": "x52ko", "actionType": "FILTER"}, {"itemTypeId": 1156616, "pathConfig": [], "type": "business_object", "actionId": "x52ko", "actionType": "FILTER"}, {"pathConfig": [], "itemTypeId": -8, "itemPropertyName": "name", "type": "bo_attribute", "actionId": "x52ko", "actionType": "FILTER"}, {"pathConfig": [], "itemTypeId": 1156616, "itemPropertyName": "virtual_custom_function", "type": "bo_attribute", "actionId": "x52ko", "actionType": "FILTER"}, {"eventPropertyName": "atm_campaign", "pathConfig": [{"path": "metadata.filters.OR.0.AND.0.propertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.0.AND.0.column", "value": "eventPropertyName"}, {"path": "metadata.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "eventPropertyName"}], "eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "actionId": "x52ko", "actionType": "FILTER"}, {"itemPropertyName": "name", "pathConfig": [{"path": "metadata.filters.OR.1.AND.0.propertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.1.AND.0.column", "value": "itemPropertyName"}, {"path": "metadata.filters.OR.1.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.1.AND.0.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "metadata.filters.OR.1.AND.0.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.filters.OR.1.AND.0.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.filters.OR.1.AND.0.metadata.itemTypeName", "value": "itemTypeName"}], "itemTypeId": -8, "eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "actionId": "x52ko", "actionType": "FILTER"}, {"itemPropertyName": "virtual_custom_function", "pathConfig": [{"path": "metadata.filters.OR.1.AND.1.propertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.1.AND.1.column", "value": "itemPropertyName"}, {"path": "metadata.filters.OR.1.AND.1.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.1.AND.1.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "metadata.filters.OR.1.AND.1.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.filters.OR.1.AND.1.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.filters.OR.1.AND.1.metadata.itemTypeName", "value": "itemTypeName"}], "itemTypeId": 1156616, "eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "actionId": "x52ko", "actionType": "FILTER"}, {"eventPropertyName": "atm_term", "pathConfig": [{"path": "metadata.filters.OR.2.AND.0.propertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.2.AND.0.column", "value": "eventPropertyName"}, {"path": "metadata.filters.OR.2.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.filters.OR.2.AND.0.metadata.itemPropertyName", "value": "eventPropertyName"}], "eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "actionId": "x52ko", "actionType": "FILTER"}]], ["Basic", "UPDATE_SEGMENT", {"branch": {"label": "Update segment", "actionId": "9z5o3", "metadata": {"action": "add_audience", "status": 1, "itemTypeId": -1007, "segmentIds": [5659527, 5661063], "itemTypeName": "user"}, "actionType": "UPDATE_SEGMENT"}}, [{"segmentId": 5659527, "itemTypeId": -1007, "pathConfig": [{"path": "metadata.segmentIds.0", "value": "segmentId"}], "type": "segment", "actionId": "9z5o3", "actionType": "UPDATE_SEGMENT"}, {"segmentId": 5661063, "itemTypeId": -1007, "pathConfig": [{"path": "metadata.segmentIds.1", "value": "segmentId"}], "type": "segment", "actionId": "9z5o3", "actionType": "UPDATE_SEGMENT"}]], ["Basic", "WAIT_EVENT", {"branch": {"label": "Wait for event", "actionId": "h6vdu", "actionType": "WAIT_EVENT", "metadata": {"type": "multi-event", "waitingTimeout": {"delta": 60, "timeUnit": "SECONDS"}, "waitingEvents": [{"filters": {"OR": [{"AND": [{"type": "item", "value": 100, "column": "price", "syntax": "items.product_id", "dataType": "number", "metadata": {"itemTypeId": 1, "itemTypeName": "product", "itemPropertyName": "price", "eventPropertySyntax": "items.product_id"}, "operator": "greater_than", "data_type": "number", "item_type_id": 1, "conditionType": "event_attribute"}, {"type": "event", "value": "1564106", "column": "user_id", "syntax": "user_id", "dataType": "string", "metadata": {"itemTypeId": null, "itemTypeName": "users", "itemPropertyName": "user_id", "eventPropertySyntax": "user_id"}, "operator": "contains", "data_type": "string", "item_type_id": null, "conditionType": "event_attribute"}]}]}, "conditionType": "event_attribute", "eventActionId": -102, "condition_type": "perf_event", "eventCategoryId": -20, "eventTrackingName": "view pageview", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}, {"filters": {"OR": [{"AND": [{"type": "item", "value": ["Call", "Conversation", "Direct", "Email"], "column": "name", "syntax": "channel_id", "dataType": "string", "metadata": {"itemTypeId": -8, "itemTypeName": "channel", "itemPropertyName": "name", "eventPropertySyntax": "channel_id"}, "operator": "matches", "data_type": "string", "item_type_id": -8, "conditionType": "event_attribute"}, {"type": "event", "value": ["image link"], "column": "utm_content", "syntax": "extra.utm_content", "dataType": "string", "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "utm_content", "eventPropertySyntax": "extra.utm_content"}, "operator": "matches", "data_type": "string", "item_type_id": null, "conditionType": "event_attribute"}]}]}, "conditionType": "event_attribute", "eventActionId": -106, "condition_type": "perf_event", "eventCategoryId": -11, "eventTrackingName": "purchase product", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}]}, "branchs": [{"label": "Event happened", "actionId": "ltdqwoh1", "actionType": "WAIT_EVENT_HAPPENED", "metadata": {}, "branchs": [{"label": "Filter", "actionId": "9gc2e", "actionType": "FILTER", "metadata": {"filterType": "user_attribute", "filters": {"OR": [{"AND": [{"value": 1709481600000, "column": "date_created", "dataType": "datetime", "operator": "equals", "time_unit": "DAY", "itemTypeId": -1007, "conditionType": "user_attribute"}]}]}}, "branchs": [{"label": "END", "actionId": "ltshtt1g", "actionType": "END", "metadata": {}, "branchs": []}]}]}, {"label": "Waiting timeout", "actionId": "ltdqwoh2", "actionType": "WAIT_EVENT_TIMEOUT", "metadata": {}, "branchs": [{"label": "Update Info", "actionId": "0sg7w", "actionType": "UPDATE_INFO", "metadata": {"params": [{"feKey": "date_created--1007", "value": "", "action": "reset_value", "dataType": "datetime", "variable": null, "propertyName": "date_created", "isUseVariable": false}], "itemTypeId": -1003, "itemTypeName": "customer"}, "branchs": [{"label": "END", "actionId": "ltshtt1h", "actionType": "END", "metadata": {}, "branchs": []}]}]}]}}, [{"itemTypeId": 1, "pathConfig": [], "type": "business_object", "actionId": "h6vdu", "actionType": "WAIT_EVENT"}, {"itemTypeId": -8, "pathConfig": [], "type": "business_object", "actionId": "h6vdu", "actionType": "WAIT_EVENT"}, {"pathConfig": [], "itemTypeId": 1, "itemPropertyName": "price", "type": "bo_attribute", "actionId": "h6vdu", "actionType": "WAIT_EVENT"}, {"pathConfig": [], "itemTypeId": -8, "itemPropertyName": "name", "type": "bo_attribute", "actionId": "h6vdu", "actionType": "WAIT_EVENT"}, {"eventActionId": -102, "eventCategoryId": -20, "pathConfig": [{"path": "metadata.waitingEvents.0.eventActionId", "value": "eventActionId"}, {"path": "metadata.waitingEvents.0.eventCategoryId", "value": "eventCategoryId"}, {"path": "metadata.waitingEvents.0.eventTrackingName", "value": "eventTrackingName"}, {"path": "metadata.waitingEvents.0.insightPropertyIds", "value": "insightPropertyIds"}], "type": "event", "actionId": "h6vdu", "actionType": "WAIT_EVENT"}, {"eventActionId": -106, "eventCategoryId": -11, "pathConfig": [{"path": "metadata.waitingEvents.1.eventActionId", "value": "eventActionId"}, {"path": "metadata.waitingEvents.1.eventCategoryId", "value": "eventCategoryId"}, {"path": "metadata.waitingEvents.1.eventTrackingName", "value": "eventTrackingName"}, {"path": "metadata.waitingEvents.1.insightPropertyIds", "value": "insightPropertyIds"}], "type": "event", "actionId": "h6vdu", "actionType": "WAIT_EVENT"}, {"eventActionId": -102, "eventCategoryId": -20, "itemPropertyName": "price", "pathConfig": [{"path": "metadata.waitingEvents.0.filters.OR.0.AND.0.syntax", "value": "eventPropertySyntax"}, {"path": "metadata.waitingEvents.0.filters.OR.0.AND.0.column", "value": "itemPropertyName"}, {"path": "metadata.waitingEvents.0.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.waitingEvents.0.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "metadata.waitingEvents.0.filters.OR.0.AND.0.item_type_id", "value": "itemTypeId"}, {"path": "metadata.waitingEvents.0.filters.OR.0.AND.0.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.waitingEvents.0.filters.OR.0.AND.0.metadata.itemTypeName", "value": "itemTypeName"}], "itemTypeId": 1, "type": "event_attribute", "actionId": "h6vdu", "actionType": "WAIT_EVENT"}, {"eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "actionId": "h6vdu", "actionType": "WAIT_EVENT", "eventPropertyName": "user_id", "pathConfig": [{"path": "metadata.waitingEvents.0.filters.OR.0.AND.1.metadata.itemPropertyName", "value": "eventPropertyName"}, {"path": "metadata.waitingEvents.0.filters.OR.0.AND.1.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.waitingEvents.0.filters.OR.0.AND.1.column", "value": "eventPropertyName"}, {"path": "metadata.waitingEvents.0.filters.OR.0.AND.1.syntax", "value": "eventPropertySyntax"}, {"path": "metadata.waitingEvents.0.filters.OR.0.AND.1.metadata.itemTypeName", "value": "itemTypeName"}]}, {"eventActionId": -106, "eventCategoryId": -11, "itemPropertyName": "name", "pathConfig": [{"path": "metadata.waitingEvents.1.filters.OR.0.AND.0.syntax", "value": "eventPropertySyntax"}, {"path": "metadata.waitingEvents.1.filters.OR.0.AND.0.column", "value": "itemPropertyName"}, {"path": "metadata.waitingEvents.1.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.waitingEvents.1.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "metadata.waitingEvents.1.filters.OR.0.AND.0.item_type_id", "value": "itemTypeId"}, {"path": "metadata.waitingEvents.1.filters.OR.0.AND.0.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.waitingEvents.1.filters.OR.0.AND.0.metadata.itemTypeName", "value": "itemTypeName"}], "itemTypeId": -8, "type": "event_attribute", "actionId": "h6vdu", "actionType": "WAIT_EVENT"}, {"eventActionId": -106, "eventCategoryId": -11, "eventPropertyName": "utm_content", "pathConfig": [{"path": "metadata.waitingEvents.1.filters.OR.0.AND.1.syntax", "value": "eventPropertySyntax"}, {"path": "metadata.waitingEvents.1.filters.OR.0.AND.1.column", "value": "eventPropertyName"}, {"path": "metadata.waitingEvents.1.filters.OR.0.AND.1.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "metadata.waitingEvents.1.filters.OR.0.AND.1.metadata.itemPropertyName", "value": "eventPropertyName"}], "type": "event_attribute", "actionId": "h6vdu", "actionType": "WAIT_EVENT"}]], ["Basic", "UPDATE_INFO", {"branch": {"label": "Update Info", "branchs": [{"label": "END", "branchs": [], "actionId": "lte4704h", "metadata": {}, "actionType": "END"}], "actionId": "0sg7w", "metadata": {"params": [{"feKey": "date_created--1007", "value": "", "action": "reset_value", "dataType": "datetime", "variable": null, "propertyName": "date_created", "isUseVariable": false}], "itemTypeId": -1003, "itemTypeName": "customer"}, "actionType": "UPDATE_INFO"}}, [{"itemTypeId": -1003, "pathConfig": [{"path": "metadata.itemTypeId", "value": "itemTypeId"}, {"path": "metadata.itemTypeName", "value": "itemTypeName"}], "type": "business_object", "actionId": "0sg7w", "actionType": "UPDATE_INFO"}, {"itemPropertyName": "date_created", "itemTypeId": -1003, "pathConfig": [{"path": "metadata.params.0.propertyName", "value": "itemPropertyName"}], "type": "bo_attribute", "actionId": "0sg7w", "actionType": "UPDATE_INFO"}]], ["Filter With Segments", "FILTER", {"branch": {"label": "Filter", "actionId": "ltp6o0gz", "metadata": {"filters": {"OR": [{"AND": [{"code": "sgmt_5659712", "value": 5659712, "column": "segment_id", "operator": "includes", "feOperator": "includes", "conditionType": "customer_segment"}]}, {"AND": [{"code": "sgmt_5659885", "value": 5659885, "column": "segment_id", "operator": "includes", "feOperator": "includes", "conditionType": "customer_segment"}]}]}, "filterType": "item_segment", "itemTypeId": -1003, "sortFilter": ["include", "exclude"], "excludedFilters": {"OR": [{"AND": [{"code": "sgmt_5648143", "value": 5648143, "column": "segment_id", "operator": "includes", "feOperator": "includes", "conditionType": "customer_segment"}]}]}, "audienceSegmentType": "customer_segment"}, "actionType": "FILTER"}}, [{"segmentId": 5648143, "pathConfig": [{"path": "metadata.excludedFilters.OR.0.AND.0.code", "value": "segmentCode"}, {"path": "metadata.excludedFilters.OR.0.AND.0.value", "value": "segmentId"}], "itemTypeId": -1003, "type": "segment", "actionId": "ltp6o0gz", "actionType": "FILTER"}, {"segmentId": 5659712, "pathConfig": [{"path": "metadata.filters.OR.0.AND.0.code", "value": "segmentCode"}, {"path": "metadata.filters.OR.0.AND.0.value", "value": "segmentId"}], "itemTypeId": -1003, "type": "segment", "actionId": "ltp6o0gz", "actionType": "FILTER"}, {"segmentId": 5659885, "pathConfig": [{"path": "metadata.filters.OR.1.AND.0.code", "value": "segmentCode"}, {"path": "metadata.filters.OR.1.AND.0.value", "value": "segmentId"}], "itemTypeId": -1003, "type": "segment", "actionId": "ltp6o0gz", "actionType": "FILTER"}]], ["Basic", "DESTINATION", {"branch": {"label": "<PERSON>", "actionId": "guqa7", "actionType": "DESTINATION", "metadata": {"isUpdated": 1, "channelId": 7, "destinationId": 510514, "catalogId": 510480, "campaignId": 5690159, "variantIds": [5690162], "campaign": {"status": 1, "campaignName": "Campaign 1", "campaignId": 5690159, "campaignSetting": {"algoMethod": "random", "random": {"5690162": 1}, "deliveryTimeConfig": {"type": "all_time", "range": [], "mode": "delay"}}, "custom_inputs": {}}, "variants": [{"variantId": 5690162, "variantKey": 5690162, "variantName": "Variant 1", "contentSetting": {"objectWidgetInput": {}, "destinationInput": {"text": "#{event.ad_zone_id||\"1234\"}  content #{visitor.last_updated||\"\"}  fasdfa #{shortlink(https://go.dev/blog/slices-intro)} fasdf #{event.article.image_url||\"\"} #{promotion_code.pool_d_20.id} \ncustomer #{customer.telephone||\"\"}"}, "variantExtraData": {"customFunction": {}, "formatAttributes": {"visitor.last_updated": {"attribute": {"dataType": "datetime", "settingsFe": {"format": {"type": "DATE_AND_TIME", "config": {}}, "dataType": "datetime"}, "datetimeFormatSettings": {"type": "date_and_time", "hasDateFormat": true, "hasTimeFormat": true, "dateParseFormat": "DD/MM/YYYY", "dateParseOption": "long", "timeParseFormat": "24", "timeParseOption": "medium", "dateFormatString": "DD MMMM YYYY H:mm:ss"}}}}}}, "status": 1, "custom_inputs": {}}]}, "branchs": [{"label": "END", "actionId": "lu0x9dcw", "actionType": "END", "metadata": {}, "branchs": []}]}, "nodeTrigger": {"label": "Action-based Trigger", "actionId": "6771z", "actionType": "EVENT_BASED", "metadata": {"endCondition": "never", "startDate": "2024-03-14", "startTime": 1710383040000, "startTimeOfDay": {"hour": 10, "minute": 24}, "endDate": null, "endTime": null, "endTimeOfDay": null, "event": {"filters": {"OR": [{"AND": []}]}, "conditionType": "event_attribute", "eventActionId": -102, "condition_type": "perf_event", "eventCategoryId": -20, "eventTrackingName": "view pageview", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, 556434020]}, "frequencyCapping": null, "timeOfWeek": [], "triggerType": "all", "journeyGoals": {}, "custom_inputs": {}}}}, [{"pathConfig": [], "type": "business_object", "actionId": "guqa7", "actionType": "DESTINATION", "itemTypeId": -1007}, {"pathConfig": [], "type": "business_object", "actionId": "guqa7", "actionType": "DESTINATION", "itemTypeName": "article"}, {"pathConfig": [], "type": "business_object", "actionId": "guqa7", "actionType": "DESTINATION", "itemTypeId": -1003}, {"pathConfig": [{"path": "metadata.variants.0.contentSetting.destinationInput.text", "pattern": "visitor.last_updated||\"\"", "value": "visitor.{itemPropertyName}||\"\""}], "type": "bo_attribute", "actionId": "guqa7", "actionType": "DESTINATION", "itemTypeId": -1007, "itemPropertyName": "last_updated"}, {"pathConfig": [], "type": "bo_attribute", "actionId": "guqa7", "actionType": "DESTINATION", "itemTypeName": "article", "itemPropertyName": "image_url"}, {"pathConfig": [{"path": "metadata.variants.0.contentSetting.destinationInput.text", "pattern": "customer.telephone||\"\"", "value": "customer.{itemPropertyName}||\"\""}], "type": "bo_attribute", "actionId": "guqa7", "actionType": "DESTINATION", "itemTypeId": -1003, "itemPropertyName": "telephone"}, {"pathConfig": [{"path": "metadata.variants.0.contentSetting.destinationInput.text", "pattern": "event.ad_zone_id||\"1234\"", "value": "event.{eventPropertyName}||\"1234\""}], "type": "event_attribute", "actionId": "guqa7", "actionType": "DESTINATION", "eventActionId": -102, "eventCategoryId": -20, "eventPropertyName": "ad_zone_id"}, {"pathConfig": [{"path": "metadata.variants.0.contentSetting.destinationInput.text", "pattern": "event.article.image_url||\"\"", "value": "event.{itemTypeName}.{itemPropertyName}||\"\""}], "type": "event_attribute", "actionId": "guqa7", "actionType": "DESTINATION", "eventActionId": -102, "eventCategoryId": -20, "itemPropertyName": "image_url", "itemTypeName": "article"}, {"pathConfig": [{"path": "metadata.destinationId", "value": "destinationId"}, {"path": "metadata.catalogId", "value": "catalogId"}, {"path": "metadata.channelId", "value": "channelId"}], "type": "destination", "actionId": "guqa7", "actionType": "DESTINATION", "destinationId": 510514, "campaignId": 5690159, "catalogId": 510480}, {"pathConfig": [{"path": "metadata.variants.0.contentSetting.destinationInput.text", "pattern": "promotion_code.pool_d_20.id", "value": "promotion_code.{poolCode}.id"}], "type": "promotion_pool", "actionId": "guqa7", "actionType": "DESTINATION", "poolCode": "pool_d_20"}]]]}