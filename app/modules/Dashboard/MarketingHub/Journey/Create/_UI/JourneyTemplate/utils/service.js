/* eslint-disable indent */
/* eslint-disable import/no-cycle */
/* eslint-disable no-param-reassign */
import {
  cloneDeep,
  differenceWith,
  isEmpty,
  isFunction,
  map,
  pick,
  uniqWith,
  set,
  first,
  isArray,
  reduce,
} from 'lodash';
import isEqual from 'react-fast-compare';
import JourneyServices from 'services/Journey';
import JTService from 'services/JourneyTemplate';
import { JOURNEY_OBJECT_TYPE, OBJECT_TYPE } from '../constant';
import { OBJ_PRIMARY_KEYS } from './config';
import {
  createDOAttribute,
  getObjectsFromBOAttributes,
  getSupportedObjsFromDOAttr,
} from './dataObjectAttr';
import {
  findObject,
  flattenJTObjects,
  getVerifyConfigObject,
  isEqualObject,
} from './helper';
import { createJourneyGoal, getObjectsFromConversion } from './journeyGoal';
import { createSegment, getObjectsFromSegment } from './segment';
import { createDO } from './dataObject';
import UserAttributes from 'services/UserAttributes';
import EventSourceService from 'services/EventSources';

import {
  parseObjectNumeric,
  isObject,
} from '../../../../../../../../utils/common';
import WhoAmIServices from 'services/WhoAmI';
import { getCurrentUserRole } from '../../../../../../../../utils/web/cookie';
import { iterateObject } from '../../../../../../../../utils/web/utils';
import { getSupportedObjsForEventAttr } from './eventAttr';

export const JourneyObject = (() => {
  const HelperFn = {
    async getObjDetails({ objectConfig }) {
      let temp = [...objectConfig];

      temp.forEach(object => {
        switch (object.type) {
          case OBJECT_TYPE.eventAttribute: {
            const {
              eventActionId,
              eventCategoryId,
              itemPropertyName,
              itemTypeId,
            } = object;

            if (!itemTypeId) break;

            temp.push(
              {
                type: OBJECT_TYPE.event,
                eventActionId,
                eventCategoryId,
              },
              {
                type: OBJECT_TYPE.dataObjectAttr,
                itemTypeId,
                itemPropertyName,
              },
              {
                type: OBJECT_TYPE.dataObject,
                itemTypeId,
              },
            );
            break;
          }
          case OBJECT_TYPE.dataObjectAttr: {
            const { itemTypeId } = object;

            temp.push({
              type: OBJECT_TYPE.dataObject,
              itemTypeId,
            });
            break;
          }
          default:
            break;
        }
      });

      temp = uniqWith(temp, isEqualObject);

      const { data } = await JTService.getIngredients({
        objectConfig: map(temp, HelperFn.getPrimaryKeys),
      });

      return { data: data.map(HelperFn.mappingDetail) };
    },

    getPrimaryKeys(obj) {
      const keys = [...OBJ_PRIMARY_KEYS[obj.type], 'type'];

      return pick(obj, keys);
    },

    mappingDetail(obj) {
      const SNAKE_CASE_SETTINGS = [
        OBJECT_TYPE.segment,
        OBJECT_TYPE.dataObjectAttr,
        OBJECT_TYPE.eventAttribute,
        OBJECT_TYPE.dataObject,
        OBJECT_TYPE.promotionPool,
      ];

      const entries = Object.entries(obj).map(([key, value]) => {
        if (!value) return [key, value];

        // The API's getDetail return object settings in camel-case,
        // but some detail settings retrieved from object's detail UI are in snake-case
        if (key === 'settings') {
          return [
            key,
            parseObjectNumeric(value, {
              keyFormat: SNAKE_CASE_SETTINGS.includes(obj.type)
                ? 'snake'
                : 'camel',
            }),
          ];
        }

        if (isObject(value)) {
          return [key, parseObjectNumeric(value)];
        }

        return [key, value];
      });

      return Object.fromEntries(entries);
    },

    getObjsFromSettings: ({ mainObjDetails, object }) => {
      const { type, settings } = object;

      const GET_OBJS_HANDLER = {
        [OBJECT_TYPE.segment]: getObjectsFromSegment,

        [OBJECT_TYPE.dataObjectAttr]: doAttrSettings => {
          const boDetail = mainObjDetails.find(obj =>
            isEqualObject(obj, {
              type: OBJECT_TYPE.dataObject,
              itemTypeId: doAttrSettings.item_type_id,
            }),
          );

          if (boDetail && boDetail.isExist) {
            return getObjectsFromBOAttributes({
              boAttrSettings: doAttrSettings,
              relatedBoSettings: boDetail.settings,
            });
          }

          return [];
        },

        [OBJECT_TYPE.journeyGoal]: getObjectsFromConversion,
      };

      const fnGetSubObjs = GET_OBJS_HANDLER[type];

      if (isEmpty(settings) || !isFunction(fnGetSubObjs)) return [];

      return fnGetSubObjs(settings);
    },

    getSupportedObjects: (objs, mainObjects, opts = {}) => {
      opts.flatten = opts.flatten || true;

      const temp = isArray(objs) ? objs : [objs];

      let result = reduce(
        temp,
        (acc, verifyObj) => {
          const { type } = verifyObj;

          if (!Object.values(JOURNEY_OBJECT_TYPE).includes(type)) return acc;

          acc.push(
            ...getSupportedObjsForEventAttr({
              eventAttrObject: verifyObj,
              objects: mainObjects,
            }),

            ...getSupportedObjsFromDOAttr({
              doAttrObject: verifyObj,
              objects: mainObjects,
            }),
          );

          return acc;
        },
        [],
      );

      if (opts.flatten) {
        result = flattenJTObjects(result);
      }

      return result;
    },
  };

  return {
    getJourneyObjectsDetail: async args => {
      {
        const { withSubObjs = true } = args;

        const mutationObjects = cloneDeep(args.objects);

        if (isEmpty(args.objects)) return [];

        const objSettings = [];

        const { data: mainObjDetails = [] } = await HelperFn.getObjDetails({
          objectConfig: args.objects,
        });

        objSettings.push(...mainObjDetails);

        if (withSubObjs && !isEmpty(mainObjDetails)) {
          let subObjs = [];

          mainObjDetails.forEach(mainObjDetail => {
            const collectedSubObjs = HelperFn.getObjsFromSettings({
              mainObjDetails,
              object: mainObjDetail,
            });

            const idx = args.objects.findIndex(obj =>
              isEqualObject(mainObjDetail, obj),
            );

            if (idx !== -1) {
              mutationObjects[idx].objects = collectedSubObjs;
            }

            subObjs.push(...collectedSubObjs);
          });

          subObjs = uniqWith(subObjs, isEqual);

          subObjs = differenceWith(subObjs, mainObjDetails, isEqualObject);

          if (!isEmpty(subObjs)) {
            const { data: subObjDetails } = await HelperFn.getObjDetails({
              objectConfig: subObjs,
            });

            objSettings.push(...subObjDetails);
          }
        }

        return {
          objects: mutationObjects,
          objectSettings: objSettings,
        };
      }
    },

    validateTemplateObjects: async args => {
      const { objects, templateObjSettings } = args;

      let verifyObjs = [];

      args.verifyObjs.forEach(verifyObj => {
        const object = findObject(verifyObj, objects);

        if (object) {
          verifyObjs.push(object);
        }
      });

      verifyObjs = [
        ...verifyObjs,
        ...HelperFn.getSupportedObjects(verifyObjs, objects),
      ];

      verifyObjs = flattenJTObjects(verifyObjs);

      const objectConfig = map(verifyObjs, object => ({
        type: object.type,

        fe_mapping: HelperFn.getPrimaryKeys(object),

        ...getVerifyConfigObject({
          object,
          objects: verifyObjs,
          templateObjSettings,
        }),
      }));

      let { data } = await JTService.validateIngredients({
        objectConfig,
      });

      data = data.map(obj => ({
        ...obj,
        ...obj.feMapping,
        feMapping: undefined,
      }));

      data = data.map(obj => HelperFn.mappingDetail(obj));

      const mutatedObjects = verifyObjs.map(obj => {
        const temp = { ...obj };

        const resObject = findObject(temp, data);

        if (resObject) {
          temp.verify = pick(resObject, [
            'dataReplace',
            'settings',
            'isExist',
            'errorMessage',
          ]);
        }

        return temp;
      });

      return mutatedObjects;
    },

    HelperFn,
  };
})();

export const getCampaignSettings = async ({ campaignIds }) => {
  if (isEmpty(campaignIds)) return [];

  const { data } = await JourneyServices.campaigns.getByIds({
    data: {
      campaign_ids: campaignIds,
      columns: [
        'campaign_id',
        'campaign_name',
        'status',
        'campaign_setting',
        'custom_inputs',
      ],
    },
  });

  return data;
};

export const getVariantSettings = async ({ variantIds }) => {
  if (isEmpty(variantIds)) return [];

  const { data: variants } = await JourneyServices.variant.getByIds({
    data: {
      variant_ids: variantIds,
      columns: [
        'variant_id',
        'variant_name',
        'content_setting',
        'status',
        'custom_inputs',
      ],
    },
  });

  const withProperties = [];

  variants.forEach((item, idx) => {
    iterateObject(item, (value, path) => {
      if (!isObject(value) || !value.fe_config_id) return;

      withProperties.push({
        feConfigId: value.fe_config_id,
        path,
        idx,
      });
    });
  });

  await Promise.all(
    withProperties.map(async item => {
      const { feConfigId, path, idx } = item;

      const { data } = await UserAttributes.settings.get(feConfigId);

      if ((first(data) || {}).properties) {
        set(variants, `${idx}.${path}.properties`, first(data).properties);
      }
    }),
  );

  return variants;
};

export const createObjectService = async args => {
  const { newObject, settings } = args;

  const SERVICE_MAPPING = {
    [OBJECT_TYPE.journeyGoal]: createJourneyGoal,
    [OBJECT_TYPE.segment]: createSegment,
    [OBJECT_TYPE.dataObject]: createDO,
    [OBJECT_TYPE.dataObjectAttr]: createDOAttribute,
  };

  const createFn = SERVICE_MAPPING[newObject.type];

  if (isFunction(createFn)) {
    return createFn(settings);
  }

  return { success: false };
};

export const getDefaultShareAccess = async () => {
  const res = await WhoAmIServices.user.info();

  const userRole = getCurrentUserRole();

  if (res.code === 200) {
    const userInfo = res.data;
    return {
      is_public: 0,
      public_role: null,
      list_access: [
        {
          allow_comment: 1,
          avatar: userInfo.avatar,
          email: userInfo.email,
          full_name: userInfo.full_name,
          user_id: userInfo.user_id,
          allow_edit: 1,
          allow_view: 1,
          role: userRole,
        },
      ],
    };
  }
  return {};
};

export const getSourcesFromEventObjs = async ({ objects }) => {
  const events = objects.filter(obj => obj.type === OBJECT_TYPE.event);

  const { data: sources } = await EventSourceService.data.getByEvents({
    eventConfig: map(events, e =>
      pick(e, ['eventActionId', 'eventCategoryId']),
    ),
  });

  return {
    sources: sources.map(s =>
      parseObjectNumeric(s, {
        keyFormat: 'camel',
        keys: ['insightPropertyType', 'status'],
      }),
    ),
  };
};
