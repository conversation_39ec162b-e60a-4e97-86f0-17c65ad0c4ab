/* eslint-disable no-param-reassign */
import { orderBy } from 'lodash';
import { getObjectsFromBOAttributes } from '../utils/dataObjectAttr';
import MOCK from './boAttribute.mock.json';

describe('boAttribute Utils', () => {
  describe('function getObjectsFromBOAttributes', () => {
    test.each(MOCK.getObjectsFromBOAttribute)(
      'Case: %s',
      (_description, args, expected) => {
        let received = getObjectsFromBOAttributes(args);

        expected = orderBy(expected, obj => JSON.stringify(obj));
        received = orderBy(received, obj => JSON.stringify(obj));

        expect(received).toHaveLength(expected.length);
        expect(received).toEqual(expect.arrayContaining(expected));
      },
    );
  });
});
