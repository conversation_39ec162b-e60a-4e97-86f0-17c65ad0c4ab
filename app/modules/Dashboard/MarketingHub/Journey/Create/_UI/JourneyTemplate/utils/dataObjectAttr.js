/* eslint-disable import/no-cycle */
/* eslint-disable no-cond-assign */
/* eslint-disable indent */
import { first, get, isEmpty, isFunction, isObject } from 'lodash';
import BOServices from 'services/BusinessObject';
import { getCurrentUserId } from '../../../../../../../../utils/web/cookie';
import { getAttrType } from '../../../../../../ApiHub/BusinessObject/Detail/Attributes/Detail/utils';
import { JOURNEY_OBJECT_TYPE, OBJECT_TYPE } from '../constant';
import { ERROR_CODE, EVENT_ATTR_CONDITION_TYPE } from './config';
import {
  addEventAttribute,
  findObject,
  groupObjects,
  isEqualObject,
  isRelatedObject,
  loopFilters,
} from './helper';

export const getDOAttrsFromFilters = (filters, options = {}) => {
  const { filtersPropName = 'filters', customPathConfig } = options;

  const dataObjAttrs = [];

  loopFilters(filters, {
    eachCondition: (condition, conditionPath) => {
      const { conditionType } = condition;

      const path = `${filtersPropName}.${conditionPath}`;

      let obj = { pathConfig: [] };

      if (conditionType === 'event_attribute') {
        const { itemTypeId, itemPropertyName } = condition.metadata;

        obj = {
          ...obj,
          itemPropertyName,
        };

        if (itemTypeId) {
          obj.itemTypeId = itemTypeId;
        }
      }

      if (['customer_attribute', 'user_attribute'].includes(conditionType)) {
        obj = {
          ...obj,
          itemTypeId: condition.itemTypeId,
          itemPropertyName: condition.column,
          pathConfig: [
            {
              path: `${path}.column`,
              value: 'itemPropertyName',
            },
          ],
        };
      }

      if (isFunction(customPathConfig)) {
        obj.pathConfig = customPathConfig({
          condition,
          path,
          defaultPathConfig: obj.pathConfig,
        });
      }

      if (obj.itemTypeId) {
        dataObjAttrs.push(obj);
      }
    },
  });

  return dataObjAttrs;
};

export const getDOAttrsFromPerfEvent = (perfEvent, options = {}) => {
  const { filtersPropName = 'filters' } = options;

  const dataObjAttrs = getDOAttrsFromFilters(perfEvent.filters, {
    filtersPropName,
    customPathConfig: () => [],
  });

  return dataObjAttrs;
};

export const getObjectsFromBOAttributes = ({
  boAttrSettings,
  relatedBoSettings,
}) => {
  const { item_type_name: itemTypeName } = relatedBoSettings;

  let objects = [];

  const getObjsFromCustomFn = () => {
    const {
      custom_function: customFn,
      item_type_id: itemTypeId,
    } = boAttrSettings;

    if (isEmpty(customFn)) return;

    const regexAttrTag = /{{(.*?)}}/gm;

    let match;

    while ((match = regexAttrTag.exec(customFn)) !== null) {
      const tagContent = match[1];

      const [, itemPropertyName] = tagContent.split('.');

      objects.push({
        type: OBJECT_TYPE.dataObjectAttr,
        itemTypeId,
        itemPropertyName,
        pathConfig: [
          {
            path: `custom_function`,
            value: tagContent.replace(
              `${itemTypeName}.${itemPropertyName}`,
              '{itemTypeName}.{itemPropertyName}',
            ),
            pattern: tagContent,
          },
        ],
      });
    }
  };

  const getObjsFromConditions = () => {
    const {
      conditions,
      compute_item_type_id: computeItemTypeId,
      compute_property_name: computePropertyName,
    } = boAttrSettings;

    if (isEmpty(get(conditions, 'rules.OR'))) return;

    const {
      event_action_id: eventActionId,
      event_category_id: eventCategoryId,
      rules,
    } = conditions;

    const eventInfo = { eventActionId, eventCategoryId };

    objects.push({
      type: OBJECT_TYPE.event,
      pathConfig: [
        {
          path: 'conditions.event_action_id',
          value: 'eventActionId',
        },
        {
          path: 'conditions.event_category_id',
          value: 'eventCategoryId',
        },
        {
          path: 'conditions.insight_property_ids',
          value: 'insightPropertyIds',
        },
      ],
      ...eventInfo,
    });

    if (computePropertyName && computeItemTypeId) {
      objects = addEventAttribute({
        objects,
        eventInfo,
        itemTypeId: computeItemTypeId,
        propertyName: computePropertyName,
        pathConfig: [
          {
            path: 'compute_item_type_id',
            value: 'itemTypeId',
          },
          {
            path: 'compute_property_name',
            value: 'itemPropertyName',
          },
        ],
      });
    }

    if (computePropertyName && !computeItemTypeId) {
      objects = addEventAttribute({
        objects,
        eventInfo,
        itemTypeId: null,
        propertyName: computePropertyName,
        pathConfig: [
          {
            path: 'compute_property_name',
            value: 'eventPropertyName',
          },
        ],
      });
    }

    loopFilters(rules, {
      eachCondition: (condition, conditionPath) => {
        const path = `conditions.rules.${conditionPath}`;

        objects = addEventAttribute({
          objects,
          eventInfo,
          itemTypeId: condition.item_type_id,
          propertyName: condition.condition_property_name,
          pathConfig: params => {
            const { type } = params;

            const pathConfigByType = {
              [EVENT_ATTR_CONDITION_TYPE.normal]: [
                {
                  path: `${path}.condition_property_name`,
                  value: 'eventPropertyName',
                },
              ],
              [EVENT_ATTR_CONDITION_TYPE.boAttr]: [
                {
                  path: `${path}.item_type_id`,
                  value: 'itemTypeId',
                },
                {
                  path: `${path}.condition_property_name`,
                  value: 'itemPropertyName',
                },
              ],
              [EVENT_ATTR_CONDITION_TYPE.boId]: [],
            };

            return pathConfigByType[type] || [];
          },
        });
      },
    });
  };

  objects.push({
    type: OBJECT_TYPE.dataObject,
    itemTypeId: boAttrSettings.item_type_id,
    pathConfig: [
      {
        path: 'item_type_id',
        value: 'itemTypeId',
      },
    ],
  });

  getObjsFromCustomFn();

  getObjsFromConditions();

  return groupObjects(objects);
};

export const getActDOAttrAdditionalInfo = (doAttrObj, objects) => {
  if (doAttrObj.type !== JOURNEY_OBJECT_TYPE.dataObjectAttr) return {};

  const { itemTypeId } = doAttrObj;

  const dataObject = findObject(
    {
      type: JOURNEY_OBJECT_TYPE.dataObject,
      itemTypeId,
    },
    objects,
  );

  return { dataObject };
};

export const createDOAttribute = async settings => {
  const groupInfo = await BOServices.groups.getListSelector({
    itemTypeId: settings.item_type_id,
  });

  const { groupId } = first(groupInfo.data);

  let newSettings = {
    ...settings,
    group_id: groupId,
    is_identity: +settings.is_identity,
    description: String(settings.description),
    item_type_id: +settings.item_type_id,

    ...(settings.is_input_via_ui && {
      input_via_ui_value: settings?.properties?.input_via_ui_value,
    }),
  };

  if (settings?.alert_setting) {
    newSettings = {
      ...newSettings,
      alert_setting: {
        alertAccountIds: [+getCurrentUserId()],
        alertScopes: settings.alert_setting.alertScopes,
      },
    };
  }

  if (settings?.object_metadata) {
    newSettings = {
      ...newSettings,
      object_metadata: isObject(settings.object_metadata)
        ? settings.object_metadata
        : {},
    };
  } else {
    newSettings = {
      ...newSettings,
      object_metadata: {},
    };
  }

  const attributeType = getAttrType(settings);

  const scopeInfo = {};

  switch (attributeType.typeRender) {
    case 'custom_attribute':
      scopeInfo.scope = 'custom_attribute';
      scopeInfo.type = 2;
      break;
    case 'custom_function':
      scopeInfo.scope = 'cf_attribute';
      scopeInfo.type = 3;
      break;

    case 'computed_attribute':
      scopeInfo.scope = 'comp_attribute';
      scopeInfo.type = 3;
      break;

    case 'conversion_attribute':
      scopeInfo.scope = 'conversion_attribute';
      scopeInfo.type = 3;
      break;

    default:
      break;
  }

  const params = {
    data: {
      ...newSettings,

      ...scopeInfo,
    },
  };

  const res = await BOServices.BOAttribute.create(params);

  if (res && res.codeMessage === '_NOTIFICATION_SAME_CODE') {
    return {
      success: false,
      error: {
        code: ERROR_CODE.BO_ATTR_CODE_EXIST,
      },
    };
  }

  if (res && res.code === 200) {
    return { success: true };
  }

  return { success: false };
};

export const getSupportedObjsFromDOAttr = ({ doAttrObject, objects }) => {
  if (doAttrObject.type !== JOURNEY_OBJECT_TYPE.dataObjectAttr) return [];

  const supportedObjects = [...getObjectsUseSameDOAttr(doAttrObject, objects)];

  objects.forEach(object => {
    if (isRelatedObject(object, doAttrObject)) {
      supportedObjects.push(object);
    }
  });

  return supportedObjects;
};

export const objectIsUseSameBOAttr = (boAttr, object) => {
  const { type } = object;

  const { itemTypeId, itemPropertyName } = boAttr;

  if (
    type === OBJECT_TYPE.dataObjectAttr ||
    type === OBJECT_TYPE.eventAttribute
  ) {
    return (
      itemTypeId === object.itemTypeId &&
      itemPropertyName === object.itemPropertyName
    );
  }

  return false;
};

export const getObjectsUseSameDOAttr = (doAttrObj, objects) => {
  if (doAttrObj.type !== OBJECT_TYPE.dataObjectAttr) return [];

  const result = [];

  objects.forEach(obj => {
    if (
      objectIsUseSameBOAttr(doAttrObj, obj) &&
      !isEqualObject(doAttrObj, obj)
    ) {
      result.push(obj);
    }
  });

  return result;
};
