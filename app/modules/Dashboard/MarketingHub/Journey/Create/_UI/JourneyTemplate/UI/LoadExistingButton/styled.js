import Popover from '@material-ui/core/Popover';
import styled from 'styled-components';
import {
  LabelSearchColumn,
  ValueSearchColumn,
  WrapperFilterTextSearch,
  WrapperSuggestion,
} from '../../../../../../../../../containers/Search/styled';
import { UIButton } from '@xlab-team/ui-components';

export const StyledPopover = styled(Popover)`
  .MuiPaper-root {
    border-radius: 10px !important;
  }

  ${WrapperSuggestion} {
    min-height: auto;
    max-width: 350px;

    ${WrapperFilterTextSearch} {
      &:hover {
        background-color: #f2f9ff;
      }

      ${LabelSearchColumn} {
        font-size: 12px;

        ${ValueSearchColumn} {
          font-family: inherit;
        }
      }
    }
  }
`;

export const StyledButton = styled(UIButton)`
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: white;
  border-color: #b8cfe6;
  flex-shrink: 0;

  .MuiSvgIcon-root {
    font-size: 20px;
  }
`;

export const LabelNodata = styled.div`
  font-size: 11px;
  color: #595959;
`;
