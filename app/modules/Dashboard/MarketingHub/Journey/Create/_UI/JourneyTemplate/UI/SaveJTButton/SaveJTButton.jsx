import React from 'react';
import { UIButton } from '@xlab-team/ui-components';
import { getTranslateMessage } from '../../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../../messages/constant';
import { useSelector } from 'react-redux';
import { selectIsLoading, selectVerification } from '../../selector';
import { isEmpty, some } from 'lodash';
import { JOURNEY_OBJECT_TYPE } from '../../constant';

const MAP_TITLE = {
  labelSave: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
};

export const SaveJTButton = props => {
  const isLoading = useSelector(selectIsLoading);
  const verification = useSelector(selectVerification);

  const { templateObjSettings, objects } = verification;

  const allowSave = some(templateObjSettings, obj => {
    const isJourneyObj = Object.values(JOURNEY_OBJECT_TYPE).includes(obj.type);

    return isJourneyObj && !obj.isExist;
  });

  const isEmptyObjects = isEmpty(objects);

  return (
    <UIButton
      variant="contained"
      theme="primary"
      disabled={allowSave || isLoading || isEmptyObjects}
      {...props}
    >
      {MAP_TITLE.labelSave}
    </UIButton>
  );
};
