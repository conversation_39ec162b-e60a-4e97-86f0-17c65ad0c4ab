import React from 'react';
import { get } from 'lodash';
import PropTypes from 'prop-types';

import PromotionDetail from 'modules/Dashboard/MarketingHub/Promotion/Detail';

import { JOURNEY_OBJECT_TYPE, MODE } from '../../constant';

import { useActiveObj } from '../../hooks/useActiveObj';
import { useSelector } from 'react-redux';
import { selectMode } from '../../selector';
import { Grid } from '@material-ui/core';

export const PromotionSettings = props => {
  const mode = useSelector(selectMode);

  const { activeObj, info, saveInfo } = useActiveObj();

  const isEventAttrObject =
    get(activeObj, 'type') === JOURNEY_OBJECT_TYPE.promotionPool;

  if (!isEventAttrObject) return null;

  const { errors } = props;

  // const errors = [
  //   {
  //     objectType: 'promotionPool',
  //     code: 'poolCode',
  //     message: 'This code has been taken, please use another one',
  //     args: {},
  //   },
  // ];

  const getComponentProperties = () => {
    const properties = {};

    // if (!info.isExist) {
    //   properties.settings = {
    //     ...saveInfo.settings,

    //     // description_multilang: saveInfo.settings.descriptionMultilang,
    //     // pool_name_multilang: saveInfo.settings.poolNameMultilang,
    //     // pool_code: saveInfo.settings.poolCode,
    //   };
    // }

    if (info.isExist && mode === MODE.Use) {
      properties.poolId = info.settings.pool_id;
    }

    if (info.isExist && mode === MODE.Save) {
      properties.poolId = activeObj.poolId;
    }
    return properties;
  };

  // console.log({ info });
  // console.log({ saveInfo });
  // console.log({ mode });
  // console.log({ templateObjSettings: verification.templateObjSettings });

  // console.log(getComponentProperties());

  if (!info.isExist) {
    return (
      <Grid container className="p-all-4">
        <Grid item xs={2}>
          <div>
            Pool name<span style={{ color: 'red' }}> *</span>
          </div>
        </Grid>
        <Grid item xs={10} style={{ color: 'red' }}>
          {saveInfo.settings.pool_name}
        </Grid>
      </Grid>
    );
  }

  return (
    <PromotionDetail isViewMode errors={errors} {...getComponentProperties()} />
  );
};

PromotionSettings.propTypes = {
  errors: PropTypes.array,
};
