/* eslint-disable import/no-cycle */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
import {
  get,
  isEmpty,
  uniqWith,
  set,
  isFunction,
  flatten,
  map,
  some,
  isArray,
} from 'lodash';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
import { NODE_TYPE } from '../../../Content/Nodes/constant';
import { JOURNEY_OBJECT_TYPE, OBJECT_TYPE } from '../constant';
import {
  parseObjectNumeric,
  safeParseArray,
} from '../../../../../../../../utils/web/utils';
import isEqual from 'react-fast-compare';
import {
  concatPathToListObj,
  concatPathToObjPathConfig,
  groupObjectsWithType,
} from './helper';
import { getSegmentFromFilters } from './segment';
import { getEventFromNodeTrigger, getEventFromPeformEvent } from './event';
import {
  getEventAttrsFromFiltersAndNodeTrigger,
  getEventAttrsFromPerfEvent,
} from './eventAttr';
import {
  getDOAttrsFromCampaign,
  getDOAttrsFromCustomInput,
  getDOsFromCampaign,
  getEventAttrsFromCampaign,
  getObjectsFromVariantCDPTemplates,
} from './campaign';
import {
  getDOAttrsFromFilters,
  getDOAttrsFromPerfEvent,
} from './dataObjectAttr';
import { getJourneyGoalFromEventBase } from './journeyGoal';
import {
  bindBOPropertiesToObjects,
  getDOsFromFilters,
  getDOsFromPerfEvent,
} from './dataObject';
import {
  getPoolObjsFromDestination,
  bindPoolIdToPoolObjects,
} from './promotionPool';
import { ERROR_CODE } from './config';
import {
  JourneyObject,
  getCampaignSettings,
  getVariantSettings,
} from './service';
import { SYSTEM_BO } from '../../../../../../../../utils/constants';

const PATH =
  'MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/objectHandler';

export const JOURNEY_OBJECT_META = (() => {
  const objectMeta = {
    [JOURNEY_OBJECT_TYPE.journeyGoal]: {
      label: 'Goal',
      belongsTo: [NODE_TYPE.EVENT_BASED, NODE_TYPE.SCHEDULED],
      get: {
        [NODE_TYPE.EVENT_BASED]: ({ metadata }) => {
          return getJourneyGoalFromEventBase({ metadata });
        },
        [NODE_TYPE.SCHEDULED]: ({ metadata }) => {
          return getJourneyGoalFromEventBase({ metadata });
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.segment]: {
      label: 'Segment',
      belongsTo: [
        NODE_TYPE.SCHEDULED,
        NODE_TYPE.CONDITION_YES,
        NODE_TYPE.FILTER,
        NODE_TYPE.UPDATE_SEGMENT,
      ],
      get: {
        [NODE_TYPE.SCHEDULED]: ({ metadata }) => {
          const audiences = [
            {
              key: 'excludedAudiences',
              value: metadata.audiences.excludedAudiences,
            },
            {
              key: 'includedAudiences',
              value: metadata.audiences.includedAudiences,
            },
          ];

          const { itemTypeId } = metadata.audiences;

          return audiences.reduce((acc, { key, value: { filters } }) => {
            let segments = getSegmentFromFilters(filters, { itemTypeId });

            segments = concatPathToListObj(
              segments,
              `metadata.audiences.${key}`,
            );

            acc.push(...segments);

            return acc;
          }, []);
        },
        [NODE_TYPE.FILTER]: ({ metadata }) => {
          const { filterType, excludedFilters, filters, itemTypeId } = metadata;

          if (filterType !== 'item_segment') return [];

          const audiences = [
            {
              key: 'excludedFilters',
              value: excludedFilters,
            },
            {
              key: 'filters',
              value: filters,
            },
          ];

          return audiences.reduce((acc, { key, value: filtersObj }) => {
            let segments = getSegmentFromFilters(filtersObj, {
              customPathConfig: ({ defaultPathConfig, conditionPath }) => {
                const temp = [...defaultPathConfig];

                set(temp, '1.path', `${conditionPath}.value`);

                return temp;
              },
              filtersPropName: key,
              itemTypeId,
            });

            segments = concatPathToListObj(segments, `metadata`);

            acc.push(...segments);

            return acc;
          }, []);
        },
        [NODE_TYPE.CONDITION_YES]: {
          fn: NODE_TYPE.FILTER,
        },
        [NODE_TYPE.UPDATE_SEGMENT]: ({ metadata }) => {
          const { itemTypeId, segmentIds } = metadata;

          return segmentIds.map((segmentId, idx) => ({
            segmentId,
            itemTypeId,
            pathConfig: [
              {
                path: `metadata.segmentIds.${idx}`,
                value: 'segmentId',
              },
            ],
          }));
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.dataObject]: {
      label: 'Data Object',
      belongsTo: [
        NODE_TYPE.CONDITION_YES,
        NODE_TYPE.FILTER,
        NODE_TYPE.UPDATE_INFO,
        NODE_TYPE.DESTINATION,
        NODE_TYPE.EVENT_BASED,
        NODE_TYPE.WAIT_EVENT,
      ],
      get: {
        [NODE_TYPE.EVENT_BASED]: ({ metadata }) => {
          const dataObjs = [];

          dataObjs.push(
            ...getDOsFromPerfEvent(metadata.event, {
              perfEventPropName: 'event',
            }),
          );

          if (!isEmpty(metadata?.custom_inputs)) {
            dataObjs.push({
              itemTypeId: SYSTEM_BO.Journey.itemTypeId,
              pathConfig: [],
            });
          }

          return [...concatPathToListObj(dataObjs, 'metadata')];
        },

        [NODE_TYPE.CONDITION_YES]: ({ metadata }, workflowSettings) => {
          const event = getEventFromNodeTrigger(workflowSettings);
          const dataObjs = getDOsFromFilters(metadata.filters, {
            event,
          });

          return [...concatPathToListObj(dataObjs, 'metadata')];
        },

        [NODE_TYPE.FILTER]: ({ metadata }, workflowSettings) => {
          const event = getEventFromNodeTrigger(workflowSettings);
          const dataObjs = getDOsFromFilters(metadata.filters, {
            event,
          });

          return [...concatPathToListObj(dataObjs, 'metadata')];
        },

        [NODE_TYPE.UPDATE_INFO]: ({ metadata }) => {
          const { itemTypeId } = metadata;

          return [
            {
              itemTypeId,
              pathConfig: [
                {
                  path: 'metadata.itemTypeId',
                  value: 'itemTypeId',
                },
                {
                  path: 'metadata.itemTypeName',
                  value: 'itemTypeName',
                },
              ],
            },
          ];
        },

        [NODE_TYPE.DESTINATION]: ({ metadata }) => {
          return getDOsFromCampaign(metadata);
        },

        [NODE_TYPE.WAIT_EVENT]: ({ metadata }) => {
          const { waitingEvents = [] } = metadata;

          return waitingEvents.flatMap((perfEvent, idx) => {
            const dataObjs = getDOsFromPerfEvent(perfEvent, {
              perfEventPropName: idx,
            });

            return [...concatPathToListObj(dataObjs, 'metadata.waitingEvents')];
          });
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.dataObjectAttr]: {
      label: 'DO attribute',
      belongsTo: [
        NODE_TYPE.CONDITION_YES,
        NODE_TYPE.FILTER,
        NODE_TYPE.UPDATE_INFO,
        NODE_TYPE.DESTINATION,
        NODE_TYPE.WAIT_EVENT,
        NODE_TYPE.EVENT_BASED,
      ],
      get: {
        [NODE_TYPE.CONDITION_YES]: ({ metadata }, workflowSettings) => {
          const event = getEventFromNodeTrigger(workflowSettings);
          const doAttrs = getDOAttrsFromFilters(metadata.filters, { event });

          return [...concatPathToListObj(doAttrs, 'metadata')];
        },
        [NODE_TYPE.FILTER]: ({ metadata }, workflowSettings) => {
          const event = getEventFromNodeTrigger(workflowSettings);
          const doAttrs = getDOAttrsFromFilters(metadata.filters, { event });

          return [...concatPathToListObj(doAttrs, 'metadata')];
        },
        [NODE_TYPE.DESTINATION]: ({ metadata }, workflowSettings) => {
          const doAttrs = getDOAttrsFromCampaign(metadata, workflowSettings);

          return [...concatPathToListObj(doAttrs, 'metadata')];
        },
        [NODE_TYPE.EVENT_BASED]: ({ metadata }) => {
          const doAttrs = [];

          doAttrs.push(
            ...getDOAttrsFromCustomInput({
              customInputs: metadata.custom_inputs,
              itemTypeId: SYSTEM_BO.Journey.itemTypeId,
            }),

            ...getDOAttrsFromPerfEvent(metadata.event, {
              perfEventPropName: 'event',
            }),
          );

          return [...concatPathToListObj(doAttrs, 'metadata')];
        },
        [NODE_TYPE.WAIT_EVENT]: ({ metadata }) => {
          const { waitingEvents = [] } = metadata;

          return waitingEvents.flatMap((perfEvent, idx) => {
            const doAttrs = getDOAttrsFromPerfEvent(perfEvent, {
              perfEventPropName: idx,
            });

            return concatPathToListObj(doAttrs, 'metadata.waitingEvents');
          });
        },
        [NODE_TYPE.UPDATE_INFO]: ({ metadata }) => {
          const { params, itemTypeId } = metadata;

          return params.map((attrUpdated, idx) => {
            const { propertyName: itemPropertyName } = attrUpdated;

            return {
              itemPropertyName,
              itemTypeId,
              pathConfig: [
                {
                  path: `metadata.params.${idx}.propertyName`,
                  value: 'itemPropertyName',
                },
              ],
            };
          });
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.event]: {
      label: 'Event',
      belongsTo: [NODE_TYPE.EVENT_BASED, NODE_TYPE.WAIT_EVENT],
      get: {
        [NODE_TYPE.EVENT_BASED]: ({ metadata }) => {
          const event = getEventFromPeformEvent(metadata.event, {
            perfEventPropName: 'event',
          });

          return [concatPathToObjPathConfig(event, 'metadata')];
        },
        [NODE_TYPE.WAIT_EVENT]: ({ metadata }) => {
          const { waitingEvents = [] } = metadata;

          return waitingEvents.map((perfEvent, idx) => {
            const event = getEventFromPeformEvent(perfEvent, {
              perfEventPropName: idx,
            });

            return concatPathToObjPathConfig(event, 'metadata.waitingEvents');
          });
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.eventAttribute]: {
      label: 'Event attribute',
      belongsTo: [
        NODE_TYPE.EVENT_BASED,
        NODE_TYPE.FILTER,
        NODE_TYPE.CONDITION_YES,
        NODE_TYPE.WAIT_EVENT,
        NODE_TYPE.DESTINATION,
      ],
      get: {
        [NODE_TYPE.EVENT_BASED]: ({ metadata }) => {
          const eventAttrs = getEventAttrsFromPerfEvent(
            get(metadata, 'event'),
            {
              perfEventPropName: 'event',
            },
          );

          return [...concatPathToListObj(eventAttrs, 'metadata')];
        },
        [NODE_TYPE.CONDITION_YES]: ({ metadata }, workflowSettings) => {
          const { filterType, filters } = metadata;

          if (filterType !== 'event_attribute') return [];

          const eventAttrs = getEventAttrsFromFiltersAndNodeTrigger({
            filters,
            nodeTrigger: workflowSettings,
          });

          return [...concatPathToListObj(eventAttrs, 'metadata')];
        },
        [NODE_TYPE.FILTER]: ({ metadata }, workflowSettings) => {
          if (metadata.filterType !== 'event_attribute') return [];

          const { filters } = metadata;

          const eventAttrs = getEventAttrsFromFiltersAndNodeTrigger({
            filters,
            nodeTrigger: workflowSettings,
          });

          return [...concatPathToListObj(eventAttrs, 'metadata')];
        },
        [NODE_TYPE.WAIT_EVENT]: ({ metadata }) => {
          const { waitingEvents } = metadata;

          return waitingEvents.flatMap((perfEvent, idx) => {
            const eventAttrs = getEventAttrsFromPerfEvent(perfEvent, {
              perfEventPropName: idx,
            });

            return [
              ...concatPathToListObj(eventAttrs, 'metadata.waitingEvents'),
            ];
          });
        },
        [NODE_TYPE.DESTINATION]: ({ metadata }, workflowSettings) => {
          const eventAttrs = getEventAttrsFromCampaign(
            metadata,
            workflowSettings,
          );

          return [...concatPathToListObj(eventAttrs, 'metadata')];
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.campaign]: {
      label: 'Campaign',
      belongsTo: [NODE_TYPE.DESTINATION],
      get: {
        [NODE_TYPE.DESTINATION]: ({ metadata }) => {
          const { destinationId, campaignId, catalogId } = metadata;

          const objects = [
            {
              destinationId,
              campaignId,
              catalogId,
              pathConfig: [
                {
                  path: 'metadata.destinationId',
                  value: 'destinationId',
                },
                {
                  path: 'metadata.catalogId',
                  value: 'catalogId',
                },
                {
                  path: 'metadata.channelId',
                  value: 'channelId',
                },
              ],
            },
          ];

          return objects;
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.promotionPool]: {
      label: 'Promotion pool',
      belongsTo: [NODE_TYPE.DESTINATION],
      get: {
        [NODE_TYPE.DESTINATION]: ({ metadata }) => {
          return getPoolObjsFromDestination({ metadata });
        },
      },
    },
  };

  Object.entries(objectMeta).forEach(([objectType, meta]) => {
    Object.entries(meta.get).forEach(([nodeType, getMethod]) => {
      if (!isFunction(getMethod) && getMethod.fn) {
        const replacedMethod = objectMeta[objectType].get[getMethod.fn];

        objectMeta[objectType].get[nodeType] = replacedMethod;
      }
    });
  });

  return Object.freeze(objectMeta);
})();

export const getObjectsFromBranch = (branch, workflowSettings) => {
  let result = [];

  const { actionType, metadata } = branch;

  try {
    Object.entries(JOURNEY_OBJECT_META).forEach(([objectType, meta]) => {
      const isBelongsTo = meta.belongsTo.includes(actionType);
      const getMethod = meta.get[actionType];

      if (isBelongsTo && isFunction(getMethod)) {
        const objects = safeParseArray(getMethod(branch, workflowSettings));

        result.push(...objects.map(obj => ({ ...obj, type: objectType })));
      }
    });

    // Retrieve objects from data in other projects (media template, email template, etc...)
    if (actionType === NODE_TYPE.DESTINATION) {
      const { variants = [] } = metadata;

      result.push(
        ...getObjectsFromVariantCDPTemplates({
          variants,
          variantsLabel: `metadata.variants`,
        }),
      );
    }
  } catch (error) {
    console.log(error);

    addMessageToQueue({
      path: PATH,
      func: 'getObjectFromBranch',
      data: error.stack,
      branch,
    });
  }

  result = result.map(obj => ({
    pathConfig: [],
    actionId: branch.actionId,
    actionType: branch.actionType,
    ...obj,
  }));

  // console.log(`object by node ${actionType}`, {
  //   branch,
  //   objects: result,
  // });

  return uniqWith(result, isEqual);
};

export const serializeJourneyTemplate = async data => {
  const { workflow_setting: workflowSetting, onError, onSucecss } = data;

  let serializedData = {
    objects: [],
    objectSettings: [],
    workflowSetting,
  };

  try {
    await bindCampaignAndVariantsToWorkflowSetting({
      workflowSetting,
    });

    const { objects } = await serializeWorkflowSetting({
      workflowSetting,
    });

    // console.log(objects);

    const mainObjects = objects.filter(obj => {
      return Object.values(JOURNEY_OBJECT_TYPE).includes(obj.type);
    });

    const objectsInfo = await JourneyObject.getJourneyObjectsDetail({
      objects: mainObjects,
    });

    if (isFunction(onSucecss)) {
      onSucecss();
    }

    serializedData = {
      ...serializedData,

      objects: objectsInfo.objects,
      objectSettings: objectsInfo.objectSettings,
    };
  } catch (error) {
    if (isFunction(onError)) {
      onError(error);
    }

    console.log(error);

    addMessageToQueue({
      path: PATH,
      func: serializeJourneyTemplate.name,
      data: error.stack,
    });

    return false;
  }

  return serializedData;
};

function loopBranchs(branchs, cb) {
  (isArray(branchs) ? branchs : [branchs]).forEach(branch => {
    const { actionType } = branch;

    cb(branch);

    if (actionType === NODE_TYPE.END) return;

    if (Array.isArray(branch.branchs)) {
      loopBranchs(branch.branchs, cb);
    }
  });
}

export const bindCampaignAndVariantsToWorkflowSetting = async ({
  workflowSetting,
}) => {
  const variantInfos = [];
  const campaignInfos = [];

  loopBranchs(workflowSetting, branch => {
    const { actionType, actionId } = branch;

    if (actionType === NODE_TYPE.DESTINATION) {
      const {
        variants,
        variantIds = [],
        campaignId = null,
        campaign,
      } = branch.metadata;

      if (isEmpty(variants)) {
        variantIds.forEach(variantId => {
          variantInfos.push({ actionId, variantId });
        });
      }

      if (isEmpty(campaign) && campaignId) {
        campaignInfos.push({ actionId, campaignId });
      }
    }
  });

  const [campaigns, variants] = await Promise.all([
    getCampaignSettings({
      campaignIds: map(campaignInfos, i => i.campaignId),
    }),

    getVariantSettings({
      variantIds: map(variantInfos, i => i.variantId),
    }),
  ]);

  loopBranchs(workflowSetting, branch => {
    const { metadata } = branch;

    if (branch.actionType !== NODE_TYPE.DESTINATION) return;

    variantInfos.forEach(({ actionId, variantId }) => {
      if (actionId === branch.actionId) {
        const variant = variants.find(i => i.variant_id === variantId);

        if (!variant) return;

        if (isEmpty(metadata.variants)) {
          metadata.variants = [];
        }

        metadata.variants.push(
          parseObjectNumeric(variant, {
            keyFormat: 'camel',
            ignoreKeys: ['custom_inputs'],
          }),
        );
      }
    });

    campaignInfos.forEach(({ actionId, campaignId }) => {
      if (actionId === branch.actionId) {
        const campaign = campaigns.find(i => i.campaign_id === campaignId);

        if (!campaign) return;

        metadata.campaign = parseObjectNumeric(campaign, {
          keyFormat: 'camel',
          ignoreKeys: ['custom_inputs'],
        });
      }
    });
  });

  // console.log(variants, campaigns);
  // console.log(variantInfos, campaignInfos);
  // console.log(workflowSetting);
};

export const serializeWorkflowSetting = async ({ workflowSetting }) => {
  // console.log('before serializeObjectsFromWorkflowSettings', workflowSetting);

  const objects = Object.fromEntries(
    Object.values(OBJECT_TYPE).map(objectType => [objectType, []]),
  );

  if (isEmpty(workflowSetting)) {
    return objects;
  }

  loopBranchs(workflowSetting, branch => {
    const { actionType } = branch;

    if (actionType !== NODE_TYPE.END) {
      const branchObjects = getObjectsFromBranch(branch, workflowSetting);

      branchObjects.forEach(object => {
        objects[object.type].push(object);
      });
    }
  });

  const allObjects = flatten(Object.values(objects));

  const eachGroupObject = Object.entries(objects).map(
    async ([objectType, objsByType]) => {
      let temp = [...objsByType];

      switch (objectType) {
        case JOURNEY_OBJECT_TYPE.dataObject:
        case JOURNEY_OBJECT_TYPE.dataObjectAttr:
        case JOURNEY_OBJECT_TYPE.eventAttribute: {
          temp = await bindBOPropertiesToObjects(objsByType);
          break;
        }
        case JOURNEY_OBJECT_TYPE.promotionPool: {
          temp = await bindPoolIdToPoolObjects(objsByType);
          break;
        }
        default:
          break;
      }

      temp = groupObjectsWithType({
        objects: temp,
        allObjects,
        objectType,
        customPathConfigProcesser: ({ processedPathConfig, mappedObject }) => {
          const { actionId, actionType } = mappedObject;

          return processedPathConfig.map(pathInfo => ({
            ...pathInfo,
            actionId,
            actionType,
          }));
        },
      });

      return temp;
    },
  );

  const objectGroups = await Promise.all(eachGroupObject);

  // console.log(objectGroups);

  return { objects: flatten(objectGroups) };
};

export const composeObjectErrors = ({ object }) => {
  const { objects: subObjects, verify } = object;

  const errors = [];

  const isMissingField = some(subObjects, obj => !get(obj, 'verify.isExist'));

  const isNonePermission =
    get(verify, 'errorMessage') === ERROR_CODE.NONE_PERMISSION;

  const isWrongDataType =
    get(verify, 'errorMessage') === ERROR_CODE.WRONG_DATA_TYPE;

  const isCatalogNotValid =
    get(verify, 'errorMessage') === ERROR_CODE.CATALOG_NOT_VALID;

  if (!get(verify, 'isExist') && isMissingField) {
    errors.push({ code: ERROR_CODE.MISSING_FIELD });
  }

  if (isNonePermission) {
    errors.push({ code: ERROR_CODE.NONE_PERMISSION });
  }

  if (isWrongDataType) {
    errors.push({ code: ERROR_CODE.WRONG_DATA_TYPE });
  }

  if (isCatalogNotValid) {
    errors.push({ code: ERROR_CODE.CATALOG_NOT_VALID });
  }

  return [...errors, ...safeParseArray(object.errors)];
};
