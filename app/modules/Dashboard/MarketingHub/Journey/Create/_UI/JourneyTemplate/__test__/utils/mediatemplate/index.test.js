/* eslint-disable no-console */
import HTML_DYN_CONTENT_CASES from './__mocks__/html_dyn_content.mock.json';
import TEMPLATE_SETTINGS from './__mocks__/template_settings.mock.json';
import TEMPLATE_DATA from './__mocks__/template_data.mock.json';
import {
  getObjectsFromHTMLContent,
  getObjectsFromTemplateData,
  getObjectsFromTemplateSettings,
} from '../../../utils/mediatemplate';
import { orderBy } from 'lodash';

const mapFn = item => {
  const temp = { ...item };

  temp.pathConfig = orderBy(item.pathConfig, ['path']);

  return temp;
};

describe('Get objects from template', () => {
  test(`function ${getObjectsFromTemplateData.name}. Check health!`, () => {
    console.time(`function ${getObjectsFromTemplateData.name}`);

    const received = getObjectsFromTemplateData({
      data: TEMPLATE_DATA,
    }).map(mapFn);

    // console.log(JSON.stringify(received));

    expect(received.length).toEqual(152);

    console.timeEnd(`function ${getObjectsFromTemplateData.name}`);
  });

  test(`function ${getObjectsFromTemplateSettings.name}`, () => {
    const received = getObjectsFromTemplateSettings({
      templateSettings: TEMPLATE_SETTINGS.settings,
    }).map(mapFn);

    const expected = TEMPLATE_SETTINGS.objects.map(mapFn);

    // console.log(JSON.stringify(received));

    expect(received).toHaveLength(expected.length);
    expect(received).toEqual(expect.arrayContaining(expected));
  });

  test.each([...HTML_DYN_CONTENT_CASES])(
    'function getObjectsFromHTMLContent. Case %s',

    (_title, args, expected) => {
      const received = getObjectsFromHTMLContent({
        ...args,
        html: args.html,
        journeySettings: args.journeySettings,
      }).map(mapFn);

      const objects = expected.objects.map(mapFn);

      expect(received).toHaveLength(objects.length);
      expect(received).toEqual(expect.arrayContaining(objects));
    },
  );
});
