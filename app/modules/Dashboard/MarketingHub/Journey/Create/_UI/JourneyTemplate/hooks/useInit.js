/* eslint-disable consistent-return */
import { useDispatch, useSelector } from 'react-redux';
import { init, reset } from '../actions';
import { selectInitialized } from '../selector';
import { useDeepCompareEffect } from '../../../../../../../../hooks';

export const useInit = args => {
  const { data, mode } = args;

  const initialized = useSelector(selectInitialized);

  const dispatch = useDispatch();

  useDeepCompareEffect(() => {
    if (!data || initialized) return;

    dispatch(
      init({
        data,
        mode,
      }),
    );

    return () => {
      dispatch(reset());
    };
  }, [data, mode, initialized]);
};
