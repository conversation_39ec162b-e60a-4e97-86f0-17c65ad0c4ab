/* eslint-disable import/no-cycle */
import { every, findIndex, get } from 'lodash';
import { createSelector } from 'reselect';
import { INITIAL_STATE, SLICE_KEY } from './constant';
import {
  findObject,
  isDisabledObject,
  isEqualObject,
  orderObjects,
} from './utils/helper';

export const selectSlice = state => state.get(SLICE_KEY) || INITIAL_STATE;

export const selectVerification = createSelector(
  selectSlice,
  slice => slice.verification,
);

export const selectMode = createSelector(
  selectSlice,
  slice => slice.mode,
);

export const selectIsLoading = createSelector(
  selectSlice,
  slice => slice.isLoading,
);

export const selectIsApplying = createSelector(
  selectSlice,
  slice => slice.isApplying,
);

export const selectJourney = createSelector(
  selectSlice,
  slice => slice.journey,
);

export const selectModuleConfig = createSelector(
  selectSlice,
  slice => slice.moduleConfig,
);

export const selectIsSaving = createSelector(
  selectSlice,
  slice => slice.isSaving,
);

export const selectInitialized = createSelector(
  selectSlice,
  slice => slice.initialized,
);

export const selectTriggerOut = createSelector(
  selectSlice,
  slice => slice.triggerOut,
);

export const selectActiveObject = createSelector(
  selectVerification,
  verification => verification.activeObj,
);

export const selectObjects = createSelector(
  selectVerification,
  verification => verification.objects,
);

export const selectObject = baseObj =>
  createSelector(
    selectObjects,
    objects => objects.find(obj => isEqualObject(obj, baseObj)),
  );

export const selectActiveObjDetail = createSelector(
  selectActiveObject,
  selectObjects,
  (activeObj, objects) => findObject(activeObj, objects),
);

export const selectAllowApplyJT = createSelector(
  selectObjects,
  objects => every(objects, obj => get(obj, 'verify.isExist')),
);

export const selectApplyState = createSelector(
  selectSlice,
  slice => slice.applyState,
);

export const selectJourneyTemplate = createSelector(
  selectSlice,
  slice => slice.journeyTemplate,
);

export const selectOrderedObjects = createSelector(
  selectActiveObject,
  selectObjects,
  (activeObj, objects) => {
    const orderedObjects = orderObjects(objects);

    const activeObjIdx = findIndex(orderedObjects, obj =>
      isEqualObject(obj, activeObj),
    );

    return { orderedObjects, activeObjIdx };
  },
);

export const selectAllowDirection = getNextIndex => {
  return createSelector(
    selectMode,
    selectVerification,
    selectOrderedObjects,
    (mode, verification, orderedObject) => {
      const { templateObjSettings } = verification;
      const { orderedObjects, activeObjIdx } = orderedObject;

      const nextIdx = getNextIndex(activeObjIdx, orderedObjects.length);
      let allow = false;
      let idx = -1;

      if (nextIdx !== -1) {
        let currentIndex = nextIdx;
        while (currentIndex >= 0 && currentIndex < orderedObjects.length) {
          const disabled = isDisabledObject({
            mode,
            object: orderedObjects[currentIndex],
            listObject: orderedObjects,
            templateObjSettings,
          });

          if (!disabled) {
            allow = true;
            idx = currentIndex;
            break;
          }
          currentIndex = getNextIndex(currentIndex, orderedObjects.length);
        }
      }

      return {
        allow,
        idx,
      };
    },
  );
};
