/* eslint-disable react/prop-types */
import React from 'react';
import DoneIcon from '@material-ui/icons/Done';
import classNames from 'classnames';
import { VERIFY_STATUS } from '../../constant';
import { VerifyStatusIconRoot } from './styled';

export const VerifyStatusIcon = props => {
  const { status, disabled } = props;

  return (
    <VerifyStatusIconRoot
      className={classNames({
        disabled,
      })}
    >
      {status === VERIFY_STATUS.Done && <DoneIcon />}
    </VerifyStatusIconRoot>
  );
};
