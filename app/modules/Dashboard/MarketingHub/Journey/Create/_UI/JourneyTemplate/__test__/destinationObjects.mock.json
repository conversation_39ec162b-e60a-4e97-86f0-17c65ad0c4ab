[{"catalogId": 1263473, "channelId": 15, "campaignId": 5690259, "variantIds": [5690260], "destinationId": 2284608, "campaignInfo": {"campaign_id": 5690259, "campaign_name": "Antsomi Line Campaign", "status": 1, "campaign_setting": {"random": {"5690260": 1}, "algoMethod": "random", "deliveryTimeConfig": {"mode": "delay", "type": "all_time", "range": []}}, "custom_inputs": {"label_01": "<EMAIL>"}, "campaign_name_multilang": {"EN": "Antsomi Line Campaign", "DEFAULT_LANG": "EN"}, "story_name_multilang": {}, "story_name": null}, "variantInfo": [{"variant_id": 5690260, "variant_name": "Variant 1", "content_setting": {"destinationInput": {"line": {"data": [{"text": "", "sticker": "", "carousel": {"static": {"slideList": [{"slideId": "o6jt32"}], "slideMaps": {"o6jt32": {"title": "#{visitor.last_updated||\"\"} ddfasdf", "actions": {"extraData": {"messageText": "#{visitor.last_updated||\"\"}"}, "buttonList": ["c28pwe"], "buttonMaps": {"c28pwe": {"url": "#{shortlink(https://apim.com)}", "URIType": {"label": "Redirect to an URL", "value": "redirect_url"}, "btnLabel": "Button 1", "actionType": {"label": "URI action", "value": "uri_action"}}}, "defaultAction": {"label": "Message action", "value": "message_action"}}, "content": "#{visitor.last_updated||\"\"}", "imageURL": "https://sandbox-st-media-template.antsomi.com/upload/2024/03/14/b6fea842-84a8-4432-a262-08e8e2947f9e.jpg"}}, "slideActive": "o6jt32"}, "contentType": "static", "recommendation": {}}, "imageUrl": "", "template": "line_carousel", "imagemapUrl": "", "previewText": "123 test", "stickerType": "", "confirmation": "", "imagemapType": "", "imageCarousel": "", "buttonTemplate": "", "imagemapMessage": "", "previewImageUrl": "", "imagemapTemplate": "", "previewTextImage": "", "previewTextButton": "", "previewTextConfirm": "", "previewTextImagemap": "", "imagemapLandingPageUrl": ""}], "type": "single"}}, "variantExtraData": {}, "objectWidgetInput": {}}, "status": 1, "custom_inputs": {}, "variant_name_multilang": {"EN": "Variant 1", "DEFAULT_LANG": "EN"}, "story_name_multilang": {}, "story_name": null}]}, {"catalogId": 496480, "channelId": 9, "campaignId": 5690255, "variantIds": [5690256], "destinationId": 496489, "campaignInfo": {"campaign_id": 5690255, "campaign_name": "Campaign 431", "status": 1, "campaign_setting": {"random": {"5690256": 1}, "algoMethod": "random", "deliveryTimeConfig": {"mode": "delay", "type": "all_time", "range": []}}, "custom_inputs": {"label_01": "<EMAIL>"}, "campaign_name_multilang": {"EN": "Campaign 431", "DEFAULT_LANG": "EN"}, "story_name_multilang": {}, "story_name": null}, "variantInfo": [{"variant_id": 5690256, "variant_name": "Variant 1", "content_setting": {"destinationInput": {"text": "#{visitor.last_updated||\"\"}", "imageUrl": "https://sandbox-st-media-template.antsomi.com/upload/2024/03/14/b6fea842-84a8-4432-a262-08e8e2947f9e.jpg", "landingPageUrl": "#{shortlink(https://go.dev/blog/slices-intros)}"}, "variantExtraData": {"customFunction": {}, "formatAttributes": {"visitor.last_updated": {"attribute": {"dataType": "datetime", "settingsFe": {"format": {"type": "DATE_AND_TIME", "config": {}}, "dataType": "datetime"}, "datetimeFormatSettings": {"type": "date_and_time", "hasDateFormat": true, "hasTimeFormat": true, "dateParseFormat": "DD/MM/YYYY", "dateParseOption": "long", "timeParseFormat": "24", "timeParseOption": "medium", "dateFormatString": "DD MMMM YYYY H:mm:ss"}}}}}, "objectWidgetInput": {}}, "status": 1, "custom_inputs": {}, "variant_name_multilang": {"EN": "Variant 1", "DEFAULT_LANG": "EN"}, "story_name_multilang": {}, "story_name": null}]}, {"catalogId": 1266539, "channelId": 6, "campaignId": 5690161, "variantIds": [5690164], "destinationId": 1270245, "campaignInfo": {"campaign_id": 5690161, "campaign_name": "Campaign 5", "status": 1, "campaign_setting": {"random": {"5690164": 1}, "algoMethod": "random", "deliveryTimeConfig": {"mode": "delay", "type": "all_time", "range": []}}, "custom_inputs": {"label_01": "<EMAIL>"}, "campaign_name_multilang": {"EN": "Campaign 5", "DEFAULT_LANG": "EN"}, "story_name_multilang": {}, "story_name": null}, "variantInfo": [{"variant_id": 5690164, "variant_name": "Variant 1", "content_setting": {"destinationInput": {"visitorId": "#{customer.customer_id||\"\"}#{event.article.id||\"\"} Khang test", "customerId": "#{customer.name||\"\"}  Add anothor", "activityName": "fasdf", "activityParams": {"234": "#{visitor.last_updated||\"\"}"}, "activitySource": ""}, "variantExtraData": {"customFunction": {}, "formatAttributes": {"visitor.last_updated": {"attribute": {"dataType": "datetime", "settingsFe": {"format": {"type": "DATE_AND_TIME", "config": {}}, "dataType": "datetime"}, "datetimeFormatSettings": {"type": "date_and_time", "hasDateFormat": true, "hasTimeFormat": true, "dateParseFormat": "DD/MM/YYYY", "dateParseOption": "long", "timeParseFormat": "24", "timeParseOption": "medium", "dateFormatString": "DD MMMM YYYY H:mm:ss"}}}}}, "objectWidgetInput": {}}, "status": 1, "custom_inputs": {}, "variant_name_multilang": {"EN": "Variant 1", "DEFAULT_LANG": "EN"}, "story_name_multilang": {}, "story_name": null}]}, {"catalogId": 334877, "channelId": 7, "campaignId": 5690160, "variantIds": [5690163], "destinationId": 1231726, "campaignInfo": {"campaign_id": 5690160, "campaign_name": "Campaign 3", "status": 1, "campaign_setting": {"random": {"5690163": 1}, "algoMethod": "random", "deliveryTimeConfig": {"mode": "delay", "type": "all_time", "range": []}}, "custom_inputs": {"label_01": "<EMAIL>"}, "campaign_name_multilang": {"EN": "Campaign 3", "DEFAULT_LANG": "EN"}, "story_name_multilang": {}, "story_name": null}, "variantInfo": [{"variant_id": 5690163, "variant_name": "Variant 1", "content_setting": {"destinationInput": {"text": "Khang #{promotion_code.pool_d_20.id} dasd"}, "variantExtraData": {"customFunction": {}}, "objectWidgetInput": {}}, "status": 1, "custom_inputs": {}, "variant_name_multilang": {"EN": "Variant 1", "DEFAULT_LANG": "EN"}, "story_name_multilang": {}, "story_name": null}]}, {"catalogId": 510480, "channelId": 7, "campaignId": 5690159, "variantIds": [5690162], "destinationId": 510514, "campaignInfo": {"campaign_id": 5690159, "campaign_name": "Campaign 1", "status": 1, "campaign_setting": {"random": {"5690162": 1}, "algoMethod": "random", "deliveryTimeConfig": {"mode": "delay", "type": "all_time", "range": []}}, "custom_inputs": {"label_01": "<EMAIL>"}, "campaign_name_multilang": {"EN": "Campaign 1", "DEFAULT_LANG": "EN"}, "story_name_multilang": {}, "story_name": null}, "variantInfo": [{"variant_id": 5690162, "variant_name": "Variant 1", "content_setting": {"destinationInput": {"text": "#{event.ad_zone_id||\"1234\"}  content #{visitor.last_updated||\"\"}  fasdfa #{shortlink(https://go.dev/blog/slices-intro)} fasdf #{event.article.image_url||\"\"}\ncustomer #{customer.telephone||\"\"}"}, "variantExtraData": {"customFunction": {}, "formatAttributes": {"visitor.last_updated": {"attribute": {"dataType": "datetime", "settingsFe": {"format": {"type": "DATE_AND_TIME", "config": {}}, "dataType": "datetime"}, "datetimeFormatSettings": {"type": "date_and_time", "hasDateFormat": true, "hasTimeFormat": true, "dateParseFormat": "DD/MM/YYYY", "dateParseOption": "long", "timeParseFormat": "24", "timeParseOption": "medium", "dateFormatString": "DD MMMM YYYY H:mm:ss"}}}}}, "objectWidgetInput": {}}, "status": 1, "custom_inputs": {}, "variant_name_multilang": {"EN": "Variant 1", "DEFAULT_LANG": "EN"}, "story_name_multilang": {}, "story_name": null}]}]