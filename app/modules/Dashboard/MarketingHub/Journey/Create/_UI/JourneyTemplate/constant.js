export const SLICE_KEY = 'mark-hub-journey-template';

export const ACTION_TYPES = {
  init: `${SLICE_KEY}@@init`,
  initDone: `${SLICE_KEY}@@initDone`,
  reset: `${SLICE_KEY}@@reset`,
  changeActiveStep: `${SLICE_KEY}@@changeActiveStep`,
  changeActiveObj: `${SLICE_KEY}@@changeActiveObj`,
  updateActiveObj: `${SLICE_KEY}@@updateActiveObj`,
  updateObj: `${SLICE_KEY}@@updateObj`,
  updateVerification: `${SLICE_KEY}@@updateVerification`,
  changeIsLoading: `${SLICE_KEY}@@changeIsLoading`,
  changeIsSaving: `${SLICE_KEY}@@changeIsSaving`,
  changeIsCreating: `${SLICE_KEY}@@changeIsCreating`,
  changeIsApplying: `${SLICE_KEY}@@changeIsApplying`,
  changeIsLoadingExistObj: `${SLICE_KEY}@@changeIsLoadingExistObj`,
  replaceWithExisting: `${SLICE_KEY}@@replaceWithExisting`,
  createObject: `${SLICE_KEY}@@createObject`,
  applyJT: `${SLICE_KEY}@@applyJourneyTemplate`,
  updateApplyState: `${SLICE_KEY}@@updateApplyState`,
  nextObject: `${SLICE_KEY}@@nextObject`,
  prevObject: `${SLICE_KEY}@@prevObject`,
  updateJourney: `${SLICE_KEY}@@updateJourney`,
};

export const MODE = Object.freeze({
  Save: 'save',
  Use: 'use',
});

export const VERIFY_STATUS = {
  Done: 'done',
  None: 'None',
};

export const JOURNEY_OBJECT_TYPE = Object.freeze({
  segment: 'segment',
  dataObject: 'business_object',
  dataObjectAttr: 'bo_attribute',
  event: 'event',
  eventAttribute: 'event_attribute',
  campaign: 'destination',
  promotionPool: 'promotion_pool',
  journeyGoal: 'conversion_event',
});

export const OBJECT_TYPE = Object.freeze({
  ...JOURNEY_OBJECT_TYPE,
  objectView: 'object_view',
  predictiveModel: 'predictive_model',
  variant: 'variant',
  journeyCampaign: 'campaign',
});

export const INITIAL_STATE = {
  applyState: {
    isApply: false,
    data: null,
  },

  initialized: false,
  mode: MODE.Save,
  activeStep: 0,
  verification: {
    activeObj: null,
    objects: [],
    templateObjSettings: [],
  },
  isLoading: false,
  isSaving: false,
  isApplying: false,

  // journey settings when save as template (Mode: Save)
  journey: null,

  // journey template detail (Mode: Use)
  journeyTemplate: null,

  moduleConfig: {},
};

export const JOURNEY_TEMPLATE_ID = 'journey-template-id';
