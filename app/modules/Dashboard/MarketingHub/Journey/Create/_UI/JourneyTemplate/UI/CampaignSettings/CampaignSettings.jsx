import React, { useMemo } from 'react';
import { JOURNEY_OBJECT_TYPE } from '../../constant';
import { get } from 'lodash';
import DestinationPreview from '../../../../../../../../../containers/UIPreview/DestinationPreview';
import { useActiveObj } from '../../hooks/useActiveObj';
import { useGetObjects } from '../../hooks/useGetObjects';
import { useSelector } from 'react-redux';
import {
  selectJourney,
  selectMode,
  selectModuleConfig,
  selectVerification,
} from '../../selector';

export const CampaignSettings = () => {
  const mode = useSelector(selectMode);
  const journey = useSelector(selectJourney);
  const verification = useSelector(selectVerification);
  const moduleConfig = useSelector(selectModuleConfig);

  const { activeObj, info, saveInfo } = useActiveObj();
  const { allObjects } = useGetObjects();
  const { destinationId, actionId, catalogId } = activeObj;

  const isBlastCampaign = Boolean(
    get(verification, 'workflowSettings.is_blast_campaign'),
  );

  const isEventAttrObject =
    get(activeObj, 'type') === JOURNEY_OBJECT_TYPE.campaign;

  const workflowSettings = get(journey, 'workflow_setting', {});

  const destinationIdPreview = useMemo(() => {
    let id = destinationId;
    if (mode === 'use' && info.isExist) {
      id = info.settings.destinationId;
    }
    return id;
  }, [info]);

  if (!isEventAttrObject) return null;

  return (
    <DestinationPreview
      mode={mode}
      destinationId={destinationIdPreview}
      catalogId={catalogId}
      isBlastCampaign={isBlastCampaign}
      actionId={actionId}
      settings={info.settings || saveInfo.settings}
      moduleConfig={moduleConfig}
      workflowSettings={workflowSettings}
      templateObjSettings={allObjects}
      isExistDestination={info.isExist}
    />
  );
};
