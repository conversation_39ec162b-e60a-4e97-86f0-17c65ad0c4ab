/* eslint-disable no-param-reassign */
import { orderBy } from 'lodash';
import {
  getObjectsFromSegment,
  segmentToCreateSegmentData,
} from '../../../utils/segment';
import MOCK from './segment.mock.json';

describe('Segment Utils', () => {
  describe('function getObjectsFromSegment', () => {
    test.each(MOCK.getObjectsFromSegment)(
      'Case: %s',
      (_description, segment, expected) => {
        let received = getObjectsFromSegment(segment);

        expected = orderBy(expected, obj => JSON.stringify(obj));
        received = orderBy(received, obj => JSON.stringify(obj));

        expect(received).toHaveLength(expected.length);
        expect(received).toEqual(expect.arrayContaining(expected));
      },
    );
  });

  describe('function segmentToCreateSegmentData', () => {
    test.each(MOCK.segmentToCreateSegmentData)(
      'Case %s',
      (_description, args) => {
        const { createData, detailData } = args;

        const received = segmentToCreateSegmentData(detailData);

        expect(createData).toEqual(received);
      },
    );
  });
});
