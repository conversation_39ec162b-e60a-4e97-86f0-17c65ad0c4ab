/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import { VerifyMessageRoot } from './styled';
import { useActiveObj } from '../../hooks/useActiveObj';
import IconXlabColor from 'components/common/UIIconXlabColor';
import ErrorOutlineIcon from '@material-ui/icons/ErrorOutline';
import classNames from 'classnames';
import { JOURNEY_OBJECT_META } from '../../utils';
import { first, lowerCase } from 'lodash';
import { composeObjectErrors } from '../../utils/journeyObjects';
import { ERROR_CODE } from '../../utils/config';
import { ContactButton } from '../ContactButton';
import { JOURNEY_OBJECT_TYPE, MODE } from '../../constant';
import { useSelector } from 'react-redux';
import { selectVerification } from '../../selector';
import { composeObjectInfo } from '../../utils/helper';
import { EditJourneyButton } from '../EditJourneyButton';

export const VerifyMesssage = props => {
  const { children, mode, onCancel } = props;

  const { activeObj, info } = useActiveObj();

  const { templateObjSettings, objects } = useSelector(selectVerification);

  const [infoSerialize, setInfoSerialize] = useState(null);

  useEffect(() => {
    let tempInfoSerialize = serializeMsgInfo({
      object: { ...activeObj, allowChange: info.allowChange },
      info,
      mode,
    });

    if (mode === MODE.Save) {
      let numOfError = 0;

      objects.forEach(obj => {
        const objectInfo = composeObjectInfo({
          object: obj,
          objects,
          templateObjSettings,
          mode,
        });

        // Temporarily, count objects with warning message as the number of errors.
        if (objectInfo.info?.warningMsg) {
          numOfError += 1;
        }
      });

      if (numOfError > 0) {
        tempInfoSerialize = {
          returnToEditJourney: true,
          type: 'warning',
          content: `${numOfError} item(s) in journey deleted or access restricted. Please adjust the journey before creating a new tactic.`,
        };
      }
    }

    setInfoSerialize(tempInfoSerialize);
  }, [activeObj, info, mode, objects]);

  const renderChildren = () => {
    if (!infoSerialize || !children) return null;

    if (infoSerialize && infoSerialize.returnToEditJourney) {
      return <EditJourneyButton onCancel={onCancel} />;
    }

    return children;
  };

  if (!infoSerialize) return null;

  return (
    <VerifyMessageRoot
      className={classNames(infoSerialize.type)}
      id="verify-message"
    >
      {infoSerialize.type === 'info' && <ErrorOutlineIcon />}

      {infoSerialize.type === 'warning' && (
        <IconXlabColor name="warning" fontSize="14px" />
      )}

      <div className="message">{infoSerialize.content}</div>

      {renderChildren()}
    </VerifyMessageRoot>
  );
};

// prettier-ignore
const serializeMsgInfo = (args) => {
  const {  info } = args;

  const object = {...args.object};

  if (!object || !object.verify) return null;

  const { type, verify } = object;


  const { isExist } = verify;

  const objectMeta = JOURNEY_OBJECT_META[type];

  if (!objectMeta) return null;

  let readyToUse = true;
  let showStatusReadyToUse = true;
  let withContact = false;
  let contactMsg = null;
  let withPlease = false;
  const message = {
    type: 'info',
    content: '',
  };

  let label = lowerCase(objectMeta.label)

  switch (object.type) {
    case JOURNEY_OBJECT_TYPE.dataObjectAttr: {
      label = 'DO attribute';
      break;
    }
    case JOURNEY_OBJECT_TYPE.campaign: {
      label = 'destination';
      break;
    }

    default:
      break;
  }

  let chooseReplacementMsg = <>Choose another available {label} as replacements.</>;

  if (isExist) {
    message.type = 'info';
    message.content = `This ${label} is ready for use.`;

    if (object.allowChange) {
      message.content = <>{message.content} Choose another available {label} if desired.</>;
    }
  }

  if (!isExist) {
    message.type = 'info';
    message.content = `${object.type=== JOURNEY_OBJECT_TYPE.campaign ? 'Destination' : objectMeta.label} not available.`;

    const error = first(composeObjectErrors({object}));

    if(error) {
      message.type = 'warning';
      message.content = '';

      switch(error.code) {
        case ERROR_CODE.MISSING_FIELD: {
          message.content = <>Mapping unsuccessful due to missing highlighted items.</>

          if (type === JOURNEY_OBJECT_TYPE.segment || type === JOURNEY_OBJECT_TYPE.dataObjectAttr || type === JOURNEY_OBJECT_TYPE.journeyGoal) {
            withPlease=true
          }

          break;
        }
        case ERROR_CODE.NONE_PERMISSION: {
          message.content = <>You do not have permission for this {label}.</>

          if (type === JOURNEY_OBJECT_TYPE.segment) {
            withPlease=true
          }

          break;
        }
        case ERROR_CODE.BO_CODE_EXIST:
        case ERROR_CODE.BO_ATTR_CODE_EXIST:
        case ERROR_CODE.CONVERSION_CODE_EXIST: {
          showStatusReadyToUse = false;
          withContact = true;

          message.content = <>Failed to create {label}.</>
          break;
        }

        case ERROR_CODE.NOT_AVAILABLE_OR_LINKED: {
          message.content = <>Predictive model not available or not linked to segment.</>

          if (type === JOURNEY_OBJECT_TYPE.segment) {
            withPlease=true
          }

          break;
        }

        case ERROR_CODE.WRONG_DATA_TYPE: {
          message.content = <>Existing attribute has different data type.</>
          withContact = true;
          showStatusReadyToUse= false;
          break;
        }

        case ERROR_CODE.CATALOG_NOT_VALID: {
          message.content = <>Catalog not available.</>
          withContact = true;
          showStatusReadyToUse= false;
          break;
        }

        default:
          break;
      }
    }

    if (withPlease) {
      chooseReplacementMsg = <>Please choose another available {label} as replacements.</>;
      message.content = <>{message.content} {contactMsg}</>;
    }

    if (withContact) {
      contactMsg = <><ContactButton/> customer support for help.</>

      if (object.allowChange) {
        chooseReplacementMsg = <>Choose another available {label} as replacements or <ContactButton isLowerCase /> customer support for help. </>
      } else {
        message.content = <>{message.content} {contactMsg}</>;
      }
    }

    if(info.allowCreate) {
      message.content = <>{message.content} Create it now.</>
      chooseReplacementMsg = <>Choose another available {label} as replacements.</>
    }

    if (object.allowChange) {
      message.content = <>{message.content} {chooseReplacementMsg}</>;
    }

    if (!info.allowCreate && !object.allowChange) {
      readyToUse = false;
    }
  }

  if (!readyToUse && showStatusReadyToUse) {
    message.type = 'warning';
    message.content = <>{message.content} This template is not ready for use.</>;
  }

  return message;
};
