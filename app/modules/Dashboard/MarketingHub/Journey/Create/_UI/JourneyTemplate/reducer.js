/* eslint-disable no-plusplus */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
import produce from 'immer';
import { ACTION_TYPES, INITIAL_STATE, MODE } from './constant';
import { pick } from 'lodash';
import { OBJ_PRIMARY_KEYS } from './utils/config';
import { isEqualObject } from './utils/helper';

const reducer = (state = INITIAL_STATE, action) => {
  let activeObjIdx = -1;

  if (state.verification.activeObj) {
    activeObjIdx = state.verification.objects.findIndex(object =>
      isEqualObject(object, state.verification.activeObj),
    );
  }

  return produce(state, draft => {
    const { type, payload } = action;

    switch (type) {
      case ACTION_TYPES.init: {
        const { data, mode, moduleConfig } = payload;

        draft.mode = mode;
        draft.isLoading = true;
        draft.moduleConfig = moduleConfig;

        switch (mode) {
          case MODE.Save: {
            draft.journey = data;
            break;
          }
          case MODE.Use: {
            draft.journey = data.config.journey;
            draft.journeyTemplate = { ...data.model };
            break;
          }
          default:
            break;
        }
        break;
      }
      case ACTION_TYPES.changeActiveStep: {
        draft.activeStep = payload.step;
        break;
      }
      case ACTION_TYPES.reset: {
        return INITIAL_STATE;
      }
      case ACTION_TYPES.updateVerification: {
        const { updated = {} } = payload;

        draft.verification = {
          ...state.verification,
          ...updated,
        };
        break;
      }
      case ACTION_TYPES.changeActiveObj: {
        const { object } = payload;

        draft.verification.activeObj = pick(object, [
          ...OBJ_PRIMARY_KEYS[object.type],
          'type',
        ]);
        break;
      }
      case ACTION_TYPES.updateActiveObj: {
        const { updated } = payload;

        if (activeObjIdx !== -1) {
          draft.verification.objects[activeObjIdx] = {
            ...state.verification.objects[activeObjIdx],
            ...updated,
          };
        }
        break;
      }
      case ACTION_TYPES.updateObj: {
        const { obj, updated } = payload;

        const idx = state.verification.objects.findIndex(checkingObj =>
          isEqualObject(obj, checkingObj),
        );

        if (idx !== -1) {
          draft.verification.objects[idx] = {
            ...state.verification.objects[idx],
            ...updated,
          };
        }
        break;
      }
      case ACTION_TYPES.changeIsLoading: {
        const { isLoading } = payload;

        draft.isLoading = isLoading;
        break;
      }
      case ACTION_TYPES.changeIsSaving: {
        const { isSaving } = payload;

        draft.isSaving = isSaving;
        break;
      }
      case ACTION_TYPES.initDone: {
        draft.initialized = true;
        draft.isLoading = false;
        break;
      }
      case ACTION_TYPES.changeIsApplying: {
        draft.isApplying = !!payload.isApplying;
        break;
      }
      case ACTION_TYPES.updateApplyState: {
        draft.applyState = payload.updated;
        break;
      }
      case ACTION_TYPES.updateJourney: {
        draft.journey = payload.journey;
        break;
      }
      default:
        break;
    }

    return draft;
  });
};

export default reducer;
