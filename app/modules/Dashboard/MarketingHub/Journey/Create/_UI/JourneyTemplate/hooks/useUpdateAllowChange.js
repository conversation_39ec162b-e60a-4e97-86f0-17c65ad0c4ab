/* eslint-disable no-param-reassign */
import produce from 'immer';
import { updateVerification } from '../actions';
import { JOURNEY_OBJECT_TYPE } from '../constant';
import { useCallback } from 'react';
import { isEmpty } from 'lodash';
import { isEqualObject } from '../utils/helper';
import { useGetObjects } from './useGetObjects';
import { useDispatch } from 'react-redux';

export const useUpdateAllowChange = () => {
  const dispatch = useDispatch();

  const { objects, mapObjects } = useGetObjects();

  const updateAllowChangeByObjectType = useCallback(
    (objectType, allowChange) => {
      if (isEmpty(objects)) return;

      const getUpdatedConfigObject = (args = {}) => {
        const { currentValue = objects, currentObjType = objectType } = args;

        const temp = produce(currentValue, draft => {
          currentValue.forEach((obj, idx) => {
            if (!obj.editable || obj.type !== currentObjType) return;

            draft[idx].allowChange = allowChange;
          });
        });

        if (currentObjType === JOURNEY_OBJECT_TYPE.dataObject) {
          return getUpdatedConfigObject({
            currentValue: temp,
            currentObjType: JOURNEY_OBJECT_TYPE.dataObjectAttr,
          });
        }

        if (currentObjType === JOURNEY_OBJECT_TYPE.event) {
          return getUpdatedConfigObject({
            currentValue: temp,
            currentObjType: JOURNEY_OBJECT_TYPE.eventAttribute,
          });
        }

        return temp;
      };

      dispatch(
        updateVerification({
          objects: getUpdatedConfigObject(),
        }),
      );
    },
    [mapObjects],
  );

  const updateAllowChangeByObject = (updatedObject, allowChange) => {
    const { editable } = updatedObject;

    if (isEmpty(objects) || !editable) return;

    dispatch(
      updateVerification({
        objects: produce(objects, draft => {
          objects.forEach((obj, idx) => {
            if (!isEqualObject(obj, updatedObject)) return;

            draft[idx].allowChange = allowChange;
          });
        }),
      }),
    );
  };

  return { updateAllowChangeByObjectType, updateAllowChangeByObject };
};
