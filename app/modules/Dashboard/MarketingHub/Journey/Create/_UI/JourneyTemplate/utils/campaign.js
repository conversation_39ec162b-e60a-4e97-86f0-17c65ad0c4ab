/* eslint-disable no-console */
/* eslint-disable import/no-cycle */
/* eslint-disable camelcase */
/* eslint-disable no-cond-assign */
/* eslint-disable indent */
import { isEmpty, isFunction } from 'lodash';
import {
  REGEX_DETECT_TOKEN,
  REGEX_DETECT_TOKEN_SUPPORT,
  SYSTEM_BO,
} from '../../../../../../../../utils/constants';
import { iterateObject } from '../../../../../../../../utils/web/utils';
import { getEventFromNodeTrigger } from './event';
import {
  EMAIL_TEMPLATE,
  JSON_TEMPLATE,
  MEDIA_TEMPLATE,
} from '../../../../../../../../components/common/UIEditorPersonalization/utils';
import {
  getObjectsFromHTMLContent,
  getObjectsFromProperties,
  getObjectsFromTemplateSettings,
} from './mediatemplate';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';

export const eachCampaignDynTag = (metadata, callback) => {
  if (!isFunction(callback)) return [];

  const objects = [];

  const dynRegex = new RegExp(
    `${REGEX_DETECT_TOKEN.source}|${REGEX_DETECT_TOKEN_SUPPORT.source}`,
    'g',
  );

  iterateObject(metadata, (value, path, { setContinute }) => {
    const inVariantExtraData = path.includes('variantExtraData');

    const isOutsideJourneyDesign =
      inVariantExtraData &&
      [MEDIA_TEMPLATE, JSON_TEMPLATE, EMAIL_TEMPLATE].includes(value?.design);

    if (isOutsideJourneyDesign) {
      setContinute(false);
      return;
    }

    if (typeof value === 'string') {
      let match;

      while ((match = dynRegex.exec(value)) !== null) {
        const tagContent = match[1];

        callback({
          content: value,
          tag: tagContent,
          path,
        });
      }
    }
  });

  return objects;
};

export const getCustomInputsFromCampaign = metadata => {
  const campaignCustomInputs = metadata?.campaign?.custom_inputs;
  const variantCustomInputs = [];

  (metadata?.variants || []).forEach((v, idx) => {
    if (isEmpty(v.custom_inputs)) return;

    variantCustomInputs.push({
      idx,
      custom_inputs: v.custom_inputs,
    });
  });

  return {
    campaignCustomInputs: isEmpty(campaignCustomInputs)
      ? null
      : campaignCustomInputs,

    variantCustomInputs,
  };
};

export const getDOsFromCampaign = metadata => {
  const doObjs = [];

  const {
    campaignCustomInputs,
    variantCustomInputs,
  } = getCustomInputsFromCampaign(metadata);

  if (campaignCustomInputs) {
    doObjs.push({
      itemTypeId: SYSTEM_BO.Campaign.itemTypeId,
      pathConfig: [],
    });
  }

  if (!isEmpty(variantCustomInputs)) {
    doObjs.push({
      itemTypeId: SYSTEM_BO.Variant.itemTypeId,
      pathConfig: [],
    });
  }

  eachCampaignDynTag(metadata, args => {
    const { tag } = args;

    let obj = null;

    if (tag.startsWith('visitor')) {
      obj = { itemTypeId: SYSTEM_BO.Visitor.itemTypeId, pathConfig: [] };
    }

    if (tag.startsWith('customer')) {
      obj = { itemTypeId: SYSTEM_BO.Customer.itemTypeId, pathConfig: [] };
    }

    if (tag.startsWith('story')) {
      obj = { itemTypeId: SYSTEM_BO.Journey.itemTypeId, pathConfig: [] };
    }

    if (tag.startsWith('variant')) {
      obj = { itemTypeId: SYSTEM_BO.Variant.itemTypeId, pathConfig: [] };
    }

    if (tag.startsWith('campaign')) {
      obj = { itemTypeId: SYSTEM_BO.Campaign.itemTypeId, pathConfig: [] };
    }

    const { valid: isBOLikeEventAttr, attr: boAttr } = isBOLikeEventAttrDynTag(
      tag,
    );

    if (isBOLikeEventAttr) {
      const { itemTypeName } = boAttr;

      obj = { itemTypeName, pathConfig: [] };
    }

    if (obj) {
      doObjs.push(obj);
    }
  });

  // console.log(doObjs);

  return doObjs;
};

export const getDOAttrsFromCustomInput = ({ customInputs, itemTypeId }) => {
  const doAttrs = [];

  if (isEmpty(customInputs)) return [];

  Object.keys(customInputs).forEach(itemPropertyName => {
    doAttrs.push({
      itemTypeId,
      itemPropertyName,
      pathConfig: [],
    });
  });

  return doAttrs;
};

export const getDOAttrsFromCampaign = metadata => {
  const doAttrObjs = [];

  const {
    campaignCustomInputs,
    variantCustomInputs,
  } = getCustomInputsFromCampaign(metadata);

  if (campaignCustomInputs) {
    doAttrObjs.push(
      ...getDOAttrsFromCustomInput({
        customInputs: campaignCustomInputs,
        itemTypeId: SYSTEM_BO.Campaign.itemTypeId,
      }),
    );
  }

  if (!isEmpty(variantCustomInputs)) {
    variantCustomInputs.forEach(({ custom_inputs: customInputs }) => {
      doAttrObjs.push(
        ...getDOAttrsFromCustomInput({
          customInputs,
          itemTypeId: SYSTEM_BO.Variant.itemTypeId,
        }),
      );
    });
  }

  eachCampaignDynTag(metadata, ({ tag, path }) => {
    let obj = null;

    const getObjectInfo = itemTypeId => {
      const value = tag.split('.')[1];
      const itemPropertyName = value.split('||')[0];

      obj = {
        itemTypeId,
        itemPropertyName,
        pathConfig: [
          {
            path,
            pattern: tag,
            value: tag.replace(itemPropertyName, '{itemPropertyName}'),
          },
        ],
      };
    };

    if (tag.startsWith('visitor')) {
      getObjectInfo(SYSTEM_BO.Visitor.itemTypeId);
    }

    if (tag.startsWith('customer')) {
      getObjectInfo(SYSTEM_BO.Customer.itemTypeId);
    }

    if (tag.startsWith('story')) {
      getObjectInfo(SYSTEM_BO.Journey.itemTypeId);
    }

    if (tag.startsWith('variant')) {
      getObjectInfo(SYSTEM_BO.Variant.itemTypeId);
    }

    if (tag.startsWith('campaign')) {
      getObjectInfo(SYSTEM_BO.Campaign.itemTypeId);
    }

    const { valid: isBOLikeEventAttr, attr: boAttr } = isBOLikeEventAttrDynTag(
      tag,
    );

    if (isBOLikeEventAttr) {
      obj = { ...boAttr, pathConfig: [] };
    }

    if (obj) {
      doAttrObjs.push(obj);
    }
  });

  return doAttrObjs;
};

export const isBOLikeEventAttrDynTag = tag => {
  const isEventDyn = tag.startsWith('event');
  const chunks = tag.split('.');

  const valid = isEventDyn && chunks.length > 2;

  let attr = null;

  if (valid) {
    attr = {
      itemTypeName: chunks[1],
      itemPropertyName: chunks[2].split('||')[0],
    };
  }

  return { valid, attr };
};

export const getEventAttrsFromCampaign = (metadata, nodeTrigger) => {
  const eventAttrs = [];

  const event = getEventFromNodeTrigger(nodeTrigger);

  eachCampaignDynTag(metadata, ({ tag, path }) => {
    let obj = null;

    const validateResult = isBOLikeEventAttrDynTag(tag);

    if (validateResult.valid) {
      const { itemTypeName, itemPropertyName } = validateResult.attr;

      obj = {
        ...event,
        itemPropertyName,
        itemTypeName,
        pathConfig: [
          {
            path,
            pattern: tag,
            value: tag.replace(
              `${itemTypeName}.${itemPropertyName}`,
              '{itemTypeName}.{itemPropertyName}',
            ),
          },
        ],
      };
    } else if (tag.startsWith('event')) {
      const chunks = tag.split('.');
      const eventPropertyName = chunks[1].split('||')[0];

      obj = {
        ...event,
        eventPropertyName,
        pathConfig: [
          {
            path,
            pattern: tag,
            value: tag.replace(eventPropertyName, '{eventPropertyName}'),
          },
        ],
      };
    }

    if (obj) eventAttrs.push(obj);
  });

  return eventAttrs;
};

export const getObjectsFromVariantCDPTemplates = args => {
  const { variants, variantsLabel } = args;

  const objects = [];

  const templates = [MEDIA_TEMPLATE, EMAIL_TEMPLATE];

  try {
    variants.forEach((variant, variantIdx) => {
      const { variantExtraData } = variant.contentSetting;

      Object.entries(variantExtraData).forEach(([key, value]) => {
        const { design, properties, template_settings, views } = value || {};

        if (
          !properties ||
          !template_settings ||
          !views ||
          !templates.includes(design)
        )
          return;

        const path = `${variantsLabel}.${variantIdx}.contentSetting.variantExtraData.${key}`;

        const addObjectsFromMediatemplate = () => {
          objects.push(
            ...getObjectsFromTemplateSettings({
              templateSettings: template_settings,
              keyName: `${path}.template_settings`,
            }),

            ...getObjectsFromProperties({
              properties,
              templateSettings: template_settings,
              keyName: `${path}.properties`,
            }),

            ...Object.entries(views).flatMap(([viewKey, view]) =>
              getObjectsFromHTMLContent({
                html: view.html,
                keyName: `${path}.views.${viewKey}.html`,
                templateSettings: template_settings,
                journeySettings: properties.journeySettings,
              }),
            ),
          );
        };

        // Now, use a similar function to retrieve objects for both media templates and email templates.
        addObjectsFromMediatemplate();
      });
    });
  } catch (error) {
    console.log(error);

    addMessageToQueue({
      args,
      error,
      func: 'getObjectsFromVariantCDPTemplates',
    });

    return [];
  }

  return objects;
};
