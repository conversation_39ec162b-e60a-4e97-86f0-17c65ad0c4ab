import SelectorService from 'services/Selector';
import { safeParseArray } from '../../../../../../../../utils/web/utils';
import { JOURNEY_OBJECT_TYPE, OBJECT_TYPE } from '../constant';
import { DEFAULT_STATUS } from '../../../../../../../../utils/constants';

export const ALLOW_CREATE_OBJECT = [
  OBJECT_TYPE.journeyGoal,
  OBJECT_TYPE.segment,
  OBJECT_TYPE.dataObject,
  OBJECT_TYPE.dataObjectAttr,
];

export const OBJ_PRIMARY_KEYS = {
  [OBJECT_TYPE.segment]: ['segmentId', 'itemTypeId'],
  [OBJECT_TYPE.dataObject]: ['itemTypeId'],
  [OBJECT_TYPE.dataObjectAttr]: ['itemTypeId', 'itemPropertyName'],
  [OBJECT_TYPE.event]: ['eventActionId', 'eventCategoryId'],
  [OBJECT_TYPE.eventAttribute]: [
    'eventActionId',
    'eventCategoryId',
    'eventPropertyName',
    'itemPropertyName',
    'itemTypeId',
  ],
  [OBJECT_TYPE.campaign]: [
    'destinationId',
    'catalogId',
    'campaignId',
    'actionId',
  ],
  [OBJECT_TYPE.promotionPool]: ['poolId'],
  [OBJECT_TYPE.journeyGoal]: ['conversionId'],
  [OBJECT_TYPE.predictiveModel]: ['modelId'],
  [OBJECT_TYPE.objectView]: ['viewId'],
  [OBJECT_TYPE.variant]: ['variantId'],
  [OBJECT_TYPE.journeyCampaign]: ['campaignId'],
};

export const PARENT_OBJECTS = [
  JOURNEY_OBJECT_TYPE.journeyGoal,
  JOURNEY_OBJECT_TYPE.segment,
  JOURNEY_OBJECT_TYPE.dataObject,
  JOURNEY_OBJECT_TYPE.event,
  JOURNEY_OBJECT_TYPE.campaign,
  JOURNEY_OBJECT_TYPE.promotionPool,
];

export const SEARCH_CONFIG = (() => {
  const defaultSugesstionType = 'suggestionMultilang';

  const getDOAttrSearchConfig = ({ itemTypeId, dataType, isInputViaUI }) => {
    const filters = {
      OR: [
        {
          AND: [
            {
              column: 'item_type_id',
              operator: 'equals',
              data_type: 'number',
              value: itemTypeId,
            },
            {
              column: 'data_type',
              data_type: 'string',
              operator: 'matches',
              type: 1,
              value: [dataType],
            },
          ],
        },
      ],
    };

    if (isInputViaUI && filters.OR.at(0)?.AND) {
      filters.OR[0].AND.push({
        column: 'is_input_via_ui',
        data_type: 'number',
        operator: 'equals',
        value: 1,
      });
    }

    return {
      suggestionType: defaultSugesstionType,
      config: {
        filters,
        objectType: 'BO_ATTRIBUTE',
      },
    };
  };

  return Object.freeze({
    [OBJECT_TYPE.journeyGoal]: () => ({
      suggestionType: defaultSugesstionType,
      config: {
        filters: {
          OR: [
            {
              AND: [
                {
                  type: 1,
                  column: 'audience_type',
                  data_type: 'string',
                  operator: 'matches',
                  value: ['customer'],
                },
              ],
            },
          ],
        },
        objectType: 'CONVERSION_SOURCES',
      },
    }),
    [OBJECT_TYPE.segment]: () => ({
      suggestionType: defaultSugesstionType,
      config: {
        objectType: 'BO_SEGMENTS',
        filters: {
          OR: [
            {
              AND: [
                {
                  column: 'item_type_id',
                  data_type: 'number',
                  operator: 'matches',
                  value: [-1003, -1007],
                },
                {
                  column: 'status',
                  data_type: 'number',
                  operator: 'matches',
                  value: [DEFAULT_STATUS.ENABLED, DEFAULT_STATUS.DISABLED],
                },
              ],
            },
          ],
        },
      },
    }),
    [OBJECT_TYPE.dataObject]: () => ({
      suggestionType: defaultSugesstionType,
      config: {
        filters: {
          OR: [
            {
              AND: [
                {
                  column: 'type',
                  operator: 'equals',
                  data_type: 'number',
                  value: 2,
                },
                {
                  column: 'item_type_id',
                  operator: 'not_matches',
                  data_type: 'number',
                  value: [-1008, -1009, -1010, -1011, -1012, -1013],
                },
              ],
            },
          ],
        },
        menuObjectType: 'DATA_OBJECT',
        objectType: 'BUSINESS_OBJECT',
      },
    }),
    [OBJECT_TYPE.dataObjectAttr]: getDOAttrSearchConfig,
    [OBJECT_TYPE.event]: () => ({
      suggestionType: defaultSugesstionType,
      config: {
        objectType: 'ES_EVENT',
      },
    }),
    [OBJECT_TYPE.eventAttribute]: async ({
      eventActionId,
      eventCategoryId,
      insightPropertyIds = [],
      itemTypeId = null,
      dataType,
    }) => {
      const {
        data,
      } = await SelectorService.eventAttribute.getListByEventAndSource({
        sourceUrl: insightPropertyIds.join(','),
        eventCategoryId,
        eventActionId,
        objectType: 'STORIES',
      });

      const options = [];

      data.forEach(eventAttr => {
        const { eventPropertyName, eventPropertyDisplay } = eventAttr;

        if (
          itemTypeId !== eventAttr.itemTypeId ||
          dataType !== eventAttr.dataType
        )
          return;

        const items = [];

        safeParseArray(eventAttr.items).forEach(boAttr => {
          const { itemPropertyName, itemPropertyDisplay } = boAttr;

          if (dataType !== boAttr.dataType) return;

          items.push({
            itemTypeId,
            itemPropertyName,
            value: itemPropertyName,
            label: `${eventPropertyDisplay} \u00BB ${itemPropertyDisplay}`,
          });
        });

        options.push({
          itemTypeId,
          eventPropertyName,
          eventPropertyDisplay,
          value: eventPropertyName,
          label: eventPropertyDisplay,
          options: items,
        });
      });

      return { options };
    },
    [OBJECT_TYPE.campaign]: ({ catalogId }) => ({
      suggestionType: defaultSugesstionType,
      config: {
        objectType: 'DESTINATIONS',
        filters: {
          OR: [
            {
              AND: [
                {
                  type: 1,
                  column: 'catalog_id',
                  data_type: 'number',
                  operator: 'matches',
                  value: [catalogId],
                },
                {
                  type: 1,
                  column: 'status',
                  data_type: 'number',
                  operator: 'matches',
                  value: [1],
                },
              ],
            },
          ],
        },
      },
    }),
    [OBJECT_TYPE.promotionPool]: () => ({
      suggestionType: defaultSugesstionType,
      config: {
        filters: {
          OR: [
            {
              AND: [
                {
                  type: 1,
                  column: 'status',
                  data_type: 'number',
                  operator: 'matches',
                  value: [1],
                },
                {
                  type: 1,
                  column: 'process_status',
                  data_type: 'number',
                  operator: 'matches',
                  value: [2],
                },
              ],
            },
          ],
        },
        objectType: 'PROMOTION_POOL',
      },
    }),
  });
})();

export const ERROR_CODE = {
  MISSING_FIELD: 'MISSING_FIELD',
  NONE_PERMISSION: 'NO_PERMISSION',
  CONVERSION_CODE_EXIST: 'CONVERSION_CODE_EXIST',
  BO_CODE_EXIST: 'BO_CODE_EXIST',
  BO_ATTR_CODE_EXIST: 'BO_ATTR_CODE_EXIST',
  NOT_AVAILABLE_OR_LINKED: 'NOT_AVAILABLE_OR_LINKED',
  WRONG_DATA_TYPE: 'WRONG_DATA_TYPE',
  NOT_EXIST: 'NOT_EXIST',
  CATALOG_NOT_VALID: 'CATALOG_NOT_VALID',
};

export const EVENT_ATTR_CONDITION_TYPE = {
  normal: 'normal_event_attr',
  boAttr: 'bo_attr_when_bo_assign_event',
};
