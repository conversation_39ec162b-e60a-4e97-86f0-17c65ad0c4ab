/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
import React, { useCallback, useEffect, useState } from 'react';
import Search from '../../../../../../../../../containers/Search';
import UISelect from 'components/form/UISelectCondition';
import { StyledPopover, StyledButton, LabelNodata } from './styled';
import KeyboardArrowDownIcon from '@material-ui/icons/KeyboardArrowDown';
import { SEARCH_CONFIG } from '../../utils/config';
import { useActiveObj } from '../../hooks/useActiveObj';
import { get, isFunction, lowerCase } from 'lodash';
import { JOURNEY_OBJECT_TYPE } from '../../constant';
import { useDispatch } from 'react-redux';
import { replaceWithExisting } from '../../actions';
import './styles.scss';
import { JOURNEY_OBJECT_META } from '../../utils';

const DEFAULT_POPOVER_PROPS = {
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'left',
  },
  transformOrigin: {
    vertical: -10,
    horizontal: 'left',
  },
};

export const LoadExistingButton = () => {
  const dispatch = useDispatch();

  const { activeObj, info, saveInfo } = useActiveObj();

  const [isLoadingOpts, setIsLoadingOpts] = useState(false);
  const [searchProperties, setSearchProperties] = useState({});

  const getSearchProperties = useCallback(async () => {
    if (!activeObj) return {};

    const fnGetConfig = SEARCH_CONFIG[activeObj.type];

    if (!isFunction(fnGetConfig)) return {};

    const args = {};

    setIsLoadingOpts(true);

    switch (activeObj.type) {
      case JOURNEY_OBJECT_TYPE.dataObjectAttr: {
        // verified Parent BO
        args.itemTypeId = get(info, 'dataObject.verify.settings.item_type_id');

        args.dataType = get(saveInfo, 'settings.data_type');
        args.isInputViaUI = !!get(saveInfo, 'settings.is_input_via_ui');
        break;
      }
      case JOURNEY_OBJECT_TYPE.eventAttribute: {
        const dataReplace = get(info, 'eventObject.verify.dataReplace', {});

        args.dataType = get(saveInfo, 'settings.data_type');
        args.eventActionId = dataReplace.eventActionId;
        args.eventCategoryId = dataReplace.eventCategoryId;
        args.insightPropertyIds = dataReplace.insightPropertyIds;
        args.itemTypeId = get(info, 'dataObject.verify.settings.item_type_id');
        break;
      }
      case JOURNEY_OBJECT_TYPE.campaign: {
        args.catalogId = activeObj.catalogId;
        break;
      }

      default:
        break;
    }

    const properties = await fnGetConfig(args);

    setIsLoadingOpts(false);

    return properties;
  }, [activeObj, saveInfo, info]);

  useEffect(() => {
    (async () => {
      const properties = await getSearchProperties();

      if (properties) {
        setSearchProperties(properties);
      }
    })();
  }, [getSearchProperties]);

  const handleClickExisting = async existing => {
    setIsLoadingOpts(true);

    if (!activeObj) return;

    const object = {
      type: activeObj.type,
    };

    switch (activeObj.type) {
      case JOURNEY_OBJECT_TYPE.dataObject: {
        object.itemTypeId = existing.id;
        break;
      }
      case JOURNEY_OBJECT_TYPE.journeyGoal: {
        object.conversionId = existing.id;
        break;
      }
      case JOURNEY_OBJECT_TYPE.segment: {
        object.segmentId = existing.id;
        object.itemTypeId = existing.itemTypeId;
        break;
      }
      case JOURNEY_OBJECT_TYPE.event: {
        object.eventActionId = existing.eventActionId;
        object.eventCategoryId = existing.eventCategoryId;
        break;
      }
      case JOURNEY_OBJECT_TYPE.dataObjectAttr: {
        object.itemPropertyName = existing.id;
        object.itemTypeId = get(
          info,
          'dataObject.verify.settings.item_type_id',
        );
        break;
      }
      case JOURNEY_OBJECT_TYPE.eventAttribute: {
        object.eventActionId = get(
          info,
          'eventObject.verify.settings.eventActionId',
        );

        object.eventCategoryId = get(
          info,
          'eventObject.verify.settings.eventCategoryId',
        );

        object.itemTypeId = existing.itemTypeId;
        object.itemPropertyName = existing.itemPropertyName;
        object.eventPropertyName = existing.eventPropertyName;
        break;
      }
      case JOURNEY_OBJECT_TYPE.campaign: {
        object.destinationId = existing.id;
        break;
      }
      case JOURNEY_OBJECT_TYPE.promotionPool: {
        object.poolId = existing.id;
        break;
      }
      default:
        break;
    }

    setIsLoadingOpts(false);

    dispatch(replaceWithExisting(object));
  };

  if (!activeObj || !activeObj.allowChange || !info.allowChange) return null;

  const objectMeta = JOURNEY_OBJECT_META[activeObj.type];

  let label = lowerCase(objectMeta.label);

  switch (activeObj.type) {
    case JOURNEY_OBJECT_TYPE.dataObjectAttr: {
      label = 'DO attribute';
      break;
    }
    case JOURNEY_OBJECT_TYPE.campaign: {
      label = 'destination';
      break;
    }
    default:
      break;
  }

  if (activeObj.type === JOURNEY_OBJECT_TYPE.eventAttribute) {
    return (
      <UISelect
        popoverProps={DEFAULT_POPOVER_PROPS}
        className="loading-existing-dropdown"
        use="tree"
        renderButtonDropdown={({ bindTrigger }) => (
          <StyledButton
            isLoading={isLoadingOpts}
            {...bindTrigger()}
            theme="outline"
          >
            <span>Load existing</span>

            <KeyboardArrowDownIcon />
          </StyledButton>
        )}
        fullWidthPopover={false}
        widthPopover={250}
        {...searchProperties}
        onChange={handleClickExisting}
        searchNodataLabel={<LabelNodata>No {label} available</LabelNodata>}
      />
    );
  }

  return (
    <Search
      popoverProps={DEFAULT_POPOVER_PROPS}
      inputSearchLabel="Search..."
      dropdownButton={({ bindTrigger }) => (
        <StyledButton
          isLoading={isLoadingOpts}
          {...bindTrigger()}
          theme="outline"
        >
          <span>Load existing</span>

          <KeyboardArrowDownIcon />
        </StyledButton>
      )}
      infinite
      PopoverComponent={StyledPopover}
      isGoTo={false}
      isAddFilter={false}
      callback={(type, value) => {
        if (type === 'SEARCH_GOTO') {
          handleClickExisting(value);
        }
      }}
      {...searchProperties}
      config={{
        limit: 20,
        page: 1,
        sd: 'asc',
        ...searchProperties.config,
      }}
      labelNodata={<LabelNodata>No {label} available</LabelNodata>}
    />
  );
};
