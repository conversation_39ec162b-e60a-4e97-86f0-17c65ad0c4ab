import NODES_TEST_CASE from './getObjects.mock.json';
import { getObjectsFromBranch } from '../utils';
import { orderBy } from 'lodash';

const { GET_OBJECTS_PER_NODE } = NODES_TEST_CASE;

describe('function getObjectFromBranch', () => {
  test.each(GET_OBJECTS_PER_NODE)(
    'Case %s, NodeType: %s',
    (_title, _nodeType, args, objects) => {
      const mapFn = item => {
        const temp = { ...item };

        temp.pathConfig = orderBy(item.pathConfig, ['path']);

        return temp;
      };

      const received = getObjectsFromBranch(
        args.branch,
        args.nodeTrigger || GET_OBJECTS_PER_NODE[0][2].branch,
      ).map(mapFn);

      const expected = objects.map(mapFn);

      // console.log(JSON.stringify(received));

      expect(received).toHaveLength(expected.length);
      expect(received).toEqual(expect.arrayContaining(expected));
    },
  );
});
