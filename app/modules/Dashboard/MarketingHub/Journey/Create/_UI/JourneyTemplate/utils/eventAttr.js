/* eslint-disable import/no-cycle */
import { isFunction, pick } from 'lodash';
import { getEventFromNodeTrigger, getEventFromPeformEvent } from './event';
import { JOURNEY_OBJECT_TYPE, OBJECT_TYPE } from '../constant';
import { OBJ_PRIMARY_KEYS } from './config';
import { findObject, loopFilters } from './helper';

export const getEventAttrsFromFilters = (filters, options = {}) => {
  const { filtersPropName = 'filters', customPathConfig } = options;

  const eventAttrs = [];

  loopFilters(filters, {
    eachCondition: (condition, conditionPath) => {
      const path = `${filtersPropName}.${conditionPath}`;

      const { itemTypeId, column, metadata } = condition;

      const eventAttr = {
        eventPropertyName: column,
        pathConfig: [
          {
            path: `${path}.propertySyntax`,
            value: 'eventPropertySyntax',
          },
          {
            path: `${path}.column`,
            value: 'eventPropertyName',
          },
          {
            path: `${path}.metadata.eventPropertySyntax`,
            value: 'eventPropertySyntax',
          },
          {
            path: `${path}.metadata.itemPropertyName`,
            value: 'eventPropertyName',
          },
        ],
      };

      if (itemTypeId) {
        delete eventAttr.eventPropertyName;

        eventAttr.itemTypeId = itemTypeId;
        eventAttr.itemPropertyName = column;

        eventAttr.pathConfig[1].value = 'itemPropertyName';
        eventAttr.pathConfig[3].value = 'itemPropertyName';

        eventAttr.pathConfig.push(
          {
            path: `${path}.itemTypeId`,
            value: 'itemTypeId',
          },
          {
            path: `${path}.metadata.itemTypeId`,
            value: 'itemTypeId',
          },
          {
            path: `${path}.metadata.itemTypeName`,
            value: 'itemTypeName',
          },
        );
      } else if (metadata.itemTypeName) {
        eventAttr.pathConfig.push({
          path: `${path}.metadata.itemTypeName`,
          value: 'itemTypeName',
        });
      }

      if (isFunction(customPathConfig)) {
        eventAttr.pathConfig = customPathConfig({
          condition,
          path,
          defaultPathConfig: eventAttr.pathConfig,
        });
      }

      eventAttrs.push(eventAttr);
    },
  });

  return eventAttrs;
};

export const getEventAttrsFromPerfEvent = (perfEvent, options = {}) => {
  const {
    filtersPropName = 'filters',
    perfEventPropName = 'perfEvent',
  } = options;

  const customPathConfig = ({ condition, path, defaultPathConfig }) => {
    const isBOLikeEventAttr = !!condition?.itemTypeId;
    const temp = [...defaultPathConfig];

    temp[0].path = `${path}.syntax`;

    if (isBOLikeEventAttr) {
      temp[4].path = `${path}.item_type_id`;
    }

    return temp.map(i => ({
      ...i,
      path: `${perfEventPropName}.${i.path}`,
    }));
  };

  const eventAttrs = getEventAttrsFromFilters(perfEvent.filters, {
    filtersPropName,
    perfEventPropName,
    customPathConfig,
  });

  const event = getEventFromPeformEvent(perfEvent, {
    perfEventPropName: 'event',
  });

  return eventAttrs.map(eventAttr => ({
    ...pick(event, ['eventActionId', 'eventCategoryId']),
    ...eventAttr,
  }));
};

export const getEventAttrsFromFiltersAndNodeTrigger = ({
  filters,
  nodeTrigger,
}) => {
  const eventProps = getEventFromNodeTrigger(nodeTrigger);

  return getEventAttrsFromFilters(filters).map(attr => ({
    ...attr,
    ...eventProps,
  }));
};

export const getActEventAttrAdditionalInfo = (doAttrObj, objects) => {
  const result = {};

  if (doAttrObj.type !== JOURNEY_OBJECT_TYPE.eventAttribute) return {};

  const { itemTypeId } = doAttrObj;

  if (itemTypeId) {
    result.dataObject = findObject(
      {
        type: JOURNEY_OBJECT_TYPE.dataObject,
        itemTypeId,
      },
      objects,
    );
  }

  return {
    ...result,

    eventObject: findObject(
      {
        type: JOURNEY_OBJECT_TYPE.event,
        ...pick(doAttrObj, OBJ_PRIMARY_KEYS[JOURNEY_OBJECT_TYPE.event]),
      },
      objects,
    ),
  };
};

export const getSupportedObjsForEventAttr = ({ eventAttrObject, objects }) => {
  if (eventAttrObject.type !== JOURNEY_OBJECT_TYPE.eventAttribute) return [];

  const suportedObjects = [];

  const { eventActionId, eventCategoryId } = eventAttrObject;

  const eventObject = findObject(
    { type: JOURNEY_OBJECT_TYPE.event, eventActionId, eventCategoryId },
    objects,
  );

  if (!eventObject) return [];

  suportedObjects.push(eventObject);

  objects.forEach(obj => {
    const isSameBO =
      obj.type === JOURNEY_OBJECT_TYPE.dataObject &&
      eventAttrObject.itemTypeId === obj.itemTypeId;

    const isSameBOAttr =
      obj.type === JOURNEY_OBJECT_TYPE.dataObjectAttr &&
      eventAttrObject.itemTypeId === obj.itemTypeId &&
      eventAttrObject.itemPropertyName === obj.itemPropertyName;

    if (isSameBO || isSameBOAttr) {
      suportedObjects.push(obj);
    }
  });

  return suportedObjects;
};

export const getEventAttrsByItemTypeId = (itemTypeId, objects = []) => {
  if (!itemTypeId) return [];

  return objects.filter(
    obj =>
      obj.type === OBJECT_TYPE.eventAttribute && obj.itemTypeId === itemTypeId,
  );
};
