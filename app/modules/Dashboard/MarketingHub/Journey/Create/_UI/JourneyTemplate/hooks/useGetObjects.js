import { get, groupBy, isEmpty } from 'lodash';
import { useMemo } from 'react';
import { isRelatedObject } from '../utils';
import {
  findObject,
  flattenJTObjects,
  getObjectSettings,
} from '../utils/helper';
import { useSelector } from 'react-redux';
import { selectMode, selectVerification } from '../selector';
import { MODE } from '../constant';

export const useGetObjects = () => {
  const mode = useSelector(selectMode);

  const { objects, templateObjSettings } = useSelector(selectVerification);

  const flattenObjects = useMemo(() => {
    if (isEmpty(objects)) return [];

    let result = flattenJTObjects(objects).map(object => {
      const temp = { ...object };

      if (mode === MODE.Save) return temp;

      if (get(object, 'verify.isExist')) {
        temp.settings = get(object, 'verify.settings');
      } else {
        const templateObject = findObject(temp, templateObjSettings);

        temp.settings = get(templateObject, 'settings', null);
        temp.settings.status = 3;
      }

      return temp;
    });

    templateObjSettings.forEach(obj => {
      if (!findObject(obj, result)) {
        result.push(obj);
      }
    });

    result = result.map(object => ({
      ...object,
      settings: getObjectSettings({
        mode,
        objects,
        templateObjSettings,
        object,
      }),
    }));

    return result;
  }, [mode, objects, templateObjSettings]);

  const mapObjects = groupBy(objects, 'type');

  const mapFlattenObjects = groupBy(flattenObjects, 'type');

  const getObjectsByType = (objectType, withRelated = false) => {
    const result = {
      objects: [],
      flattenObjects: [],
    };

    if (!mapObjects || !mapObjects[objectType]) {
      return result;
    }

    const objectsByType = mapObjects[objectType];

    if (!withRelated) {
      result.objects = objectsByType;
      result.flattenObjects = objectsByType;

      return result;
    }

    objectsByType.forEach(object => {
      result.flattenObjects.push(object);

      const temp = { ...object };

      if (temp.children === undefined) {
        temp.children = [];
      }

      objects.forEach(relatedObj => {
        if (isRelatedObject(object, relatedObj)) {
          temp.children.push(relatedObj);

          result.flattenObjects.push(relatedObj);
        }
      });

      result.objects.push(temp);
    });

    return result;
  };

  return {
    objects,
    mapObjects,
    mapAllObjects: mapFlattenObjects,
    allObjects: flattenObjects,
    getObjectsByType,
  };
};
