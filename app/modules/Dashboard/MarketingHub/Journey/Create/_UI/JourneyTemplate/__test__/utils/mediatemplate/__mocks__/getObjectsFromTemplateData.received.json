[{"type": "event", "pathConfig": [{"path": "properties.journeySettings.triggerEvent.eventActionId", "value": "eventActionId"}, {"path": "properties.journeySettings.triggerEvent.eventCategoryId", "value": "eventCategoryId"}, {"path": "properties.journeySettings.triggerEvent.insightPropertyIds", "value": "insightPropertyIds"}]}, {"type": "event", "eventActionId": -102, "eventCategoryId": -20, "pathConfig": []}, {"type": "business_object", "itemTypeName": "ad_zone", "pathConfig": []}, {"type": "bo_attribute", "itemTypeName": "ad_zone", "itemPropertyName": "u_user_id", "pathConfig": []}, {"eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "itemTypeName": "ad_zone", "itemPropertyName": "u_user_id", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "event.{itemTypeName}.{itemPropertyName}", "pattern": "event.ad_zone.u_user_id"}]}, {"type": "business_object", "itemTypeName": "browser", "pathConfig": []}, {"type": "bo_attribute", "itemTypeName": "browser", "itemPropertyName": "id", "pathConfig": []}, {"eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "itemTypeName": "browser", "itemPropertyName": "id", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "event.{itemTypeName}.{itemPropertyName}", "pattern": "event.browser.id"}]}, {"type": "business_object", "itemTypeId": 1, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "image_url", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "groups.csgoxel3[{itemTypeId}].{itemPropertyName}", "pattern": "groups.csgoxel3[1].image_url"}]}, {"type": "promotion_pool", "poolCode": "qc_review_pool_169__update_3", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "promotion_code.{poolCode}.last_used_time", "pattern": "promotion_code.qc_review_pool_169__update_3.last_used_time"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "name", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "groups.csgoxel3[{itemTypeId}].{itemPropertyName}", "pattern": "groups.csgoxel3[1].name"}]}, {"type": "business_object", "itemTypeId": -1007, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "user_id", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "visitor.{itemPropertyName}", "pattern": "visitor.user_id"}]}, {"type": "business_object", "itemTypeId": 3, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": 3, "itemPropertyName": "image_url", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "groups.csgoxel3[{itemTypeId}].{itemPropertyName}", "pattern": "groups.csgoxel3[3].image_url"}]}, {"type": "business_object", "itemTypeName": "campaign", "pathConfig": []}, {"type": "bo_attribute", "itemTypeName": "campaign", "itemPropertyName": "campaign_name", "pathConfig": []}, {"eventActionId": -102, "eventCategoryId": -20, "type": "event_attribute", "itemTypeName": "campaign", "itemPropertyName": "campaign_name", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "event.{itemTypeName}.{itemPropertyName}", "pattern": "event.campaign.campaign_name"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "fe_test_datetime__2", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "groups.csgoxel3[{itemTypeId}].{itemPropertyName}", "pattern": "groups.csgoxel3[1].fe_test_datetime__2"}]}, {"type": "business_object", "itemTypeId": -3, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "name", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "#BEGIN_FOR_TABLE_y55a3yfmcgd3zu9qskqn#$1#{item.{itemPropertyName}}$2#END_FOR_TABLE_y55a3yfmcgd3zu9qskqn#", "regex": {"source": "#BEGIN_FOR_TABLE_y55a3yfmcgd3zu9qskqn#([\\s\\S]*?)#{item\\.name([\\s\\S]*?)#END_FOR_TABLE_y55a3yfmcgd3zu9qskqn#", "flags": ""}}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "date_created", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "#BEGIN_FOR_TABLE_y55a3yfmcgd3zu9qskqn#$1#{item.{itemPropertyName}}$2#END_FOR_TABLE_y55a3yfmcgd3zu9qskqn#", "regex": {"source": "#BEGIN_FOR_TABLE_y55a3yfmcgd3zu9qskqn#([\\s\\S]*?)#{item\\.date_created([\\s\\S]*?)#END_FOR_TABLE_y55a3yfmcgd3zu9qskqn#", "flags": ""}}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "last_updated", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "#BEGIN_FOR_TABLE_y55a3yfmcgd3zu9qskqn#$1#{item.{itemPropertyName}}$2#END_FOR_TABLE_y55a3yfmcgd3zu9qskqn#", "regex": {"source": "#BEGIN_FOR_TABLE_y55a3yfmcgd3zu9qskqn#([\\s\\S]*?)#{item\\.last_updated([\\s\\S]*?)#END_FOR_TABLE_y55a3yfmcgd3zu9qskqn#", "flags": ""}}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "testcache2", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "#BEGIN_FOR_TABLE_y55a3yfmcgd3zu9qskqn#$1#{item.{itemPropertyName}}$2#END_FOR_TABLE_y55a3yfmcgd3zu9qskqn#", "regex": {"source": "#BEGIN_FOR_TABLE_y55a3yfmcgd3zu9qskqn#([\\s\\S]*?)#{item\\.testcache2([\\s\\S]*?)#END_FOR_TABLE_y55a3yfmcgd3zu9qskqn#", "flags": ""}}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "huytcx", "pathConfig": [{"path": "properties.viewPages.1.html", "value": "#BEGIN_FOR_TABLE_y55a3yfmcgd3zu9qskqn#$1#{item.{itemPropertyName}}$2#END_FOR_TABLE_y55a3yfmcgd3zu9qskqn#", "regex": {"source": "#BEGIN_FOR_TABLE_y55a3yfmcgd3zu9qskqn#([\\s\\S]*?)#{item\\.huytcx([\\s\\S]*?)#END_FOR_TABLE_y55a3yfmcgd3zu9qskqn#", "flags": ""}}]}, {"type": "promotion_pool", "poolId": 230554, "pathConfig": [{"path": "properties.viewPages.1.html", "value": "data-wheel=\"$1poolId$2:{poolId}$3\"", "regex": {"source": "data-wheel=\"([^\"]*)poolId([^:]*):230554([^\"]*)\"", "flags": ""}}]}, {"type": "promotion_pool", "poolId": 470621, "pathConfig": [{"path": "properties.viewPages.1.html", "value": "data-wheel=\"$1poolId$2:{poolId}$3\"", "regex": {"source": "data-wheel=\"([^\"]*)poolId([^:]*):470621([^\"]*)\"", "flags": ""}}]}, {"type": "event", "eventActionId": 471165, "eventCategoryId": -15, "pathConfig": [{"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.event", "pattern": "471165:-15", "value": "{eventActionId}:{eventCategoryId}"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.source", "value": "insightPropertyIds"}]}, {"type": "business_object", "itemTypeId": -1013, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -1013, "itemPropertyName": "u_user_id", "pathConfig": []}, {"type": "event_attribute", "eventActionId": 471165, "eventCategoryId": -15, "itemPropertyName": "u_user_id", "itemTypeId": -1013, "pathConfig": [{"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.attribute.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.attribute.itemTypeId", "value": "itemTypeId"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.attribute.itemTypeName", "value": "itemTypeName"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.attribute.propertyName", "value": "itemPropertyName"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.attribute.value", "value": "itemPropertyName"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.mappingFields", "pattern": "event.ad_zone.u_user_id", "value": "event.{itemTypeName}.{itemPropertyName}"}]}, {"eventActionId": -101, "eventCategoryId": -23, "type": "event", "pathConfig": [{"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.event_metadata.event_action_id", "value": "eventActionId"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.event_metadata.event_category_id", "value": "eventCategoryId"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.event_metadata.event_property_syntax", "value": "eventPropertySyntax"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.event_metadata.event_tracking_name", "value": "eventTrackingName"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.event_metadata.insight_property_ids", "value": "insightPropertyIds"}]}, {"type": "business_object", "itemTypeId": -3, "pathConfig": [{"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.event_metadata.item_type_id", "value": "itemTypeId"}, {"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.event_metadata.item_type_name", "value": "itemTypeName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "id", "pathConfig": []}, {"eventActionId": -101, "eventCategoryId": -23, "type": "event_attribute", "itemTypeId": -3, "itemPropertyName": "id", "pathConfig": [{"path": "properties.viewPages.1.blocks.5q7qokbjht5u8s2i4qty.settings.blockStylesSettings.displayCondition.event_metadata.item_property_name", "value": "itemPropertyName"}]}, {"type": "business_object", "itemTypeId": 1, "pathConfig": [{"path": "properties.contentSources.groups.0.itemTypeId", "value": "itemTypeId"}, {"path": "properties.contentSources.groups.0.itemTypeName", "value": "itemTypeName"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "product_viewed_most_often", "pathConfig": [{"path": "properties.contentSources.groups.0.fallback", "value": "itemPropertyName"}]}, {"type": "business_object", "itemTypeId": -1007, "pathConfig": [{"path": "properties.contentSources.groups.1.itemTypeId", "value": "itemTypeId"}, {"path": "properties.contentSources.groups.1.itemTypeName", "value": "itemTypeName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "product_viewed_most_often", "pathConfig": [{"path": "properties.contentSources.groups.1.fallback", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "last_updated", "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.0.AND.0.column", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "bd_month", "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.0.AND.1.column", "value": "itemPropertyName"}]}, {"eventActionId": 12, "eventCategoryId": -11, "type": "event", "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.0.AND.1.event_metadata.event_action_id", "value": "eventActionId"}, {"path": "properties.contentSources.groups.1.filters.OR.0.AND.1.event_metadata.event_category_id", "value": "eventCategoryId"}, {"path": "properties.contentSources.groups.1.filters.OR.0.AND.1.event_metadata.event_property_syntax", "value": "eventPropertySyntax"}, {"path": "properties.contentSources.groups.1.filters.OR.0.AND.1.event_metadata.event_tracking_name", "value": "eventTrackingName"}, {"path": "properties.contentSources.groups.1.filters.OR.0.AND.1.event_metadata.insight_property_ids", "value": "insightPropertyIds"}]}, {"eventActionId": 12, "eventCategoryId": -11, "type": "event_attribute", "eventPropertyName": "duration", "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.0.AND.1.event_metadata.item_property_name", "value": "eventPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "full_name", "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.0.AND.2.column", "value": "itemPropertyName"}]}, {"eventActionId": 41, "eventCategoryId": 5, "type": "event", "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.0.AND.2.event_metadata.event_action_id", "value": "eventActionId"}, {"path": "properties.contentSources.groups.1.filters.OR.0.AND.2.event_metadata.event_category_id", "value": "eventCategoryId"}, {"path": "properties.contentSources.groups.1.filters.OR.0.AND.2.event_metadata.event_property_syntax", "value": "eventPropertySyntax"}, {"path": "properties.contentSources.groups.1.filters.OR.0.AND.2.event_metadata.event_tracking_name", "value": "eventTrackingName"}, {"path": "properties.contentSources.groups.1.filters.OR.0.AND.2.event_metadata.insight_property_ids", "value": "insightPropertyIds"}]}, {"type": "business_object", "itemTypeId": -1009, "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.0.AND.2.event_metadata.item_type_id", "value": "itemTypeId"}, {"path": "properties.contentSources.groups.1.filters.OR.0.AND.2.event_metadata.item_type_name", "value": "itemTypeName"}]}, {"type": "bo_attribute", "itemTypeId": -1009, "itemPropertyName": "story_name", "pathConfig": []}, {"eventActionId": 41, "eventCategoryId": 5, "type": "event_attribute", "itemTypeId": -1009, "itemPropertyName": "story_name", "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.0.AND.2.event_metadata.item_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "user_id", "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.1.AND.0.column", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "ba_test_custom", "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.1.AND.1.column", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "full_name", "pathConfig": [{"path": "properties.contentSources.groups.1.filters.OR.1.AND.1.visitor_metadata.item_property_name", "value": "itemPropertyName"}, {"path": "properties.contentSources.groups.1.filters.OR.1.AND.1.visitor_metadata.item_type_id", "value": "itemTypeId"}]}, {"type": "business_object", "itemTypeId": 1, "pathConfig": [{"path": "template_setting.contentSources.0.itemTypeId", "value": "itemTypeId"}, {"path": "template_setting.contentSources.0.itemTypeName", "value": "itemTypeName"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "product_viewed_most_often", "pathConfig": [{"path": "template_setting.contentSources.0.fallback", "value": "itemPropertyName"}]}, {"type": "business_object", "itemTypeId": -1007, "pathConfig": [{"path": "template_setting.contentSources.1.itemTypeId", "value": "itemTypeId"}, {"path": "template_setting.contentSources.1.itemTypeName", "value": "itemTypeName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "product_viewed_most_often", "pathConfig": [{"path": "template_setting.contentSources.1.fallback", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "last_updated", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.0.column", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "bd_month", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.1.column", "value": "itemPropertyName"}]}, {"eventActionId": 12, "eventCategoryId": -11, "type": "event", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.event_action_id", "value": "eventActionId"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.event_category_id", "value": "eventCategoryId"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.event_property_syntax", "value": "eventPropertySyntax"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.event_tracking_name", "value": "eventTrackingName"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.insight_property_ids", "value": "insightPropertyIds"}]}, {"eventActionId": 12, "eventCategoryId": -11, "type": "event_attribute", "eventPropertyName": "duration", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.item_property_name", "value": "eventPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "full_name", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.2.column", "value": "itemPropertyName"}]}, {"eventActionId": 41, "eventCategoryId": 5, "type": "event", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.event_action_id", "value": "eventActionId"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.event_category_id", "value": "eventCategoryId"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.event_property_syntax", "value": "eventPropertySyntax"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.event_tracking_name", "value": "eventTrackingName"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.insight_property_ids", "value": "insightPropertyIds"}]}, {"type": "business_object", "itemTypeId": -1009, "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.item_type_id", "value": "itemTypeId"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.item_type_name", "value": "itemTypeName"}]}, {"eventActionId": 41, "eventCategoryId": 5, "type": "event_attribute", "itemTypeId": -1009, "itemPropertyName": "story_name", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.item_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "user_id", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.1.AND.0.column", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "ba_test_custom", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.1.AND.1.column", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "full_name", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.1.AND.1.visitor_metadata.item_property_name", "value": "itemPropertyName"}, {"path": "template_setting.contentSources.1.filters.OR.1.AND.1.visitor_metadata.item_type_id", "value": "itemTypeId"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "user_id", "pathConfig": [{"path": "template_setting.actions.9.textData.8dcpolfm.attribute.value", "value": "itemPropertyName"}, {"path": "template_setting.actions.9.textData.8dcpolfm.mappingFields", "pattern": "visitor.user_id", "value": "visitor.{itemPropertyName}"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "name", "pathConfig": [{"path": "template_setting.actions.9.textData.lermzl87.attribute.itemTypeName", "value": "itemTypeName"}, {"path": "template_setting.actions.9.textData.lermzl87.attribute.value", "value": "itemPropertyName"}, {"path": "template_setting.actions.9.textData.lermzl87.mappingFields", "pattern": "groups.csgoxel3[1].name", "value": "groups.csgoxel3[{itemTypeId}].{itemPropertyName}"}, {"path": "template_setting.actions.9.textData.lermzl87.type", "pattern": "content-source::csgoxel3::1", "value": "content-source::csgoxel3::{itemTypeId}"}]}, {"type": "promotion_pool", "poolCode": "qc_review_pool_169__update_3", "pathConfig": [{"path": "template_setting.actions.13.textData.sm0y0az8.mappingFields", "pattern": "promotion_code.qc_review_pool_169__update_3.pool_id", "value": "promotion_code.{poolCode}.pool_id"}, {"path": "template_setting.actions.13.textData.sm0y0az8.pool", "value": "poolCode"}]}, {"type": "event", "eventActionId": 471165, "eventCategoryId": -15, "pathConfig": [{"path": "template_setting.actions.14.textData.nxju010f.event", "pattern": "471165:-15", "value": "{eventActionId}:{eventCategoryId}"}, {"path": "template_setting.actions.14.textData.nxju010f.source", "value": "insightPropertyIds"}]}, {"type": "business_object", "itemTypeId": -1010, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -1010, "itemPropertyName": "campaign_name", "pathConfig": []}, {"type": "event_attribute", "eventActionId": 471165, "eventCategoryId": -15, "itemPropertyName": "campaign_name", "itemTypeId": -1010, "pathConfig": [{"path": "template_setting.actions.14.textData.nxju010f.attribute.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "template_setting.actions.14.textData.nxju010f.attribute.itemTypeId", "value": "itemTypeId"}, {"path": "template_setting.actions.14.textData.nxju010f.attribute.itemTypeName", "value": "itemTypeName"}, {"path": "template_setting.actions.14.textData.nxju010f.attribute.propertyName", "value": "itemPropertyName"}, {"path": "template_setting.actions.14.textData.nxju010f.attribute.value", "value": "itemPropertyName"}, {"path": "template_setting.actions.14.textData.nxju010f.mappingFields", "pattern": "event.campaign.campaign_name", "value": "event.{itemTypeName}.{itemPropertyName}"}]}, {"type": "business_object", "itemTypeId": -3, "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.itemTypeId", "value": "itemTypeId"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "name", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.columns.0.value", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "date_created", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.columns.1.value", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "last_updated", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.columns.2.value", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "testcache2", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.columns.3.value", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "huytcx", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.columns.4.value", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "id", "pathConfig": [{"path": "template_setting.objectRelationships.0.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.0.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "user_id", "pathConfig": [{"path": "template_setting.objectRelationships.1.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.1.object_property_name", "value": "itemPropertyName"}]}, {"type": "business_object", "itemTypeId": -100, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -100, "itemPropertyName": "last_used_time", "pathConfig": [{"path": "template_setting.objectRelationships.2.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.2.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1010, "itemPropertyName": "campaign_name", "pathConfig": [{"path": "template_setting.objectRelationships.3.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.3.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "id", "pathConfig": [{"path": "template_setting.objectRelationships.4.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.4.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "last_updated", "pathConfig": [{"path": "template_setting.objectRelationships.5.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.5.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "bd_month", "pathConfig": [{"path": "template_setting.objectRelationships.6.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.6.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "full_name", "pathConfig": [{"path": "template_setting.objectRelationships.8.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.8.object_property_name", "value": "itemPropertyName"}]}, {"type": "business_object", "itemTypeId": -1009, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -1009, "itemPropertyName": "story_name", "pathConfig": [{"path": "template_setting.objectRelationships.9.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.9.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "ba_test_custom", "pathConfig": [{"path": "template_setting.objectRelationships.10.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.10.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "id", "pathConfig": [{"path": "template_setting.objectRelationships.11.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.11.object_property_name", "value": "itemPropertyName"}]}]