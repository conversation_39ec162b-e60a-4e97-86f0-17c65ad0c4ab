/* eslint-disable import/no-cycle */
import { isEmpty, isFunction, keyBy } from 'lodash';
import { loopFilters } from './helper';
import BOServices from 'services/BusinessObject';
import { getDefaultShareAccess } from './service';
import { getCurrentUserId } from '../../../../../../../../utils/web/cookie';
import { ERROR_CODE } from './config';
import { OBJECT_TYPE } from '../constant';

export const getDOsFromFilters = (filters, options = {}) => {
  const { customPathConfig, filtersPropName = 'filters' } = options;

  const dataObjects = [];

  loopFilters(filters, {
    eachCondition: (condition, conditionPath) => {
      const path = `${filtersPropName}.${conditionPath}`;

      let obj = {
        pathConfig: [],
      };

      const { conditionType } = condition;

      if (conditionType === 'event_attribute') {
        const { itemTypeId } = condition.metadata;

        if (itemTypeId) {
          obj.itemTypeId = itemTypeId;
        }

        obj = { ...obj };
      }

      if (['customer_attribute', 'user_attribute'].includes(conditionType)) {
        obj.itemTypeId = condition?.itemTypeId;

        obj.pathConfig = [
          {
            path: `${path}.itemTypeId`,
            value: 'itemTypeId',
          },
        ];
      }

      if (isFunction(customPathConfig)) {
        obj.pathConfig = customPathConfig({
          condition,
          path,
        });
      }

      if (obj.itemTypeId) {
        dataObjects.push(obj);
      }
    },
  });

  return dataObjects;
};

export const getDOsFromPerfEvent = (perfEvent, options = {}) => {
  const { filtersPropName = 'filters' } = options;

  return getDOsFromFilters(perfEvent.filters, {
    filtersPropName,
    customPathConfig: () => [],
  });
};

export const createDO = async settings => {
  const defaultShareAccess = await getDefaultShareAccess();

  const mappingSettings = {
    _owner_id: getCurrentUserId(),
    data_upd_method: settings.data_upd_methods,
    description: settings.description,
    description_multiLang: settings.description_multilang,
    item_display_multilang: settings.item_display_multilang,
    item_type_display: settings.item_type_display,
    item_type_name: settings.item_type_name,
    object_owner_id: getCurrentUserId(),
    share_access: defaultShareAccess,
    treat_as_co: +settings.treat_as_co,
    update_constantly: settings.update_constantly,
  };

  const res = await BOServices.businessObject.create(mappingSettings);

  if (res.codeMessage === '_NOTIFICATION_SAME_CODE') {
    return {
      success: false,
      error: {
        code: ERROR_CODE.BO_CODE_EXIST,
      },
    };
  }

  if (res.code === 200) {
    return { success: true };
  }

  return { success: false };
};

export const getObjectsUseSameDO = (doObject, objects) => {
  if (doObject.type !== OBJECT_TYPE.dataObject) return [];

  return objects.filter(
    checkingObj => checkingObj.itemTypeId === doObject.itemTypeId,
  );
};

export const bindBOPropertiesToObjects = async objects => {
  if (!objects) return [];

  const itemTypeNames = [];

  objects.forEach(obj => {
    if (!obj.itemTypeName) return;

    itemTypeNames.push(obj.itemTypeName);
  });

  if (isEmpty(itemTypeNames)) return objects;

  let result = [];

  const { data: listBO = [] } = await BOServices.businessObject.getByBOCodes({
    itemTypeNames,
  });

  result = objects.map(obj => {
    const boValue = keyBy(listBO, 'itemTypeName')[obj.itemTypeName];
    const itemTypeId = +boValue?.itemTypeId;

    if (!Number.isNaN(itemTypeId)) {
      return { itemTypeId, ...obj };
    }

    return { ...obj };
  });

  return result;
};
