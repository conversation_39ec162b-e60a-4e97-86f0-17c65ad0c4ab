import styled from 'styled-components';
import { VerifyMessageRoot } from '../VerifyMessage/styled';

export const ObjectSettingsRoot = styled.div`
  display: flex;
  flex: 1;
  min-width: 0;
  overflow: auto;
  flex-direction: column;
  overflow: auto;
  border-left: 1px solid #d4d4d4;
  position: relative;

  .settings-content {
    position: relative;
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    overflow: auto;

    &.isLoading {
      overflow: hidden;
      opacity: 0.7;
    }

    .overlay {
      background-color: transparent;
    }
  }

  ${VerifyMessageRoot} {
    margin: 15px;
  }
`;
