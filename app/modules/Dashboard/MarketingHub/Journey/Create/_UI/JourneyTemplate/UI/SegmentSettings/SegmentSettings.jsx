import React, { useMemo } from 'react';
import { JOURNEY_OBJECT_TYPE } from '../../constant';
import { get } from 'lodash';
import SegmentPreview from '../../../../../../../../../containers/UIPreview/SegmentPreview';
import { useActiveObj } from '../../hooks/useActiveObj';
import { useGetObjects } from '../../hooks/useGetObjects';
import { useSelector } from 'react-redux';
import { selectMode } from '../../selector';

export const SegmentSettings = () => {
  const mode = useSelector(selectMode);

  const { activeObj, info, saveInfo, addErrors = () => {} } = useActiveObj();
  const { allObjects } = useGetObjects();
  const { segmentId } = activeObj;

  const segmemntIdPreview = useMemo(() => {
    let id = segmentId;
    if (mode === 'use' && info.isExist) {
      id = info.settings.segment_id || info.settings.segmentId;
    }
    return id;
  }, [info]);

  const isSegmentObject =
    get(activeObj, 'type') === JOURNEY_OBJECT_TYPE.segment;

  if (!isSegmentObject) return null;

  return (
    <SegmentPreview
      mode={mode}
      segmentId={segmemntIdPreview}
      settings={info.settings || saveInfo.settings}
      templateObjSettings={allObjects}
      isExistSegment={info.isExist}
      addErrors={addErrors}
    />
  );
};
