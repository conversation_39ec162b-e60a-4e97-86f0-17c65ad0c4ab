/* eslint-disable no-param-reassign */
import { orderBy } from 'lodash';
import { getObjectsFromConversion } from '../utils/journeyGoal';
import MOCK from './conversion.mock.json';

describe('Journey Goal Utils', () => {
  describe('function getObjectsFromConversion', () => {
    test.each(MOCK.getObjectsFromConversion)(
      'Case: %s',
      (_description, args, expected) => {
        let received = getObjectsFromConversion(args.conversionSettings);

        expected = orderBy(expected, obj => JSON.stringify(obj));
        received = orderBy(received, obj => JSON.stringify(obj));

        expect(received).toHaveLength(expected.length);
        expect(received).toEqual(expect.arrayContaining(expected));
      },
    );
  });
});
