import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectApplyState, selectMode } from '../selector';
import { updateApplyState } from '../actions';
import { INITIAL_STATE, MODE } from '../constant';
import { isFunction } from 'lodash';

export const useApplyTemplate = args => {
  const dispatch = useDispatch();

  const { onApply } = args;

  const mode = useSelector(selectMode);
  const applyState = useSelector(selectApplyState);

  useEffect(() => {
    if (mode !== MODE.Use) return;

    if (applyState.isApply && isFunction(onApply)) {
      onApply(applyState.data);

      dispatch(updateApplyState(INITIAL_STATE.applyState));
    }
  }, [applyState, mode]);
};
