import styled from 'styled-components';

export const VerifyMessageRoot = styled.div`
  padding: 18px 15px;
  display: flex;
  align-items: center;
  gap: 15px;
  border-radius: 3px;
  overflow: hidden;

  .message {
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
    white-space: nowrap;

    .link {
      cursor: pointer;
      color: #007bff;
      text-decoration: underline;
    }
  }

  &.info {
    background-color: #f2f9ff;
  }

  &.warning {
    background-color: #ffd8db;
  }
`;
