import styled from 'styled-components';
import COLOR from 'utils/colors';
import { VerifyStatusIconRoot } from '../VerifyStatusIcon/styled';

export const ListObjectRoot = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
  padding: 10px 0px 10px 15px;
`;

export const ListObjectItemsWrapper = styled.div`
  margin-bottom: 10px;
  margin-left: 12px;
  border-left: 2px solid #81bcf4;
  padding-left: 5px;
  display: flex;
  flex-direction: column;
  gap: 2px;
`;

export const ObjectItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 0 5px 10px;

  .left {
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 0;
    overflow: hidden;

    ${VerifyStatusIconRoot} {
      margin-right: 6px;
    }

    .obj-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &:not(.disabled) {
    .left {
      .obj-name {
        &:hover,
        &.active {
          cursor: pointer;
          font-weight: bold;
          color: ${COLOR.primary};
        }
      }
    }
  }

  &.disabled {
    cursor: not-allowed;
    color: #7f7f7f !important;
  }
`;

export const BulletPoint = styled.div`
  height: 10px;
  width: 10px;
  position: relative;
  flex-shrink: 0;

  &:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    top: 50%;
    background-color: #c2dbf0;
    transform: translateY(-50%);
  }
`;
