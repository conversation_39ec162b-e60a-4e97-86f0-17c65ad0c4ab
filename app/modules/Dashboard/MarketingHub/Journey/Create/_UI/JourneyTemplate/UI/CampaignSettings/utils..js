import { convertCamelcaseToSnakecase } from '../../../../../../../../../utils/common';

export const mapVariantsToSnakeCase = destinationData => {
  const data = {
    variants: [],
  };
  if (destinationData) {
    const { variants } = destinationData.metadata;
    variants.forEach(variant => {
      const newVariant = {};
      Object.keys(variant).forEach(key => {
        newVariant[convertCamelcaseToSnakecase(key)] = variant[key];
      });
      data.variants.push(newVariant);
    });
  }
  return data;
};
