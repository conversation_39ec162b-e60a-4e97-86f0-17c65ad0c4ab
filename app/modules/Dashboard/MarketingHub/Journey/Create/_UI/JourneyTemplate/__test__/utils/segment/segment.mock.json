{"getObjectsFromSegment": [["Segment MANUAL", {"segment_id": 5703528, "segment_type": 1, "segment_code": "sgmt_5703528", "segment_display": "[K] Segment From Conversion Event 2", "item_type_id": -1003, "insight_property_ids": [], "conditions": {"OR": [{"AND": [{"value": "12", "version": "v2", "operator": "greater_than_equal", "data_type": "number", "time_range": {"version": "v2", "calendar": {"endDate": {"date": "2024-03-20 23:59:59", "value": 0, "dateType": "fixed", "calculationDate": "days", "calculationType": "minus"}, "startDate": {"date": "2024-03-07 00:00:00", "value": 0, "dateType": "fixed", "calculationDate": "days", "calculationType": "minus"}}}, "aggregation": "event_counter", "condition_type": "perf_event", "event_action_id": -101, "event_category_id": -23, "event_tracking_name": "click_advertising", "insight_property_ids": [556300706, 556300709, 556301356, 556301357, 556301368, 556301392, 556301455, 556301460, 556301497, 556301507, 556301517, 556301550, 556301560, 556301672, 556301674, 556301676, 556301684, 556301686, 556301727, 556301728, 556301764, 556301766, 556301875, 556301954, 556301986, 556302006, 556302007, 556302008, 556302009, 556302010, 556302011, 556302012, 556302013, 556302014, 556302019, 556302020, 556302021, 556302092, 556302111, 556336465, 556352104, 556352325, 556352326, 556356103, 556380312, 556427943], "refine_with_properties": {"AND": []}}, {"value": [{"key": "fromDate", "date": "2024-03-21 16:59:56", "value": 0, "dateType": "today", "calculationDate": "days", "calculationType": "minus"}], "version": "v2", "operator": "greater_than_equal", "data_type": "number", "semantics": "MM DD YYYY", "time_unit": "DAY", "time_range": {"version": "v2", "calendar": {"endDate": {"date": "2024-03-20 23:59:59", "value": 0, "dateType": "fixed", "calculationDate": "days", "calculationType": "minus"}, "startDate": {"date": "2024-03-07 00:00:00", "value": 0, "dateType": "fixed", "calculationDate": "days", "calculationType": "minus"}}}, "aggregation": "unique_list_count", "condition_type": "perf_event", "event_action_id": -106, "compute_data_type": "datetime", "event_category_id": -11, "event_tracking_name": "purchase_product", "compute_item_type_id": -8, "insight_property_ids": [556300706, 556301356, 556301370, 556301372, 556301392, 556301429, 556301455, 556301460, 556301497, 556301517, 556301550, 556301560, 556301672, 556301673, 556301674, 556301676, 556301684, 556301686, 556301727, 556301728, 556301764, 556301766, 556301875, 556301954, 556301986, 556302006, 556302007, 556302008, 556302009, 556302010, 556302012, 556302013, 556302014, 556302019, 556302020, 556302021, 556302026, 556302111, 556336465, 556352104, 556352325, 556352326, 556356103, 556357615, 556427943, 556427944, 556434020], "compute_property_name": "date_created", "refine_with_properties": {"AND": [{"type": "item", "value": [{"key": "fromDate", "date": "2024-03-21 16:59:59", "value": 0, "dateType": "today", "calculationDate": "days", "calculationType": "minus"}], "version": "v2", "operator": "equals", "data_type": "datetime", "semantics": "MM DD YYYY", "time_unit": "DAY", "item_type_id": -3, "property_name": "date_created"}]}}]}, {"AND": [{"value": "123", "version": "v2", "operator": "contains", "data_type": "number", "time_range": {"version": "v2", "calendar": {"endDate": {"date": "2024-03-20 23:59:59", "value": 0, "dateType": "fixed", "calculationDate": "days", "calculationType": "minus"}, "startDate": {"date": "2024-03-07 00:00:00", "value": 0, "dateType": "fixed", "calculationDate": "days", "calculationType": "minus"}}}, "aggregation": "most_frequent", "condition_type": "perf_event", "event_action_id": 5703008, "compute_data_type": "string", "event_category_id": 1280229, "event_tracking_name": "conversion_t_test_create", "compute_item_type_id": 0, "insight_property_ids": [556351881], "compute_property_name": "atm_campaign", "refine_with_properties": {"AND": [{"type": "event", "value": "12", "operator": "contains", "data_type": "string", "item_type_id": null, "property_name": "goal_type"}, {"type": "item", "value": [{"key": "fromDate", "date": "2024-03-21 16:59:59", "value": 0, "dateType": "today", "calculationDate": "days", "calculationType": "minus"}], "version": "v2", "operator": "equals", "data_type": "datetime", "semantics": "MM DD YYYY", "time_unit": "DAY", "item_type_id": -1003, "property_name": "date_created"}]}}]}, {"AND": [{"value": [{"key": "fromDate", "date": "2024-03-21 17:00:09", "value": 0, "dateType": "today", "calculationDate": "days", "calculationType": "minus"}], "version": "v2", "operator": "equals", "data_type": "datetime", "semantics": "MM DD YYYY", "time_unit": "DAY", "item_type_id": -1007, "property_name": "last_updated", "condition_type": "comp_attr"}]}]}, "refine": {"top_n_checkbox": false}, "compute_schedule": {"type": "static", "endTime": {"type": "on_day", "onDay": {"day": "2024-03-28", "hour": "02:00"}, "numRunning": 1}, "isLoading": false, "repeatType": "none", "repeatValue": 1, "repeatOnValue": [], "repeatStartTime": {"day": "2024-03-21", "hour": "16:00"}}, "status": 1, "description": null, "process_status": "2", "c_user_id": **********, "u_user_id": **********, "ctime": "2024-03-21T09:00:21.653Z", "utime": "2024-03-21T09:00:21.653Z", "alert_setting": {"alertAccountIds": [**********], "alertScopes": {"success": [], "failure": ["email", "app_push"]}}, "owner_id": "**********", "view_id": null, "share_access": {"is_public": 0, "public_role": 3, "owner_id": **********, "list_access": [{"network_id": "33167", "object_id": "5703528", "object_type": "3", "user_id": "**********", "is_public": "0", "role": "1", "public_role": null, "allow_view": "1", "allow_edit": "1", "allow_comment": "1", "full_name": "KhangLNH", "email": "<EMAIL>", "avatar": "//c0-platform.ants.tech/avatar/2024/02/06/291dbg51fg.png", "status": "1"}]}, "segment_version": {"user_info": {"user_id": "**********", "full_name": "KhanhHV - Antsomi Team", "email": "<EMAIL>"}, "utime": "2024-03-21T09:00:21.653Z", "version": 2, "version_info": {"ip": "*************", "os": "Linux", "browser": "Chrome", "description": "has changed this segment", "current_version": true}}}, [{"events": [{"eventActionId": 5703008, "eventCategoryId": 1280229}], "itemPropertyName": "date_created", "itemTypeId": -1003, "pathConfig": [], "type": "bo_attribute"}, {"events": [], "itemPropertyName": "last_updated", "itemTypeId": -1007, "pathConfig": [{"path": "conditions.OR.2.AND.0.property_name", "value": "itemPropertyName"}], "type": "bo_attribute"}, {"events": [{"eventActionId": -106, "eventCategoryId": -11}], "itemPropertyName": "date_created", "itemTypeId": -3, "pathConfig": [], "type": "bo_attribute"}, {"events": [{"eventActionId": -106, "eventCategoryId": -11}], "itemPropertyName": "date_created", "itemTypeId": -8, "pathConfig": [], "type": "bo_attribute"}, {"events": [{"eventActionId": 5703008, "eventCategoryId": 1280229}], "itemTypeId": -1003, "pathConfig": [], "type": "business_object"}, {"events": [], "itemTypeId": -1007, "pathConfig": [{"path": "conditions.OR.2.AND.0.item_type_id", "value": "itemTypeId"}], "type": "business_object"}, {"events": [{"eventActionId": -106, "eventCategoryId": -11}], "itemTypeId": -3, "pathConfig": [], "type": "business_object"}, {"events": [{"eventActionId": -106, "eventCategoryId": -11}], "itemTypeId": -8, "pathConfig": [], "type": "business_object"}, {"eventActionId": -101, "eventCategoryId": -23, "pathConfig": [{"path": "conditions.OR.0.AND.0.event_action_id", "value": "eventActionId"}, {"path": "conditions.OR.0.AND.0.event_category_id", "value": "eventCategoryId"}, {"path": "conditions.OR.0.AND.0.event_tracking_name", "value": "eventTrackingName"}, {"path": "conditions.OR.0.AND.0.insight_property_ids", "value": "insightPropertyIds"}], "type": "event"}, {"eventActionId": -106, "eventCategoryId": -11, "pathConfig": [{"path": "conditions.OR.0.AND.1.event_action_id", "value": "eventActionId"}, {"path": "conditions.OR.0.AND.1.event_category_id", "value": "eventCategoryId"}, {"path": "conditions.OR.0.AND.1.event_tracking_name", "value": "eventTrackingName"}, {"path": "conditions.OR.0.AND.1.insight_property_ids", "value": "insightPropertyIds"}], "type": "event"}, {"eventActionId": 5703008, "eventCategoryId": 1280229, "pathConfig": [{"path": "conditions.OR.1.AND.0.event_action_id", "value": "eventActionId"}, {"path": "conditions.OR.1.AND.0.event_category_id", "value": "eventCategoryId"}, {"path": "conditions.OR.1.AND.0.event_tracking_name", "value": "eventTrackingName"}, {"path": "conditions.OR.1.AND.0.insight_property_ids", "value": "insightPropertyIds"}], "type": "event"}, {"eventActionId": -106, "eventCategoryId": -11, "itemPropertyName": "date_created", "itemTypeId": -3, "pathConfig": [{"path": "conditions.OR.0.AND.1.refine_with_properties.AND.0.item_type_id", "value": "itemTypeId"}, {"path": "conditions.OR.0.AND.1.refine_with_properties.AND.0.property_name", "value": "itemPropertyName"}], "type": "event_attribute"}, {"eventActionId": -106, "eventCategoryId": -11, "itemPropertyName": "date_created", "itemTypeId": -8, "pathConfig": [{"path": "conditions.OR.0.AND.1.compute_item_type_id", "value": "itemTypeId"}, {"path": "conditions.OR.0.AND.1.compute_property_name", "value": "itemPropertyName"}], "type": "event_attribute"}, {"eventActionId": 5703008, "eventCategoryId": 1280229, "eventPropertyName": "atm_campaign", "pathConfig": [{"path": "conditions.OR.1.AND.0.compute_property_name", "value": "eventPropertyName"}], "type": "event_attribute"}, {"eventActionId": 5703008, "eventCategoryId": 1280229, "eventPropertyName": "goal_type", "pathConfig": [{"path": "conditions.OR.1.AND.0.refine_with_properties.AND.0.property_name", "value": "eventPropertyName"}], "type": "event_attribute"}, {"eventActionId": 5703008, "eventCategoryId": 1280229, "itemPropertyName": "date_created", "itemTypeId": -1003, "pathConfig": [{"path": "conditions.OR.1.AND.0.refine_with_properties.AND.1.item_type_id", "value": "itemTypeId"}, {"path": "conditions.OR.1.AND.0.refine_with_properties.AND.1.property_name", "value": "itemPropertyName"}], "type": "event_attribute"}]], ["Segment BULK_UPLOAD", {"segment_id": 1391132, "segment_type": 2, "segment_code": "sgmt_1391132", "segment_display": "[c] test create segment with share access updatedd", "item_type_id": -1003, "insight_property_ids": [], "conditions": {"method": "create", "delimiter": "\t", "extension": "csv", "file_name": "-1003_**********_1698722544294.csv", "file_path": "/S3/cdp-import-export/import/segment/33167", "criteria_logic": "or", "mapping_fields": [{"operator": "exactly", "header_code": "ID", "header_index": 0, "item_attribute_code": "is_"}], "original_file_name": "import_data_sample_csv.csv"}, "refine": {"top_n_checkbox": false}, "compute_schedule": {"type": "dynamic", "endTime": {"type": "on_day", "onDay": {"day": "2023-11-08", "hour": "02:00"}, "numRunning": 1}, "isLoading": false, "repeatType": "none", "repeatValue": 1, "repeatOnValue": [], "repeatStartTime": {"day": "2023-10-31", "hour": "11:00"}}, "status": 1, "description": null, "process_status": "2", "c_user_id": **********, "u_user_id": **********, "ctime": "2023-10-31T03:22:55.069Z", "utime": "2023-10-31T07:26:07.195Z", "alert_setting": {"alertAccountIds": [**********], "alertScopes": {"success": [], "failure": ["email", "app_push"]}}, "owner_id": "**********", "view_id": null, "share_access": {"is_public": 0, "public_role": 3, "owner_id": **********, "list_access": [{"network_id": "33167", "object_id": "1391132", "object_type": "3", "user_id": "**********", "is_public": "0", "role": "1", "public_role": null, "allow_view": "1", "allow_edit": "1", "allow_comment": "1", "full_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "avatar": "//c0-platform.ants.tech/avatar/2023/10/19/aa91afyrqn.png", "status": "1"}]}, "segment_version": {"user_info": {"user_id": "**********", "full_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "utime": "2023-10-31T07:26:07.195Z", "version": 1, "version_info": {"ip": "**************", "os": "macOS", "browser": "Edge", "description": "has created this segment", "current_version": true}}}, [{"type": "business_object", "itemTypeId": -1003, "pathConfig": [], "events": []}, {"type": "bo_attribute", "itemPropertyName": "is_", "itemTypeId": -1003, "pathConfig": [{"path": "conditions.mapping_fields.0.item_attribute_code", "value": "itemPropertyName"}], "events": []}]], ["Predictive modal", {"segment_id": 5083605, "segment_type": 5, "segment_code": "sgmt_5083605", "segment_display": "[Untitled Lifecycle Stages#2023-12-12 08:47:52 ádfas fasf] La<PERSON>d Buyer", "item_type_id": -1003, "insight_property_ids": [], "conditions": {"OR": [{"AND": [{"value": "Lapsed Buyer", "operator": "equals", "data_type": "string", "table_code": "untitled_lifecycle_stages20231212_08_4918", "property_name": "customer_stage", "condition_type": "default_attr"}]}]}, "refine": {"top_n_checkbox": false}, "compute_schedule": {"type": "static"}, "status": 1, "description": null, "process_status": "7", "c_user_id": **********, "u_user_id": **********, "ctime": "2023-12-12T02:32:40.263Z", "utime": "2023-12-12T02:32:40.263Z", "alert_setting": {"alertAccountIds": [**********], "alertScopes": {"success": [], "failure": ["email", "app_push"]}}, "owner_id": "**********", "view_id": null, "object_info": {"id": *********, "name": "Untitled Lifecycle Stages#2023-12-12 08:49:18"}, "share_access": {"is_public": 0, "public_role": 3, "owner_id": **********, "list_access": [{"network_id": "33167", "object_id": "5083605", "object_type": "3", "user_id": "**********", "is_public": "0", "role": "1", "public_role": null, "allow_view": "1", "allow_edit": "1", "allow_comment": "1", "full_name": "Đạ<PERSON>", "email": "<EMAIL>", "avatar": "//c0-platform.ants.tech/avatar/2021/09/17/0xgbkurioo.png", "status": "1"}]}, "segment_version": {"user_info": {"user_id": "**********", "full_name": "Đạ<PERSON>", "email": "<EMAIL>"}, "utime": "2023-12-12T02:32:40.263Z", "version": 1, "version_info": {"ip": "*************", "description": "has created this segment", "current_version": true}}}, [{"type": "predictive_model", "modelId": *********, "pathConfig": []}]]], "segmentToCreateSegmentData": [["Segment MANUAL", {"createData": {"insightPropertyIds": [], "segmentType": 1, "conditions": {"OR": [{"AND": [{"condition_type": "perf_event", "event_category_id": -23, "event_action_id": -101, "event_tracking_name": "click_advertising", "data_type": "number", "insight_property_ids": [556300706, 556300709, 556301356, 556301357, 556301368, 556301392, 556301455, 556301460, 556301497, 556301507, 556301517, 556301550, 556301560, 556301672, 556301674, 556301676, 556301684, 556301686, 556301727, 556301728, 556301764, 556301766, 556301875, 556301954, 556301986, 556302006, 556302007, 556302008, 556302009, 556302010, 556302011, 556302012, 556302013, 556302014, 556302019, 556302020, 556302021, 556302092, 556302111, 556336465, 556352104, 556352325, 556352326, 556356103, 556380312, 556427943], "operator": "greater_than_equal", "value": "123", "refine_with_properties": {"AND": [{"type": "event", "property_name": "atm_campaign", "data_type": "string", "item_type_id": null, "operator": "matches", "value": ["conversion_campaign"]}, {"type": "item", "property_name": "name", "data_type": "string", "item_type_id": -1, "operator": "matches", "value": ["Personal computer", "Robot"]}]}, "time_range": {"version": "v2", "calendar": {"startDate": {"date": "2024-03-06 00:00:00", "calculationDate": "days", "value": 0, "calculationType": "minus", "dateType": "fixed"}, "endDate": {"date": "2024-03-19 23:59:59", "calculationDate": "days", "value": 0, "calculationType": "minus", "dateType": "fixed"}}}, "aggregation": "sum", "version": "v2", "compute_property_name": "sum__purchase", "compute_item_type_id": -1003, "compute_data_type": "number"}]}, {"AND": [{"condition_type": "comp_attr", "property_name": "date_created", "item_type_id": -1003, "data_type": "datetime", "operator": "before_date", "value": [{"date": "2024-03-20 14:54:20", "dateType": "today", "calculationDate": "days", "calculationType": "minus", "value": 0, "key": "fromDate"}], "time_unit": "DAY", "version": "v2", "semantics": "MM DD YYYY"}]}]}, "itemTypeId": -1003, "includeAnonymous": 1, "computeSchedule": {"type": "static", "isLoading": false, "repeatType": "none", "repeatOnValue": [], "repeatValue": 1, "repeatStartTime": {"day": "2024-03-20", "hour": "14:00"}, "endTime": {"type": "on_day", "numRunning": 1, "onDay": {"day": "2024-03-27", "hour": "02:00"}}}, "refine": {"top_n_checkbox": false}, "segmentDisplay": "[K] Create Segment 1", "description": "", "shareAccess": {"is_public": 0, "public_role": null, "list_access": [{"allow_comment": 1, "avatar": "//c0-platform.ants.tech/avatar/2022/01/21/kbil4orr9t.png", "email": "<EMAIL>", "full_name": "<PERSON><PERSON><PERSON>", "user_id": "**********", "allow_edit": 1, "allow_view": 1, "role": 1}]}, "viewId": null, "alertSetting": {"alertAccountIds": [**********], "alertScopes": {"success": [], "failure": ["email", "app_push"]}}, "isBuild": true, "versionInfo": {"os": "Linux", "browser": "Chrome", "description": "has created this segment"}, "isNewVersion": true}, "detailData": {"segment_id": 5701314, "segment_type": 1, "segment_code": "sgmt_5701314", "segment_display": "[K] Create Segment 1", "item_type_id": -1003, "insight_property_ids": [], "conditions": {"OR": [{"AND": [{"value": "123", "version": "v2", "operator": "greater_than_equal", "data_type": "number", "time_range": {"version": "v2", "calendar": {"endDate": {"date": "2024-03-19 23:59:59", "value": 0, "dateType": "fixed", "calculationDate": "days", "calculationType": "minus"}, "startDate": {"date": "2024-03-06 00:00:00", "value": 0, "dateType": "fixed", "calculationDate": "days", "calculationType": "minus"}}}, "aggregation": "sum", "condition_type": "perf_event", "event_action_id": -101, "compute_data_type": "number", "event_category_id": -23, "event_tracking_name": "click_advertising", "compute_item_type_id": -1003, "insight_property_ids": [556300706, 556300709, 556301356, 556301357, 556301368, 556301392, 556301455, 556301460, 556301497, 556301507, 556301517, 556301550, 556301560, 556301672, 556301674, 556301676, 556301684, 556301686, 556301727, 556301728, 556301764, 556301766, 556301875, 556301954, 556301986, 556302006, 556302007, 556302008, 556302009, 556302010, 556302011, 556302012, 556302013, 556302014, 556302019, 556302020, 556302021, 556302092, 556302111, 556336465, 556352104, 556352325, 556352326, 556356103, 556380312, 556427943], "compute_property_name": "sum__purchase", "refine_with_properties": {"AND": [{"type": "event", "value": ["conversion_campaign"], "operator": "matches", "data_type": "string", "item_type_id": null, "property_name": "atm_campaign"}, {"type": "item", "value": ["Personal computer", "Robot"], "operator": "matches", "data_type": "string", "item_type_id": -1, "property_name": "name"}]}}]}, {"AND": [{"value": [{"key": "fromDate", "date": "2024-03-20 14:54:20", "value": 0, "dateType": "today", "calculationDate": "days", "calculationType": "minus"}], "version": "v2", "operator": "before_date", "data_type": "datetime", "semantics": "MM DD YYYY", "time_unit": "DAY", "item_type_id": -1003, "property_name": "date_created", "condition_type": "comp_attr"}]}]}, "refine": {"top_n_checkbox": false}, "compute_schedule": {"type": "static", "endTime": {"type": "on_day", "onDay": {"day": "2024-03-27", "hour": "02:00"}, "numRunning": 1}, "isLoading": false, "repeatType": "none", "repeatValue": 1, "repeatOnValue": [], "repeatStartTime": {"day": "2024-03-20", "hour": "14:00"}}, "status": 1, "description": null, "process_status": "1", "c_user_id": **********, "u_user_id": **********, "ctime": "2024-03-20T06:58:14.918Z", "utime": "2024-03-20T06:58:14.918Z", "alert_setting": {"alertAccountIds": [**********], "alertScopes": {"success": [], "failure": ["email", "app_push"]}}, "owner_id": "**********", "view_id": null, "share_access": {"is_public": 0, "public_role": 3, "owner_id": **********, "list_access": [{"network_id": "33167", "object_id": "5701314", "object_type": "3", "user_id": "**********", "is_public": "0", "role": "1", "public_role": null, "allow_view": "1", "allow_edit": "1", "allow_comment": "1", "full_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "avatar": "//c0-platform.ants.tech/avatar/2022/01/21/kbil4orr9t.png", "status": "1"}]}, "segment_version": {"user_info": {"user_id": "**********", "full_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "utime": "2024-03-20T06:58:14.918Z", "version": 1, "version_info": {"ip": "*************", "os": "Linux", "browser": "Chrome", "description": "has created this segment", "current_version": true}}}}]]}