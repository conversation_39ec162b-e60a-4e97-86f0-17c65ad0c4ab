/* eslint-disable import/no-cycle */
/* eslint-disable indent */
/* eslint-disable camelcase */
import { first, isArray, isFunction } from 'lodash';
import { getCurrentUserId } from '../../../../../../../../utils/web/cookie';
import { getBrowser, getClientOS } from '../../../utils.version';
import { OBJECT_TYPE } from '../constant';
import { addEventAttribute, groupObjects, loopFilters } from './helper';
import { snakeToCamel } from '../../../../../../../../utils/common';
import SegmentServices from 'services/Segment';

export const CONFIG_SEGMENT_TYPE = {
  MANUAL: 1,
  BULK_UPLOAD: 2,
  PREDICTIVE_SEGMENT: 5,
  EMPTY_SEGMENT: 3,
  UNSUBSCRIBE_SEGMENT: 4,
};

export const getSegmentFromFilters = (filters, options = {}) => {
  const { filtersPropName = 'filters', itemTypeId, customPathConfig } = options;

  const segments = [];

  loopFilters(filters, {
    eachCondition: (condition, conditionPath) => {
      const { value } = condition;

      const path = `${filtersPropName}.${conditionPath}`;

      let pathConfig = [
        {
          path: `${path}.code`,
          value: 'segmentCode',
        },
        {
          path: isArray(value) ? `${path}.value.0` : `${path}.value`,
          value: 'segmentId',
        },
      ];

      if (isFunction(customPathConfig)) {
        pathConfig = customPathConfig({
          condition,
          conditionPath: path,
          defaultPathConfig: pathConfig,
        });
      }

      segments.push({
        segmentId: isArray(value) ? first(value) : value,

        pathConfig,
        ...(itemTypeId && { itemTypeId }),
      });
    },
  });

  return segments;
};

export const segmentToCreateSegmentData = segment => {
  const result = {
    alertSetting: segment.alert_setting,
    computeSchedule: segment.compute_schedule,
    conditions: segment.conditions,
    description: segment.description || '',
    includeAnonymous: 1,
    insightPropertyIds: segment.insight_property_ids,
    isBuild: true,
    isNewVersion: true,
    itemTypeId: segment.item_type_id,
    refine: segment.refine,
    segmentDisplay: segment.segment_display,
    segmentType: segment.segment_type,
    shareAccess: {
      is_public: 0,
      list_access: [
        {
          allow_comment: 1,
          allow_edit: 1,
          allow_view: 1,
          user_id: getCurrentUserId(),
        },
      ],
      public_role: null,
    },
    versionInfo: {
      os: getClientOS(),
      browser: getBrowser(),
      description: 'has created this segment',
    },
    viewId: segment.view_id,
  };

  return result;
};

export const getObjectsFromSegment = segment => {
  const { conditions, view_id: viewId, segment_type: segmentType } = segment;

  let objects = [];

  if (viewId) {
    objects.push({
      type: OBJECT_TYPE.objectView,
      viewId: +viewId,
      pathConfig: [
        {
          value: 'viewId',
          path: 'view_id',
          valueType: 'number',
        },
      ],
    });
  }

  switch (+segmentType) {
    case CONFIG_SEGMENT_TYPE.MANUAL: {
      loopFilters(conditions, {
        eachCondition: (condition, conditionPath) => {
          const path = `conditions.${conditionPath}`;

          const { condition_type } = condition;

          if (condition_type === 'comp_attr') {
            const { item_type_id: itemTypeId } = condition;

            objects.push({
              type: OBJECT_TYPE.dataObject,
              itemTypeId,
              pathConfig: [
                {
                  value: 'itemTypeId',
                  path: `${path}.item_type_id`,
                },
              ],
            });

            objects.push({
              type: OBJECT_TYPE.dataObjectAttr,
              itemPropertyName: condition.property_name,
              itemTypeId,
              pathConfig: [
                {
                  value: 'itemPropertyName',
                  path: `${path}.property_name`,
                },
              ],
            });
          }

          if (condition_type === 'perf_event') {
            const {
              event_action_id: eventActionId,
              event_category_id: eventCategoryId,
              aggregation,
            } = condition;

            const eventInfo = { eventActionId, eventCategoryId };

            objects.push({
              type: OBJECT_TYPE.event,
              pathConfig: [
                {
                  path: `${path}.event_action_id`,
                  value: 'eventActionId',
                },
                {
                  path: `${path}.event_category_id`,
                  value: 'eventCategoryId',
                },
                {
                  path: `${path}.event_tracking_name`,
                  value: 'eventTrackingName',
                },
                {
                  path: `${path}.insight_property_ids`,
                  value: 'insightPropertyIds',
                },
              ],
              ...eventInfo,
            });

            if (aggregation && aggregation !== 'event_counter') {
              const {
                compute_item_type_id: itemTypeId,
                compute_property_name: propertyName,
              } = condition;

              objects = addEventAttribute({
                objects,
                eventInfo,
                itemTypeId,
                propertyName,
                pathConfig: itemTypeId
                  ? [
                      {
                        path: `${path}.compute_item_type_id`,
                        value: 'itemTypeId',
                      },
                      {
                        path: `${path}.compute_property_name`,
                        value: 'itemPropertyName',
                      },
                    ]
                  : [
                      {
                        path: `${path}.compute_property_name`,
                        value: 'eventPropertyName',
                      },
                    ],
              });
            }

            condition.refine_with_properties.AND.forEach(
              (eventPerfCondition, idx) => {
                const refinePropertiesPath = `${path}.refine_with_properties.AND.${idx}`;

                const {
                  item_type_id: itemTypeId,
                  property_name: propertyName,
                } = eventPerfCondition;

                objects = addEventAttribute({
                  objects,
                  eventInfo,
                  itemTypeId,
                  propertyName,
                  pathConfig: itemTypeId
                    ? [
                        {
                          path: `${refinePropertiesPath}.item_type_id`,
                          value: 'itemTypeId',
                        },
                        {
                          path: `${refinePropertiesPath}.property_name`,
                          value: 'itemPropertyName',
                        },
                      ]
                    : [
                        {
                          path: `${refinePropertiesPath}.property_name`,
                          value: 'eventPropertyName',
                        },
                      ],
                });
              },
            );
          }
        },
      });
      break;
    }
    case CONFIG_SEGMENT_TYPE.BULK_UPLOAD: {
      const { item_type_id: itemTypeId } = segment;

      objects.push({
        type: OBJECT_TYPE.dataObject,
        itemTypeId,
        pathConfig: [],
      });

      conditions.mapping_fields.forEach((field, idx) => {
        objects.push({
          type: OBJECT_TYPE.dataObjectAttr,
          itemTypeId,
          itemPropertyName: field.item_attribute_code,
          pathConfig: [
            {
              path: `conditions.mapping_fields.${idx}.item_attribute_code`,
              value: 'itemPropertyName',
            },
          ],
        });
      });

      break;
    }
    case CONFIG_SEGMENT_TYPE.PREDICTIVE_SEGMENT: {
      const { object_info: objectInfo } = segment;

      objects.push({
        type: OBJECT_TYPE.predictiveModel,
        modelId: objectInfo.id,
        pathConfig: [],
      });

      break;
    }
    default:
      break;
  }

  objects = groupObjects(objects);

  return objects;
};

export const createSegment = async settings => {
  // Maping here

  const newSettings = snakeToCamel(settings);

  if (newSettings.description === 0) {
    newSettings.description = null;
  }
  if (newSettings.viewId === 0) {
    newSettings.viewId = null;
  }
  if (newSettings.insightPropertyIds === 0) {
    newSettings.insightPropertyIds = [];
  }

  const res = await SegmentServices.create(newSettings);

  let isSuccess = false;

  if (res.code === 200) {
    isSuccess = true;
  }

  return { success: isSuccess, res: res.data };
};
