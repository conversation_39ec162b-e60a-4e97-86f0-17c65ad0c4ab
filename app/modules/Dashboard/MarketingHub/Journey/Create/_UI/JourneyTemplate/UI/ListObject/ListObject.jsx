/* eslint-disable indent */
import React, { useEffect } from 'react';
import {
  BulletPoint,
  ListObjectItemsWrapper,
  ListObjectRoot,
  ObjectItem,
} from './styled';
import { MODE } from '../../constant';
import { MainObjectTitle } from '../MainObjectTitle';
import { isEmpty, pick } from 'lodash';
import CheckBox from 'components/Molecules/CheckBox';
import { Tooltip } from '@material-ui/core';
import { changeActiveObj } from '../../actions';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { useGetObjects } from '../../hooks/useGetObjects';
import { useUpdateAllowChange } from '../../hooks/useUpdateAllowChange';
import { OBJ_PRIMARY_KEYS, PARENT_OBJECTS } from '../../utils/config';
import { useActiveObj } from '../../hooks/useActiveObj';
import classNames from 'classnames';
import { VerifyStatusIcon } from '../VerifyStatusIcon';
import {
  composeObjectInfo,
  getVerifyStatus,
  isDisabledObject,
  isEqualObject,
} from '../../utils/helper';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectMode,
  selectOrderedObjects,
  selectVerification,
} from '../../selector';
import IconXlabColor from 'components/common/UIIconXlabColor';

const PATH =
  'MarketingHub/Journey/Create/_UI/JourneyTemplate/UI/ListObject/ListObject';

export const ListObject = () => {
  const dispatch = useDispatch();

  // const { state, dispatch: stateDispatch } = useJourneyTemplate();

  const verification = useSelector(selectVerification);
  const mode = useSelector(selectMode);
  const { orderedObjects } = useSelector(selectOrderedObjects);

  const { templateObjSettings } = verification;

  const { activeObj } = useActiveObj();

  const { mapObjects, objects, getObjectsByType } = useGetObjects();

  const { updateAllowChangeByObject } = useUpdateAllowChange();

  useEffect(() => {
    if (activeObj) return;

    let defaultActObj = null;

    orderedObjects.forEach(obj => {
      const isDisable = isDisabledObject({
        mode,
        object: obj,
        listObject: orderedObjects,
        templateObjSettings,
      });

      if (!isDisable && !defaultActObj) {
        defaultActObj = obj;

        dispatch(changeActiveObj(defaultActObj));
      }
    });
  }, [activeObj, dispatch, templateObjSettings, orderedObjects, mode]);

  const renderListObjectByType = (objectType, index) => {
    if (isEmpty(mapObjects)) return null;

    const title = (
      <MainObjectTitle key={objectType} objectType={objectType} index={index} />
    );

    const { objects: objectsByType } = getObjectsByType(objectType, true);

    if (isEmpty(objectsByType)) return null;

    const renderObj = (obj, depth = 0) => {
      // if (!isVisibleObject(obj, objects)) return null;

      let rightSideWarning = false;
      let showAllowChange = true;

      const objChilds = obj.children || [];
      const indent = depth > 1 ? (depth - 1) * 10 : 0;

      const key = JSON.stringify(pick(obj, OBJ_PRIMARY_KEYS[obj.type]));

      const isActive = isEqualObject(obj, activeObj);

      const { info } = composeObjectInfo({
        object: obj,
        objects,
        templateObjSettings,
        mode,
      });

      const { name, warningMsg } = info;

      const disabled = info.disabled || warningMsg;

      if (mode === MODE.Save && warningMsg) {
        rightSideWarning = true;
        showAllowChange = false;
      }

      return (
        <React.Fragment key={key}>
          <ObjectItem
            className={classNames('block', {
              disabled,
            })}
          >
            <div style={{ paddingLeft: indent }} className={classNames('left')}>
              {mode === MODE.Use && (
                <VerifyStatusIcon
                  status={getVerifyStatus(obj)}
                  disabled={disabled}
                />
              )}

              {depth >= 1 && <BulletPoint />}

              <Tooltip title={warningMsg || name} placement="top">
                <div
                  role="button"
                  tabIndex={0}
                  className={classNames('obj-name', {
                    active: isActive,
                  })}
                  {...!disabled && {
                    onKeyDown: () => dispatch(changeActiveObj(obj)),
                    onClick: () => dispatch(changeActiveObj(obj)),
                  }}
                >
                  {warningMsg || name}
                </div>
              </Tooltip>
            </div>

            {mode === MODE.Save && (
              <div className="right">
                {rightSideWarning && (
                  <IconXlabColor name="warning" fontSize="14px" />
                )}

                {showAllowChange && (
                  <CheckBox
                    checked={obj.allowChange}
                    disabled={!obj.editable}
                    onClick={() => {
                      updateAllowChangeByObject(obj, !obj.allowChange);
                    }}
                  />
                )}
              </div>
            )}
          </ObjectItem>

          {objChilds.map(childrenObj => renderObj(childrenObj, depth + 1))}
        </React.Fragment>
      );
    };

    return (
      <>
        {title}

        {!!objectsByType.length && (
          <ListObjectItemsWrapper>
            {objectsByType.map(obj => renderObj(obj))}
          </ListObjectItemsWrapper>
        )}
      </>
    );
  };

  const renderListObject = () => {
    if (isEmpty(mapObjects)) return null;

    const content = [];

    let index = 1;

    PARENT_OBJECTS.forEach(objectType => {
      const objectsByType = mapObjects[objectType];

      if (Array.isArray(objectsByType) && objectsByType.length) {
        content.push(renderListObjectByType(objectType, index));

        index += 1;
      }
    });

    return content;
  };

  return (
    <ErrorBoundary path={PATH}>
      <ListObjectRoot>{renderListObject()}</ListObjectRoot>
    </ErrorBoundary>
  );
};
