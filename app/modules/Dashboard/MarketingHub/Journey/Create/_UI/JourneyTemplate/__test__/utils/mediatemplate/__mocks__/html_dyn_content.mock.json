[["Event dyn tag", {"journeySettings": {"unsubscribeSegmentType": "visitor", "triggerType": "event_based", "triggerEvent": {"eventActionId": -102, "eventCategoryId": -20, "insightPropertyIds": [556300706, 556301356, 556301357, 556301368, 556301370, 556301372, 556301397, 556301423, 556301424, 556301455, 556301457, 556301460, 556301497, 556301507, 556301517, 556301518, 556301550, 556301560, 556301654, 556301672, 556301673, 556301674, 556301676, 556301684, 556301686, 556301727, 556301728, 556301764, 556301766, 556301875, 556301954, 556301985, 556301988, 556302006, 556302007, 556302008, 556302009, 556302010, 556302011, 556302012, 556302013, 556302014, 556302019, 556302020, 556302021, 556302026, 556302092, 556336465, 556352104, 556352326, 556356103, 556356959, 556380312, 556427944, 556430427, 556434020]}}, "html": "There are #{event.event_id.format(\"828fc70gu3gexq34e9bg-iwozh865\")} many data-compare=\"#{event.ad_zone.u_user_id}\" variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc. right;\">#{event.atm_source}</span> Unlocke"}, {"objects": [{"eventActionId": -102, "eventCategoryId": -20, "pathConfig": [], "type": "event"}, {"eventPropertyName": "event_id", "pathConfig": [{"path": "html", "pattern": "event.event_id.format(\"828fc70gu3gexq34e9bg-iwozh865\")", "value": "event.{eventPropertyName}.format(\"828fc70gu3gexq34e9bg-iwozh865\")"}], "type": "event_attribute"}, {"itemTypeName": "ad_zone", "pathConfig": [], "type": "business_object"}, {"itemPropertyName": "u_user_id", "itemTypeName": "ad_zone", "pathConfig": [], "type": "bo_attribute"}, {"eventActionId": -102, "eventCategoryId": -20, "itemPropertyName": "u_user_id", "itemTypeName": "ad_zone", "pathConfig": [{"path": "html", "pattern": "event.ad_zone.u_user_id", "value": "event.{itemTypeName}.{itemPropertyName}"}], "type": "event_attribute"}, {"eventPropertyName": "atm_source", "pathConfig": [{"path": "html", "pattern": "event.atm_source", "value": "event.{eventPropertyName}"}], "type": "event_attribute"}]}], ["Pool dyn tag", {"journeySettings": {"unsubscribeSegmentType": "visitor", "triggerType": "event_based", "triggerEvent": {"eventActionId": -102, "eventCategoryId": -20, "insightPropertyIds": [556300706, 556301356, 556301357, 556301368, 556301370, 556301372, 556301397, 556301423, 556301424, 556301455, 556301457, 556301460, 556301497, 556301507, 556301517, 556301518, 556301550, 556301560, 556301654, 556301672, 556301673, 556301674, 556301676, 556301684, 556301686, 556301727, 556301728, 556301764, 556301766, 556301875, 556301954, 556301985, 556301988, 556302006, 556302007, 556302008, 556302009, 556302010, 556302011, 556302012, 556302013, 556302014, 556302019, 556302020, 556302021, 556302026, 556302092, 556336465, 556352104, 556352326, 556356103, 556356959, 556380312, 556427944, 556430427, 556434020]}}, "html": "There are #{promotion_code.qc_review_pool_169__update_3.pool_id.format(\"828fc70gu3gexq34e9bg-799ie7wq\")}"}, {"objects": [{"type": "promotion_pool", "poolCode": "qc_review_pool_169__update_3", "pathConfig": [{"path": "html", "value": "promotion_code.{poolCode}.pool_id.format(\"828fc70gu3gexq34e9bg-799ie7wq\")", "pattern": "promotion_code.qc_review_pool_169__update_3.pool_id.format(\"828fc70gu3gexq34e9bg-799ie7wq\")"}]}]}], ["Customer, Visitor dyn tag", {"journeySettings": {"unsubscribeSegmentType": "visitor", "triggerType": "event_based", "triggerEvent": {"eventActionId": -102, "eventCategoryId": -20, "insightPropertyIds": [556300706, 556301356, 556301357, 556301368, 556301370, 556301372, 556301397, 556301423, 556301424, 556301455, 556301457, 556301460, 556301497, 556301507, 556301517, 556301518, 556301550, 556301560, 556301654, 556301672, 556301673, 556301674, 556301676, 556301684, 556301686, 556301727, 556301728, 556301764, 556301766, 556301875, 556301954, 556301985, 556301988, 556302006, 556302007, 556302008, 556302009, 556302010, 556302011, 556302012, 556302013, 556302014, 556302019, 556302020, 556302021, 556302026, 556302092, 556336465, 556352104, 556352326, 556356103, 556356959, 556380312, 556427944, 556430427, 556434020]}}, "html": "There #{customer.date_created.format(\"828fc70gu3gexq34e9bg-3yat78s7\")} are #{visitor.user_id} some one #{customer.name}"}, {"objects": [{"type": "business_object", "itemTypeId": -1003, "pathConfig": []}, {"type": "business_object", "itemTypeId": -1007, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -1003, "itemPropertyName": "name", "pathConfig": [{"path": "html", "value": "customer.{itemPropertyName}", "pattern": "customer.name"}]}, {"type": "bo_attribute", "itemTypeId": -1003, "itemPropertyName": "date_created", "pathConfig": [{"path": "html", "value": "customer.{itemPropertyName}.format(\"828fc70gu3gexq34e9bg-3yat78s7\")", "pattern": "customer.date_created.format(\"828fc70gu3gexq34e9bg-3yat78s7\")"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "user_id", "pathConfig": [{"path": "html", "value": "visitor.{itemPropertyName}", "pattern": "visitor.user_id"}]}]}], ["Content source dyn tag", {"journeySettings": {"unsubscribeSegmentType": "visitor", "triggerType": "event_based", "triggerEvent": {"eventActionId": -102, "eventCategoryId": -20, "insightPropertyIds": [556430427, 556434020]}}, "templateSettings": {"contentSources": [{"level": "parent", "filters": {"OR": []}, "groupId": "csg5q2cl", "ranking": {"type": "algorithms", "custom": "", "algorithms": {"sort": "mix", "value": [{"value": "seen_products", "quantity": 5}], "filters": []}}, "fallback": "product_bought_most_often", "groupName": "Group 1", "itemTypeId": 1, "itemTypeName": "product", "itemTypeDisplay": "Product", "maxIndex": 3}]}, "html": "There are some text #{groups.csg5q2cl[1].price.format(\"gyb8x09m4dvum80s5xz1-jzw8p23u\")}. And another tag #{groups.csg5q2cl[1].fe_test_datetime__2}"}, {"objects": [{"itemTypeId": 1, "pathConfig": [], "type": "business_object"}, {"itemPropertyName": "price", "itemTypeId": 1, "pathConfig": [{"path": "html", "pattern": "groups.csg5q2cl[1].price.format(\"gyb8x09m4dvum80s5xz1-jzw8p23u\")", "value": "groups.csg5q2cl[1].{itemPropertyName}.format(\"gyb8x09m4dvum80s5xz1-jzw8p23u\")"}], "type": "bo_attribute"}, {"itemPropertyName": "fe_test_datetime__2", "itemTypeId": 1, "pathConfig": [{"path": "html", "pattern": "groups.csg5q2cl[1].fe_test_datetime__2", "value": "groups.csg5q2cl[1].{itemPropertyName}"}], "type": "bo_attribute"}]}], ["Table dyn tags", {"journeySettings": {"unsubscribeSegmentType": "visitor", "triggerType": "event_based", "triggerEvent": {"eventActionId": -102, "eventCategoryId": -20, "insightPropertyIds": [556430427, 556434020]}}, "templateSettings": {"boTableSettings": {"efk9fzp751y6wvx3jf02": {"sort": "date_created", "columns": [{"value": "date_created", "colType": "dimension", "dataType": "datetime"}, {"value": "audience_type", "colType": "metric", "dataType": "number", "displayFormat": {"type": "number"}}], "filters": {"OR": []}, "showTop": 10, "direction": "desc", "rowStyles": ["style='background-color: #ffffff'", "style='background-color: #ffffff'"], "itemTypeId": -100, "showMissingDataType": ""}, "nsvm5r9kbyxokkvi3j9z": {"sort": "sku", "columns": [{"value": "sku", "colType": "dimension", "dataType": "string"}, {"value": "sku", "colType": "dimension", "dataType": "string"}, {"value": "price", "colType": "metric", "dataType": "number", "displayFormat": {"type": "number"}}], "filters": {"OR": []}, "showTop": 10, "direction": "desc", "rowStyles": ["style='background-color: #ffffff'", "style='background-color: #ffffff'"], "itemTypeId": 1, "showMissingDataType": ""}}}, "html": "er-box; word-break: break-word; font-weight: inherit; text-align: right; vertical-align: middle; width: 19.7143%; text-overflow: ellipsis; overflow: hidden;\">Price</th></tr></thead><tbody style=\"color: rgb(97, 97, 97); font-family: Roboto; font-weight: 400; font-size: 12px; text-transform: none; text-decoration: none; line-height: 1; letter-spacing: 0px; font-style: normal; text-align: left;\">#BEGIN_FOR_TABLE_nsvm5r9kbyxokkvi3j9z#<tr data-ats-row-style=\"true\"><td class=\"cell--nowrap\" style=\"padding: 10px; border: 1px solid rgb(204, 204, 204); box-sizing: border-box; word-break: break-all; vertical-align: middle; text-align: center; text-overflow: ellipsis; overflow: hidden;\">#{item.ats_row_index}</td><td class=\"cell--nowrap\" style=\"padding: 10px; border: 1px solid rgb(204, 204, 204); box-sizing: border-box; word-break: break-all; vertical-align: middle; text-align: left; text-overflow: ellipsis; overflow: hidden;\">#{item.sku || \"\"}</td><td class=\"cell--nowrap\" style=\"padding: 10px; border: 1px solid rgb(204, 204, 204); box-sizing: border-box; word-break: break-all; vertical-align: middle; text-align: left; text-overflow: ellipsis; overflow: hidden;\">#{item.sku || \"\"}</td><td class=\"cell--nowrap\" style=\"padding: 10px; border: 1px solid rgb(204, 204, 204); box-sizing: border-box; word-break: break-all; vertical-align: middle; text-align: right; text-overflow: ellipsis; overflow: hidden;\">#{item.price || \"\"}</td></tr>#END_FOR_TABLE_nsvm5r9kbyxokkvi3j9z#</tbody></table></div></div></div></div></div><div class=\"template-element template-el-2 Element mt-element\"><div id=\"block-wrapper-efk9fzp751y6wvx3jf02\" class=\"styled__StyledBlockWrapper-sc-1iyxbro-0 upwxK --inside --invisible !ants-border-none\" style=\"height: auto;\"><div id=\"template-TableElemen1left;\">#BEGIN_FOR_TABLE_efk9fzp751y6wvx3jf02#<tr data-ats-row-style=\"true\"><td class=\"cell--nowrap\" style=\"padding: 10px; border: 1px solid rgb(204, 204, 204); box-sizing: border-box; word-break: break-all; vertical-align: middle; text-align: center; text-overflow: ellipsis; overflow: hidden;\">#{item.ats_row_index}</td><td class=\"cell--nowrap\" style=\"padding: 10px; border: 1px solid rgb(204, 204, 204); box-sizing: border-box; word-break: break-all; vertical-align: middle; text-align: left; text-overflow: ellipsis; overflow: hidden;\">#{item.date_created || \"\"}</td><td class=\"cell--nowrap\" style=\"padding: 10px; border: 1px solid rgb(204, 204, 204); box-sizing: border-box; word-break: break-all; vertical-align: middle; text-align: right; text-overflow: ellipsis; overflow: hidden;\">#{item.audience_type || \"\"}</td></tr>#END_FOR_TABLE_efk9fzp751y6wvx3jf02#</tbody></table></div></div></div></div></div></div></div><div></div></div></div></div></div></div></div></div></div></div>\n  "}, {"objects": [{"itemTypeId": 1, "pathConfig": [], "type": "business_object"}, {"itemPropertyName": "sku", "itemTypeId": 1, "pathConfig": [{"path": "html", "regex": {"source": "#BEGIN_FOR_TABLE_nsvm5r9kbyxokkvi3j9z#([\\s\\S]*?)#{item\\.sku([\\s\\S]*?)#END_FOR_TABLE_nsvm5r9kbyxokkvi3j9z#", "flags": ""}, "value": "#BEGIN_FOR_TABLE_nsvm5r9kbyxokkvi3j9z#$1#{item.{itemPropertyName}}$2#END_FOR_TABLE_nsvm5r9kbyxokkvi3j9z#"}], "type": "bo_attribute"}, {"itemPropertyName": "price", "itemTypeId": 1, "pathConfig": [{"path": "html", "regex": {"source": "#BEGIN_FOR_TABLE_nsvm5r9kbyxokkvi3j9z#([\\s\\S]*?)#{item\\.price([\\s\\S]*?)#END_FOR_TABLE_nsvm5r9kbyxokkvi3j9z#", "flags": ""}, "value": "#BEGIN_FOR_TABLE_nsvm5r9kbyxokkvi3j9z#$1#{item.{itemPropertyName}}$2#END_FOR_TABLE_nsvm5r9kbyxokkvi3j9z#"}], "type": "bo_attribute"}, {"itemTypeId": -100, "pathConfig": [], "type": "business_object"}, {"itemPropertyName": "date_created", "itemTypeId": -100, "pathConfig": [{"path": "html", "regex": {"source": "#BEGIN_FOR_TABLE_efk9fzp751y6wvx3jf02#([\\s\\S]*?)#{item\\.date_created([\\s\\S]*?)#END_FOR_TABLE_efk9fzp751y6wvx3jf02#", "flags": ""}, "value": "#BEGIN_FOR_TABLE_efk9fzp751y6wvx3jf02#$1#{item.{itemPropertyName}}$2#END_FOR_TABLE_efk9fzp751y6wvx3jf02#"}], "type": "bo_attribute"}, {"itemPropertyName": "audience_type", "itemTypeId": -100, "pathConfig": [{"path": "html", "regex": {"source": "#BEGIN_FOR_TABLE_efk9fzp751y6wvx3jf02#([\\s\\S]*?)#{item\\.audience_type([\\s\\S]*?)#END_FOR_TABLE_efk9fzp751y6wvx3jf02#", "flags": ""}, "value": "#BEGIN_FOR_TABLE_efk9fzp751y6wvx3jf02#$1#{item.{itemPropertyName}}$2#END_FOR_TABLE_efk9fzp751y6wvx3jf02#"}], "type": "bo_attribute"}]}], ["Game with data-wheel setup", {"journeySettings": {"unsubscribeSegmentType": "visitor", "triggerType": "event_based", "triggerEvent": {"eventActionId": -102, "eventCategoryId": -20, "insightPropertyIds": [556430427, 556434020]}}, "html": "\n  <head>ground: transparent; border-style: none; border-color: rgb(0, 0, 0); box-shadow: none; width: auto; height: auto; max-width: 100%; position: relative; z-index: 0; align-items: flex-start; inset: 0px;\"><div class=\"styled__ColumnsBlockWrapper-sc-tne0x7-0 fwRyjx template-row template-row-1 mt-row Row row__block\" style=\"align-items: flex-start;\"><div class1 Element mt-element\"><div id=\"block-wrapper-azhszk9b9oqf7ztv0r3k\" class=\"styled__StyledBlockWrapper-sc-1iyxbro-0 upwxK --inside --invisible !ants-border-none\" style=\"height: auto;\"><div id=\"template-ShakeAndWinElement--wrapper--azhszk9b9oqf7ztv0r3k\" class=\"styled__ContentBlock-sc-1iyxbro-3 iTLRHS content-block template-ShakeAndWinElement--wrapper\" style=\"border-radius: 0px; border-width: 0px; padding: 0px; margin: 0px; background: transparent; border-style: none; border-color: rgb(0, 0, 0); box-shadow: none; width: auto; height: auto; max-width: 100%; position: relative; z-index: 0; inset: 0px;\">  <div id=\"template-ShakeAndWinElement--azhszk9b9oqf7ztv0r3k\" class=\"styled__ShakeAndWinBlockWrapper-sc-1dqpyc8-0 jetXHS template-imge-content template-ShakeAndWinElement--content\" data-wheel=\"[{&quot;label&quot;:&quot;Almost&quot;,&quot;internalCode&quot;:&quot;almost_test&quot;,&quot;win&quot;:true,&quot;view&quot;:&quot;success&quot;,&quot;winChance&quot;:33.3,&quot;pool&quot;:true,&quot;code&quot;:&quot;pool_trinh_xem&quot;,&quot;limitSpinning&quot;:{&quot;type&quot;:&quot;out_of_code&quot;},&quot;frequency&quot;:&quot;lifetime&quot;,&quot;cappingLevel&quot;:&quot;journey&quot;,&quot;codeAttr&quot;:&quot;pool_id&quot;,&quot;poolId&quot;:470621},{&quot;label&quot;:&quot;25% Off&quot;,&quot;internalCode&quot;:&quot;25_percent_off_3&quot;,&quot;win&quot;:true,&quot;view&quot;:&quot;success&quot;,&quot;winChance&quot;:33.3,&quot;pool&quot;:false,&quot;code&quot;:&quot;123&quot;,&quot;limitSpinning&quot;:{&quot;type&quot;:&quot;unlimited&quot;},&quot;frequency&quot;:&quot;lifetime&quot;,&quot;cappingLevel&quot;:&quot;journey&quot;},{&quot;label&quot;:&quot;Section 1&quot;,&quot;internalCode&quot;:&quot;section_1&quot;,&quot;win&quot;:true,&quot;view&quot;:&quot;success&quot;,&quot;winChance&quot;:33.3,&quot;pool&quot;:true,&quot;code&quot;:&quot;test_remaining&quot;,&quot;limitSpinning&quot;:{&quot;type&quot;:&quot;out_of_code&quot;},&quot;frequency&quot;:null,&quot;cappingLevel&quot;:null,&quot;codeAttr&quot;:&quot;audience_type&quot;,&quot;poolId&quot;:1246049}]\" style=\"text-align: left;\"><div class=\"styled__ShakeAndWinContainer-sc-1dqpyc8-1 hLBsXJ animate__animated animate__fadeIn\"><img srent--wrapper\" style=\"border-radius: 0px; border-width: 0px; padding: 0px; margin: 0px; background: transparent; border-style: none; border-color: rgb(0, 0, 0); box-shadow: none; width: auto; height: auto; max-width: 100%; position: relative; z-index: 0; inset: 0px;\">  <div id=\"template-SurpriseTreasureHuntElement--aks1edb2rds0gfex3z7c\" class=\"styled__SurpriseTreasureHuntBlockWrapper-sc-pz436g-0 dldHRH template-imge-content template-SurpriseTreasureHuntElement--content\" data-wheel=\"[{&quot;label&quot;:&quot;Free shipping&quot;,&quot;internalCode&quot;:&quot;free_shipping_1&quot;,&quot;win&quot;:true,&quot;view&quot;:&quot;success&quot;,&quot;winChance&quot;:33.3,&quot;pool&quot;:true,&quot;code&quot;:&quot;thanh_target&quot;,&quot;limitSpinning&quot;:{&quot;type&quot;:&quot;out_of_code&quot;},&quot;frequency&quot;:&quot;lifetime&quot;,&quot;cappingLevel&quot;:&quot;journey&quot;,&quot;codeAttr&quot;:&quot;pool_id&quot;,&quot;poolId&quot;:523541},{&quot;label&quot;:&quot;10% Off&quot;,&quot;internalCode&quot;:&quot;10_percent_off_2&quot;,&quot;win&quot;:true,&quot;view&quot;:&quot;success&quot;,&quot;winChance&quot;:33.3,&quot;pool&quot;:true,&quot;code&quot;:&quot;ytest&quot;,&quot;limitSpinning&quot;:{&quot;type&quot;:&quot;out_of_code&quot;},&quot;frequency&quot;:&quot;lifetime&quot;,&quot;cappingLevel&quot;:&quot;journey&quot;,&quot;codeAttr&quot;:&quot;story_id&quot;,&quot;poolId&quot;:1317026},{&quot;label&quot;:&quot;Spin again&quot;,&quot;internalCode&quot;:&quot;spin_again_test&quot;,&quot;win&quot;:true,&quot;view&quot;:&quot;success&quot;,&quot;winChance&quot;:33.3,&quot;pool&quot;:false,&quot;code&quot;:&quot;23&quot;,&quot;limitSpinning&quot;:{&quot;type&quot;:&quot;unlimited&quot;},&quot;frequency&quot;:null,&quot;cappingLevel&quot;:null}]\"><div rows=\"3\" width=\"100\" class=\"styled__GiftContainer-sc-pz436g-1 bpuAip animate__animated animate__fadeIn gift-container\" style=\"border-radius: 0px; border-width: 0px; padding: 0px; margin: 0px; background-image: unset; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; backgroutry again later.</div></div></div></div></div></div></div><div></div></div></div></div></div></div></div></div></div></div>\n  "}, {"objects": [{"pathConfig": [{"path": "html", "regex": {"flags": "", "source": "data-wheel=\"([^\"]*)poolId([^:]*):470621([^\"]*)\""}, "value": "data-wheel=\"$1poolId$2:{poolId}$3\""}], "poolId": 470621, "type": "promotion_pool"}, {"pathConfig": [{"path": "html", "regex": {"flags": "", "source": "data-wheel=\"([^\"]*)poolId([^:]*):1246049([^\"]*)\""}, "value": "data-wheel=\"$1poolId$2:{poolId}$3\""}], "poolId": 1246049, "type": "promotion_pool"}, {"pathConfig": [{"path": "html", "regex": {"flags": "", "source": "data-wheel=\"([^\"]*)poolId([^:]*):523541([^\"]*)\""}, "value": "data-wheel=\"$1poolId$2:{poolId}$3\""}], "poolId": 523541, "type": "promotion_pool"}, {"pathConfig": [{"path": "html", "regex": {"flags": "", "source": "data-wheel=\"([^\"]*)poolId([^:]*):1317026([^\"]*)\""}, "value": "data-wheel=\"$1poolId$2:{poolId}$3\""}], "poolId": 1317026, "type": "promotion_pool"}]}]]