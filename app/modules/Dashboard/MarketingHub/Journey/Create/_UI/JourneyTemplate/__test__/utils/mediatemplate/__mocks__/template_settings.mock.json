{"settings": {"id": "25451", "name": "[K] JT / Objects 2", "type": "pop_up", "fonts": {"Poppins": [500, 400], "Montserrat": [400]}, "status": "active", "actions": [{"type": "redirect", "event": "click", "pageId": "optin", "options": {"url": "https://antsomi.com/", "copy": "", "name": "success", "pass": false, "close": false, "phone": "", "track": true}, "scripts": "", "selector": "#template-ButtonElement--1up5ooqcfalzrto2xk6v", "element_id": "template-ButtonElement--1up5ooqcfalzrto2xk6v", "element_type": "button"}, {"type": "", "event": "shake-and-win", "config": {"shakeType": "vertical_shake", "imageAfter": false, "imageBefore": "https://sandbox-st-media-template.antsomi.com/upload/2024/04/03/6caa3c59-9192-4e14-99ea-9b1dfc53a136.jpg", "timeToShake": 10, "imageToShake": false, "translationTime": 10, "triggerSettings": {"by": "click_on_this_block", "referral": "", "timeToDelay": 0, "shakeTrigger": "system", "reminderNotification": "Let shake your phone"}}, "pageId": "optin", "options": {"url": "", "copy": "", "name": "", "pass": false, "close": false, "phone": "", "track": false}, "scripts": "", "selector": "#template-ShakeAndWinElement--6x4w4r6c37qrlok8k464", "element_id": "template-ShakeAndWinElement--6x4w4r6c37qrlok8k464", "element_type": "shake-and-win"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {}, "textData": {}}, "selector": "#template-TextElement--wrapper--8k6nc2yx8a2i5khdrtej", "element_id": "template-TextElement--wrapper--8k6nc2yx8a2i5khdrtej", "element_type": "text"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {}, "textData": {}}, "selector": "#template-TextElement--wrapper--91g9p8fgw63yyesclylj", "element_id": "template-TextElement--wrapper--91g9p8fgw63yyesclylj", "element_type": "text"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {}, "textData": {"0kp91194": {"type": "event-attribute", "event": "471165:-15", "source": [*********], "attribute": {"type": 2, "label": "Start date", "value": "campaign.start_date", "status": 1, "children": [], "dataType": "datetime", "itemTypeId": -1010, "itemTypeName": "campaign", "propertyName": "start_date", "eventPropertySyntax": "dims.campaign_id", "datetimeFormatSettings": {"type": "datetime", "language": "en", "hasDateFormat": true, "hasTimeFormat": true, "dateParseFormat": "MM/DD/YYYY", "dateParseOption": "long", "timeParseFormat": "12hour", "timeParseOption": "medium", "dateFormatString": "MMMM DD, YYYY h:mm:ss A"}}, "mappingKey": "96v2drcu3j9qxqb3c9w3-0kp91194", "mappingFields": "event.campaign.start_date"}, "0y9izutl": {"type": "event-attribute", "event": "-102:-20", "source": [*********], "attribute": {"type": 1, "label": "Duration", "value": "duration", "status": 1, "children": [], "dataType": "number", "itemTypeId": null, "propertyName": "duration", "numberFormatSettings": {"type": "number", "decimal": ".", "grouping": ",", "isCompact": true, "prefixType": "code", "currencyCode": "USD", "decimalPlaces": 3}}, "mappingKey": "96v2drcu3j9qxqb3c9w3-0y9izutl", "mappingFields": "event.duration"}, "17jyiory": {"type": "event-attribute", "event": "-102:-20", "source": [*********], "attribute": {"type": 1, "label": "UTM Campaign", "value": "utm_campaign", "status": 1, "children": [], "dataType": "string", "itemTypeId": null, "propertyName": "utm_campaign"}, "mappingKey": "96v2drcu3j9qxqb3c9w3-17jyiory", "mappingFields": "event.utm_campaign"}, "54yaad4r": {"type": "event-attribute", "event": "-102:-20", "source": [*********], "attribute": {"type": 2, "label": "Price", "value": "product.price", "status": 1, "children": [], "dataType": "number", "itemTypeId": 1, "itemTypeName": "product", "propertyName": "price", "eventPropertySyntax": "dims.product_id", "numberFormatSettings": {"type": "number", "decimal": ".", "grouping": ",", "isCompact": true, "prefixType": "code", "currencyCode": "USD", "decimalPlaces": 3}}, "mappingKey": "96v2drcu3j9qxqb3c9w3-54yaad4r", "mappingFields": "event.product.price"}}}, "selector": "#template-TextElement--wrapper--96v2drcu3j9qxqb3c9w3", "element_id": "template-TextElement--wrapper--96v2drcu3j9qxqb3c9w3", "element_type": "text"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {}, "textData": {}}, "selector": "#template-TextElement--wrapper--9xfu3l2w4lm7kt5zaw9j", "element_id": "template-TextElement--wrapper--9xfu3l2w4lm7kt5zaw9j", "element_type": "text"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {}, "textData": {}}, "selector": "#template-TextElement--wrapper--hbs50hit7jrq28f7bqvz", "element_id": "template-TextElement--wrapper--hbs50hit7jrq28f7bqvz", "element_type": "text"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {}, "textData": {}}, "selector": "#template-TextElement--wrapper--jmlzwex0vi8w21h2co5n", "element_id": "template-TextElement--wrapper--jmlzwex0vi8w21h2co5n", "element_type": "text"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {}, "textData": {}}, "selector": "#template-TextElement--wrapper--k97ukvu61wwv7xq4p3gx", "element_id": "template-TextElement--wrapper--k97ukvu61wwv7xq4p3gx", "element_type": "text"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {}, "textData": {"8dcpolfm": {"type": "visitor-attribute", "attribute": {"label": "User ID", "value": "user_id", "status": 1, "dataType": "string", "disabled": false}, "mappingKey": "khcqxvqun6tt1ah5ly2r-8dcpolfm", "mappingFields": "visitor.user_id"}, "lermzl87": {"type": "content-source::csgoxel3::1", "index": 1, "attribute": {"key": "name", "label": "Name", "value": "name", "status": "1", "dataType": "string", "disabled": false, "itemTypeName": "product"}, "mappingKey": "khcqxvqun6tt1ah5ly2r-lermzl87", "mappingFields": "groups.csgoxel3[1].name"}}}, "selector": "#template-TextElement--wrapper--khcqxvqun6tt1ah5ly2r", "element_id": "template-TextElement--wrapper--khcqxvqun6tt1ah5ly2r", "element_type": "text"}, {"type": "redirect", "event": "click", "pageId": "optin", "options": {"url": "https://antsomi.com/", "copy": "", "name": "success", "pass": false, "close": false, "phone": "", "track": true}, "scripts": "", "selector": "#template-ButtonElement--kncaalz077o5pnis4uwq", "element_id": "template-ButtonElement--kncaalz077o5pnis4uwq", "element_type": "button"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {}, "textData": {}}, "selector": "#template-TextElement--wrapper--mocmhcyty481xjjbjmu2", "element_id": "template-TextElement--wrapper--mocmhcyty481xjjbjmu2", "element_type": "text"}, {"type": "", "event": "slide", "config": {"editor": "SlideShowElementEditor", "autoSlide": false, "columnGap": 20, "component": "SlideShowElement", "skipItems": 1, "slideLoop": true, "slideDelay": 5, "totalItems": 5, "displayItems": 3, "displayStyle": "full", "slideDuration": 1, "slideDirection": "horizontal", "slideTransition": "slide"}, "pageId": "optin", "options": {"url": "", "name": "", "pass": false, "track": true}, "scripts": "", "selector": "#template-SlideShowElement--qhxhu3e9uatbzucykfix", "element_id": "template-SlideShowElement--qhxhu3e9uatbzucykfix", "element_type": "slide-show"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {}, "textData": {"sm0y0az8": {"pool": "qc_review_pool_169__update_3", "type": "promotion-code", "attribute": {"label": "Last used time", "value": "last_used_time", "status": "1", "dataType": "datetime", "disabled": false, "datetimeFormatSettings": {"type": "datetime", "language": "en", "hasDateFormat": true, "hasTimeFormat": true, "dateParseFormat": "MM/DD/YYYY", "dateParseOption": "medium", "timeParseFormat": "12hour", "timeParseOption": "medium", "dateFormatString": "MMM DD, YYYY h:mm:ss A"}}, "mappingKey": "rlh76ytbpb8dxbjgglfk-sm0y0az8", "mappingFields": "promotion_code.qc_review_pool_169__update_3.last_used_time"}}}, "selector": "#template-TextElement--wrapper--rlh76ytbpb8dxbjgglfk", "element_id": "template-TextElement--wrapper--rlh76ytbpb8dxbjgglfk", "element_type": "text"}, {"type": "", "pageId": "optin", "dynamic": {"linkData": {"f9cjiv9x": {"text": "Product", "type": "content-source::csgoxel3::1", "index": 2, "title": "Link To Product", "linkType": "dynamic", "attribute": {"key": "page_url", "label": "Page url", "value": "page_url", "disabled": false}, "openNewTab": true}}, "textData": {"nxju010f": {"type": "event-attribute", "event": "471165:-15", "source": [*********], "attribute": {"type": 2, "label": "Campaign name", "value": "campaign.campaign_name", "status": 1, "children": [], "dataType": "string", "itemTypeId": -1010, "itemTypeName": "campaign", "propertyName": "campaign_name", "eventPropertySyntax": "dims.campaign_id"}, "mappingKey": "rqoae3u8o9qfljjffrm5-nxju010f", "mappingFields": "event.campaign.campaign_name"}}}, "selector": "#template-TextElement--wrapper--rqoae3u8o9qfljjffrm5", "element_id": "template-TextElement--wrapper--rqoae3u8o9qfljjffrm5", "element_type": "text"}, {"type": "redirect", "event": "click", "pageId": "optin", "options": {"url": "https://antsomi.com/", "copy": "", "name": "success", "pass": false, "close": false, "phone": "", "track": true}, "scripts": "", "selector": "#template-ButtonElement--saxzge0425yc46hz6qs5", "element_id": "template-ButtonElement--saxzge0425yc46hz6qs5", "element_type": "button"}, {"type": "redirect", "event": "click", "pageId": "optin", "options": {"url": "https://antsomi.com/", "copy": "", "name": "success", "pass": false, "close": false, "phone": "", "track": true}, "scripts": "", "selector": "#template-ButtonElement--tcygjgq6460qg7b4rfek", "element_id": "template-ButtonElement--tcygjgq6460qg7b4rfek", "element_type": "button"}, {"type": "", "event": "surprise-treasure-hunt", "config": {"metrics": {"rows": 3, "columns": 3}, "cellImages": {"images": null, "imageAllUrlAfter": "", "imageAllUrlBefore": ""}, "dimensions": {"boxes": {"width": 100}, "cells": {"gap": 0}}, "animationHover": "highlight", "translationTime": 10, "animationClicked": {"type": "shake", "imagePreviewUrl": ""}}, "pageId": "optin", "options": {"url": "", "copy": "", "name": "", "pass": false, "close": false, "phone": "", "track": false}, "scripts": "", "selector": "#template-SurpriseTreasureHuntElement--vbp2eh85ubgw25bu6zy7", "element_id": "template-SurpriseTreasureHuntElement--vbp2eh85ubgw25bu6zy7", "element_type": "surprise-treasure-hunt"}, {"type": "redirect", "event": "click", "pageId": "optin", "options": {"url": "https://antsomi.com/", "copy": "", "name": "success", "pass": false, "close": false, "phone": "", "track": true}, "scripts": "", "selector": "#template-ButtonElement--vk0v31e8iknxlpqgfyio", "element_id": "template-ButtonElement--vk0v31e8iknxlpqgfyio", "element_type": "button"}, {"type": "", "pageId": "success", "dynamic": {"linkData": {}, "textData": {}}, "selector": "#template-TextElement--wrapper--a1hcnm8cmea9yzl0rs87", "element_id": "template-TextElement--wrapper--a1hcnm8cmea9yzl0rs87", "element_type": "text"}], "folders": [], "options": {"gaId": "", "uaId": "", "mobile": 0, "bgClose": false, "chatbot": [], "mcTitle": "", "gamified": 1, "countdown": 0, "pageSlide": false, "slideOpen": false, "autoToggle": 60, "enableLock": false, "lockMethod": "obfuscate", "mcImageUrl": "", "customColors": ["#f7da64", "#8912dd", "#ed1515", "#230439", "#d11a66", "#ffbd64", "#f1ab96", "#824ccd", "#5858e9", "#57b8c2"], "delayShowing": {"enable": false, "conditions": [{"type": "percented_scroll", "value": 20}]}, "smartSuccess": false, "lazyLoadImage": false, "slidePosition": "right", "enableWebFonts": 1, "seenCookieType": "after_x_days", "slideDirection": "replace", "slideToggleState": false, "thumbnailCapture": true, "autoCloseTemplate": {"at": "template", "after": 10, "enable": false}, "mcMetaDescription": "", "showAffiliateLink": 1, "slideDisplayStyle": "text", "successCookieType": "after_x_days", "analyticsAccountId": 0, "seenCookieDuration": 30, "attentionActivation": false, "floatingBarPosition": "bottom", "crossSubdomainCookies": 0, "interactionCookieType": "after_x_days", "successCookieDuration": 365, "interactionCookieDuration": 30}, "customJS": {"rawEditorOutput": ""}, "prefixEl": "mt", "rulesets": [{"name": "Default Ruleset", "actions": [{"type": "show-campaign", "value": "optin"}]}], "namespace": "template", "parent_id": null, "updated_at": "2024-04-17T07:33:38.238Z", "enableFonts": true, "utmTracking": {"term": "", "medium": "", "source": "", "content": "", "campaign": ""}, "contentSources": [{"level": "parent", "filters": {"OR": []}, "groupId": "csgoxel3", "ranking": {"type": "algorithms", "custom": "", "algorithms": {"sort": "mix", "value": [{"value": "seen_products", "quantity": 5}], "filters": []}}, "fallback": "product_viewed_most_often", "maxIndex": 3, "groupName": "Group 1", "itemTypeId": 1, "itemTypeName": "product", "itemTypeDisplay": "Product"}, {"filters": {"OR": [{"AND": [{"value": "2024-04-12 00:00:00", "column": "last_updated", "extend": [], "operator": "before_date", "data_type": "datetime", "value_type": "normal", "condition_type": "comp_attr"}, {"value": null, "column": "bd_month", "extend": [], "operator": "equals", "data_type": "number", "value_type": "event", "condition_type": "comp_attr", "event_metadata": {"useBo": false, "item_type_id": null, "item_type_name": null, "event_action_id": 12, "event_category_id": -11, "item_property_name": "duration", "event_tracking_name": "computed_property_list product", "item_property_label": "Duration", "insight_property_ids": [*********, *********, *********], "event_property_syntax": "duration_time"}, "visitor_metadata": {}, "customer_metadata": {}}, {"value": null, "column": "full_name", "extend": [], "operator": "contains", "data_type": "string", "value_type": "event", "condition_type": "comp_attr", "event_metadata": {"useBo": false, "item_type_id": -1009, "item_type_name": "story", "event_action_id": 41, "event_category_id": 5, "item_property_name": "story_name", "event_tracking_name": "sign_in info", "item_property_label": "Journey >> Story name", "insight_property_ids": [*********, *********], "event_property_syntax": "dims.story_id"}, "visitor_metadata": {}, "customer_metadata": {}}]}, {"AND": [{"value": "10", "column": "user_id", "extend": [], "operator": "contains", "data_type": "string", "value_type": "normal", "condition_type": "comp_attr", "event_metadata": {"item_type_id": null, "item_type_name": null, "event_action_id": null, "event_category_id": null, "item_property_name": null, "event_tracking_name": null, "insight_property_id": null, "item_property_label": null, "insight_property_ids": null, "event_property_syntax": null}, "visitor_metadata": {"item_type_id": -1007, "item_type_name": "visitors", "item_property_name": ""}, "customer_metadata": {"item_type_id": -1003, "item_type_name": "customers", "item_property_name": ""}}, {"value": null, "column": "ba_test_custom", "extend": [], "operator": "contains", "data_type": "text", "value_type": "visitor", "condition_type": "comp_attr", "event_metadata": {}, "visitor_metadata": {"item_type_id": -1007, "item_type_name": "visitors", "item_property_name": "full_name"}, "customer_metadata": {}}]}]}, "groupId": "csg7tmix", "ranking": {"type": "algorithms", "custom": "", "algorithms": {"sort": "mix", "value": [{"value": "get_top", "sort_by": "last_updated", "quantity": 5, "sort_type": "desc"}], "filters": []}}, "fallback": "product_viewed_most_often", "maxIndex": 0, "groupName": "Group 2", "itemTypeId": -1007, "itemTypeName": "users", "itemTypeDisplay": "Visitor"}], "trackingModule": {"data": {"term": "", "medium": "", "source": "", "content": "", "campaign": ""}, "mode": "utm"}, "boTableSettings": {"y55a3yfmcgd3zu9qskqn": {"sort": "date_created", "columns": [{"value": "name", "colType": "dimension", "dataType": "string"}, {"value": "date_created", "colType": "dimension", "dataType": "datetime"}, {"value": "last_updated", "colType": "dimension", "dataType": "datetime"}, {"value": "testcache2", "colType": "metric", "dataType": "number", "displayFormat": {"type": "number"}}, {"value": "huytcx", "colType": "metric", "dataType": "number", "displayFormat": {"type": "number"}}], "filters": {"OR": [{"AND": [{"value": 1713286800000, "column": "last_updated", "extend": [], "operator": "equals", "data_type": "datetime", "value_type": "normal", "condition_type": "comp_attr"}]}, {"AND": [{"value": null, "column": "id", "extend": [], "operator": "greater_than", "data_type": "number", "value_type": "event", "condition_type": "comp_attr", "event_metadata": {"useBo": false, "item_type_id": -3, "item_type_name": "browser", "event_action_id": 471165, "event_category_id": 5, "item_property_name": "id", "event_tracking_name": "submit info", "item_property_label": "Browser >> ID", "insight_property_ids": [*********], "event_property_syntax": "browser_id"}, "visitor_metadata": {}, "customer_metadata": {}}]}]}, "showTop": 10, "direction": "desc", "rowStyles": ["style='background-color: #ffffff'", "style='background-color: #ffffff'"], "itemTypeId": -3, "showMissingDataType": ""}}, "objectRelationships": [{"object_id": -3, "object_name": null, "object_type": 1, "object_property_name": "id"}, {"object_id": -1010, "object_name": null, "object_type": 1, "object_property_name": "start_date"}, {"object_id": null, "object_name": null, "object_type": 1, "object_property_name": "duration"}, {"object_id": null, "object_name": null, "object_type": 1, "object_property_name": "utm_campaign"}, {"object_id": 1, "object_name": null, "object_type": 1, "object_property_name": "price"}, {"object_id": -1007, "object_name": null, "object_type": 1, "object_property_name": "user_id"}, {"object_id": -100, "object_name": null, "object_type": 1, "object_property_name": "last_used_time"}, {"object_id": -1010, "object_name": null, "object_type": 1, "object_property_name": "campaign_name"}, {"object_id": 1, "object_name": null, "object_type": 1, "object_property_name": "id"}, {"object_id": -1007, "object_name": null, "object_type": 1, "object_property_name": "last_updated"}, {"object_id": -1007, "object_name": null, "object_type": 1, "object_property_name": "bd_month"}, {"object_id": -1007, "object_name": null, "object_type": 1, "object_property_name": "full_name"}, {"object_id": -1009, "object_name": null, "object_type": 1, "object_property_name": "story_name"}, {"object_id": -1007, "object_name": null, "object_type": 1, "object_property_name": "ba_test_custom"}, {"object_id": -1007, "object_name": null, "object_type": 1, "object_property_name": "id"}]}, "objects": [{"type": "business_object", "itemTypeId": 1, "pathConfig": [{"path": "template_setting.contentSources.0.itemTypeId", "value": "itemTypeId"}, {"path": "template_setting.contentSources.0.itemTypeName", "value": "itemTypeName"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "product_viewed_most_often", "pathConfig": [{"path": "template_setting.contentSources.0.fallback", "value": "itemPropertyName"}]}, {"type": "business_object", "itemTypeId": -1007, "pathConfig": [{"path": "template_setting.contentSources.1.itemTypeId", "value": "itemTypeId"}, {"path": "template_setting.contentSources.1.itemTypeName", "value": "itemTypeName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "product_viewed_most_often", "pathConfig": [{"path": "template_setting.contentSources.1.fallback", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "last_updated", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.0.column", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "bd_month", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.1.column", "value": "itemPropertyName"}]}, {"eventActionId": 12, "eventCategoryId": -11, "type": "event", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.event_action_id", "value": "eventActionId"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.event_category_id", "value": "eventCategoryId"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.event_property_syntax", "value": "eventPropertySyntax"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.event_tracking_name", "value": "eventTrackingName"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.insight_property_ids", "value": "insightPropertyIds"}]}, {"eventActionId": 12, "eventCategoryId": -11, "type": "event_attribute", "eventPropertyName": "duration", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.1.event_metadata.item_property_name", "value": "eventPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "full_name", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.2.column", "value": "itemPropertyName"}]}, {"eventActionId": 41, "eventCategoryId": 5, "type": "event", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.event_action_id", "value": "eventActionId"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.event_category_id", "value": "eventCategoryId"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.event_property_syntax", "value": "eventPropertySyntax"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.event_tracking_name", "value": "eventTrackingName"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.insight_property_ids", "value": "insightPropertyIds"}]}, {"type": "business_object", "itemTypeId": -1009, "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.item_type_id", "value": "itemTypeId"}, {"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.item_type_name", "value": "itemTypeName"}]}, {"type": "bo_attribute", "itemTypeId": -1009, "itemPropertyName": "story_name", "pathConfig": []}, {"eventActionId": 41, "eventCategoryId": 5, "type": "event_attribute", "itemTypeId": -1009, "itemPropertyName": "story_name", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.0.AND.2.event_metadata.item_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "user_id", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.1.AND.0.column", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "ba_test_custom", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.1.AND.1.column", "value": "itemPropertyName"}]}, {"type": "business_object", "itemTypeId": -1007, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "full_name", "pathConfig": [{"path": "template_setting.contentSources.1.filters.OR.1.AND.1.visitor_metadata.item_property_name", "value": "itemPropertyName"}, {"path": "template_setting.contentSources.1.filters.OR.1.AND.1.visitor_metadata.item_type_id", "value": "itemTypeId"}]}, {"type": "event", "eventActionId": 471165, "eventCategoryId": -15, "pathConfig": [{"path": "template_setting.actions.4.dynamic.textData.0kp91194.event", "pattern": "471165:-15", "value": "{eventActionId}:{eventCategoryId}"}, {"path": "template_setting.actions.4.dynamic.textData.0kp91194.source", "value": "insightPropertyIds"}]}, {"type": "business_object", "itemTypeId": -1010, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -1010, "itemPropertyName": "start_date", "pathConfig": []}, {"type": "event_attribute", "eventActionId": 471165, "eventCategoryId": -15, "itemPropertyName": "start_date", "itemTypeId": -1010, "pathConfig": [{"path": "template_setting.actions.4.dynamic.textData.0kp91194.attribute.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "template_setting.actions.4.dynamic.textData.0kp91194.attribute.itemTypeId", "value": "itemTypeId"}, {"path": "template_setting.actions.4.dynamic.textData.0kp91194.attribute.itemTypeName", "value": "itemTypeName"}, {"path": "template_setting.actions.4.dynamic.textData.0kp91194.attribute.propertyName", "value": "itemPropertyName"}, {"path": "template_setting.actions.4.dynamic.textData.0kp91194.attribute.value", "value": "itemPropertyName"}, {"path": "template_setting.actions.4.dynamic.textData.0kp91194.mappingFields", "pattern": "event.campaign.start_date", "value": "event.{itemTypeName}.{itemPropertyName}"}]}, {"type": "event", "eventActionId": -102, "eventCategoryId": -20, "pathConfig": [{"path": "template_setting.actions.4.dynamic.textData.0y9izutl.event", "pattern": "-102:-20", "value": "{eventActionId}:{eventCategoryId}"}, {"path": "template_setting.actions.4.dynamic.textData.0y9izutl.source", "value": "insightPropertyIds"}]}, {"type": "event_attribute", "eventActionId": -102, "eventCategoryId": -20, "eventPropertyName": "duration", "pathConfig": [{"path": "template_setting.actions.4.dynamic.textData.0y9izutl.attribute.propertyName", "value": "eventPropertyName"}, {"path": "template_setting.actions.4.dynamic.textData.0y9izutl.attribute.value", "value": "eventPropertyName"}, {"path": "template_setting.actions.4.dynamic.textData.0y9izutl.mappingFields", "pattern": "event.duration", "value": "event.{eventPropertyName}"}]}, {"type": "event", "eventActionId": -102, "eventCategoryId": -20, "pathConfig": [{"path": "template_setting.actions.4.dynamic.textData.17jyiory.event", "pattern": "-102:-20", "value": "{eventActionId}:{eventCategoryId}"}, {"path": "template_setting.actions.4.dynamic.textData.17jyiory.source", "value": "insightPropertyIds"}]}, {"type": "event_attribute", "eventActionId": -102, "eventCategoryId": -20, "eventPropertyName": "utm_campaign", "pathConfig": [{"path": "template_setting.actions.4.dynamic.textData.17jyiory.attribute.propertyName", "value": "eventPropertyName"}, {"path": "template_setting.actions.4.dynamic.textData.17jyiory.attribute.value", "value": "eventPropertyName"}, {"path": "template_setting.actions.4.dynamic.textData.17jyiory.mappingFields", "pattern": "event.utm_campaign", "value": "event.{eventPropertyName}"}]}, {"type": "event", "eventActionId": -102, "eventCategoryId": -20, "pathConfig": [{"path": "template_setting.actions.4.dynamic.textData.54yaad4r.event", "pattern": "-102:-20", "value": "{eventActionId}:{eventCategoryId}"}, {"path": "template_setting.actions.4.dynamic.textData.54yaad4r.source", "value": "insightPropertyIds"}]}, {"type": "business_object", "itemTypeId": 1, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "price", "pathConfig": []}, {"type": "event_attribute", "eventActionId": -102, "eventCategoryId": -20, "itemPropertyName": "price", "itemTypeId": 1, "pathConfig": [{"path": "template_setting.actions.4.dynamic.textData.54yaad4r.attribute.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "template_setting.actions.4.dynamic.textData.54yaad4r.attribute.itemTypeId", "value": "itemTypeId"}, {"path": "template_setting.actions.4.dynamic.textData.54yaad4r.attribute.itemTypeName", "value": "itemTypeName"}, {"path": "template_setting.actions.4.dynamic.textData.54yaad4r.attribute.propertyName", "value": "itemPropertyName"}, {"path": "template_setting.actions.4.dynamic.textData.54yaad4r.attribute.value", "value": "itemPropertyName"}, {"path": "template_setting.actions.4.dynamic.textData.54yaad4r.mappingFields", "pattern": "event.product.price", "value": "event.{itemTypeName}.{itemPropertyName}"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "user_id", "pathConfig": [{"path": "template_setting.actions.9.dynamic.textData.8dcpolfm.attribute.value", "value": "itemPropertyName"}, {"path": "template_setting.actions.9.dynamic.textData.8dcpolfm.mappingFields", "pattern": "visitor.user_id", "value": "visitor.{itemPropertyName}"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "name", "pathConfig": [{"path": "template_setting.actions.9.dynamic.textData.lermzl87.attribute.itemTypeName", "value": "itemTypeName"}, {"path": "template_setting.actions.9.dynamic.textData.lermzl87.attribute.value", "value": "itemPropertyName"}, {"path": "template_setting.actions.9.dynamic.textData.lermzl87.mappingFields", "pattern": "groups.csgoxel3[1].name", "value": "groups.csgoxel3[{itemTypeId}].{itemPropertyName}"}, {"path": "template_setting.actions.9.dynamic.textData.lermzl87.type", "pattern": "content-source::csgoxel3::1", "value": "content-source::csgoxel3::{itemTypeId}"}]}, {"type": "promotion_pool", "poolCode": "qc_review_pool_169__update_3", "pathConfig": [{"path": "template_setting.actions.13.dynamic.textData.sm0y0az8.mappingFields", "pattern": "promotion_code.qc_review_pool_169__update_3.pool_id", "value": "promotion_code.{poolCode}.pool_id"}, {"path": "template_setting.actions.13.dynamic.textData.sm0y0az8.pool", "value": "poolCode"}]}, {"type": "event", "eventActionId": 471165, "eventCategoryId": -15, "pathConfig": [{"path": "template_setting.actions.14.dynamic.textData.nxju010f.event", "pattern": "471165:-15", "value": "{eventActionId}:{eventCategoryId}"}, {"path": "template_setting.actions.14.dynamic.textData.nxju010f.source", "value": "insightPropertyIds"}]}, {"type": "bo_attribute", "itemTypeId": -1010, "itemPropertyName": "campaign_name", "pathConfig": []}, {"type": "event_attribute", "eventActionId": 471165, "eventCategoryId": -15, "itemPropertyName": "campaign_name", "itemTypeId": -1010, "pathConfig": [{"path": "template_setting.actions.14.dynamic.textData.nxju010f.attribute.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "template_setting.actions.14.dynamic.textData.nxju010f.attribute.itemTypeId", "value": "itemTypeId"}, {"path": "template_setting.actions.14.dynamic.textData.nxju010f.attribute.itemTypeName", "value": "itemTypeName"}, {"path": "template_setting.actions.14.dynamic.textData.nxju010f.attribute.propertyName", "value": "itemPropertyName"}, {"path": "template_setting.actions.14.dynamic.textData.nxju010f.attribute.value", "value": "itemPropertyName"}, {"path": "template_setting.actions.14.dynamic.textData.nxju010f.mappingFields", "pattern": "event.campaign.campaign_name", "value": "event.{itemTypeName}.{itemPropertyName}"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "page_url", "pathConfig": [{"path": "template_setting.actions.14.dynamic.linkData.f9cjiv9x.attribute.itemTypeName", "value": "itemTypeName"}, {"path": "template_setting.actions.14.dynamic.linkData.f9cjiv9x.attribute.value", "value": "itemPropertyName"}, {"path": "template_setting.actions.14.dynamic.linkData.f9cjiv9x.type", "pattern": "content-source::csgoxel3::1", "value": "content-source::csgoxel3::{itemTypeId}"}]}, {"type": "business_object", "itemTypeId": -3, "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.itemTypeId", "value": "itemTypeId"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "last_updated", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.filters.OR.0.AND.0.column", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "id", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.filters.OR.1.AND.0.column", "value": "itemPropertyName"}]}, {"eventActionId": 471165, "eventCategoryId": 5, "type": "event", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.filters.OR.1.AND.0.event_metadata.event_action_id", "value": "eventActionId"}, {"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.filters.OR.1.AND.0.event_metadata.event_category_id", "value": "eventCategoryId"}, {"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.filters.OR.1.AND.0.event_metadata.event_property_syntax", "value": "eventPropertySyntax"}, {"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.filters.OR.1.AND.0.event_metadata.event_tracking_name", "value": "eventTrackingName"}, {"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.filters.OR.1.AND.0.event_metadata.insight_property_ids", "value": "insightPropertyIds"}]}, {"type": "business_object", "itemTypeId": -3, "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.filters.OR.1.AND.0.event_metadata.item_type_id", "value": "itemTypeId"}, {"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.filters.OR.1.AND.0.event_metadata.item_type_name", "value": "itemTypeName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "id", "pathConfig": []}, {"eventActionId": 471165, "eventCategoryId": 5, "type": "event_attribute", "itemTypeId": -3, "itemPropertyName": "id", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.filters.OR.1.AND.0.event_metadata.item_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "name", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.columns.0.value", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "date_created", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.columns.1.value", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "last_updated", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.columns.2.value", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "testcache2", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.columns.3.value", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "huytcx", "pathConfig": [{"path": "template_setting.boTableSettings.y55a3yfmcgd3zu9qskqn.columns.4.value", "value": "itemPropertyName"}]}, {"type": "business_object", "itemTypeId": -3, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -3, "itemPropertyName": "id", "pathConfig": [{"path": "template_setting.objectRelationships.0.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.0.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1010, "itemPropertyName": "start_date", "pathConfig": [{"path": "template_setting.objectRelationships.1.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.1.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "price", "pathConfig": [{"path": "template_setting.objectRelationships.4.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.4.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "user_id", "pathConfig": [{"path": "template_setting.objectRelationships.5.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.5.object_property_name", "value": "itemPropertyName"}]}, {"type": "business_object", "itemTypeId": -100, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -100, "itemPropertyName": "last_used_time", "pathConfig": [{"path": "template_setting.objectRelationships.6.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.6.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1010, "itemPropertyName": "campaign_name", "pathConfig": [{"path": "template_setting.objectRelationships.7.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.7.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": 1, "itemPropertyName": "id", "pathConfig": [{"path": "template_setting.objectRelationships.8.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.8.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "last_updated", "pathConfig": [{"path": "template_setting.objectRelationships.9.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.9.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "bd_month", "pathConfig": [{"path": "template_setting.objectRelationships.10.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.10.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "full_name", "pathConfig": [{"path": "template_setting.objectRelationships.11.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.11.object_property_name", "value": "itemPropertyName"}]}, {"type": "business_object", "itemTypeId": -1009, "pathConfig": []}, {"type": "bo_attribute", "itemTypeId": -1009, "itemPropertyName": "story_name", "pathConfig": [{"path": "template_setting.objectRelationships.12.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.12.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "ba_test_custom", "pathConfig": [{"path": "template_setting.objectRelationships.13.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.13.object_property_name", "value": "itemPropertyName"}]}, {"type": "bo_attribute", "itemTypeId": -1007, "itemPropertyName": "id", "pathConfig": [{"path": "template_setting.objectRelationships.14.object_id", "value": "itemTypeId"}, {"path": "template_setting.objectRelationships.14.object_property_name", "value": "itemPropertyName"}]}]}