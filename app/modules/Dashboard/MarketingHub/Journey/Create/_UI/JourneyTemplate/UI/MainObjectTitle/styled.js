import styled from 'styled-components';

export const CircleNumber = styled.div`
  width: 24px;
  height: 24px;
  border: 1px solid #b8cfe6;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const MainObjectTitleRoot = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;

  .block-top,
  .block-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .block-top .left {
    font-size: 14px;
    font-weight: bold;
    color: #005eb8;
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .block-top .right {
    color: #7f7f7f;
  }

  .block-bottom {
    .hint {
      margin-left: 28px;
      color: #7f7f7f;
    }
  }

  &.verify-done {
    ${CircleNumber} {
      color: white;
      background-color: #005eb8;
    }
  }
`;
