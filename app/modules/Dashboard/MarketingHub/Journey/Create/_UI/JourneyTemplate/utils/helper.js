/* eslint-disable no-console */
/* eslint-disable prefer-destructuring */
/* eslint-disable import/no-cycle */
/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
/* eslint-disable no-cond-assign */
/* eslint-disable indent */
import produce from 'immer';
import {
  cloneDeep,
  every,
  first,
  flatten,
  forEach,
  get,
  groupBy,
  isArray,
  isEmpty,
  isFunction,
  isUndefined,
  keyBy,
  omit,
  omitBy,
  pick,
  set,
  some,
  uniqWith,
} from 'lodash';
import isEqual from 'react-fast-compare';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { parseObjectNumeric } from '../../../../../../../../utils/common';
import group, { flatRollup } from '../../../../../../../../utils/group';
import { getUntitledName } from '../../../../../../../../utils/web/properties';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
import { safeParseArray } from '../../../../../../../../utils/web/utils';
import { NODE_TYPE } from '../../../Content/Nodes/constant';
import {
  JOURNEY_OBJECT_TYPE,
  MODE,
  OBJECT_TYPE,
  VERIFY_STATUS,
} from '../constant';
import {
  ALLOW_CREATE_OBJECT,
  ERROR_CODE,
  EVENT_ATTR_CONDITION_TYPE,
  OBJ_PRIMARY_KEYS,
  PARENT_OBJECTS,
} from './config';
import { getObjectsUseSameDO } from './dataObject';
import {
  getActDOAttrAdditionalInfo,
  getObjectsUseSameDOAttr,
} from './dataObjectAttr';
import { getActEventAttrAdditionalInfo } from './eventAttr';
import { JOURNEY_OBJECT_META, composeObjectErrors } from './journeyObjects';
import { getSourcesFromEventObjs } from './service';
import { SYSTEM_BO } from '../../../../../../../../utils/constants';
import SelectorServices from 'services/Selector';
import { addRegexFlags } from '../../../../../../../../utils/regex';

const PATH =
  'modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/helper';

function safeParseCondition(condition) {
  const temp = { ...condition };

  if (!condition.itemTypeId) {
    temp.itemTypeId = condition.item_type_id;
  }

  if (!condition.type && condition.itemTypeId !== undefined) {
    temp.type = condition.itemTypeId ? 'item' : 'event';
  }

  return temp;
}

export const isEditableObject = (obj, objects) => {
  if (obj.type === JOURNEY_OBJECT_TYPE.dataObject) {
    const { itemTypeId } = obj;

    const nonEditableBO = [
      SYSTEM_BO.Customer.itemTypeId,
      SYSTEM_BO.Visitor.itemTypeId,
      SYSTEM_BO.Variant.itemTypeId,
      SYSTEM_BO.Journey.itemTypeId,
      SYSTEM_BO.Campaign.itemTypeId,
    ];

    const isUseForEventAttr = some(
      getObjectsUseSameDO(obj, objects),

      checkingObj => {
        return checkingObj.type === OBJECT_TYPE.eventAttribute;
      },
    );

    return !(nonEditableBO.includes(+itemTypeId) || isUseForEventAttr);
  }

  if (obj.type === JOURNEY_OBJECT_TYPE.dataObjectAttr) {
    const isUseForEventAttr = some(
      getObjectsUseSameDOAttr(obj, objects),

      checkingObj => {
        return checkingObj.type === OBJECT_TYPE.eventAttribute;
      },
    );

    return !isUseForEventAttr;
  }

  return true;
};

export function loopFilters(filters, opts) {
  const { eachCondition, filterCondition = () => true } = opts;

  if (!filters) return;

  const orObj = get(filters, 'OR', []);

  orObj.forEach((orItem, orIndex) => {
    const andConditions = get(orItem, 'AND', []);

    if (!Array.isArray(andConditions)) return;

    andConditions.forEach((condition, andIndex) => {
      if (!filterCondition(condition)) return;

      const path = `OR.${orIndex}.AND.${andIndex}`;

      if (eachCondition) {
        eachCondition(safeParseCondition(condition), path, {
          and: andIndex,
          or: orIndex,
        });
      }
    });
  });
}

export const getObjectName = (args = {}) => {
  const { object, settings, defaultName = null } = args;

  const { type } = object;

  try {
    if (!settings) return defaultName;

    const itemTypeId = get(settings, 'item_type_id');

    const NAME_KEY = {
      [OBJECT_TYPE.journeyGoal]: 'conversionName',
      [OBJECT_TYPE.campaign]: 'destinationName',
      [OBJECT_TYPE.promotionPool]: 'pool_name',
      [OBJECT_TYPE.dataObjectAttr]: 'item_property_display',
      [OBJECT_TYPE.eventAttribute]: itemTypeId
        ? 'item_property_display'
        : 'event_property_display',
    };

    let name = null;

    const nameKey = NAME_KEY[type];

    if (nameKey) {
      name = settings[nameKey];
    }

    if (!name) {
      // Automatically detect key ending with 'display'
      Object.keys(settings).forEach(key => {
        if (name) return;

        if (/display$/i.test(key)) {
          name = settings[key];
        }
      });
    }

    return name || defaultName;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      func: 'getObjectName',
      error,
    });

    return defaultName;
  }
};

export const getObjectSettings = ({
  object,
  templateObjSettings,
  objects,
  mode,
}) => {
  try {
    let { settings } = findObject(object, templateObjSettings);

    if (mode === MODE.Use) {
      const tempObj = findObject(object, objects);

      if (tempObj && get(tempObj, 'verify.settings')) {
        settings = get(tempObj, 'verify.settings');
      }
    }

    return settings;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      object,
      func: 'getObjectSettings',
    });

    return {};
  }
};

const getObjectWarningMsg = ({ object, templateObjSettings, mode }) => {
  let objectLabel = JOURNEY_OBJECT_META[object.type].label;

  let warningMsg = null;

  if (object.type === OBJECT_TYPE.eventAttribute && object.itemTypeId) {
    objectLabel = JOURNEY_OBJECT_META.bo_attribute.label;
  }

  if (mode === MODE.Save) {
    const { errorMessage } = findObject(object, templateObjSettings) || {};

    if (errorMessage === ERROR_CODE.NOT_EXIST) {
      warningMsg = `${objectLabel} deleted`;
    }

    if (errorMessage === ERROR_CODE.NONE_PERMISSION) {
      warningMsg = `${objectLabel} access restricted`;
    }
  }

  return { warningMsg };
};

export const getObjectNameDisplay = (args = {}) => {
  const {
    object = {},
    objects = [],
    templateObjSettings = [],
    defaultName = null,
    mode,
  } = args;

  const { type = '' } = object;

  let { warningMsg } = getObjectWarningMsg({
    object,
    templateObjSettings,
    mode,
  });

  try {
    const settings = getObjectSettings({
      object,
      objects,
      templateObjSettings,
      mode,
    });

    let name = getObjectName({
      object,
      settings,
      defaultName,
    });

    if (type === OBJECT_TYPE.eventAttribute && object.itemTypeId) {
      const { warningMsg: boWarningMsg } = getObjectWarningMsg({
        object: {
          type: OBJECT_TYPE.dataObject,
          itemTypeId: object.itemTypeId,
        },
        templateObjSettings,
        mode,
      });

      if (boWarningMsg) {
        warningMsg = `Reference DO: ${boWarningMsg}`;
      } else {
        const boSettings = getObjectSettings({
          object: {
            type: OBJECT_TYPE.dataObject,
            itemTypeId: object.itemTypeId,
          },
          objects,
          templateObjSettings,
          mode,
        });

        const boName = getObjectName({
          object,
          settings: boSettings,
          defaultName,
        });

        name = `Reference DO: ${boName} \u00BB ${name}`;

        if (warningMsg) {
          warningMsg = `Reference DO: ${boName} \u00BB ${warningMsg}`;
        }
      }
    }

    return { name: name || defaultName, warningMsg };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      func: 'getObjectName',
      error,
    });

    return defaultName;
  }
};

export const isEqualObject = (obj, compareObj) => {
  if (get(obj, 'type') !== get(compareObj, 'type')) return false;

  const result = isEqual(getBasicObject(obj), getBasicObject(compareObj));

  return result;
};

export const isRelatedObject = (obj, relatedObj) => {
  let isRelated = false;

  if (
    obj.type === JOURNEY_OBJECT_TYPE.dataObject &&
    relatedObj.type === JOURNEY_OBJECT_TYPE.dataObjectAttr
  ) {
    isRelated = obj.itemTypeId === relatedObj.itemTypeId;
  }

  if (
    obj.type === JOURNEY_OBJECT_TYPE.event &&
    relatedObj.type === JOURNEY_OBJECT_TYPE.eventAttribute
  ) {
    const checkedKeys = ['eventActionId', 'eventCategoryId'];

    isRelated = isEqual(pick(obj, checkedKeys), pick(relatedObj, checkedKeys));
  }

  return isRelated;
};

export const concatPathToObjPathConfig = (object, path) => {
  const { pathConfig } = object;

  return {
    ...object,
    pathConfig: pathConfig.map(pathConfigItem => ({
      ...pathConfigItem,
      path: `${path}.${pathConfigItem.path}`,
    })),
  };
};

export const concatPathToListObj = (objs, path) => {
  if (!objs) return [];

  if (!path) return objs;

  return objs.map(obj => concatPathToObjPathConfig(obj, path));
};

export const getErrorMessageByCode = (errorList = [], errorCode) => {
  const matchingError = errorList.find(error => error.code === errorCode);

  return matchingError ? matchingError.message : undefined;
};

export const groupObjectsWithType = ({
  objectType = '',
  objects = [],
  allObjects = [],
  customObjectProcessor,
  customPathConfigProcesser,
  checkAllowChange = true,
  checkEditable = true,
}) => {
  const results = [];

  try {
    const primaryKeys = OBJ_PRIMARY_KEYS[objectType];

    if (!primaryKeys) return [];

    flatRollup(
      objects,
      v => {
        let object = {
          ...pick(first(v), primaryKeys),
          type: objectType,
          pathConfig: [],
        };

        let allowChange = true;
        let editable = true;

        v.forEach(rollupItem => {
          let { pathConfig = [] } = rollupItem;

          if (isFunction(customPathConfigProcesser)) {
            pathConfig = customPathConfigProcesser({
              processedPathConfig: pathConfig,
              mappedObject: rollupItem,
            });
          }

          object.pathConfig.push(...pathConfig);

          if (rollupItem.allowChange === false) {
            allowChange = false;
          }

          if (rollupItem.editable === false) {
            editable = false;
          }
        });

        if (isFunction(customObjectProcessor)) {
          object = customObjectProcessor({
            processedObject: object,
            groupedObjects: v,
          });
        }

        editable = editable && isEditableObject(object, allObjects);

        allowChange = editable && allowChange;

        results.push({
          ...object,

          ...(checkAllowChange && { allowChange }),
          ...(checkEditable && { editable }),
        });
      },

      ...primaryKeys.map(key => obj => obj[key]),
    );

    return results;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      path: PATH,
      func: 'groupObjects',
      error,
    });

    return results;
  }
};

export const groupObjects = (objects, opts = {}) => {
  const { checkAllowChange = false, checkEditable = false } = opts;

  const groupObjs = groupBy(objects, 'type');

  return flatten(
    Object.entries(groupObjs).map(([type, objsByType]) =>
      groupObjectsWithType({
        objects: objsByType,
        allObjects: objects,
        objectType: type,
        checkAllowChange,
        checkEditable,
      }),
    ),
  );
};

export const getBasicObject = obj => {
  const keys = [...OBJ_PRIMARY_KEYS[obj.type], 'type'];

  return omitBy(pick(obj, keys), isUndefined);
};

export const findObject = (obj, anyListObjects) => {
  return anyListObjects.find(i => isEqualObject(obj, i));
};

export const addEventAttribute = addEventAttrArgs => {
  const { objects, eventInfo, itemTypeId, propertyName } = addEventAttrArgs;

  let pathConfig = () => [];

  if (typeof addEventAttrArgs.pathConfig === 'function') {
    pathConfig = addEventAttrArgs.pathConfig;
  }

  if (Array.isArray(pathConfig)) {
    pathConfig = () => addEventAttrArgs.pathConfig;
  }

  const tempObjs = [...objects];

  if (!itemTypeId) {
    tempObjs.push({
      type: OBJECT_TYPE.eventAttribute,
      pathConfig: pathConfig({
        type: EVENT_ATTR_CONDITION_TYPE.normal,
      }),
      eventPropertyName: propertyName,
      ...eventInfo,
    });

    return tempObjs;
  }

  const boAttrInfo = {
    itemTypeId,
    itemPropertyName: propertyName,
  };

  tempObjs.push(
    {
      type: OBJECT_TYPE.dataObject,
      itemTypeId,
      pathConfig: [],
      ...eventInfo,
    },
    {
      type: OBJECT_TYPE.dataObjectAttr,
      pathConfig: [],
      ...boAttrInfo,
      ...eventInfo,
    },
    {
      type: OBJECT_TYPE.eventAttribute,
      pathConfig: pathConfig({
        type: EVENT_ATTR_CONDITION_TYPE.boAttr,
      }),
      ...boAttrInfo,
      ...eventInfo,
    },
  );

  return tempObjs;
};

export const isDisabledObject = args => {
  const { mode, object: checkingObj, listObject, templateObjSettings } = args;

  let disabled = false;

  if (mode === MODE.Save) {
    const objSettings = findObject(checkingObj, templateObjSettings);

    if (!objSettings.isExist) {
      disabled = true;
    }
  }

  if (mode === MODE.Use) {
    listObject.forEach(object => {
      const verifyStatus = getVerifyStatus(object);

      if (
        isRelatedObject(object, checkingObj) &&
        verifyStatus !== VERIFY_STATUS.Done
      ) {
        disabled = true;
      }
    });

    if (checkingObj.type === OBJECT_TYPE.eventAttribute) {
      const withItemTypeId = !!checkingObj.itemTypeId;

      if (withItemTypeId) {
        const boObject = findObject(
          {
            type: OBJECT_TYPE.dataObject,
            itemTypeId: checkingObj.itemTypeId,
          },
          listObject,
        );

        if (!boObject || !get(boObject, 'verify.isExist')) {
          disabled = true;
        }
      }
    }
  }

  return disabled;
};

export const isVisibleObject = (object, objects) => {
  let isVisible = true;

  if (object.type === JOURNEY_OBJECT_TYPE.dataObject) {
    const hasPathConfig = !isEmpty(object.pathConfig);

    const relatedObjects = objects.filter(checkingObj =>
      isRelatedObject(object, checkingObj),
    );

    isVisible =
      !hasPathConfig &&
      every(relatedObjects, relatedObj => !isVisible(relatedObj));
  }

  return isVisible;
};

export const getVerifyStatus = object => {
  const { verify } = object;

  if (get(verify, 'isExist')) {
    return VERIFY_STATUS.Done;
  }

  return VERIFY_STATUS.None;
};

export const getVerifyConfigObject = ({
  object,
  objects,
  templateObjSettings,
}) => {
  const { type: objectDetailType } = object;

  function getSettings(objectArg) {
    const args = {
      object: objectArg || object,
      objects,
      templateObjSettings,
      mode: MODE.Use,
    };

    return getObjectSettings(args);
  }

  let parentObjSettings = null;

  const parentObject = objects.find(o => isRelatedObject(o, object));

  if (parentObject) {
    parentObjSettings = getSettings(parentObject);
  }

  const objSettings = getSettings();

  try {
    const HANDLER_MAP = {
      [OBJECT_TYPE.segment]: () => ({
        segmentDisplay: objSettings.segment_display,
      }),
      [OBJECT_TYPE.dataObject]: () => ({
        itemTypeDisplay: objSettings.item_type_display,
      }),
      [OBJECT_TYPE.dataObjectAttr]: () => ({
        itemTypeDisplay: parentObjSettings.item_type_display,
        itemPropertyDisplay: objSettings.item_property_display,
        dataType: objSettings.data_type,
        isInputViaUI: +!!objSettings.is_input_via_ui,
      }),
      [OBJECT_TYPE.event]: () => ({
        eventNameDisplay: objSettings.eventNameDisplay,
      }),
      [OBJECT_TYPE.eventAttribute]: () => {
        const { itemTypeId } = object;

        if (itemTypeId) {
          const boObjSettings = getSettings({
            type: OBJECT_TYPE.dataObject,
            itemTypeId,
          });

          if (!templateObjSettings) return null;

          const eventPropertyDisplay = boObjSettings.item_type_display;

          return {
            eventNameDisplay: parentObjSettings.eventNameDisplay,
            eventPropertyDisplay,
            itemPropertyDisplay: objSettings.item_property_display,
            dataType: objSettings.data_type,
            isInputViaUI: +!!objSettings.is_input_via_ui,
          };
        }

        return {
          eventNameDisplay: parentObjSettings.eventNameDisplay,
          eventPropertyDisplay: objSettings.event_property_display,
          dataType: objSettings.data_type,
        };
      },
      [OBJECT_TYPE.campaign]: () => ({
        destinationName: objSettings.destinationName,
        catalogId: objSettings.catalogId,
      }),
      [OBJECT_TYPE.journeyGoal]: () => ({
        conversionName: objSettings.conversionName,
      }),
      [OBJECT_TYPE.objectView]: () => ({
        viewDisplay: objSettings.viewDisplay,
      }),
      [OBJECT_TYPE.predictiveModel]: () => ({
        modelName: objSettings.modelName,
      }),
      [OBJECT_TYPE.promotionPool]: () => ({
        poolName: objSettings.pool_name,
      }),
    };

    if (isFunction(HANDLER_MAP[objectDetailType])) {
      const fields = HANDLER_MAP[objectDetailType]();

      return fields;
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      func: 'getVerifyConfigObject',
      object,
      objects,
      templateObjSettings,
    });
  }

  return null;
};

export const unFlattenJTObjects = flattenObjs => {
  const result = [];

  function addObject(currentObject, objects) {
    const nestedObjects = [];

    const filteredObjects = objects.filter(obj =>
      isEqualObject(obj.belongTo, currentObject),
    );

    filteredObjects.forEach(object => {
      delete object.belongTo;

      if (object.objects) {
        addObject(object, flattenObjs);
      }

      nestedObjects.push(object);
    });

    return nestedObjects;
  }

  const topLevelObjects = flattenObjs.filter(obj => !obj.belongTo);

  topLevelObjects.forEach(obj => {
    delete obj.belongTo;

    const newObj = { ...obj };

    newObj.objects = addObject(obj, flattenObjs);

    result.push(newObj);
  });

  return result;
};

export const flattenJTObjects = objects => {
  const result = [];

  (function addObject(currentObjs, belongTo = undefined) {
    currentObjs.forEach(obj => {
      let temp = { ...obj };

      if (isEmpty(obj.objects)) {
        result.push({
          ...temp,
          belongTo,
        });

        return;
      }

      temp = omit(temp, 'objects');

      result.push(temp);

      addObject(obj.objects, getBasicObject(temp));
    });
  })(objects);

  return uniqWith(result, isEqualObject);
};

export const formatDataChange = state => {
  const result = produce(state, draft => {
    const { verification } = state;

    (verification.objects || []).forEach((obj, idx) => {
      const temp = omit(obj, ['errors']);

      draft.verification.objects[idx] = temp;
    });
  });

  return result;
};

export const toStorySettings = async ({
  journeyTemplate,
  templateObjSettings,
  objects,
}) => {
  let result = null;

  const [zonesReponse, sourcesResponse] = await Promise.all([
    SelectorServices.zones.getList(),
    getSourcesFromEventObjs({ objects }),
  ]);

  const { data: zones = [] } = zonesReponse;

  let { sources = [] } = sourcesResponse;

  const mapZones = keyBy(zones, 'zoneId');

  sources = group(
    sources,
    s => s.eventActionId,
    s => s.eventCategoryId,
    s => s.insightPropertyType,
  );

  function loopAndUpdate(node, cb) {
    cb(node);

    if (Array.isArray(node.branchs)) {
      forEach(node.branchs, childrenNode => {
        loopAndUpdate(childrenNode, cb);
      });
    }
  }

  function updateWithPathConfig({
    workflowSetting,
    pathConfig,
    dataReplace,
    object,
    objectTemplate = {},
  }) {
    function loopUpdateMethod(methods) {
      let isNext = true;

      methods.forEach(updateHandleFn => {
        if (!isNext) return;

        if (isFunction(updateHandleFn)) {
          isNext = false;

          updateHandleFn(() => {
            isNext = true;
          });
        }
      });
    }

    loopAndUpdate(workflowSetting, node => {
      forEach(pathConfig, config => {
        // console.log({ workflowSetting, node, pathConfig, config });

        const checkingReplaceCondition = next => {
          let isNeedReplace = true;

          const { replaceCondition } = config;

          if (isArray(replaceCondition)) {
            replaceCondition.forEach(condition => {
              const { key, args } = condition || {};

              switch (key) {
                case 'Only-replace_onChange': {
                  const { settingFields = [] } = args || {};

                  isNeedReplace = settingFields.some(
                    fieldName =>
                      !isEqual(
                        get(object.verify.settings, fieldName),
                        get(objectTemplate.settings, fieldName),
                      ),
                  );
                  break;
                }
                default:
                  break;
              }
            });
          }

          if (isNeedReplace) {
            next();
          }
        };

        const replaceWithValue = next => {
          const { path, value: valueKey } = config;

          const updatedValue = get(node, path);

          let valueReplace = get(
            {
              ...(config.dataReplace || {}),
              ...(dataReplace || {}),
            },
            valueKey,
          );

          if (valueReplace && updatedValue) {
            switch (valueKey) {
              case 'insightPropertyIds': {
                const { eventActionId, eventCategoryId } = object;

                // Only apply sources with type 1 or 2 for events in journey
                const sourcesByType = [
                  ...(sources
                    ?.get(eventActionId)
                    ?.get(eventCategoryId)
                    ?.get(1) || []),

                  ...(sources
                    ?.get(eventActionId)
                    ?.get(eventCategoryId)
                    ?.get(2) || []),
                ];

                valueReplace = valueReplace.filter(sourceId => {
                  return some(
                    sourcesByType,
                    s => s.insightPropertyId === sourceId,
                  );
                });
                break;
              }
              default:
                break;
            }

            set(node, path, valueReplace);

            return;
          }

          next();
        };

        const replaceWithPattern = next => {
          const { path, value: valueKey } = config;

          let { pattern } = config;

          if (config.regex) {
            pattern = new RegExp(config.regex.source, config.regex.flags || '');
          }

          if (pattern) {
            const valueReplace = replaceTags(valueKey, dataReplace);

            const currentValue = get(node, path);

            if (typeof currentValue !== 'string') {
              throw Error(
                `The value in path "${path}" will be replaced with a match from a regular expression pattern, not a literal string`,
              );
            }

            if (currentValue === valueReplace) {
              return;
            }

            if (typeof pattern === 'string' && pattern !== `${valueReplace}`) {
              const updatedValue = currentValue.replaceAll(
                pattern,
                valueReplace,
              );

              set(node, path, updatedValue);
            }

            if (pattern instanceof RegExp) {
              const updatedValue = currentValue.replaceAll(
                addRegexFlags(pattern, 'g'),
                valueReplace,
              );

              set(node, path, updatedValue);
            }

            return;
          }

          next();
        };

        try {
          const { actionId } = config;

          if (actionId !== node.actionId) return;

          loopUpdateMethod([
            checkingReplaceCondition,
            replaceWithPattern,
            replaceWithValue,
          ]);
        } catch (error) {
          addMessageToQueue({
            message: 'replace faild',
            args: { config, dataReplace },
            error,
          });

          // eslint-disable-next-line no-console
          console.log(error);
        }
      });
    });
  }

  function updateDestinations({ workflowSetting }) {
    loopAndUpdate(workflowSetting, node => {
      const { metadata } = node;

      if (node.actionType === NODE_TYPE.DESTINATION) {
        metadata.campaignInfo = null;
        metadata.variantInfo = [];

        if (Object.keys(mapZones).length > 0 && !mapZones[metadata.zoneId]) {
          metadata.zoneId = zones.at(0).zoneId;
        }

        if (metadata.campaign) {
          metadata.campaignInfo = {
            ...metadata.campaign,
            campaign_id: null,
          };

          delete metadata.campaign;
        }

        if (safeParseArray(metadata.variants).length) {
          metadata.variantInfo = [...metadata.variants].map(variant => {
            const { contentSetting = {} } = variant;
            const { variantExtraData = {} } = contentSetting;

            return {
              ...variant,
              variant_id: null,
              contentSetting: {
                ...contentSetting,
                variantExtraData: produce(variantExtraData || {}, draft => {
                  Object.keys(draft).forEach(key => {
                    if (!draft[key]?.fe_config_id) return;

                    draft[key].fe_config_id = null;
                  });
                }),
              },
            };
          });

          delete metadata.variants;
        }

        metadata.campaignId = null;
        metadata.variantIds = [];

        metadata.campaignInfo = parseObjectNumeric(metadata.campaignInfo, {
          keyFormat: 'snake',
        });

        metadata.variantInfo = metadata.variantInfo.map(variant =>
          parseObjectNumeric(variant, {
            keyFormat: 'snake',
          }),
        );
      }
    });
  }

  try {
    const { config } = cloneDeep(journeyTemplate);
    const { journey } = config;
    const { workflow_setting } = journey;

    objects.forEach(object => {
      const { pathConfig, verify } = object;

      const { dataReplace } = verify;

      const objectTemplate = findObject(object, templateObjSettings);

      updateWithPathConfig({
        workflowSetting: workflow_setting,
        dataReplace,
        pathConfig,
        object,
        objectTemplate,
      });
    });

    updateDestinations({
      workflowSetting: workflow_setting,
      templateObjSettings,
    });

    workflow_setting.story_name = getUntitledName(
      getTranslateMessage(TRANSLATE_KEY._UNTITLED_STORY, 'Untitled Story'),
    );

    result = {
      ...journey,
      workflow_setting,
      tactic: {
        templateId: journeyTemplate.template_id,
        templateName: journeyTemplate.template_name,
        networkId: journeyTemplate.network_id,
      },
    };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      message: error.message || 'Some thing wrong',
      func: 'toStorySettings',
      path: PATH,
      args: {
        journeyTemplate,
        objects,
      },
    });

    return { result: null, error };
  }

  return { result, error: null };
};

export const orderObjects = objects => {
  const result = [];

  PARENT_OBJECTS.forEach(parentObjType => {
    objects.forEach(obj => {
      if (parentObjType === obj.type) {
        result.push(obj);

        objects.forEach(relatedObj => {
          const isRelated = isRelatedObject(obj, relatedObj);

          if (isRelated) {
            result.push(relatedObj);
          }
        });
      }
    });
  });

  return result;
};

function replaceTags(value, dataReplace, regex = /\{(.*?)\}/g) {
  const matches = value.match(regex);

  if (!matches) return value;

  let replacedValue = value;

  matches.forEach(tag => {
    const key = tag.slice(1, -1);

    if (get(dataReplace, key)) {
      replacedValue = replacedValue.replace(tag, dataReplace[key]);
      return;
    }

    throw new Error(`Missing data replace for tag '${tag}'`);
  });

  return replacedValue;
}

export const getRelatedObjects = (object, objects = []) =>
  objects.filter(checkingObj => isRelatedObject(object, checkingObj));

export const getNextIndex = (currentIndex, length) => {
  return currentIndex + 1 < length ? currentIndex + 1 : -1;
};

export const getPrevIndex = currentIndex => {
  return currentIndex - 1 >= 0 ? currentIndex - 1 : -1;
};

export const composeObjectInfo = args => {
  const { objects, templateObjSettings, mode = MODE.Use } = args;

  const object = findObject(args.object, objects);

  const result = { info: null, saveInfo: undefined };

  if (!object || isEmpty(templateObjSettings)) {
    return result;
  }

  const disabled = isDisabledObject({
    mode,
    object,
    listObject: objects,
    templateObjSettings,
  });

  const { name, warningMsg } = getObjectNameDisplay({
    object,
    objects,
    templateObjSettings,
    defaultName: 'Unknown',
    mode,
  });

  result.info = {
    type: object.type,
    name,
    warningMsg,
    disabled,
  };

  if (mode === MODE.Save) {
    const templateObj = findObject(object, templateObjSettings);

    result.info = {
      ...result.info,
      ...templateObj,

      errors: composeObjectErrors({ object: templateObj, mode }),
    };
  }

  if (mode === MODE.Use && !isEmpty(object.verify)) {
    const objInfo = findObject(object, templateObjSettings);

    result.saveInfo = { ...objInfo };

    const { verify, type, isCreating, isLoadExisting } = object;

    const getAdditionalInfo = () => ({
      ...getActDOAttrAdditionalInfo(object, objects),
      ...getActEventAttrAdditionalInfo(object, objects),
    });

    const composedErrors = composeObjectErrors({ object, mode });

    const allowCreate =
      !get(verify, 'isExist') &&
      isEmpty(composedErrors) &&
      ALLOW_CREATE_OBJECT.includes(type);

    const allowChange =
      get(verify, 'errorMessage') === ERROR_CODE.CATALOG_NOT_VALID
        ? false
        : object.allowChange;

    result.info = {
      ...result.info,
      isCreating,
      allowCreate,
      allowChange,
      isLoadExisting,
      isExist: verify.isExist,
      settings: verify.settings,
      additionalInfo: verify.dataReplace,
      errors: composedErrors,
      ...getAdditionalInfo(),
    };
  }

  return result;
};
