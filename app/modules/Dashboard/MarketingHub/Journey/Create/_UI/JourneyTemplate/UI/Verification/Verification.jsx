/* eslint-disable react/prop-types */
import React from 'react';
import { ListObject } from '../ListObject/Loadable';
import { ObjectSettings } from '../ObjectSettings/Loadable';
import { VerificationRoot } from './styled';
import { VerifyMesssage } from '../VerifyMessage';
import { LoadExistingButton } from '../LoadExistingButton/LoadExistingButton';
import { MODE } from '../../constant';
import { VerifyNavigation } from '../VerifyNavigation/Loadable';
import { ApplyButton } from '../ApplyButton/Loadable';
import { useSelector } from 'react-redux';
import { selectMode } from '../../selector';

export const Verification = props => {
  const { onCancel } = props;

  const mode = useSelector(selectMode);

  return (
    <VerificationRoot>
      <ListObject />

      <ObjectSettings
        header={
          <VerifyMesssage mode={mode} onCancel={onCancel}>
            <LoadExistingButton />
          </VerifyMesssage>
        }
        footer={
          mode === MODE.Use && (
            <div className="verify-footer">
              <VerifyNavigation />

              {/* <ApplyButton /> */}
            </div>
          )
        }
      />
    </VerificationRoot>
  );
};
