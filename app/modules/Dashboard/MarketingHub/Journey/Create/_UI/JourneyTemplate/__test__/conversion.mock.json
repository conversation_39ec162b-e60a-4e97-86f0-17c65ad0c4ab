{"getObjectsFromConversion": [["Basic case", {"conversionSettings": {"conversionId": 5707799, "portalId": 33167, "insightPropertyId": "556351881", "eventActionId": "5707800", "eventCategoryId": "1280229", "eventTrackingName": "conversion_k_journey_goal_object", "conversionCode": "k_journey_goal_object", "conversionName": "[K] Journey Goal Object", "conversionNameMultilang": "{\"EN\":\"[K] Journey Goal Object\",\"DEFAULT_LANG\":\"EN\"}", "description": "", "descriptionMultilang": "{}", "storageType": 3, "status": 1, "audienceType": "customer", "iconUrl": "font-awesome fab alipay", "conversionEvent": "transaction_event", "conversionEventSetting": {"conditions": {"rules": {"OR": [{"AND": [{"type": "item", "value": 1711036800000, "operator": "before_date", "data_type": "datetime", "item_type_id": -1007, "condition_property_name": "date_created"}, {"type": "event", "value": 12, "operator": "greater_than", "data_type": "number", "item_type_id": null, "condition_property_name": "is_bounce"}]}]}}, "settingsUI": {"filters": {"OR": [{"AND": [{"type": "item", "value": 1711036800000, "column": "date_created", "syntax": "user_id", "dataType": "datetime", "metadata": {"itemTypeId": -1007, "itemTypeName": "users", "itemPropertyName": "date_created", "eventPropertySyntax": "user_id"}, "operator": "before_date", "data_type": "datetime", "time_unit": "DAY", "item_type_id": -1007, "conditionType": "event_attribute"}, {"type": "event", "value": 12, "column": "is_bounce", "syntax": "bounce_flag", "dataType": "number", "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "is_bounce", "eventPropertySyntax": "bounce_flag"}, "operator": "greater_than", "data_type": "number", "item_type_id": null, "conditionType": "event_attribute"}]}]}, "conditionType": "event_attribute", "eventActionId": -106, "condition_type": "perf_event", "eventCategoryId": -15, "eventTrackingName": "purchase transaction", "insightPropertyIds": [*********, *********, *********, *********, *********, *********]}, "event_tracking_name": "transaction_event", "insight_property_ids": [*********, *********, *********, *********, *********, *********]}, "calculateType": 1, "calculateSetting": {}, "attributionModel": "data_driven", "contributeSetting": {"contribute_1": {"conditions": {"rules": {"OR": [{"AND": [{"type": "event", "value": ["check_pro_t"], "operator": "matches", "data_type": "string", "item_type_id": null, "condition_property_name": "atm_term"}]}]}}, "settingsUI": {"filters": {"OR": [{"AND": [{"type": "event", "value": ["check_pro_t"], "column": "atm_term", "syntax": "extra.atm_term", "dataType": "string", "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "atm_term", "eventPropertySyntax": "extra.atm_term"}, "operator": "matches", "data_type": "string", "item_type_id": null, "conditionType": "event_attribute"}]}]}, "conditionType": "event_attribute", "eventActionId": -102, "condition_type": "perf_event", "eventCategoryId": -20, "eventTrackingName": "view pageview", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}, "is_absolute": false, "window_type": "days", "window_value": 1, "event_tracking_name": "view_pageview", "insight_property_ids": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}, "contribute_2": {"conditions": {"rules": {"OR": [{"AND": [{"type": "item", "value": ["327455", "327480"], "operator": "matches", "data_type": "array_string", "item_type_id": -1010, "condition_property_name": "label_ids"}]}]}}, "settingsUI": {"filters": {"OR": [{"AND": [{"type": "item", "value": ["327455", "327480"], "column": "label_ids", "syntax": "dims.campaign_id", "dataType": "array_string", "metadata": {"itemTypeId": -1010, "itemTypeName": "campaign", "itemPropertyName": "label_ids", "eventPropertySyntax": "dims.campaign_id"}, "operator": "matches", "data_type": "array_string", "item_type_id": -1010, "conditionType": "event_attribute"}]}]}, "conditionType": "event_attribute", "eventActionId": -103, "condition_type": "perf_event", "eventCategoryId": -11, "eventTrackingName": "add_to_cart product", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}, "is_absolute": false, "window_type": "days", "window_value": 1, "event_tracking_name": "add_to_cart_product", "insight_property_ids": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}, "contribute_3": {"conditions": {"rules": {"OR": [{"AND": [{"type": "event", "value": "conversion_source", "operator": "contains", "data_type": "string", "item_type_id": null, "condition_property_name": "atm_source"}]}]}}, "settingsUI": {"filters": {"OR": [{"AND": [{"type": "event", "value": "conversion_source", "column": "atm_source", "syntax": "extra.atm_source", "dataType": "string", "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "atm_source", "eventPropertySyntax": "extra.atm_source"}, "operator": "contains", "data_type": "string", "item_type_id": null, "conditionType": "event_attribute"}]}]}, "conditionType": "event_attribute", "eventActionId": -101, "condition_type": "perf_event", "eventCategoryId": -23, "eventTrackingName": "click advertising", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}, "is_absolute": false, "window_type": "days", "window_value": 1, "event_tracking_name": "click_advertising", "insight_property_ids": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}}, "processStatus": 7, "lastProcessedDate": null, "ctime": "2024-03-23T13:10:01.982Z", "utime": "2024-03-23T13:10:01.983Z", "cUserId": 1600085284, "uUserId": 1600085284, "latestVersion": 1, "isRecalculate": 0, "properties": {"iconType": "system"}}}, [{"eventActionId": -101, "eventCategoryId": -23, "eventPropertyName": "atm_source", "pathConfig": [{"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.column", "value": "eventPropertyName"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.syntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "eventPropertyName"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_3.conditions.rules.OR.0.AND.0.condition_property_name", "value": "eventPropertyName"}], "type": "event_attribute"}, {"eventActionId": -101, "eventCategoryId": -23, "pathConfig": [{"path": "contributeSetting.contribute_3.event_tracking_name", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_3.insight_property_ids", "value": "insightPropertyIds"}, {"path": "contributeSetting.contribute_3.settingsUI.eventActionId", "value": "eventActionId"}, {"path": "contributeSetting.contribute_3.settingsUI.eventCategoryId", "value": "eventCategoryId"}, {"path": "contributeSetting.contribute_3.settingsUI.eventTrackingName", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_3.settingsUI.insightPropertyIds", "value": "insightPropertyIds"}], "type": "event"}, {"eventActionId": -102, "eventCategoryId": -20, "eventPropertyName": "atm_term", "pathConfig": [{"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.0.column", "value": "eventPropertyName"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.0.syntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "eventPropertyName"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_1.conditions.rules.OR.0.AND.0.condition_property_name", "value": "eventPropertyName"}], "type": "event_attribute"}, {"eventActionId": -102, "eventCategoryId": -20, "pathConfig": [{"path": "contributeSetting.contribute_1.event_tracking_name", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_1.insight_property_ids", "value": "insightPropertyIds"}, {"path": "contributeSetting.contribute_1.settingsUI.eventActionId", "value": "eventActionId"}, {"path": "contributeSetting.contribute_1.settingsUI.eventCategoryId", "value": "eventCategoryId"}, {"path": "contributeSetting.contribute_1.settingsUI.eventTrackingName", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_1.settingsUI.insightPropertyIds", "value": "insightPropertyIds"}], "type": "event"}, {"eventActionId": -103, "eventCategoryId": -11, "itemPropertyName": "label_ids", "itemTypeId": -1010, "pathConfig": [{"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.column", "value": "itemProperyName"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.syntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.item_type_id", "value": "itemTypeId"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.metadata.itemTypeName", "value": "itemTypeName"}, {"path": "contributeSetting.contribute_2.conditions.rules.OR.0.AND.0.condition_property_name", "value": "itemPropertyName"}, {"path": "contributeSetting.contribute_2.conditions.rules.OR.0.AND.0.item_type_id", "value": "itemTypeId"}], "type": "event_attribute"}, {"eventActionId": -103, "eventCategoryId": -11, "pathConfig": [{"path": "contributeSetting.contribute_2.event_tracking_name", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_2.insight_property_ids", "value": "insightPropertyIds"}, {"path": "contributeSetting.contribute_2.settingsUI.eventActionId", "value": "eventActionId"}, {"path": "contributeSetting.contribute_2.settingsUI.eventCategoryId", "value": "eventCategoryId"}, {"path": "contributeSetting.contribute_2.settingsUI.eventTrackingName", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_2.settingsUI.insightPropertyIds", "value": "insightPropertyIds"}], "type": "event"}, {"eventActionId": -106, "eventCategoryId": -15, "eventPropertyName": "is_bounce", "pathConfig": [{"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.1.column", "value": "eventPropertyName"}, {"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.1.syntax", "value": "eventPropertySyntax"}, {"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.1.metadata.itemPropertyName", "value": "eventPropertyName"}, {"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.1.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "conversionEventSetting.conditions.rules.OR.0.AND.1.condition_property_name", "value": "eventPropertyName"}], "type": "event_attribute"}, {"eventActionId": -106, "eventCategoryId": -15, "itemPropertyName": "date_created", "itemTypeId": -1007, "pathConfig": [{"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.0.column", "value": "itemProperyName"}, {"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.0.syntax", "value": "eventPropertySyntax"}, {"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.0.item_type_id", "value": "itemTypeId"}, {"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.0.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "conversionEventSetting.settingsUI.filters.OR.0.AND.0.metadata.itemTypeName", "value": "itemTypeName"}, {"path": "conversionEventSetting.conditions.rules.OR.0.AND.0.condition_property_name", "value": "itemPropertyName"}, {"path": "conversionEventSetting.conditions.rules.OR.0.AND.0.item_type_id", "value": "itemTypeId"}], "type": "event_attribute"}, {"eventActionId": -106, "eventCategoryId": -15, "pathConfig": [{"path": "conversionEventSetting.event_tracking_name", "value": "eventTrackingName"}, {"path": "conversionEventSetting.insight_property_ids", "value": "insightPropertyIds"}, {"path": "conversionEventSetting.settingsUI.eventActionId", "value": "eventActionId"}, {"path": "conversionEventSetting.settingsUI.eventCategoryId", "value": "eventCategoryId"}, {"path": "conversionEventSetting.settingsUI.eventTrackingName", "value": "eventTrackingName"}, {"path": "conversionEventSetting.settingsUI.insightPropertyIds", "value": "insightPropertyIds"}], "type": "event"}, {"itemPropertyName": "date_created", "itemTypeId": -1007, "pathConfig": [], "type": "bo_attribute"}, {"itemTypeId": -1007, "pathConfig": [], "type": "business_object"}, {"itemPropertyName": "label_ids", "itemTypeId": -1010, "pathConfig": [], "type": "bo_attribute"}, {"itemTypeId": -1010, "pathConfig": [], "type": "business_object"}]], ["Basic case 2", {"conversionSettings": {"conversionId": 15314334, "portalId": 561236459, "insightPropertyId": "564994220", "eventActionId": "15314335", "eventCategoryId": "8046687", "eventTrackingName": "conversion_k_journey_goal_object", "conversionCode": "k_journey_goal_object", "conversionName": "[K] Journey Goal Object", "conversionNameMultilang": "{\"EN\":\"[K] Journey Goal Object\",\"DEFAULT_LANG\":\"EN\"}", "description": "", "descriptionMultilang": "{}", "storageType": 3, "status": 1, "audienceType": "customer", "iconUrl": "font-awesome fab artstation", "conversionEvent": "transaction_event", "conversionEventSetting": {"conditions": {"rules": {"OR": [{"AND": []}]}}, "settingsUI": {"filters": {"OR": [{"AND": []}]}, "conditionType": "event_attribute", "eventActionId": -106, "condition_type": "perf_event", "eventCategoryId": -15, "eventTrackingName": "purchase transaction", "insightPropertyIds": [564990850, 564991004]}, "event_tracking_name": "transaction_event", "insight_property_ids": [564990850, 564991004]}, "calculateType": 1, "calculateSetting": {}, "attributionModel": "data_driven", "contributeSetting": {"contribute_1": {"conditions": {"rules": {"OR": [{"AND": [{"type": "event", "value": "23", "operator": "doesnt_contain", "data_type": "string", "item_type_id": null, "condition_property_name": "atm_campaign"}, {"type": "item", "value": "Add", "operator": "contains", "data_type": "string", "item_type_id": -1036, "condition_property_name": "title"}]}]}}, "settingsUI": {"filters": {"OR": [{"AND": [{"type": "event", "value": "23", "column": "atm_campaign", "syntax": "extra.atm_campaign", "dataType": "string", "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "atm_campaign", "eventPropertySyntax": "extra.atm_campaign"}, "operator": "doesnt_contain", "data_type": "string", "item_type_id": null, "conditionType": "event_attribute"}, {"type": "item", "value": "Add", "column": "title", "syntax": "items.article_id", "dataType": "string", "metadata": {"itemTypeId": -1036, "itemTypeName": "article", "itemPropertyName": "title", "eventPropertySyntax": "items.article_id"}, "operator": "contains", "data_type": "string", "item_type_id": -1036, "conditionType": "event_attribute"}]}]}, "conditionType": "event_attribute", "eventActionId": -102, "condition_type": "perf_event", "eventCategoryId": -20, "eventTrackingName": "view pageview", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}, "is_absolute": false, "window_type": "days", "window_value": 1, "event_tracking_name": "view_pageview", "insight_property_ids": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}, "contribute_2": {"conditions": {"rules": {"OR": [{"AND": [{"type": "item", "value": ["Applebot", "Chrome"], "operator": "matches", "data_type": "string", "item_type_id": -3, "condition_property_name": "name"}]}]}}, "settingsUI": {"filters": {"OR": [{"AND": [{"type": "item", "value": ["Applebot", "Chrome"], "column": "name", "syntax": "browser_id", "dataType": "string", "metadata": {"itemTypeId": -3, "itemTypeName": "browser", "itemPropertyName": "name", "eventPropertySyntax": "browser_id"}, "operator": "matches", "data_type": "string", "item_type_id": -3, "conditionType": "event_attribute"}]}]}, "conditionType": "event_attribute", "eventActionId": -128, "condition_type": "perf_event", "eventCategoryId": -23, "eventTrackingName": "viewable advertising", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}, "is_absolute": false, "window_type": "days", "window_value": 1, "event_tracking_name": "viewable_advertising", "insight_property_ids": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]}, "contribute_3": {"conditions": {"rules": {"OR": [{"AND": [{"type": "item", "value": ["Variant 1", "Variant 2"], "operator": "matches", "data_type": "string", "item_type_id": -1011, "condition_property_name": "name"}, {"type": "event", "value": 60, "operator": "greater_than", "data_type": "number", "item_type_id": null, "condition_property_name": "duration"}]}]}}, "settingsUI": {"filters": {"OR": [{"AND": [{"type": "item", "value": ["Variant 1", "Variant 2"], "column": "name", "syntax": "dims.variant_id", "dataType": "string", "metadata": {"itemTypeId": -1011, "itemTypeName": "variant", "itemPropertyName": "name", "eventPropertySyntax": "dims.variant_id"}, "operator": "matches", "data_type": "string", "item_type_id": -1011, "conditionType": "event_attribute"}, {"type": "event", "value": 60, "column": "duration", "syntax": "duration_time", "dataType": "number", "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "duration", "eventPropertySyntax": "duration_time"}, "operator": "greater_than", "data_type": "number", "item_type_id": null, "conditionType": "event_attribute"}]}]}, "conditionType": "event_attribute", "eventActionId": 3830387, "condition_type": "perf_event", "eventCategoryId": -23, "eventTrackingName": "missing_receiver advertising", "insightPropertyIds": [*********]}, "is_absolute": false, "window_type": "days", "window_value": 1, "event_tracking_name": "missing_receiver_tracking", "insight_property_ids": [*********]}}, "processStatus": 7, "lastProcessedDate": null, "ctime": "2024-03-23T13:25:36.408Z", "utime": "2024-03-23T13:25:36.415Z", "cUserId": 1600083836, "uUserId": 1600083836, "latestVersion": 1, "isRecalculate": 0, "properties": {"iconType": "system"}}}, [{"eventActionId": -102, "eventCategoryId": -20, "eventPropertyName": "atm_campaign", "pathConfig": [{"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.0.column", "value": "eventPropertyName"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.0.syntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "eventPropertyName"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_1.conditions.rules.OR.0.AND.0.condition_property_name", "value": "eventPropertyName"}], "type": "event_attribute"}, {"eventActionId": -102, "eventCategoryId": -20, "itemPropertyName": "title", "itemTypeId": -1036, "pathConfig": [{"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.1.column", "value": "itemProperyName"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.1.syntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.1.item_type_id", "value": "itemTypeId"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.1.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.1.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.1.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "contributeSetting.contribute_1.settingsUI.filters.OR.0.AND.1.metadata.itemTypeName", "value": "itemTypeName"}, {"path": "contributeSetting.contribute_1.conditions.rules.OR.0.AND.1.condition_property_name", "value": "itemPropertyName"}, {"path": "contributeSetting.contribute_1.conditions.rules.OR.0.AND.1.item_type_id", "value": "itemTypeId"}], "type": "event_attribute"}, {"eventActionId": -102, "eventCategoryId": -20, "pathConfig": [{"path": "contributeSetting.contribute_1.event_tracking_name", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_1.insight_property_ids", "value": "insightPropertyIds"}, {"path": "contributeSetting.contribute_1.settingsUI.eventActionId", "value": "eventActionId"}, {"path": "contributeSetting.contribute_1.settingsUI.eventCategoryId", "value": "eventCategoryId"}, {"path": "contributeSetting.contribute_1.settingsUI.eventTrackingName", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_1.settingsUI.insightPropertyIds", "value": "insightPropertyIds"}], "type": "event"}, {"eventActionId": -106, "eventCategoryId": -15, "pathConfig": [{"path": "conversionEventSetting.event_tracking_name", "value": "eventTrackingName"}, {"path": "conversionEventSetting.insight_property_ids", "value": "insightPropertyIds"}, {"path": "conversionEventSetting.settingsUI.eventActionId", "value": "eventActionId"}, {"path": "conversionEventSetting.settingsUI.eventCategoryId", "value": "eventCategoryId"}, {"path": "conversionEventSetting.settingsUI.eventTrackingName", "value": "eventTrackingName"}, {"path": "conversionEventSetting.settingsUI.insightPropertyIds", "value": "insightPropertyIds"}], "type": "event"}, {"eventActionId": -128, "eventCategoryId": -23, "itemPropertyName": "name", "itemTypeId": -3, "pathConfig": [{"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.column", "value": "itemProperyName"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.syntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.item_type_id", "value": "itemTypeId"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "contributeSetting.contribute_2.settingsUI.filters.OR.0.AND.0.metadata.itemTypeName", "value": "itemTypeName"}, {"path": "contributeSetting.contribute_2.conditions.rules.OR.0.AND.0.condition_property_name", "value": "itemPropertyName"}, {"path": "contributeSetting.contribute_2.conditions.rules.OR.0.AND.0.item_type_id", "value": "itemTypeId"}], "type": "event_attribute"}, {"eventActionId": -128, "eventCategoryId": -23, "pathConfig": [{"path": "contributeSetting.contribute_2.event_tracking_name", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_2.insight_property_ids", "value": "insightPropertyIds"}, {"path": "contributeSetting.contribute_2.settingsUI.eventActionId", "value": "eventActionId"}, {"path": "contributeSetting.contribute_2.settingsUI.eventCategoryId", "value": "eventCategoryId"}, {"path": "contributeSetting.contribute_2.settingsUI.eventTrackingName", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_2.settingsUI.insightPropertyIds", "value": "insightPropertyIds"}], "type": "event"}, {"eventActionId": 3830387, "eventCategoryId": -23, "eventPropertyName": "duration", "pathConfig": [{"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.1.column", "value": "eventPropertyName"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.1.syntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.1.metadata.itemPropertyName", "value": "eventPropertyName"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.1.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_3.conditions.rules.OR.0.AND.1.condition_property_name", "value": "eventPropertyName"}], "type": "event_attribute"}, {"eventActionId": 3830387, "eventCategoryId": -23, "itemPropertyName": "name", "itemTypeId": -1011, "pathConfig": [{"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.column", "value": "itemProperyName"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.syntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.item_type_id", "value": "itemTypeId"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.metadata.itemPropertyName", "value": "itemPropertyName"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.metadata.eventPropertySyntax", "value": "eventPropertySyntax"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.metadata.itemTypeId", "value": "itemTypeId"}, {"path": "contributeSetting.contribute_3.settingsUI.filters.OR.0.AND.0.metadata.itemTypeName", "value": "itemTypeName"}, {"path": "contributeSetting.contribute_3.conditions.rules.OR.0.AND.0.condition_property_name", "value": "itemPropertyName"}, {"path": "contributeSetting.contribute_3.conditions.rules.OR.0.AND.0.item_type_id", "value": "itemTypeId"}], "type": "event_attribute"}, {"eventActionId": 3830387, "eventCategoryId": -23, "pathConfig": [{"path": "contributeSetting.contribute_3.event_tracking_name", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_3.insight_property_ids", "value": "insightPropertyIds"}, {"path": "contributeSetting.contribute_3.settingsUI.eventActionId", "value": "eventActionId"}, {"path": "contributeSetting.contribute_3.settingsUI.eventCategoryId", "value": "eventCategoryId"}, {"path": "contributeSetting.contribute_3.settingsUI.eventTrackingName", "value": "eventTrackingName"}, {"path": "contributeSetting.contribute_3.settingsUI.insightPropertyIds", "value": "insightPropertyIds"}], "type": "event"}, {"itemPropertyName": "name", "itemTypeId": -1011, "pathConfig": [], "type": "bo_attribute"}, {"itemTypeId": -1011, "pathConfig": [], "type": "business_object"}, {"itemPropertyName": "title", "itemTypeId": -1036, "pathConfig": [], "type": "bo_attribute"}, {"itemTypeId": -1036, "pathConfig": [], "type": "business_object"}, {"itemPropertyName": "name", "itemTypeId": -3, "pathConfig": [], "type": "bo_attribute"}, {"itemTypeId": -3, "pathConfig": [], "type": "business_object"}]]], "conversionToCreateConversionData": [{"createData": {"conversionCode": "k_journey_goal_object", "conversionName": "[K] Journey Goal Object", "conversionNameMultilang": {"EN": "[K] Journey Goal Object", "DEFAULT_LANG": "EN"}, "iconUrl": "font-awesome fab alipay", "description": "", "descriptionMultilang": {}, "storageType": 3, "audienceType": "customer", "calculateType": 1, "attributionModel": "data_driven", "calculateSetting": {}, "properties": {"iconType": "system"}, "contributeSetting": {"contribute_1": {"event_tracking_name": "view_pageview", "insight_property_ids": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********], "settingsUI": {"condition_type": "perf_event", "conditionType": "event_attribute", "eventCategoryId": -20, "eventActionId": -102, "eventTrackingName": "view pageview", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********], "filters": {"OR": [{"AND": [{"type": "event", "column": "atm_term", "dataType": "string", "data_type": "string", "item_type_id": null, "operator": "matches", "syntax": "extra.atm_term", "conditionType": "event_attribute", "value": ["check_pro_t"], "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "atm_term", "eventPropertySyntax": "extra.atm_term"}}]}]}}, "window_type": "days", "window_value": 1, "is_absolute": false, "conditions": {"rules": {"OR": [{"AND": [{"type": "event", "value": ["check_pro_t"], "operator": "matches", "data_type": "string", "item_type_id": null, "condition_property_name": "atm_term"}]}]}}}, "contribute_2": {"event_tracking_name": "add_to_cart_product", "insight_property_ids": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********], "settingsUI": {"condition_type": "perf_event", "conditionType": "event_attribute", "eventCategoryId": -11, "eventActionId": -103, "eventTrackingName": "add_to_cart product", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********], "filters": {"OR": [{"AND": [{"type": "item", "column": "label_ids", "dataType": "array_string", "data_type": "array_string", "item_type_id": -1010, "operator": "matches", "syntax": "dims.campaign_id", "conditionType": "event_attribute", "value": ["327455", "327480"], "metadata": {"itemTypeId": -1010, "itemTypeName": "campaign", "itemPropertyName": "label_ids", "eventPropertySyntax": "dims.campaign_id"}}]}]}}, "window_type": "days", "window_value": 1, "is_absolute": false, "conditions": {"rules": {"OR": [{"AND": [{"type": "item", "value": ["327455", "327480"], "operator": "matches", "data_type": "array_string", "item_type_id": -1010, "condition_property_name": "label_ids"}]}]}}}, "contribute_3": {"event_tracking_name": "click_advertising", "insight_property_ids": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********], "settingsUI": {"condition_type": "perf_event", "conditionType": "event_attribute", "eventCategoryId": -23, "eventActionId": -101, "eventTrackingName": "click advertising", "insightPropertyIds": [*********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********], "filters": {"OR": [{"AND": [{"type": "event", "column": "atm_source", "dataType": "string", "data_type": "string", "item_type_id": null, "operator": "contains", "syntax": "extra.atm_source", "conditionType": "event_attribute", "value": "conversion_source", "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "atm_source", "eventPropertySyntax": "extra.atm_source"}}]}]}}, "window_type": "days", "window_value": 1, "is_absolute": false, "conditions": {"rules": {"OR": [{"AND": [{"type": "event", "value": "conversion_source", "operator": "contains", "data_type": "string", "item_type_id": null, "condition_property_name": "atm_source"}]}]}}}}, "conversionEvent": "transaction_event", "conversionEventSetting": {"settingsUI": {"condition_type": "perf_event", "conditionType": "event_attribute", "eventCategoryId": -15, "eventActionId": -106, "eventTrackingName": "purchase transaction", "insightPropertyIds": [*********, *********, *********, *********, *********, *********], "filters": {"OR": [{"AND": [{"type": "item", "column": "date_created", "dataType": "datetime", "data_type": "datetime", "item_type_id": -1007, "operator": "before_date", "syntax": "user_id", "conditionType": "event_attribute", "value": 1711036800000, "time_unit": "DAY", "metadata": {"itemTypeId": -1007, "itemTypeName": "users", "itemPropertyName": "date_created", "eventPropertySyntax": "user_id"}}, {"type": "event", "column": "is_bounce", "dataType": "number", "data_type": "number", "item_type_id": null, "operator": "greater_than", "syntax": "bounce_flag", "conditionType": "event_attribute", "value": 12, "metadata": {"itemTypeId": null, "itemTypeName": null, "itemPropertyName": "is_bounce", "eventPropertySyntax": "bounce_flag"}}]}]}}, "event_tracking_name": "transaction_event", "insight_property_ids": [*********, *********, *********, *********, *********, *********], "conditions": {"rules": {"OR": [{"AND": [{"type": "item", "value": 1711036800000, "operator": "before_date", "data_type": "datetime", "item_type_id": -1007, "condition_property_name": "date_created"}, {"type": "event", "value": 12, "operator": "greater_than", "data_type": "number", "item_type_id": null, "condition_property_name": "is_bounce"}]}]}}}, "versionInfo": {"os": "Linux", "browser": "Chrome", "description": "has created conversion event."}}}]}