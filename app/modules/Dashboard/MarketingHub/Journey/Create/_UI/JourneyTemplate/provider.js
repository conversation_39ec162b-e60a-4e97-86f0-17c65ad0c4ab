/* eslint-disable react/prop-types */
import { useEffect } from 'react';
import { useInjectReducer } from 'utils/injectReducer';
import { useInjectSaga } from 'utils/injectSaga';
import saga from './saga';
import reducer from './reducer';
import { SLICE_KEY } from './constant';
import { useDispatch, useSelector } from 'react-redux';
import { init, reset } from './actions';
import { selectSlice } from './selector';

export const JourneyTemplateProvider = props => {
  useInjectReducer({
    key: SLICE_KEY,
    reducer,
  });

  useInjectSaga({
    key: SLICE_KEY,
    saga,
  });

  const { children, data, mode, moduleConfig = {} } = props;

  const dispatch = useDispatch();

  const state = useSelector(selectSlice);

  const { initialized } = state;

  useEffect(() => {
    if (!data || initialized) return;

    dispatch(
      init({
        data,
        mode,
        moduleConfig,
      }),
    );
  }, [moduleConfig, data, mode, initialized]);

  useEffect(() => () => dispatch(reset()), [dispatch]);

  return children;
};
