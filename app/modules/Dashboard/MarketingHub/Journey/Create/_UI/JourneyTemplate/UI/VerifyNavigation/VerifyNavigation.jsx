/* eslint-disable indent */
import React from 'react';
import { UIButton } from '@xlab-team/ui-components';
import KeyboardArrowRightIcon from '@material-ui/icons/KeyboardArrowRight';
import KeyboardArrowLeftIcon from '@material-ui/icons/KeyboardArrowLeft';
import { VerifyNavigationRoot } from './styled';
import { useActiveObj } from '../../hooks/useActiveObj';
import { useDispatch, useSelector } from 'react-redux';
import { createObject, nextObject, prevObject } from '../../actions';
import { selectAllowDirection, selectOrderedObjects } from '../../selector';
import { getNextIndex, getPrevIndex } from '../../utils/helper';

export const VerifyNavigation = () => {
  const dispatch = useDispatch();

  const { info } = useActiveObj();

  const { allow: allowNext } = useSelector(selectAllowDirection(getNextIndex));
  const { allow: allowPrev } = useSelector(selectAllowDirection(getPrevIndex));

  const orderObject = useSelector(selectOrderedObjects);

  const { activeObjIdx, orderedObjects } = orderObject;

  const isFirstObject = activeObjIdx === 0;

  const isLastObject = orderedObjects.length - 1 === activeObjIdx;

  const handleCreateAndNext = () => {
    if (!info.allowCreate) {
      dispatch(nextObject());

      return;
    }

    dispatch(createObject());
  };

  return (
    <VerifyNavigationRoot>
      {!isFirstObject && (
        <UIButton
          disabled={!allowPrev}
          theme="outline"
          onClick={() => {
            dispatch(prevObject());
          }}
        >
          <KeyboardArrowLeftIcon />
          <span>Back</span>
        </UIButton>
      )}

      {(!isLastObject || info.allowCreate) && (
        <UIButton
          disabled={!allowNext && !info.allowCreate}
          isLoading={info.allowCreate && info.isCreating}
          theme="outline"
          onClick={handleCreateAndNext}
        >
          <span>{info.allowCreate && 'Create & '}Next</span>

          <KeyboardArrowRightIcon />
        </UIButton>
      )}
    </VerifyNavigationRoot>
  );
};
