import { ACTION_TYPES } from './constant';

export const changeActiveStep = step => ({
  type: ACTION_TYPES.changeActiveStep,
  payload: { step },
});

export const reset = () => ({
  type: ACTION_TYPES.reset,
});

export const updateVerification = updated => ({
  type: ACTION_TYPES.updateVerification,
  payload: { updated },
});

export const changeActiveObj = object => ({
  type: ACTION_TYPES.changeActiveObj,
  payload: { object },
});

export const updateActiveObj = updated => ({
  type: ACTION_TYPES.updateActiveObj,
  payload: { updated },
});

export const changeIsLoading = isLoading => ({
  type: ACTION_TYPES.changeIsLoading,
  payload: { isLoading },
});

export const changeIsApplying = isApplying => ({
  type: ACTION_TYPES.changeIsApplying,
  payload: { isApplying },
});

export const changeIsSaving = isSaving => ({
  type: ACTION_TYPES.changeIsSaving,
  payload: { isSaving },
});

export const init = ({ data, mode, moduleConfig }) => ({
  type: ACTION_TYPES.init,
  payload: { data, mode, moduleConfig },
});

export const initDone = () => ({
  type: ACTION_TYPES.initDone,
});

export const replaceWithExisting = existingObj => ({
  type: ACTION_TYPES.replaceWithExisting,
  payload: { existingObj },
});

export const createObject = (newObject, options) => ({
  type: ACTION_TYPES.createObject,
  payload: { newObject, options },
});

export const applyJT = () => ({
  type: ACTION_TYPES.applyJT,
});

export const updateApplyState = updated => ({
  type: ACTION_TYPES.updateApplyState,
  payload: { updated },
});

export const nextObject = () => ({
  type: ACTION_TYPES.nextObject,
});

export const prevObject = () => ({
  type: ACTION_TYPES.prevObject,
});

export const updateObj = (obj, updated) => ({
  type: ACTION_TYPES.updateObj,
  payload: { obj, updated },
});

export const updateJourney = journey => ({
  type: ACTION_TYPES.updateJourney,
  payload: { journey },
});
