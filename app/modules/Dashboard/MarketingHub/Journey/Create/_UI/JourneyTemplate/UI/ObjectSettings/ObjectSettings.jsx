/* eslint-disable react/prop-types */
/* eslint-disable indent */
import React from 'react';
import { ObjectSettingsRoot } from './styled';
import { JOURNEY_OBJECT_TYPE } from '../../constant';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { SegmentSettings } from '../SegmentSettings/Loadable';
import { EventSettings } from '../EventSettings/Loadable';
import { EventAttrSettings } from '../EventAttrSettings/Loadable';
import { DataObjectSettings } from '../DataObjectSettings/Loadable';
import { DataObjectAttrSettings } from '../DataObjectAttrSettings/Loadable';
import { CampaignSettings } from '../CampaignSettings/Loadable';
import { PromotionSettings } from '../PromotionSettings/Loadable';
import { JourneyGoalSettings } from '../JourneyGoalSettings/Loadable';
import { OBJ_PRIMARY_KEYS } from '../../utils/config';
import { pick } from 'lodash';
import { useActiveObj } from '../../hooks/useActiveObj';
import classNames from 'classnames';
import LoadingOverlay from '../../../../../../../../../components/common/LoadingOverlay';
import { CircularProgress } from '@material-ui/core';

const PATH =
  'MarketingHub/Journey/Create/_UI/JourneyTemplate/UI/ObjectSettings/ObjectSettings';

export const ObjectSettings = props => {
  const { header, footer } = props;

  const { activeObj } = useActiveObj();

  const renderObjectSettings = () => {
    if (!activeObj) return null;

    const { type: objectType } = activeObj;

    let content = null;

    switch (objectType) {
      case JOURNEY_OBJECT_TYPE.segment: {
        content = <SegmentSettings />;
        break;
      }
      case JOURNEY_OBJECT_TYPE.dataObject: {
        content = <DataObjectSettings />;
        break;
      }
      case JOURNEY_OBJECT_TYPE.dataObjectAttr: {
        content = <DataObjectAttrSettings />;
        break;
      }
      case JOURNEY_OBJECT_TYPE.event: {
        content = <EventSettings />;
        break;
      }
      case JOURNEY_OBJECT_TYPE.eventAttribute: {
        content = <EventAttrSettings />;
        break;
      }
      case JOURNEY_OBJECT_TYPE.campaign: {
        content = <CampaignSettings />;
        break;
      }
      case JOURNEY_OBJECT_TYPE.promotionPool: {
        content = <PromotionSettings />;
        break;
      }
      case JOURNEY_OBJECT_TYPE.journeyGoal: {
        content = <JourneyGoalSettings />;
        break;
      }
      default:
        break;
    }

    const key = JSON.stringify(pick(activeObj, OBJ_PRIMARY_KEYS[objectType]));

    const isContentLoading = activeObj.isLoadExisting || activeObj.isCreating;

    return (
      <>
        {header}

        <ErrorBoundary key={key} path={PATH} activeObj>
          <div
            className={classNames('settings-content', {
              isLoading: isContentLoading,
            })}
          >
            {content}

            {isContentLoading && (
              <LoadingOverlay>
                <CircularProgress size={24} />
              </LoadingOverlay>
            )}
          </div>
        </ErrorBoundary>

        {footer}
      </>
    );
  };

  return <ObjectSettingsRoot>{renderObjectSettings()}</ObjectSettingsRoot>;
};
