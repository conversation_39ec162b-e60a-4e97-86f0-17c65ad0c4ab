import { get, pick } from 'lodash';
import { NODE_TYPE } from '../../../Content/Nodes/constant';

export const getEventFromNodeTrigger = branch => {
  if (branch.actionType !== NODE_TYPE.EVENT_BASED) return null;

  const event = get(branch, 'metadata.event');

  return pick(event, ['eventActionId', 'eventCategoryId']);
};

export const getEventFromPeformEvent = (event, options = {}) => {
  const { perfEventPropName = 'perfEvent' } = options;

  const pathConfig = [
    {
      path: `${perfEventPropName}.eventActionId`,
      value: 'eventActionId',
    },
    {
      path: `${perfEventPropName}.eventCategoryId`,
      value: 'eventCategoryId',
    },
    {
      path: `${perfEventPropName}.eventTrackingName`,
      value: 'eventTrackingName',
    },
    {
      path: `${perfEventPropName}.insightPropertyIds`,
      value: 'insightPropertyIds',
    },
  ];

  return {
    ...pick(event, ['eventActionId', 'eventCategoryId']),
    pathConfig,
  };
};
