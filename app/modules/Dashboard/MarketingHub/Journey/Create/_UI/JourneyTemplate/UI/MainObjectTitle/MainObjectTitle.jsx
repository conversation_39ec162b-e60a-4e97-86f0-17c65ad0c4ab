/* eslint-disable react/prop-types */
import React from 'react';
import { CircleNumber, MainObjectTitleRoot } from './styled';
import classNames from 'classnames';
import CheckBox from 'components/Molecules/CheckBox';
import { JOURNEY_OBJECT_META } from '../../utils';
import { useGetObjects } from '../../hooks/useGetObjects';
import { useUpdateAllowChange } from '../../hooks/useUpdateAllowChange';
import { JOURNEY_OBJECT_TYPE, MODE, VERIFY_STATUS } from '../../constant';
import { getVerifyStatus } from '../../utils/helper';
import { useSelector } from 'react-redux';
import { selectMode } from '../../selector';
import { lowerCase } from 'lodash';
import { capitalizeFirstLetters } from '../../../../../../../../../utils/web/utils';

export const MainObjectTitle = props => {
  const { objectType, index } = props;

  const mode = useSelector(selectMode);

  const objectMeta = JOURNEY_OBJECT_META[objectType];

  const { getObjectsByType } = useGetObjects();

  const { updateAllowChangeByObjectType } = useUpdateAllowChange();

  const { flattenObjects } = getObjectsByType(objectType, true);

  const isAllowAll = flattenObjects.every(object => object.allowChange);

  const isVerifyDone = flattenObjects.every(
    object => getVerifyStatus(object) === VERIFY_STATUS.Done,
  );

  let { label } = objectMeta;

  if (objectType === JOURNEY_OBJECT_TYPE.journeyGoal) {
    label = 'Journey Goal';
  }

  return (
    <MainObjectTitleRoot
      className={classNames({
        'verify-done': isVerifyDone,
      })}
    >
      <div className="block block-top">
        <div className="left">
          <CircleNumber>{index}</CircleNumber>

          {`Verify ${capitalizeFirstLetters(label)}`}
        </div>

        {mode === MODE.Save && (
          <div className="right">{index === 1 && 'Allow change'}</div>
        )}
      </div>

      <div className="block block-bottom">
        <div className="hint left">
          Template using the following {lowerCase(objectMeta.label)}
        </div>

        {mode === MODE.Save && (
          <div className="right">
            <CheckBox
              checked={isAllowAll}
              onClick={() => {
                updateAllowChangeByObjectType(objectType, !isAllowAll);
              }}
            />
          </div>
        )}
      </div>
    </MainObjectTitleRoot>
  );
};
