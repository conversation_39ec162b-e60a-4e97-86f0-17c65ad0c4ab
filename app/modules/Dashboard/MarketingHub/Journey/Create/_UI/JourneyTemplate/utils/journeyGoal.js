/* eslint-disable import/no-cycle */
/* eslint-disable indent */
import { isEmpty } from 'lodash';
import { OBJECT_TYPE } from '../constant';
import { ERROR_CODE, EVENT_ATTR_CONDITION_TYPE } from './config';
import { addEventAttribute, groupObjects, loopFilters } from './helper';
import ConversionServices from 'services/Conversion';

export const getJourneyGoalFromEventBase = ({ metadata }) => {
  const { journeyGoals } = metadata;

  if (isEmpty(journeyGoals)) return [];

  const objects = [];

  Object.entries(journeyGoals).forEach(([key, value]) => {
    const { conversionId } = value;

    objects.push({
      conversionId,
      pathConfig: [
        {
          path: `metadata.journeyGoals.${key}.conversionId`,
          value: 'conversionId',
        },
      ],
    });
  });

  return objects;
};

export const getObjectsFromConversion = conversionEvent => {
  let objects = [];

  const getObjsFromSetting = (setting, pathSetting) => {
    const { settingsUI } = setting;

    const { eventActionId, eventCategoryId, filters } = settingsUI;

    const eventInfo = { eventActionId, eventCategoryId };

    objects.push({
      type: OBJECT_TYPE.event,
      ...eventInfo,
      pathConfig: [
        {
          path: `${pathSetting}.event_tracking_name`,
          value: 'eventTrackingName',
        },
        {
          path: `${pathSetting}.insight_property_ids`,
          value: 'insightPropertyIds',
        },
        {
          path: `${pathSetting}.settingsUI.eventActionId`,
          value: 'eventActionId',
        },
        {
          path: `${pathSetting}.settingsUI.eventCategoryId`,
          value: 'eventCategoryId',
        },
        {
          path: `${pathSetting}.settingsUI.eventTrackingName`,
          value: 'eventTrackingName',
        },
        {
          path: `${pathSetting}.settingsUI.insightPropertyIds`,
          value: 'insightPropertyIds',
        },
      ],
    });

    loopFilters(filters, {
      eachCondition: (condition, pathCondition) => {
        const { item_type_id: itemTypeId, column: propertyName } = condition;

        objects = addEventAttribute({
          objects,
          eventInfo,
          itemTypeId,
          propertyName,
          pathConfig: params => {
            const { type } = params;

            const pathConfigByType = {
              [EVENT_ATTR_CONDITION_TYPE.normal]: [
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.column`,
                  value: 'eventPropertyName',
                },
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.syntax`,
                  value: 'eventPropertySyntax',
                },
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.metadata.itemPropertyName`,
                  value: 'eventPropertyName',
                },
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.metadata.eventPropertySyntax`,
                  value: 'eventPropertySyntax',
                },
                {
                  path: `${pathSetting}.conditions.rules.${pathCondition}.condition_property_name`,
                  value: 'eventPropertyName',
                },
              ],
              [EVENT_ATTR_CONDITION_TYPE.boAttr]: [
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.column`,
                  value: 'itemProperyName',
                },
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.syntax`,
                  value: 'eventPropertySyntax',
                },
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.item_type_id`,
                  value: 'itemTypeId',
                },
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.metadata.itemPropertyName`,
                  value: 'itemPropertyName',
                },
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.metadata.eventPropertySyntax`,
                  value: 'eventPropertySyntax',
                },
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.metadata.itemTypeId`,
                  value: 'itemTypeId',
                },
                {
                  path: `${pathSetting}.settingsUI.filters.${pathCondition}.metadata.itemTypeName`,
                  value: 'itemTypeName',
                },
                {
                  path: `${pathSetting}.conditions.rules.${pathCondition}.condition_property_name`,
                  value: 'itemPropertyName',
                },
                {
                  path: `${pathSetting}.conditions.rules.${pathCondition}.item_type_id`,
                  value: 'itemTypeId',
                },
              ],
            };

            return pathConfigByType[type] || [];
          },
        });
      },
    });
  };

  const { conversionEventSetting, contributeSetting } = conversionEvent;

  getObjsFromSetting(conversionEventSetting, 'conversionEventSetting');

  Object.entries(contributeSetting).forEach(([key, value]) => {
    getObjsFromSetting(value, `contributeSetting.${key}`);
  });

  return groupObjects(objects);
};

export const createJourneyGoal = async settings => {
  const params = {
    data: {
      ...settings,
    },
  };

  const conversionCode = settings?.conversionCode;

  const data = {
    conversionCode,
  };

  const conversionDetail = await ConversionServices.data.getConversionCodeExist(
    data,
  );

  if (conversionDetail.code === 200) {
    return {
      success: false,
      error: {
        code: ERROR_CODE.CONVERSION_CODE_EXIST,
      },
    };
  }

  const res = await ConversionServices.data.create(params);

  if (res.code === 200) {
    return { success: true };
  }

  return { success: false };
};
