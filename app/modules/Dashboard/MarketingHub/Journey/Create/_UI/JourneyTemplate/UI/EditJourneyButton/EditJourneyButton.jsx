/* eslint-disable react/prop-types */
import React from 'react';
import { StyledButton } from './styled';

export const EditJourneyButton = props => {
  const { onCancel, ...rest } = props;

  const onClickEdit = () => {
    if (!onCancel) return;

    onCancel();
  };

  return (
    <StyledButton
      {...rest}
      onClick={onClickEdit}
      variant="outline"
      theme="outline"
    >
      Edit journey
    </StyledButton>
  );
};
