/* eslint-disable no-param-reassign */
/* eslint-disable indent */
import { composeObjectInfo, isEqualObject } from '../utils/helper';
import { updateActiveObj } from '../actions';
import { useDispatch, useSelector } from 'react-redux';
import { selectMode, selectVerification } from '../selector';
import { useMemo } from 'react';

export const useActiveObj = () => {
  const mode = useSelector(selectMode);

  const dispatch = useDispatch();

  const {
    activeObj: activeObjState,
    templateObjSettings,
    objects,
  } = useSelector(selectVerification);

  const activeObj = objects.find(obj => isEqualObject(obj, activeObjState));

  const activeObjectInfoValues = useMemo(
    () =>
      composeObjectInfo({
        object: activeObj,
        objects,
        templateObjSettings,
        mode,
      }),
    [activeObj, objects, templateObjSettings, mode],
  );

  const addErrors = errors => {
    if (!errors) return;

    dispatch(updateActiveObj({ errors }));
  };

  return {
    activeObj,
    addErrors,
    ...activeObjectInfoValues,
  };
};
