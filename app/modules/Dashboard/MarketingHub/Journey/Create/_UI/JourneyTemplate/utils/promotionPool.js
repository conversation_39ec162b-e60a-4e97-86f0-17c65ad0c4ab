/* eslint-disable import/no-cycle */
import { get, isEmpty, keyBy } from 'lodash';
import { eachCampaignDynTag } from './campaign';
import PromotionService from 'services/Promotion';

export const getPoolObjsFromDestination = ({ metadata }) => {
  const poolObjs = [];

  eachCampaignDynTag(metadata, ({ tag, path }) => {
    if (tag.startsWith('promotion_code')) {
      const chunks = tag.split('.');
      const poolCode = chunks[1];

      poolObjs.push({
        poolCode,
        pathConfig: [
          {
            path: `metadata.${path}`,
            pattern: tag,
            value: tag.replace(poolCode, '{poolCode}'),
          },
        ],
      });
    }
  });

  return poolObjs;
};

export const bindPoolIdToPoolObjects = async poolObjs => {
  const poolCodes = [];

  poolObjs.forEach(poolObj => {
    if (!poolObj.poolCode) return;

    poolCodes.push(poolObj.poolCode);
  });

  if (isEmpty(poolCodes)) return poolObjs;

  const { data } = await PromotionService.promotionPool.data.getList({
    data: {
      columns: ['pool_code', 'pool_id'],
      filters: {
        OR: [
          {
            AND: [
              {
                type: 1,
                column: 'pool_code',
                data_type: 'string',
                operator: 'matches',
                extendValue: [],
                value: poolCodes,
              },
            ],
          },
        ],
      },
    },
  });

  const mapData = keyBy(data, 'pool_code');

  return poolObjs.map(poolObj => {
    const temp = { ...poolObj };

    if (poolObj.poolId !== undefined) return poolObj;

    const poolId = get(mapData, `${poolObj.poolCode}.pool_id`);

    if (!poolId) return poolObj;

    temp.poolId = poolId;

    return temp;
  });
};
