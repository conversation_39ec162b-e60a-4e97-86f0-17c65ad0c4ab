/* eslint-disable react/no-danger */
import moment from 'moment';
import React from 'react';
import { toNodeTriggerScheduledAPI } from '../../../Content/Nodes/TriggerScheduled/utils';
import { NODE_TYPE } from '../../../Content/Nodes/constant';
import { toNodeTriggerEventBasedAPI } from '../../../Content/Nodes/TriggerEventBased/utils';
import { toUISelectTime } from '../../../../../../../../components/common/UISchedulerTrigger/utils';

const DEFAULT_CONFIRM_CONTENT =
  'The journey is scheduled to run {$1}. Do you want to activate the journey?';

export const labelFromFrequencyValue = (value, repeatInterval = 0) => {
  let content = value.slice(0, -2);

  if (value === 'daily') {
    content = 'day';
  }

  return repeatInterval > 1 ? `${repeatInterval} ${content}s` : content;
};

const renderContent = $1 => {
  const content = DEFAULT_CONFIRM_CONTENT.replace('{$1}', $1);

  return <span dangerouslySetInnerHTML={{ __html: content }} />;
};

export const getConfirmContent = (triggerNode, channel) => {
  try {
    const { triggerInfo, nodeData } = triggerNode;

    if (triggerInfo.type === NODE_TYPE.SCHEDULED) {
      const meta = toNodeTriggerScheduledAPI(nodeData, channel);

      // Case - Once
      if (meta.frequency === 'once' && meta.triggerType === 'specific_date') {
        // slice 5 to remove 'from'
        return renderContent(`at ${getDateRangeContent(meta).slice(5)}`);
      }

      if (meta.frequency === 'once' && meta.triggerType === 'after_activate') {
        return renderContent('immediately');
      }

      // Case - Every
      const { repeatInterval, frequency } = meta;

      const content = `<b>Every ${labelFromFrequencyValue(
        frequency,
        repeatInterval,
      )}</b> ${getDateRangeContent(meta)}`;

      return renderContent(content);
    }

    if (triggerInfo.type === NODE_TYPE.EVENT_BASED) {
      const meta = toNodeTriggerEventBasedAPI(nodeData, channel);

      return renderContent(getDateRangeContent(meta));
    }

    return null;
  } catch (err) {
    return null;
  }
};

const getTime = ({ date, timeOfDay }) => {
  const tempDate = new Date(date);
  const tempTime = new Date(timeOfDay);

  tempDate.setHours(tempTime.getHours());
  tempDate.setMinutes(tempTime.getMinutes());

  return tempDate;
};

const getFormatedTime = ({ date, timeOfDay }) => {
  const tempDate = getTime({ date, timeOfDay });

  return moment(tempDate).format('MMMM DD, YYYY LT');
};

export const getDateRange = meta => {
  const { startDate, endDate, startTime, endTime } = toUISelectTime(
    meta,
  ).frequencyTime;

  const isStartInPast = moment().isAfter(
    getTime({
      date: startDate,
      timeOfDay: startTime,
    }),
    'minute',
  );

  const startTimeString = getFormatedTime({
    date: startDate,
    timeOfDay: startTime,
  });

  let endTimeString = null;

  if (meta.endDate) {
    endTimeString = getFormatedTime({
      date: endDate,
      timeOfDay: endTime,
    });
  }

  return { isStartInPast, startTimeString, endTimeString };
};

export const getDateRangeContent = meta => {
  const { isStartInPast, startTimeString, endTimeString } = getDateRange(meta);

  let content = `from <b>${startTimeString}</b>`;

  if (endTimeString) {
    content = `${content} to <b>${endTimeString}</b>`;
  }

  if (isStartInPast) {
    content += `. The start date of the journey is in the past`;
  }

  return content;
};
