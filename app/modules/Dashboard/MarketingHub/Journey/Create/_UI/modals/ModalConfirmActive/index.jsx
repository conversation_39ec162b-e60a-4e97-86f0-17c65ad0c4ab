/* eslint-disable react/prop-types */
import React from 'react';
import { DialogActions, DialogContent, DialogTitle } from '@material-ui/core';
import { UIButton } from '@xlab-team/ui-components';
import { useDispatch, useSelector } from 'react-redux';
import { StyledDialog } from './styled';
import {
  makeSelectMainCreateWorkflow,
  makeSelectTriggerNode,
} from '../../../selectors';
import { makeSelectJourneyChannelActive } from '../../../../selectors';
import { getConfirmContent } from './utils';
import { updateValue } from '../../../../../../../../redux/actions';
import { ModalV2 } from '@antscorp/antsomi-ui';

const ModalConfirmActive = ({ moduleConfig }) => {
  const dispatch = useDispatch();

  const channel = useSelector(makeSelectJourneyChannelActive());
  const main = useSelector(state =>
    makeSelectMainCreateWorkflow()(state, { moduleConfig }),
  );
  const triggerNode = useSelector(makeSelectTriggerNode(moduleConfig));

  const { modalConfirmActive } = main;

  const handleConfirm = activated => {
    const action = updateValue(`${moduleConfig.key}@@MODAL_CONFIRM_ACTIVE`, {
      value: { show: false },
      activated,
    });

    return () => dispatch(action);
  };

  return (
    <ModalV2
      open={modalConfirmActive.show}
      title="Confirm Activate"
      okText="Activate"
      cancelText="Cancel"
      onOk={handleConfirm(true)}
      onCancel={handleConfirm(false)}
      zIndex={1400}
      styles={{ header: { marginBottom: 0 }, footer: { marginTop: 0 } }}
    >
      {getConfirmContent(triggerNode, channel)}
    </ModalV2>
  );
};

export default ModalConfirmActive;
