/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
import React, { useEffect } from 'react';
import { PropTypes } from 'prop-types';
import { connect } from 'react-redux';

import {
  UIModal,
  UIModalHeader,
  UIModalBody,
  UIModalFooter,
} from '@xlab-team/ui-components';

import styled from 'styled-components';
import ModalFooter from 'components/Molecules/ModalFooter/index';
import { UIButton as Button } from '@xlab-team/ui-components';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { addNotification } from '../../../../../../../redux/actions';
// import { makeStyles } from '@material-ui/core';

const WrapperBody = styled.div``;

export const ButtonStyle = styled(Button)`
  margin: 0rem 0.25rem;
`;

export const ModalFooterStyle = styled(ModalFooter)`
  justify-content: flex-end;
  padding-right: 2rem;
`;

function ModalFlowChartConfirmChangeItemTypeId(props) {
  const { node } = props;
  useEffect(() => {
    if (props.isOpen) {
      // console.log('....', array, arrayBranch);
    }
  }, [props.isOpen]);

  const onCancel = () => {
    props.toggle(false);
  };

  const onApply = () => {
    props.toggle(false);
    props.callback('CONFIRM_RESET_ITEM_TYPE_ID', true);
  };

  // const classes = useStyles();

  return (
    <UIModal isOpen={props.isOpen} toggle={props.toggle}>
      <UIModalHeader>
        {getTranslateMessage(TRANSLATE_KEY._WARNING, 'Warning!')}
      </UIModalHeader>
      <UIModalBody>
        <WrapperBody className="p-bottom-2">
          {getTranslateMessage(TRANSLATE_KEY._WARN_CHANGE_AUDIENCE_TYPE, '')}
        </WrapperBody>
      </UIModalBody>
      <UIModalFooter>
        <ButtonStyle theme="outline" onClick={onCancel}>
          {getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel')}
        </ButtonStyle>
        <ButtonStyle theme="primary" onClick={onApply}>
          {getTranslateMessage(TRANSLATE_KEY._ACT_CONFIRM, 'Confirm')}
        </ButtonStyle>
      </UIModalFooter>
    </UIModal>
  );
}

function mapDispatchToProps(dispatch) {
  return {
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

ModalFlowChartConfirmChangeItemTypeId.propTypes = {
  isOpen: PropTypes.bool,
  toggle: PropTypes.func,
};
ModalFlowChartConfirmChangeItemTypeId.defaultProps = {
  isOpen: false,
  toggle: () => {},
};
export default connect(
  null,
  mapDispatchToProps,
)(ModalFlowChartConfirmChangeItemTypeId);
