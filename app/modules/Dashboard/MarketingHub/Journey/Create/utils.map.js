/* eslint-disable camelcase */
/* eslint-disable no-else-return */
/* eslint-disable no-param-reassign */
import { Map } from 'immutable';
import _, { forEach } from 'lodash';

import {
  safeParse,
  generateKey,
  getObjectPropSafely,
} from '../../../../../utils/common';
import {
  CATALOG_CODE,
  NODE_DESTINATION_ICON,
  NODE_ICON,
  NODE_TYPE,
} from './Content/Nodes/constant';
import {
  toNodeTriggerScheduledAPI,
  toNodeTriggerScheduleUI,
} from './Content/Nodes/TriggerScheduled/utils';
import {
  toNodeTriggerEventBasedAPI,
  toNodeTriggerEventBasedUI,
} from './Content/Nodes/TriggerEventBased/utils';
import {
  toNodeDestinationAPI,
  toNodeDestinationUI,
} from './Content/Nodes/Destination/utils';
import {
  toNodeClassicListBranchAPI,
  toNodeClassicListBranchUI,
} from './Content/Nodes/ClassicListBranch/utils';
import { toNodeDelayAPI, toNodeDelayUI } from './Content/Nodes/Delay/utils';
import {
  toNodeBlastCampaignAudiences,
  toNodeConditionYesAPI,
  toNodeConditionYesUI,
} from './Content/Nodes/ConditionYes/utils';
import { toNodeFilterAPI, toNodeFilterUI } from './Content/Nodes/Filter/utils';
import {
  toNodeConditionNoAPI,
  toNodeConditionNoUI,
} from './Content/Nodes/ConditionNo/utils';
import {
  toNodeSplitBranchUI,
  toNodeSplitBranchAPI,
} from './Content/Nodes/SplitBranch/utils';
import {
  toNodeSplitNodeUI,
  toNodeSplitNodeAPI,
} from './Content/Nodes/SplitNode/utils';
import {
  toNodeWaitEventUI,
  toNodeWaitEventAPI,
} from './Content/Nodes/WaitEvent/utils';
import {
  toNodeUpdateInfoAPI,
  toNodeUpdateInfoUI,
} from './Content/Nodes/UpdateInfo/utils';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import {
  toNodeUpdateSegmentAPI,
  toNodeUpdateSegmentToUI,
} from './Content/Nodes/UpdateSegment/utils';
import {
  EMAIL_TEMPLATE,
  JSON_TEMPLATE,
  MEDIA_TEMPLATE,
} from '../../../../../components/common/UIEditorPersonalization/utils';
import { random } from '../../../../../components/common/UIEditorPersonalization/utils.3rd';
import { RESET_DATA_LIST_TEMPLATE } from './Content/Nodes/Destination/constants';
import {
  toNodeWFRBranchAPI,
  toNodeWFRBranchUI,
} from './Content/Nodes/WFRBranch/utils';
import {
  toNodeWFRNodeAPI,
  toNodeWFRNodeUI,
} from './Content/Nodes/WFRNode/utils';
// import TRANSLATE_KEY from '../../../../../messages/constant';

// {key: 'if_then_branch', icon: 'icon-antsomi-diagram', label: 'if/then branch', type: 1},
//     {key: 'scheduled_trigger', icon: 'icon-antsomi-clock-o', label: 'Scheduled Trigger', type: 2},
//     {key: 'action_based_trigger', icon: 'icon-antsomi-action-trigger', label: 'Action-based Trigger', type: 3},
//     {key: 'delay', icon: 'icon-antsomi-hourglass-end', label: 'Delay', type: 4},
//     {key: 'divider-1', icon: '', label: '', type: 'divider'},
//     {key: 'http_api', icon: 'icon-antsomi-wifi', label: 'HTTP API', type: 5},
//     {key: 'messenger', icon: 'icon-messenger', label: 'Messenger', type: 6},
//     {key: 'mepuzz', icon: 'icon-mepuzz', label: 'Mepuzz', type: 7},
//     {key: 'amazon_ses', icon: 'icon-amazon-ses', label: 'Amazon SES', type: 8},
//     {key: 'erp_api_bibomart', icon: 'icon-erp-api-bibomart', label: 'ERP API BIBOMART', type: 9},
//     {key: 'divider-2', icon: '', label: '', type: 'divider'},
//     {key: 'personalization', icon: 'icon-antsomi-profile', label: 'Personalization', type: 10}

function getMetaData(
  node,
  infoNode,
  design,
  variantIdArray,
  channelActive,
  versionMT,
  copyId,
  thumbnails,
) {
  const nodeType = node.type;
  if (nodeType === NODE_TYPE.SCHEDULED) {
    return toNodeTriggerScheduledAPI(infoNode, channelActive);
  } else if (nodeType === NODE_TYPE.EVENT_BASED) {
    return toNodeTriggerEventBasedAPI(infoNode, channelActive);
  } else if (
    nodeType === NODE_TYPE.CLASSIC_LIST_BRANCH ||
    nodeType === NODE_TYPE.PARALLEL_LIST_BRANCH
  ) {
    return toNodeClassicListBranchAPI(infoNode);
  } else if (nodeType === NODE_TYPE.DESTINATION) {
    const destination = infoNode.get('destination') || {};
    const status = infoNode.get('status');

    return toNodeDestinationAPI({
      destination,
      status,
      design,
      variantIdArray,
      versionMT,
      copyId,
      thumbnails,
    });
  } else if (nodeType === NODE_TYPE.WFR_BRANCH) {
    return toNodeWFRBranchAPI(infoNode);
  } else if (
    nodeType === NODE_TYPE.WFR_NODE ||
    nodeType === NODE_TYPE.WFR_NODE_NO
  ) {
    return toNodeWFRNodeAPI(infoNode, node);
  } else if (nodeType === NODE_TYPE.DELAY) {
    return toNodeDelayAPI(infoNode);
  } else if (nodeType === NODE_TYPE.CONDITION_YES) {
    return toNodeConditionYesAPI(infoNode, node);
  } else if (nodeType === NODE_TYPE.CONDITION_NO) {
    return toNodeConditionNoAPI(infoNode, node);
  } else if (nodeType === NODE_TYPE.FILTER) {
    return toNodeFilterAPI(infoNode, node);
  } else if (nodeType === NODE_TYPE.SPLIT_BRANCH) {
    return toNodeSplitBranchAPI(infoNode, node);
  } else if (nodeType === NODE_TYPE.SPLIT_NODE) {
    return toNodeSplitNodeAPI(infoNode, node);
  } else if (nodeType === NODE_TYPE.WAIT_EVENT) {
    return toNodeWaitEventAPI(infoNode, node);
  } else if (nodeType === NODE_TYPE.UPDATE_INFO) {
    return toNodeUpdateInfoAPI(infoNode, node);
  } else if (nodeType === NODE_TYPE.UPDATE_SEGMENT) {
    return toNodeUpdateSegmentAPI(infoNode, design);
  } else if (nodeType === NODE_TYPE.AB_SPLIT_CONTROL_NODE) {
    return toNodeAbSplitControl({ infoNode, node, channelActive, design });
  }

  return {};
}

/** To UI from API */
function toNodeAbSplitControl({ infoNode, channelActive, design }) {
  const controlGroupData = infoNode.get('controlGroup');
  const weight = infoNode.get('weight');
  const channelId = +channelActive.value;

  const { campaignId, variantIds, order } = controlGroupData;

  const meta = {
    branchName: 'Control Group',
    description: '',
    weight: +weight / 100,
    isUpdated: 1,
    channelId,
    campaignId: null,
    variantIds: [],
    order,
  };

  if (campaignId && variantIds && design === 'update') {
    meta.campaignId = campaignId;
    meta.variantIds = variantIds;
    meta.isUpdated = 0;
  } else {
    const id = random(8);
    const newKey = `fe_${id}`;
    meta.campaign = {
      status: 1,
      campaignName: `Control Group - #STORY_ID# - ${order}`,
      campaignId: null,
      campaignSetting: {
        algoMethod: 'random',
        random: {
          [newKey]: 1,
        },
        deliveryTimeConfig: {
          type: 'all_time',
          range: [],
          mode: 'delay',
        },
      },
      custom_inputs: {},
    };
    meta.variants = [
      {
        variantId: null,
        variantKey: newKey,
        variantName: `Control Group - #STORY_ID# - ${order}`,
        contentSetting: {
          objectWidgetInput: {},
          destinationInput: {
            text: 'This is control group',
          },
          variantExtraData: {},
        },
        status: 1,
        custom_inputs: {},
      },
    ];
  }

  return meta;
}

function toReducerFromMetaData(
  flattenNodes,
  node,
  metadata,
  role,
  customInput,
  isDisplayDiagramBlastCampaign = false,
) {
  const nodeType = node.type;

  if (nodeType === NODE_TYPE.EVENT_BASED) {
    return toNodeTriggerEventBasedUI(metadata, customInput);
  } else if (
    nodeType === NODE_TYPE.CLASSIC_LIST_BRANCH ||
    nodeType === NODE_TYPE.PARALLEL_LIST_BRANCH
  ) {
    return toNodeClassicListBranchUI(metadata);
  } else if (nodeType === NODE_TYPE.DESTINATION) {
    return toNodeDestinationUI(metadata, role);
  } else if (nodeType === NODE_TYPE.SCHEDULED) {
    return toNodeTriggerScheduleUI(metadata, customInput);
  } else if (nodeType === NODE_TYPE.WFR_BRANCH) {
    return toNodeWFRBranchUI(metadata);
  } else if (
    nodeType === NODE_TYPE.WFR_NODE ||
    nodeType === NODE_TYPE.WFR_NODE_NO
  ) {
    return toNodeWFRNodeUI(metadata);
  } else if (nodeType === NODE_TYPE.DELAY) {
    return toNodeDelayUI(metadata);
  } else if (nodeType === NODE_TYPE.FILTER) {
    return toNodeFilterUI(metadata);
  } else if (nodeType === NODE_TYPE.CONDITION_YES) {
    if (isDisplayDiagramBlastCampaign) {
      return toNodeBlastCampaignAudiences(metadata);
    }
    return toNodeConditionYesUI(metadata);
  } else if (nodeType === NODE_TYPE.CONDITION_NO) {
    return toNodeConditionNoUI(metadata);
  } else if (nodeType === NODE_TYPE.SPLIT_BRANCH) {
    return toNodeSplitBranchUI(metadata, node, flattenNodes);
  } else if (
    nodeType === NODE_TYPE.SPLIT_NODE ||
    nodeType === NODE_TYPE.AB_SPLIT_CONTROL_NODE
  ) {
    return toNodeSplitNodeUI(metadata);
  } else if (nodeType === NODE_TYPE.WAIT_EVENT) {
    return toNodeWaitEventUI(metadata);
  } else if (nodeType === NODE_TYPE.UPDATE_INFO) {
    return toNodeUpdateInfoUI(metadata);
  } else if (nodeType === NODE_TYPE.UPDATE_SEGMENT) {
    return toNodeUpdateSegmentToUI(metadata);
  }

  return Map({});
}

function serializeNodeAPI(
  node,
  infoNodes,
  design,
  feKeyVariantId,
  channelActive,
  versionMT,
  copyId,
  thumbnails,
) {
  const variantIdArray = [];
  if (
    feKeyVariantId &&
    feKeyVariantId.length > 0 &&
    node.type === 'DESTINATION'
  ) {
    feKeyVariantId.forEach(data => {
      // console.log(data, node.nodeId);
      if (data.actionId === node.nodeId) {
        variantIdArray.push(data);
      }
    });
  }
  const tmp = {
    label: node.label,
    actionId: node.nodeId,
    actionType: node.type,
    metadata: getMetaData(
      node,
      safeParse(infoNodes.get(node.nodeId), Map({})),
      design,
      variantIdArray,
      channelActive,
      versionMT,
      copyId,
      thumbnails,
    ),
    branchs: serializeWorkflowSettingAPI(
      node.branchs,
      infoNodes,
      design,
      feKeyVariantId,
      channelActive,
      versionMT,
      copyId,
      thumbnails,
    ),
  };
  return tmp;
}

function serializeNodeUI(node, parentId, mapNode) {
  // console.log(node, mapNode);
  const tmp = {
    // ...node,
    label: node.label,
    nodeId: node.actionId,
    type: node.actionType,
    parentId,
    metadata: node.metadata,
    icon: NODE_ICON[node.actionType],
    iconUrl: '',
  };
  // console.log(tmp);
  let nodeInfo = mapNode[node.actionType];
  if (nodeInfo) {
    // console.log('nodeInfo', nodeInfo);
    tmp.icon = nodeInfo.icon;
    tmp.iconUrl = nodeInfo.iconUrl;

    tmp.catalogCode = nodeInfo.catalogCode;
    tmp.channelCode = nodeInfo.channelCode;
    tmp.channelId = nodeInfo.channelId;
  }

  if (node.actionType === NODE_TYPE.DESTINATION) {
    nodeInfo = mapNode[node.metadata.catalogId];
    if (nodeInfo !== undefined) {
      tmp.channelCode = nodeInfo.channelCode;
      tmp.iconUrl = nodeInfo.iconUrl;
      tmp.catalogCode = nodeInfo.catalogCode;
      tmp.channelId = nodeInfo.channelId;
    }
    tmp.value = node.metadata.catalogId;
    tmp.channelId = node.metadata.channelId;

    if (Object.keys(NODE_DESTINATION_ICON).includes(tmp.catalogCode)) {
      tmp.icon = NODE_DESTINATION_ICON[tmp.catalogCode];
      delete tmp.iconUrl;
    }
  } else if (
    node.actionType === NODE_TYPE.WFR_NODE ||
    node.actionType === NODE_TYPE.WFR_NODE_NO ||
    node.actionType === NODE_TYPE.CONDITION_YES ||
    node.actionType === NODE_TYPE.CONDITION_NO
  ) {
    tmp.label = node.metadata.branchName;
  } else if (node.actionType === NODE_TYPE.SPLIT_NODE) {
    tmp.label = node.metadata.branchName;
  } else if (node.actionType === NODE_TYPE.AB_SPLIT_CONTROL_NODE) {
    tmp.label = node.metadata.branchName;
    tmp.isDisableActionBranch = true;
  } else if (nodeInfo) {
    tmp.label = nodeInfo.label;
  }

  return tmp;
  // return tmp;
}

export function serializeWorkflowSettingAPI(
  trees,
  infoNodes,
  design = 'update',
  feKeyVariantId,
  channelActive,
  versionMT,
  copyId,
  thumbnails,
) {
  const data = [];
  if (Array.isArray(trees) && trees.length > 0) {
    trees.forEach(tree => {
      data.push(
        serializeNodeAPI(
          tree,
          infoNodes,
          design,
          feKeyVariantId,
          channelActive,
          versionMT,
          copyId,
          thumbnails,
        ),
      );
    });
  } else {
    data.push({
      label: 'END',
      actionId: generateKey(),
      actionType: NODE_TYPE.END,
      metadata: {},
      branchs: [],
    });
  }
  return data;
}

export function loopNodes(node, cb) {
  cb(node);

  if (Array.isArray(node.branchs)) {
    forEach(node.branchs, childrenNode => {
      loopNodes(childrenNode, cb);
    });
  }
}

export function callbackSerializeFlattenNodesUI(branchs, parentId, mapNode) {
  let list = [];

  branchs.forEach(branch => {
    if (branch.actionType !== NODE_TYPE.END) {
      const node = serializeNodeUI(branch, parentId, mapNode);
      list.push(node);
      if (Array.isArray(branch.branchs)) {
        list = list.concat(
          callbackSerializeFlattenNodesUI(branch.branchs, node.nodeId, mapNode),
        );
      }
    }
  });

  return list;
}

export function serializeFlattenNodesUI({
  branchs = {},
  mapNode,
  role = 'INIT',
  customInput = {},
  isDisplayDiagramBlastCampaign = false,
} = {}) {
  try {
    // console.log('mapNode', customInput);
    if (Object.keys(branchs).length === 0) {
      return { flattenNodes: [], cacheNodes: Map({}), nodeTrigger: {} };
    }
    let listBranches = branchs;
    if (!Array.isArray(branchs)) {
      listBranches = [branchs];
    }

    const list = callbackSerializeFlattenNodesUI(listBranches, null, mapNode);

    const flattenNodes = [];
    const fullInfoflattenNodes = [];

    let cacheNodes = Map({});
    const nodeTrigger = list[0];

    list.forEach(item => {
      fullInfoflattenNodes.push(item);
    });
    // console.log(list);
    list.forEach(item => {
      // const { metadata: itemMetadata, ...otherData } = item;
      // // console.log('metadata', metadata);

      // // TODO: push toReducerFromMetaData instead
      // flattenNodes.push(itemMetadata);

      // const metadata = toReducerFromMetaData(
      //   fullInfoflattenNodes,
      //   otherData,
      //   itemMetadata,
      //   role,
      //   customInput,
      //   isDisplayDiagramBlastCampaign,
      // );
      // cacheNodes = cacheNodes.set(item.nodeId, metadata);

      // console.log({ item, metadata, itemMetadata });

      const { metadata, ...otherData } = item;
      // console.log('metadata', metadata);
      flattenNodes.push(item);
      cacheNodes = cacheNodes.set(
        item.nodeId,
        toReducerFromMetaData(
          fullInfoflattenNodes,
          otherData,
          metadata,
          role,
          customInput,
          isDisplayDiagramBlastCampaign,
        ),
      );
    });

    return { flattenNodes, cacheNodes, nodeTrigger };
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/utils.map.js',
      func: 'updateDataVariantWithFeConfigID',
      data: err.stack,
    });
    console.log(err);
  }
  return { flattenNodes: [], cacheNodes: Map({}), nodeTrigger: {} };
}

export function internalGetFeConfigVariantExtra(res, item) {
  if (item.actionType === 'DESTINATION') {
    const { variants = [] } = item.metadata;
    // console.log('internalGetFeConfigVariantExtra', variants);

    variants.forEach(obj => {
      const { variantKey, contentSetting = {} } = obj;
      const { variantExtraData = {} } = contentSetting;
      Object.keys(variantExtraData).forEach(key => {
        const tmp = variantExtraData[key] || {};
        const {
          type = 'editor',
          fe_config_id = null,
          template_settings = {},
        } = tmp;
        const isMediaTemplate =
          type === MEDIA_TEMPLATE && tmp.design === MEDIA_TEMPLATE;
        const isJsonTemplate =
          type === JSON_TEMPLATE && tmp.design === JSON_TEMPLATE;
        const isEmailTemplate =
          type === EMAIL_TEMPLATE && tmp.design === EMAIL_TEMPLATE;

        if (
          isMediaTemplate ||
          isJsonTemplate ||
          isEmailTemplate ||
          (type === 'editor' && tmp.design === 'template')
        ) {
          // emailConfig || properties
          const properties =
            isMediaTemplate || isJsonTemplate || isEmailTemplate
              ? tmp.properties || {}
              : tmp.emailConfig;
          // const { properties = {}, emailConfig = {} } = tmp;

          if (fe_config_id === null) {
            res.create.push({
              actionId: item.actionId,
              variantKey,
              emailConfig: properties,
              fieldName: key,
              fe_config_id,
              cachedId:
                !tmp.properties &&
                !_.isEmpty(obj.cachedIds) &&
                Object.keys(obj.cachedIds).includes(template_settings.id)
                  ? obj.cachedIds[template_settings.id]
                  : null,
            });
          }
          // có những case emailConfig chưa được chỉnh sửa thì khi đó nếu update sẽ xóa đi content trước đó.
          else if (Object.keys(properties).length > 0) {
            res.update.push({
              actionId: item.actionId,
              variantKey,
              emailConfig: properties,
              fieldName: key,
              fe_config_id,
            });
          }
        }
      });
    });
  }
  if (item.branchs.length > 0) {
    item.branchs.forEach(obj => {
      internalGetFeConfigVariantExtra(res, obj);
    });
  }
  // return arr;
}

export function getFeConfigVariantExtra(data) {
  const res = { create: [], update: [] };
  try {
    const { workflow_setting = [] } = data;
    internalGetFeConfigVariantExtra(res, workflow_setting);
    // console.log('getFeConfigVariantExtra', paramsCreate);
    return res;
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/utils.map.js',
      func: 'getFeConfigVariantExtra',
      data: err.stack,
    });
    console.log(err);
  }
  return res;
}

export const loopVariantExtraData = ({
  variantExtraData = {},
  handler = () => {},
}) => {
  Object.entries(variantExtraData).forEach(([propertyKey, propertyValue]) => {
    const { type } = propertyValue;

    const isMediaTemplate =
      type === MEDIA_TEMPLATE && propertyValue.design === MEDIA_TEMPLATE;

    const isJsonTemplate =
      type === JSON_TEMPLATE && propertyValue.design === JSON_TEMPLATE;

    const isEmailTemplate =
      type === EMAIL_TEMPLATE && propertyValue.design === EMAIL_TEMPLATE;

    try {
      const info = {
        isMediaTemplate,
        isJsonTemplate,
        isEmailTemplate,
      };

      handler({
        key: propertyKey,
        value: propertyValue,
        info,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);

      addMessageToQueue({
        error: error.stack,
        func: 'loopVariantExtraData',
        args: { variantExtraData },
      });
    }
  });
};

export function internalUpdateDataVariantWithFeConfigID(item, params) {
  if (item.actionType === 'DESTINATION') {
    // console.log({ variants: item.metadata.variants, params });
    // const { variants = [] } = item.metadata;
    if (item.metadata.isUpdated === 1) {
      item.metadata.variants.forEach(obj => {
        // console.log('Test 123 ', { obj });
        const { variantKey, contentSetting = {} } = obj;
        // const { variantExtraData = {} } = contentSetting;
        const variantExtraData = _.cloneDeep(
          contentSetting.variantExtraData || {},
        );

        loopVariantExtraData({
          variantExtraData,
          handler: ({ key = '', value = {}, info = {} }) => {
            const { isMediaTemplate, isJsonTemplate, isEmailTemplate } = info;

            const { type = 'editor', design, fe_config_id = null } = value;

            if (
              isMediaTemplate ||
              isJsonTemplate ||
              isEmailTemplate ||
              (type === 'editor' && design === 'template')
            ) {
              if (fe_config_id === null) {
                const findObj = params.find(
                  tmpFind => tmpFind.variantKey === variantKey,
                );

                if (findObj) {
                  variantExtraData[key].fe_config_id = findObj.fe_config_id;
                }

                obj.contentSetting.variantExtraData = variantExtraData;
              }
            }
          },
        });
      });
    }
  }

  if (item.branchs.length > 0) {
    item.branchs.forEach(obj => {
      internalUpdateDataVariantWithFeConfigID(obj, params);
    });
  }
  // return arr;
}

function internalRemoveDataUnUseForAPI(branch, flattenNodes = [], opts = {}) {
  const { saveAsTemplate = false } = opts;

  if (branch.actionType === 'DESTINATION') {
    // console.log({ item });
    // const { variants = [] } = item.metadata;
    if (branch.metadata.isUpdated === 1) {
      const variants = [];
      const catalogId = getObjectPropSafely(() => branch.metadata.catalogId);

      branch.metadata.variants.forEach(obj => {
        const objClone = { ...obj };

        let indexSmartInboxApp;

        if (Array.isArray(flattenNodes)) {
          indexSmartInboxApp = flattenNodes.findIndex(
            each =>
              each.catalogCode === CATALOG_CODE.SMART_INBOX &&
              each.value === catalogId,
          );
        }

        if (indexSmartInboxApp !== -1) {
          const destinationInput = getObjectPropSafely(
            () => objClone.contentSetting.destinationInput,
          );
          const selectedTemplate = getObjectPropSafely(
            () => destinationInput.inboxTemplate,
          );

          if (
            selectedTemplate &&
            Array.isArray(RESET_DATA_LIST_TEMPLATE[selectedTemplate])
          ) {
            RESET_DATA_LIST_TEMPLATE[selectedTemplate].forEach(each => {
              _.update(destinationInput, [each], () => '');
            });
          }
        }

        const variantExtraData = safeParse(
          objClone.contentSetting.variantExtraData,
          {},
        );

        // console.log({ objClone });
        if (objClone.cachedIds) {
          delete objClone.cachedIds;
        }

        Object.keys(variantExtraData).forEach(key => {
          const tmp = { ...(variantExtraData[key] || {}) };
          if (tmp.design === 'template') {
            variantExtraData[key].emailConfig = {};
            // eslint-disable-next-line no-param-reassign
            objClone.contentSetting.variantExtraData = variantExtraData;
          }

          const needDeleteProperties = [
            MEDIA_TEMPLATE,
            JSON_TEMPLATE,
            EMAIL_TEMPLATE,
          ].includes(tmp.design);

          if (needDeleteProperties && !saveAsTemplate) {
            variantExtraData[key].thumbnail =
              getObjectPropSafely(
                () => variantExtraData[key].properties.thumbnail,
              ) || '';
            delete variantExtraData[key].properties;
            objClone.contentSetting.variantExtraData = variantExtraData;
          }
        });
        variants.push(objClone);
      });
      // eslint-disable-next-line no-param-reassign
      branch.metadata.variants = variants;
    }
  }

  if (branch.branchs.length > 0) {
    branch.branchs.forEach(obj => {
      internalRemoveDataUnUseForAPI(obj, flattenNodes, opts);
    });
  }
  // return arr;
}

export function removeDataUnUseForAPI(data, flattenNodes = [], opts = {}) {
  try {
    const output = _.cloneDeep(data);

    internalRemoveDataUnUseForAPI(output.workflow_setting, flattenNodes, opts);

    // console.log('getFeConfigVariantExtra', paramsCreate);
    return output;
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/utils.map.js',
      func: 'updateDataVariantWithFeConfigID',
      data: err.stack,
    });
    console.log(err);
  }
  return data;
}

export function updateDataVariantWithFeConfigID(data, params = []) {
  try {
    internalUpdateDataVariantWithFeConfigID(data.workflow_setting, params);
    // console.log('getFeConfigVariantExtra', paramsCreate);
    return data;
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/utils.map.js',
      func: 'updateDataVariantWithFeConfigID',
      data: err.stack,
    });
    console.log(err);
  }
  return data;
}

export const hardAudienceFilterForDefaultRichMenu = () => ({
  filters: [
    {
      includedFilters: {
        OR: [
          {
            AND: [
              {
                conditionType: 'customer_id',
                column: 'customer_id',
                data_type: 'string',
                operator: 'matches',
                value: ['all'],
              },
            ],
          },
        ],
      },
      excludedFilters: {
        OR: [],
      },
    },
  ],
  itemTypeId: -1003,
  object: 'customer',
});
