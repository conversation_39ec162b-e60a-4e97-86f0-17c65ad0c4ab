/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */
import isEmpty from 'lodash/isEmpty';
import isArray from 'lodash/isArray';
import get from 'lodash/get';
import set from 'lodash/set';
import uniq from 'lodash/uniq';
import mapValues from 'lodash/mapValues';

import { NODE_TYPE } from './Content/Nodes/constant';
import { validateNodeFilter } from './Content/Nodes/Filter/utils';
import { validateNodeTriggerEventBased } from './Content/Nodes/TriggerEventBased/utils';
import { validateNodeTriggerScheduled } from './Content/Nodes/TriggerScheduled/utils';
import { validateNodeDestination } from './Content/Nodes/Destination/utils';
import { validateNodeDelay } from './Content/Nodes/Delay/utils';
import { validateNodeYes } from './Content/Nodes/ConditionYes/utils';
import { validateConditionNo } from './Content/Nodes/ConditionNo/utils';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { validateNodeWaitEvent } from './Content/Nodes/WaitEvent/utils';
import { validateNodeUpdateInfo } from './Content/Nodes/UpdateInfo/utils';
import { validateNodeUpdateSegment } from './Content/Nodes/UpdateSegment/utils';
import { createFeVarinatId } from './Content/Nodes/Destination/utils.state';
import SelectorServices from '../../../../../services/Selector';
import OperateServices from '../../../../../services/Operate';
import {
  DATA_ACCESS_OBJECT,
  DEFAULT_STATUS,
} from '../../../../../utils/constants';
import {
  permissionEdit,
  permissionEditOrView,
} from '../../../../../utils/web/permission';
import { getObjectPropSafely } from '../../../../../utils/common';
import { loopNodes } from './utils.map';
import { find, map } from 'lodash';
import JourneyService from 'services/Journey';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { validateNodeWFRNode } from './Content/Nodes/WFRNode/utils';

const PATH = 'modules/Dashboard/MarketingHub/Journey/Create/utils.validate.js';

export const validateNodeActive = (
  activeNode,
  infoNode,
  isValidateFormatDateTime,
) => {
  try {
    const { type, nodeId } = activeNode;

    if (type === NODE_TYPE.EVENT_BASED) {
      const res = validateNodeTriggerEventBased(
        infoNode,
        isValidateFormatDateTime,
      );
      // console.log('object validateNodeTriggerEventBased', res, infoNode.toJS());
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.DESTINATION) {
      const res = validateNodeDestination(infoNode);
      // console.log('NODE_TYPE.DESTINATION', res);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.SCHEDULED) {
      const res = validateNodeTriggerScheduled(
        infoNode,
        isValidateFormatDateTime,
      );
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.FILTER) {
      const res = validateNodeFilter(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.DELAY) {
      const res = validateNodeDelay(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.WFR_NODE || type === NODE_TYPE.WFR_NODE_NO) {
      const res = validateNodeWFRNode(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.CONDITION_YES) {
      const res = validateNodeYes(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.CONDITION_NO) {
      const res = validateConditionNo(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.WAIT_EVENT) {
      const res = validateNodeWaitEvent(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.UPDATE_INFO) {
      const res = validateNodeUpdateInfo(infoNode);
      // console.log('res', res);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.UPDATE_SEGMENT) {
      const res = validateNodeUpdateSegment(infoNode);
      // console.log('res', res);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    }

    return { [nodeId]: false };
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/utils.validate.js',
      func: 'validateNodeActive',
      data: err.stack,
    });

    // eslint-disable-next-line no-console
    console.log(err);
  }

  // console.log('dataNodeActive===>', infoNode);
  return {};
};
export const validateMessage = (
  activeNode,
  infoNode,
  isValidateFormatDateTime,
) => {
  try {
    const { nodeId, type } = activeNode;

    if (type === NODE_TYPE.SCHEDULED) {
      const res = validateNodeTriggerScheduled(
        infoNode,
        isValidateFormatDateTime,
      );
      if (res.status === false) {
        return { [nodeId]: res.errors };
      }
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/utils.validate.js',
      func: 'validateNodeActive',
      data: err.stack,
    });

    // eslint-disable-next-line no-console
    console.log(err);
  }

  // console.log('dataNodeActive===>', infoNode);
  return {};
};
export const validateNodesDuplicateCampaignId = nodes => {
  const uniqueElements = [];

  if (
    !nodes ||
    !nodes.workflow_setting ||
    !nodes.workflow_setting.branchs ||
    nodes.workflow_setting.branchs.length === 0 ||
    !isArray(nodes.workflow_setting.branchs)
  ) {
    return true;
  }

  const { branchs } = nodes.workflow_setting;

  const validateCampaingId = branchsData => {
    branchsData.forEach(each => {
      if (each.actionType === 'DESTINATION' && each.metadata.campaignId) {
        uniqueElements.push(each.metadata.campaignId);
      }
      validateCampaingId(each.branchs);
    });
  };

  validateCampaingId(branchs);

  const isDuplicateCampaignId = uniqueElements.some(
    (element, index) => uniqueElements.indexOf(element) !== index,
  );
  return isDuplicateCampaignId;
  // isDuplicateCampaignId === true: Có id campaignId trùng || Sai
  // isDuplicateCampaignId === false: Có id campaignId trùng || Đúng
};

export const replaceDuplicateVariantKeys = ({ node, uniqVariantKeys = [] }) => {
  try {
    if (node.actionType === 'END' || Object.keys(node).length === 0) {
      return node;
    }

    if (node.branchs && node.branchs.length) {
      node.branchs.forEach(childNode => {
        if (childNode.actionType === NODE_TYPE.DESTINATION) {
          const variants = get(childNode, 'metadata.variants', []);

          if (variants.length) {
            // for replace random setting
            const mapOldVariantKeys = {};

            variants.forEach(variant => {
              const oldVariantKey = variant.variantKey;

              if (oldVariantKey) {
                const isDuplicate = uniqVariantKeys.includes(oldVariantKey);

                if (isDuplicate) {
                  const newVariantKey = createFeVarinatId();

                  // Reassign values to workflow_setting
                  variant = set(variant, 'variantKey', newVariantKey);

                  mapOldVariantKeys[oldVariantKey] = newVariantKey;
                }

                uniqVariantKeys.push(oldVariantKey);
              }
            });

            const random = get(
              childNode,
              'metadata.campaign.campaignSetting.random',
              {},
            );

            // replace variantKeys in random setting
            const newRandom = Object.entries(random).reduce((acc, cur) => {
              const [variantId, percent] = cur;

              const newVariantKey = mapOldVariantKeys[variantId];

              if (newVariantKey) {
                acc[newVariantKey] = percent;
              } else {
                acc[variantId] = percent;
              }

              return acc;
            }, {});

            // Reassign values to workflow_setting
            childNode = set(
              childNode,
              'metadata.campaign.campaignSetting.random',
              newRandom,
            );
          }
        }

        replaceDuplicateVariantKeys({
          node: childNode,
          uniqVariantKeys,
        });
      });
    }

    return {
      node,
      uniqVariantKeys,
    };
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/utils.validate.js',
      func: 'replaceDuplicateVariantKeys',
      data: error.stack,
    });
  }

  return null;
};

const hasDuplicates = a => {
  return uniq(a).length !== a.length;
};

export const handleReplaceDuplicateVariantKeys = workflowSetting => {
  try {
    // replace and check duplicate of new variantKeys, maximum 3 times
    for (let i = 0; i < 3; i += 1) {
      const {
        node: newWorkflowSetting,
        uniqVariantKeys,
      } = replaceDuplicateVariantKeys({
        node: workflowSetting,
      });

      const isDuplicate = hasDuplicates(uniqVariantKeys);

      if (!isDuplicate) {
        return newWorkflowSetting;
      }
    }

    return null;
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/utils.validate.js',
      func: 'handleReplaceDuplicateVariantKeys',
      data: error.stack,
    });
  }

  return null;
};

export const validateAllJourney = async nodes => {
  let data = {
    isValidate: true,
    error: { message: '', data: nodes, actionId: null },
  };

  if (isEmpty(nodes)) {
    data.isValidate = false;
    data.error.message = 'nodes is required';
  } else if (isEmpty(nodes.workflow_setting)) {
    data.isValidate = false;
    data.error.message = 'workflow_setting is required exist';
  } else if (!isArray(nodes.workflow_setting.branchs)) {
    data.isValidate = false;
    data.error.message = 'branchs is required typeof an array';
  } else if (nodes.workflow_setting.branchs.length === 0) {
    data.isValidate = false;
    data.error.message = 'branchs is require length > 0';
  } else if (nodes.trigger_type === 'SCHEDULED') {
    /* ----------- Check Case SCHEDULED: segmentIds not exist segment ----------- */
    const { workflow_setting } = nodes;
    const { audiences } = workflow_setting.metadata;
    const { excludedAudiences, includedAudiences } = audiences;

    if (isEmpty(audiences)) {
      data.isValidate = false;
      data.error.message = 'audiences is required';
    } else if (
      excludedAudiences.specificAudienceIds.length === 0 &&
      excludedAudiences.filters.OR.length === 0 &&
      includedAudiences.specificAudienceIds.length === 0 &&
      includedAudiences.filters.OR.length === 0
    ) {
      data.isValidate = false;
      data.error.message = 'SCHEDULED: SegmentIds Nothing';
    }
  }

  if (data.isValidate) {
    const nodeDestinations = [];

    loopNodes(nodes.workflow_setting, node => {
      if (node.actionType === NODE_TYPE.DESTINATION) {
        nodeDestinations.push(node);
      }
    });

    const validateDestinationResult = await valiateDestinations({
      destinations: nodeDestinations,
    });

    if (!validateDestinationResult.isValidate) {
      data = validateDestinationResult;
    }
  }

  return data;

  // isValidate => True => pass cho action tiep
};

export const valiateDestinations = async ({ destinations }) => {
  const result = {
    isValidate: true,
    error: { message: '', data: destinations, actionId: null },
  };

  destinations.forEach(dest => {
    if (!result.isValidate) return;

    const { metadata = {}, actionId } = dest;

    const noCampaign = !metadata.campaignId && !metadata.campaign;

    const noVariants =
      isEmpty(metadata.variantIds) && isEmpty(metadata.variants);

    if (noCampaign || noVariants) {
      result.isValidate = false;
      result.error.actionId = actionId;
    }
  });

  if (result.isValidate) {
    const campaignNamesValidateResult = await validateCampaignNames({
      nodeDestinations: destinations,
    });

    if (!campaignNamesValidateResult.isValidate) {
      const { info } = campaignNamesValidateResult;

      result.isValidate = false;
      result.error.message = `Campaigns with the same name: ${
        info.campaignName
      }`;
      result.error.actionId = info.actionId;
      result.error.detail = {
        actionId: info.actionId,
        actionType: NODE_TYPE.DESTINATION,
        errors: [getTranslateMessage(TRANSLATE_KEY._NOTIFICATION_NAMESAKE)],
        invalidFields: ['campaignName'],
      };
    }
  }

  return result;
};

export const validateCampaignNames = async ({ nodeDestinations }) => {
  const temp = [...nodeDestinations];

  const notChangedCampaigns = [];
  const newCampaigns = [];

  temp.forEach(node => {
    const { actionId, metadata } = node;

    const { campaignId, campaign } = metadata;

    if (!campaign && campaignId) {
      notChangedCampaigns.push({ actionId, campaignId });
    }

    if (campaign?.campaignName) {
      newCampaigns.push({
        actionId,
        campaignName: campaign.campaignName,
      });
    }
  });

  const { data: campaigns } = await JourneyService.campaigns.getByIds({
    data: {
      campaign_ids: map(notChangedCampaigns, c => c.campaignId),
    },
  });

  const campaignInfos = [...newCampaigns];

  notChangedCampaigns.forEach(info => {
    const { campaignId } = info;

    const campaign = find(campaigns, c => c.campaign_id === campaignId);

    if (!campaign) return;

    campaignInfos.push({
      campaignId,
      campaignName: campaign.campaign_name,
    });
  });

  const result = { isValidate: true, info: null };

  newCampaigns.forEach(info => {
    const { campaignName, actionId } = info;

    if (!result.isValidate) return;

    campaignInfos.forEach(campaign => {
      if (campaign.actionId === actionId) return;

      if (campaign.campaignName === campaignName) {
        result.isValidate = false;
        result.info = {
          actionId: campaign.actionId || actionId,
          campaignName,
        };
      }
    });
  });

  return result;
};

export const validateCampaignEmptySelected = (campaignActive = {}) => {
  const { actionId = '', audiences = {} } = campaignActive;
  let isError = false;
  if (isEmpty(audiences)) {
    return { [actionId]: true };
  }

  const { includedAudiences = {}, excludedAudiences = {} } = audiences;
  const includeAudienceTypes = includedAudiences.audienceTypes;
  const excludeAudienceTypes = excludedAudiences.audienceTypes;
  const includeFilterOR = includedAudiences.filters.OR || [];
  const excludeFilterOR = excludedAudiences.filters.OR || [];

  if (isEmpty(includeAudienceTypes)) {
    return { [actionId]: true };
  }

  if (
    (isEmpty(includedAudiences.specificAudienceIds) &&
      includeFilterOR.some(filterItem => isEmpty(filterItem.AND))) ||
    (isEmpty(includedAudiences.specificAudienceIds) && isEmpty(includeFilterOR))
  ) {
    isError = true;
  }

  if (isError) {
    return { [actionId]: isError };
  }

  if (!isEmpty(excludeAudienceTypes)) {
    if (
      (isEmpty(excludedAudiences.specificAudienceIds) &&
        isEmpty(excludeFilterOR)) ||
      (isEmpty(excludedAudiences.specificAudienceIds) &&
        excludeFilterOR.some(filterItem => isEmpty(filterItem.AND)))
    ) {
      isError = true;
    }
  }

  return { [actionId]: isError };
};

export const validateCampaignSenTo = (
  targetAudience = {},
  nodeId = '',
  design = '',
) => {
  const { currentData = {}, backup = {} } = targetAudience;
  const [errorKey, message, emptyDataAudiences] = [nodeId, '', false];

  if (!isEmpty(backup) && isEmpty(currentData)) {
    if (design === 'update' && isEmpty(backup)) {
      return { errorKey, message, isEmpty: emptyDataAudiences };
    }

    const isNext = Object.keys(backup || {}).some(
      audienceKey => !isEmpty(backup[audienceKey]),
    );

    if (isNext) {
      return {
        errorKey,
        message,
        isEmpty: emptyDataAudiences,
      };
    }

    return {
      errorKey,
      message: `*This field can't be empty`,
      isEmpty: true,
    };
  }

  if (design === 'update' && isEmpty(currentData)) {
    return { errorKey, message, isEmpty: emptyDataAudiences };
  }

  const isNext = Object.keys(currentData || {}).some(
    audienceKey => !isEmpty(currentData[audienceKey]),
  );

  if (isNext) {
    return {
      errorKey,
      message,
      isEmpty: emptyDataAudiences,
    };
  }

  return {
    errorKey,
    message: `*This field can't be empty`,
    isEmpty: true,
  };
};

export const validateSegmentsStatus = async ({ segmentIds }) => {
  const params = {
    data: {
      objectType: 'BO_SEGMENTS',
      isSnakeCase: 1,
      limit: null,
      sort: 'utime',
      sd: 'desc',
      columns: [
        'segment_display',
        'segment_id',
        'item_type_id',
        'segment_code',
      ],
      filters: {
        OR: [
          {
            AND: [
              {
                column: 'segment_id',
                data_type: 'number',
                operator: 'matches',
                value: segmentIds.map(Number),
              },
              {
                column: 'status',
                data_type: 'number',
                operator: 'matches',
                value: [1, 2, 3, 4],
              },
            ],
          },
        ],
      },
    },
  };

  const { data } = await SelectorServices.segments.getListByObjectType(params);

  const filterByStatus = status =>
    data.filter(i => status === +i.status).map(i => i.segment_id);

  return {
    archived: filterByStatus(DEFAULT_STATUS.ARCHIVED),
    removed: filterByStatus(DEFAULT_STATUS.REMOVED),
  };
};

export const validateSegmentsInNodes = async ({
  segmentIds,
  segmentsByNodes,
  flattenNodes,
}) => {
  let results = {};

  try {
    results = mapValues(segmentsByNodes, () => ({
      nonPermissions: [],
      archived: [],
      removed: [],
    }));

    const promies = [
      OperateServices.permission.getInfo({
        body: {
          objects: segmentIds.map(id => ({
            objectId: id,
            objectType: DATA_ACCESS_OBJECT.SEGMENT,
          })),
        },
      }),
      validateSegmentsStatus({ segmentIds }),
    ];

    const [
      { data: accessInfos },
      { archived: archivedSegments, removed: removedSegments },
    ] = await Promise.all(promies);

    const validateFnMap = {
      [NODE_TYPE.SCHEDULED]: permissionEditOrView,
      [NODE_TYPE.FILTER]: permissionEditOrView,
      [NODE_TYPE.CONDITION_YES]: permissionEditOrView,
      [NODE_TYPE.UPDATE_SEGMENT]: permissionEdit,
    };

    Object.entries(segmentsByNodes).forEach(([nodeId, segemntIdsByNode]) => {
      segemntIdsByNode.forEach(segmentId => {
        const accessInfo = accessInfos.find(i => i.objectId === segmentId);
        const nodeInfo = flattenNodes.find(i => i.nodeId === nodeId);
        const fnCheck = () => validateFnMap[nodeInfo.type]({ accessInfo });

        if (getObjectPropSafely(fnCheck, null) === false) {
          results[nodeId].nonPermissions.push(segmentId);
        }

        if (archivedSegments.includes(segmentId)) {
          results[nodeId].archived.push(segmentId);
        }

        if (removedSegments.includes(segmentId)) {
          results[nodeId].removed.push(segmentId);
        }
      });
    });
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      function: validateSegmentsInNodes.name,
      data: error.stack,
    });

    // eslint-disable-next-line no-console
    console.log(error);
  }

  return results;
};

export const mergeIsErrorNodes = (...errorObjs) => {
  const result = {};

  errorObjs.forEach(errorObj => {
    Object.entries(errorObj).forEach(([nodeId, isError]) => {
      if (result[nodeId]) return;

      if (result[nodeId] === undefined && isError) {
        result[nodeId] = true;
      }
    });
  });

  return result;
};
