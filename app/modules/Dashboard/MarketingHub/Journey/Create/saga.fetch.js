import { call, select, put } from 'redux-saga/effects';
import _isEmpty from 'lodash/isEmpty';
// import { push } from 'react-router-redux';
// import { delay } from 'redux-saga';
// import delay from '@redux-saga/delay-p';
import JourneyServices from 'services/Journey';
import UserAttributesServices from 'services/UserAttributes';
import { MODULE_CONFIG as MODULE_CONFIG_DETAIL } from '../Detail/config';
import { makeSelectJourneyChannelActive } from '../selectors';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { safeParse } from '../../../../../utils/common';
import { updateDone, updateValue } from '../../../../../redux/actions';
import { getFeConfigVariantExtra } from './utils.map';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
  MENU_CODE,
} from '../../../../../utils/web/permission';
import { getCurrentAccessUserId } from '../../../../../utils/web/cookie';

export function* handleStoryFetchListNode(action) {
  try {
    const channelActive = yield select(makeSelectJourneyChannelActive());

    const searchParams = new URLSearchParams(window.location.search);

    const channelId = searchParams.get('channelId');
    // console.log('channelActive', channelActive);
    const res = yield call(JourneyServices.node.getList, {
      channelId: channelId || channelActive.value,
    });
    // Bắt buộc trong modal show các node thì phải có 3 group để render cho đúng
    // Vì thế  đối với single channel thì phải có thêm 1 group rỗng ở vị trí đầu tiên
    const data = [...res.data];
    // console.log({ data });
    if (![8, 10].includes(channelActive.value)) {
      data.unshift({
        groupId: '',
        groupName: '',
        nodes: [],
        translateCode: '',
      });
    }
    // return data;
    return data;
  } catch (err) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/saga.fetch.js',
      func: 'handleStoryFetchListNode',
      data: err.stack,
    });
    console.log('err', err);
  }
  return [];
}

export function* handleFetchJourneyDetail(activeId) {
  try {
    // check quyền VIEW EVERYTHING và truyền param _owner_id tương ứng
    const hasRoleViewEverything = checkingRoleScope(
      MENU_CODE.JOURNEY,
      APP_ACTION.VIEW,
      APP_ROLE_SCOPE.EVERYTHING,
    );
    const res = yield call(JourneyServices.versionHistory.getDetail, {
      objectId: activeId,
      _owner_id: hasRoleViewEverything ? null : getCurrentAccessUserId(),
    });
    return res.data;
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Detail/saga.js',
      func: 'handleFetchObjectDetail',
      data: error.stack,
    });
    console.log(error);
    return {};
  }
}

export function* getAndUpdateAvailabelStatusAndpermission(activeId, args) {
  try {
    const prefix = args.moduleConfig.key;
    const journeyDetail = yield call(handleFetchJourneyDetail, activeId);
    // console.log(journeyDetail);
    // eslint-disable-next-line camelcase
    const available_status = safeParse(journeyDetail.available_status, []);
    // eslint-disable-next-line camelcase
    const accepted_actions = safeParse(journeyDetail.accepted_actions, []);
    const status = safeParse(journeyDetail.status, '');
    yield put(
      updateValue(`${prefix}@@AVAILABLE_STATUS&ACCEPTED_ACTIONS@@`, {
        available_status,
        accepted_actions,
      }),
    );
    yield put(updateDone(`${prefix}@@ACTIVE_ROW_STATUS@@`, status));
    yield put(
      updateValue(`${MODULE_CONFIG_DETAIL.key}@@STORY_STATUS@@`, {
        status,
        availableStatus: available_status,
        accepAction: accepted_actions,
      }),
    );
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Detail/saga.js',
      func: 'handleFetchObjectDetail',
      data: error.stack,
    });
    console.log(error);
  }
}

export function* generateFeConfigWithAPI(data) {
  try {
    const res = getFeConfigVariantExtra(data);
    const missIdList = [];

    let paramsCreate = res.create.map(item => {
      if (data.copyId && item.cachedId && !missIdList.includes(item.cachedId)) {
        missIdList.push(item.cachedId);
      }

      return {
        type: 1,
        properties: item.emailConfig,
      };
    });
    const paramsUpdate = res.update.map(item => ({
      settingId: item.fe_config_id,
      properties: item.emailConfig,
    }));
    // console.log('generateFeConfigWithAPI', paramsCreate);
    let resultPropertiesAPI = [];
    if (
      data.copyId &&
      missIdList.length > 0 &&
      paramsCreate.some(item => _isEmpty(item.properties))
    ) {
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < missIdList.length; i++) {
        const resVariant = yield call(
          UserAttributesServices.settings.get,
          missIdList[i],
        );

        if (resVariant.code === 200) {
          resultPropertiesAPI = [
            ...resultPropertiesAPI,
            {
              type: 1,
              properties: safeParse(resVariant.data[0].properties),
            },
          ];
        }
      }

      if (!_isEmpty(resultPropertiesAPI)) {
        paramsCreate = [...paramsCreate, ...resultPropertiesAPI].filter(
          item => !_isEmpty(item.properties),
        );
      }
    }

    if (paramsCreate.length > 0) {
      const resAPI = yield call(UserAttributesServices.settings.create, {
        settings: paramsCreate,
      });

      // chưa có api update bulk
      resAPI.data.forEach((item, index) => {
        if (res.create[index]) {
          res.create[index].fe_config_id = item.settingId;
        }
      });

      if (resAPI.code !== 200) {
        return [];
      }
    }

    if (paramsUpdate.length > 0) {
      const resUpdate = yield call(UserAttributesServices.settings.update, {
        settings: paramsUpdate,
      });

      if (resUpdate.code !== 200) {
        return [];
      }
      // console.log('res', { res, resAPI, resUpdate });
    }

    return [...res.create, res.update];

    // const mapVariantWithApiID = {};
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Detail/saga.js',
      func: 'handleFetchObjectDetail',
      data: error.stack,
    });
    console.log(error);
  }
  return [];
}
