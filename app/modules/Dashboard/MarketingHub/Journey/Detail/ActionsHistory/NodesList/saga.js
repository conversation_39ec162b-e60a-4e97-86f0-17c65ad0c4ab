/* eslint-disable prefer-destructuring */
import {
  all,
  call,
  delay,
  put,
  select,
  takeLatest,
  take,
  race,
} from 'redux-saga/effects';
import { OrderedMap } from 'immutable';
import JourneyServices from 'services/Journey';
import { safeParse } from 'utils/common';
import { subYears } from 'date-fns';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import ReduxTypes from '../../../../../../../redux/constants';
import { MODULE_CONFIG } from './config';
import {
  makeSelectListStoryAttributes,
  makeSelectListActionsHistoryColumn,
  makeSelectListActionsHistoryDomainMainAllNodes,
  makeSelectListActionsHistoryFilterAllNodes,
  makeSelectListActionsHistoryTableAllNodes,
  makeSelectModuleConfig,
  makeSelectModuleConfigColumn,
} from './selectors';
import {
  addNotification,
  getList,
  getListDone,
  initDone,
  onTableLimits,
  onTablePaging,
  onTableSelectAll,
  onUpdateTableSelectRow,
  updateDone,
  updateValue,
} from '../../../../../../../redux/actions';
import {
  toConditionAPI,
  toConditionUI,
} from '../../../../../../../containers/Filters/utils';
import { makeSelectPortal } from '../../../../../selector';
import {
  commonHandleFilterSearchAddItemCallback,
  commonHandleGetLastFilter,
} from '../../../../../../../containers/Filters/saga';
import { initRules } from '../../../../../../../containers/Filters/action';
import {
  OPERATORS,
  OPERATORS_OPTION,
} from '../../../../../../../containers/Filters/_UI/operators';
import { commonHandleGetColumns } from '../../../../../../../containers/ModifyColumn/saga';
import {
  getCurrentAccessUserId,
  getCurrentOwnerId,
} from '../../../../../../../utils/web/cookie';
import { addMessageToQueue } from '../../../../../../../utils/web/queue';
import { getColumnTablePerformance } from '../../../../../../../utils/web/attribute';
import { getErrorMessageV2Translate } from '../../../../../../../utils/web/message';
import { buildTableColumns } from './utils';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
  MENU_CODE,
} from '../../../../../../../utils/web/permission';
import { makeSelectDateRangeAction } from '../selectors';

const PREFIX = MODULE_CONFIG.key;
const DEFAULT_METRICS = ['impression', 'click'];
const getReducer = state => state.get(PREFIX);

export default function* workerCustomerSaga(args) {
  yield takeLatest(`${PREFIX}${ReduxTypes.INIT}`, handleInit);
  yield takeLatest(`${PREFIX}${ReduxTypes.GET_LIST}`, handleGetListData);

  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_TABLE_COLUMN${ReduxTypes.UPDATE_VALUE}`,
    handleBuildTableColumn,
  );

  /** FILTER HANDLE */
  yield takeLatest(
    `${PREFIX}@@COMMON_RESET_PAGING${ReduxTypes.UPDATE_VALUE}`,
    handleResetPaging,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_FILTER_SEARCH_GOTO${ReduxTypes.UPDATE_VALUE}`,
    handleFilterSearchGoto,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_SEARCH_ADD_ITEM${ReduxTypes.UPDATE_VALUE}`,
    handleFilterSearchAddItem,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_FILTER_RULES${ReduxTypes.UPDATE_VALUE}`,
    buildFilterRules,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_CHANGE_RULES${ReduxTypes.UPDATE_VALUE}`,
    handelChangeRuleFilter,
  );
  /** END FILTER HANDLE */

  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_FILTER_RULES_WITH_LOOKUP${ReduxTypes.UPDATE_VALUE
    }`,
    buildFilterRulesWithLookupInfo,
  );
  /** END FILTER HANDLE */

  yield takeLatest(
    `${PREFIX}@@CELL_STATUS${ReduxTypes.UPDATE}`,
    handleUpdateStatus,
  );
  // yield takeLatest(
  //   `${PREFIX}@@DEFAULT_METRICS${ReduxTypes.UPDATE}`,
  //   handleSaveDefaultMetrics,
  // );
  yield takeLatest(
    `${PREFIX}@@RESUME_TRIGGER${ReduxTypes.UPDATE}`,
    handleResumeTrigger,
  );
}

function* handleInit(_action, _args) {
  // yield call(handleGetDefaultMetrics);
  yield put(initDone(PREFIX));
  yield call(handleSetLimits);
  yield call(handleInitTable);
}

function* handleInitTable(action, args) {
  const moduleConfig = yield select(makeSelectModuleConfig());
  // call get list attribute before get modify column
  yield call(handleGetListAttributes);

  const moduleConfigColumn = yield select(makeSelectModuleConfigColumn());

  yield all([
    call(commonHandleGetColumns, PREFIX, moduleConfigColumn),
    call(
      commonHandleGetLastFilter,
      PREFIX,
      moduleConfig,
      buildFilterRulesWithLookupInfo,
    ),
  ]);

  yield call(handleGetListData, action);
  yield race([
    // call(intervalHandleGetData),
    take(`${PREFIX}${ReduxTypes.RESET}`),
  ]);
}

function* intervalHandleGetData() {
  try {
    while (true) {
      yield delay(10000);
      yield call(handleGetDataInterval);
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/Variants/saga.js',
      func: 'intervalHandleGetData',
      data: err.stack,
    });
    console.error(err);
  }
}

function* handleGetDataInterval() {
  try {
    const { main } = yield select(getReducer);
    const reducer = yield select(
      makeSelectListActionsHistoryDomainMainAllNodes(),
    );
    const { dateRange } = reducer;
    const {
      data,
      groupAttributes: { map },
    } = main;
    const reducerColumm = yield select(makeSelectListActionsHistoryColumn());
    const { columnObj } = reducerColumm;
    // const columns = [...columnObj.columns.columnsAlias];
    const columnsAlias = [...columnObj.columns.columnsAlias];

    const perfColumns = [];
    const columns = [];
    // columnsAlias.forEach(each => {
    //   if (map[each].type == '2') {
    //     perfColumns.push(each);
    //   } else {
    //     columns.push(each);
    //   }
    // });
    columnsAlias.forEach(each => {
      if (map[each] && map[each].type === 2) {
        perfColumns.push(each);
      } else if (map[each] && map[each].type === 1) {
        columns.push(each);
      }
    });
    ['variant_id', 'variant_name', 'status'].forEach(each => {
      if (!columns.includes(each)) {
        columns.push(each);
      }
    });
    // const index = columns.findIndex(ele => ele === 'labels');
    // columns.splice(index, 1);

    if (data.length > 0) {
      const variantIds = [];
      data.forEach(item => {
        variantIds.push(item.variant_id);
      });
      const params = {
        data: {
          variant_ids: variantIds,
          columns,
          // perfColumns,
          dateRange,
        },
      };
      // console.log('params', params);
      const { portalId } = yield select(state => makeSelectPortal(state));
      const res = yield call(JourneyServices.variant.getByIds, params);
      // console.log('res detail variant', res);
      yield put(
        updateValue(`${PREFIX}@@UPDATE_DATA_INTERVAL`, {
          data: res.data,
          portalId,
        }),
      );
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/Variants/saga.js',
      func: 'handleGetDataInterval',
      data: err.stack,
    });
    console.error(err);
  }
}

function* handleGetListData(action) {
  try {
    const reducer = yield select(
      makeSelectListActionsHistoryDomainMainAllNodes(),
    );
    const reducerFilter = yield select(
      makeSelectListActionsHistoryFilterAllNodes(),
    );
    const reducerColumm = yield select(makeSelectListActionsHistoryColumn());
    const moduleConfig = yield select(makeSelectModuleConfig());
    // const channelActive = yield select(makeSelectJourneyChannelActive());
    const reducerTable = yield select(
      makeSelectListActionsHistoryTableAllNodes(),
    );
    const dateRange = yield select(makeSelectDateRangeAction());
    const { columnObj } = reducerColumm;
    const { rules } = reducerFilter;
    const { paging, sort } = reducerTable;
    const { key, by } = sort;
    const {
      groupAttributes: { map },
    } = reducer;
    let columnsAlias = [...columnObj.columns.columnsAlias];

    if (!columnsAlias.includes('journey_version')) {
      columnsAlias = [...columnsAlias, 'journey_version'];
    }

    const columnPerformance = getColumnTablePerformance(columnsAlias, map);
    const { perfColumns } = columnPerformance;
    const { columns } = columnPerformance;
    // columnsAlias.forEach(each => {
    //   if (
    //     (map[each] && map[each].type === 2) ||
    //     safeParse(map[each], {}).type == '3'
    //   ) {
    //     perfColumns.push(each);
    //   } else if (map[each] && map[each].type === 1) {
    //     columns.push(each);
    //   }
    // });
    const apiRules = toConditionAPI(rules);

    const params = {
      objectId: moduleConfig.objectId,
      data: {
        filters: apiRules,
        columns,
        durations: {
          fromDate: dateRange.value.fromDate,
          toDate: dateRange.value.toDate,
          timeRange: dateRange.selectionType,
        },
        perf_columns: perfColumns,
        limit: paging.limit,
        page: paging.page,
        sort: safeParse(key, 'start_time'),
        sd: safeParse(by, 'desc'),
        object_type: moduleConfig.objectType,
        story_id: Number(moduleConfig.objectId),
        story_type: reducer.moduleConfigColumn.objectId,
        _owner_id: getCurrentOwnerId(),
      },
    };
    // const response = yield call(JourneyServices.variant.getListV2_1, params);
    const response = yield call(
      JourneyServices.actionsHistory.getListActionHistoryNodes,
      params,
    );

    if (response !== null) {
      const data = {
        totalRecord: response.totalRecord,
      };

      const portal = yield select(state => makeSelectPortal(state));

      yield put(
        getListDone(PREFIX, {
          data: response.data,
          portalId: portal.portalId,
          totalPerfomance: response.meta.totalPerf || {},
        }),
      );
      yield put(onTablePaging(PREFIX, { data }));

      /* ------------------------- KEEP CHECK SELECTED ROW ------------------------ */
      if (action && action.payload && typeof action.payload === 'object') {
        // isKeepSelectedRow => Giữ lại những row cho check khi get list
        // isSelectedAll => Có select All  cái item khôngDang
        const { isKeepSelectedRow, isSelectedAll } = action.payload;

        if (isKeepSelectedRow && !isSelectedAll) {
          yield put(
            onUpdateTableSelectRow(PREFIX, {
              selectedRows: action.payload.selectedRows,
            }),
          );
        } else if (isKeepSelectedRow && isSelectedAll) {
          yield put(
            onTableSelectAll(PREFIX, {
              data: { isSelectedAll: true },
            }),
          );
        }
      }
      /* ------------------------------------ END ----------------------------------- */
    }

    // yield put(setTotalList(response));
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/ActionsHistory/saga.js',
      func: 'handleGetListData',
      data: err.stack,
    });
    console.error(err);
  }
}

function* handleGetListAttributes() {
  try {
    const moduleConfig = yield select(makeSelectModuleConfig());
    const params = {
      ...moduleConfig,
    };
    const response = yield call(
      JourneyServices.actionsHistory.getListGroupAttrsForActionHistoryListing,
      params,
    );

    yield put(
      getListDone(`${PREFIX}@@GROUP_ATTRS`, safeParse(response.data, [])),
    );
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/ActionsHistory/saga.js',
      func: 'handleGetListAttributes',
      data: err.stack,
    });
    console.error(err);
  }
}

function* handleBuildTableColumn(action) {
  try {
    const { columns, isFetch } = action.payload;
    const reducerMain = yield select(
      makeSelectListActionsHistoryDomainMainAllNodes(),
    );
    const { groupAttributes } = reducerMain;
    const list = [];
    columns.forEach(col => {
      list.push(groupAttributes.map[col]);
    });
    // filter elements has undefined data
    const handleList = list.filter(Boolean);
    const feColumns = buildTableColumns(handleList);
    yield put(updateValue(`${PREFIX}@@COMMON_COLUMN_TABLE`, feColumns));
    if (isFetch) {
      yield call(handleGetListData);
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/ActionsHistory/saga.js',
      func: 'handleBuildTableColumn',
      data: err.stack,
    });
    console.error(err);
  }
}

function* handleResetPaging() {
  yield put(onTablePaging(PREFIX, { data: { page: 1 } }));
}

function* handleFilterSearchGoto(action) {
  const data = action.payload;
  try {
    // window.location.href = `${getLocationOrigin()}${
    //   APP.PREFIX
    // }/${getPortalId()}/profile/segments/${data.value}/detail`;
    // yield put(push(url));
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/ActionsHistory/saga.js',
      func: 'handleFilterSearchGoto',
      data: err.stack,
    });
    console.error(err);
  }
}

function* buildFilterRules(action) {
  const { rules } = action.payload;
  const reducer = yield select(
    makeSelectListActionsHistoryDomainMainAllNodes(),
  );
  const moduleConfig = yield select(makeSelectModuleConfig());
  const { groupAttributes, mapInfo } = reducer;
  const tmp = toConditionUI(
    rules,
    groupAttributes.map,
    mapInfo.itemAttribute,
    moduleConfig.objectId,
  );
  yield put(initRules(PREFIX, { data: tmp }));
}

function* buildFilterRulesWithLookupInfo(action) {
  const { rules, isFetch } = action.payload;
  const reducer = yield select(
    makeSelectListActionsHistoryDomainMainAllNodes(),
  );
  const moduleConfig = yield select(makeSelectModuleConfig());
  const { groupAttributes, mapInfo } = reducer;
  const tmp = toConditionUI(
    rules,
    groupAttributes.map,
    mapInfo.itemAttribute,
    moduleConfig.objectId,
  );
  yield put(initRules(PREFIX, { data: tmp }));
  if (isFetch) {
    yield call(handleGetListData);
  }
}

function* handelChangeRuleFilter(action) {
  try {
    const reducerFilter = yield select(
      makeSelectListActionsHistoryFilterAllNodes(),
    );
    const moduleConfig = yield select(makeSelectModuleConfig());
    const customFilterId = reducerFilter.config.library.filterCustom.filterId;
    yield put(
      updateValue(`${PREFIX}@@COMMON_FILTER_OBJ`, {
        filterId: customFilterId,
      }),
    );

    const data = {
      type: 'apply',
      filterId: customFilterId,
      rules: toConditionAPI(reducerFilter.rules),
    };

    yield put(
      updateValue(`${PREFIX}@@TO_COMMON_FILTER`, {
        data,
        callbackPrefix: PREFIX,
        moduleConfig,
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/ActionsHistory/saga.js',
      func: 'handelChangeRuleFilter',
      data: err.stack,
    });
    console.error(err);
  }
}

function* handleFilterSearchAddItem(action) {
  try {
    const { data } = action.payload;
    const moduleConfig = yield select(makeSelectModuleConfig());
    const reducerAttribute = yield select(makeSelectListStoryAttributes());
    const property = reducerAttribute.map.process_id;
    if (property) {
      const itemFilter = OrderedMap({
        value: data,
        property,
        operator: OPERATORS_OPTION.CONTAINS,
        operators: OPERATORS.string,
        dataType: property.dataType,
      });
      yield call(
        commonHandleFilterSearchAddItemCallback,
        PREFIX,
        itemFilter,
        makeSelectListActionsHistoryFilterAllNodes,
        moduleConfig,
      );
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/ActionsHistory/saga.js',
      func: 'handleFilterSearchAddItem',
      data: err.stack,
    });
    console.error(err);
  }
}

function* handleUpdateStatus(action) {
  // console.log(object)
  try {
    const { status, row } = action.payload;
    // console.log(action.payload);
    // const reducer = yield select(makeSelectListStoryCampaignDomainMain());
    const params = {
      data: { variantIds: [row.variant_id], status: status ? 1 : 2 },
      _owner_id: row.owner_id,
    };

    const res = yield call(JourneyServices.actionsHistory.updateStatus, params);
    const data = {
      row,
      isSuccessed: false,
    };
    if (res.code === 200) {
      data.isSuccessed = true;
    } else {
      const noti = NOTI.updateStatus.fail(res);
      yield put(addNotification(noti));
      data.isSuccessed = false;
      data.status = status;
    }
    yield put(updateDone(`${PREFIX}@@CELL_STATUS`, data));
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/Variants/saga.js',
      func: 'handleUpdateStatus',
      data: err.stack,
    });
    console.error(err);
  }
}

// function* handleGetDefaultMetrics() {
//   try {
//     const channelActive = yield select(makeSelectJourneyChannelActive());
//     const { key } = channelActive;
//     const dataSetLocalStorage = {};
//     const defalultMetrix = {};
//     defalultMetrix[key] = DEFAULT_METRICS;
//     const initMetric = getDefaultMetricChart(
//       key,
//       dataSetLocalStorage,
//       defalultMetrix,
//       JOURNEY_CHART_METRIC,
//       'app/modules/Dashboard/MarketingHub/Journey/Detail/Variants/saga.js',
//     );
//     yield put(getDetailDone(`${PREFIX}@@DEFAULT_METRICS`, initMetric));
//   } catch (err) {
//     addMessageToQueue({
//       path:
//         'app/modules/Dashboard/MarketingHub/Journey/Detail/Variants/saga.js',
//       func: 'handleGetDefaultMetricsParent',
//       data: err.stack,
//     });
//     console.error(err);
//   }
// }
// function* handleSaveDefaultMetrics(action) {
//   try {
//     const channelActive = yield select(makeSelectJourneyChannelActive());
//     const { key } = channelActive;
//     const dateRange = yield select(
//       makeSelectListActionsHistoryDefaultMetrics(),
//     ) || DEFAULT_METRICS;
//     const item = getLocalStorage(JOURNEY_CHART_METRIC);
//     const dataGetLocalStorage = JSON.parse(item);
//     const dataSetLocalStorage = {
//       ...dataGetLocalStorage,
//     };
//     dataSetLocalStorage[key] = dateRange;
//     setLocalStorage(JOURNEY_CHART_METRIC, JSON.stringify(dataSetLocalStorage));
//   } catch (err) {
//     addMessageToQueue({
//       path:
//         'app/modules/Dashboard/MarketingHub/Journey/Detail/Variants/saga.js',
//       func: 'handleSaveDefaultMetrics',
//       data: err.stack,
//     });
//     console.error(err);
//   }
// }
function* handleResumeTrigger(action) {
  try {
    const { selectedRows } = action.payload;
    const reducer = yield select(
      makeSelectListActionsHistoryDomainMainAllNodes(),
    );
    const table = yield select(makeSelectListActionsHistoryTableAllNodes());
    const reducerFilter = yield select(
      makeSelectListActionsHistoryFilterAllNodes(),
    );
    const { rules } = reducerFilter;
    const apiRules = toConditionAPI(rules);
    const processId = [];
    let version;
    selectedRows.forEach(each => {
      processId.push(each.process_id);
      version = each.journey_version;
    });
    const hasRoleViewEverything = checkingRoleScope(
      MENU_CODE.JOURNEY,
      APP_ACTION.VIEW,
      APP_ROLE_SCOPE.EVERYTHING,
    );
    const params = {
      storyId: parseInt(reducer.storyId),
      _owner_id: hasRoleViewEverything ? null : getCurrentAccessUserId(),
      data: {
        processIds: table.isSelectedAll ? [] : processId,
        version,
        filters: apiRules,
      },
    };
    const res = yield call(JourneyServices.data.resumeTrigger, params);
    if (res && res.code === 200) {
      const notification = NOTI.updateStatus.success(res);
      yield put(addNotification(notification));
      yield put(getList(MODULE_CONFIG.key));
    } else {
      const notification = NOTI.updateStatus.fail(res);
      yield put(addNotification(notification));
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/SchedulesHistory/saga.js',
      func: 'handleUpdateStatus',
      data: err.stack,
    });
    console.error(err);
  }
}

function* handleSetLimits() {
  try {
    yield put(
      onTableLimits(PREFIX, {
        data: {
          limits: [
            { value: 50, label: 50 },
            { value: 100, label: 100 },
            { value: 500, label: 500 },
          ],
          limit: 50,
        },
      }),
    );
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Detail/ActionsHistory/NodesList/saga.js',
      func: 'handleSetLimits',
      data: error.stack,
    });
    console.error(error);
  }
}
/*
 +-+-+-+-+
 |N|O|T|I|
 +-+-+-+-+
*/

const NOTI = {
  updateStatus: {
    fail: res => ({
      id: 'update-status-error',
      ...getErrorMessageV2Translate(res.codeMessage),
      timeout: 4000,
      timestamp: 1554173349265,
      type: 'danger',
    }),
    success: res => ({
      id: 'success',
      message: res.design === 'create' ? 'Created' : 'Updates saved!',
      translateCode:
        res.design === 'create'
          ? TRANSLATE_KEY._NOTIFICATION_CREATE_SUCCESS
          : TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
      timeout: 1500,
      timestamp: new Date().getTime(),
      type: 'success',
    }),
  },
};
