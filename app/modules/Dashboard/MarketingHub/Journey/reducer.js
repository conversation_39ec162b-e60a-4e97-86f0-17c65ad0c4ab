/* eslint-disable indent */
/* eslint-disable prefer-destructuring */
/* eslint-disable consistent-return */
/* eslint-disable no-undef */
/*
 *
 * Customer jeducer
 *
 */
// import React from 'react';

import produce from 'immer';
import { combineReducers } from 'redux';

// Constants
import { MODULE_CONFIG } from './config';
import { mapChannel } from '../../../../services/map';
import ReduxTypes from '../../../../redux/constants';

// Utils
import { safeParse } from '../../../../utils/common';
import { getLocalStorage, setLocalStorage } from '../../../../utils/web/cookie';

// import { DEFAULT_ACTION } from './constants';
const PREFIX = MODULE_CONFIG.key;
export const KEY_LOCAL_STORY = 'journey-channel';

// Loại All channel tu hard chu API Ko tra ra
const ALL_CHANNEL = {
  key: 0,
  code: 'all',
  label: 'All Channels',
  logoUrl: '',
  value: 0,
};

export const initialState = {
  // groupAttributes -> for customer
  isLoading: true,
  isInitDone: false,
  channels: {
    list: [],
    map: {},
  },
  channelActive: {},
  creatingJourneyInfo: {
    templateType: '',
    channelCode: null,
    data: {},
  },
  thirdPartyCampaigns: {
    data: [],
    status: 'idle',
  },
  cacheCreateCopy3rdParty: {},
  cacheZaloZNSTemplate: {},
  cacheWhatsappTemplate: { status: 'idle', data: {} },
  isOpenModalJourneyInfo: false,
  drawerJourneyInfo: {
    design: 'create',
    storyId: '',
    versionId: '',
    tempOwnerId: '',
    channelActive: {},
    loadingOnStepTwo: false,
    isOpenCreateJourney: false,
    isOpenSubDrawer: false,
    isOpenLayoutJourney: false,
    isSaveDone: false,
    isOpenModalConfirmCreateCopy: false,
  },
  isRenameSuccess: false,
  isFullScreen: false,
};

/* eslint-disable default-case, no-param-reassign */
const mainReducerFor = () => {
  const mainReducer = (state = initialState, action) =>
    produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.INIT_DONE}`: {
          // draft.isLoading = false;
          draft.isInitDone = true;
          return;
        }
        case `${PREFIX}${ReduxTypes.RESET}`: {
          // draft.isLoading = false;
          return { ...initialState };
          // return;
        }
        case `${PREFIX}@@CHANNEL@@${ReduxTypes.GET_LIST_DONE}`: {
          const data = action.payload;

          const map = mapChannel(data);
          draft.channels = map;

          const cacheChannelActive = safeParse(
            getLocalStorage(KEY_LOCAL_STORY),
            '8',
          );

          // Neu f5 ma channel active = 0 thi gan gia tri cho channel active la all channel
          if (cacheChannelActive === '0') {
            draft.channelActive = ALL_CHANNEL;
          } else {
            draft.channelActive = safeParse(
              map.map[cacheChannelActive],
              map.list.length > 0 ? map.list[0] : {},
            );
          }
          return;
        }
        case `${PREFIX}@@CHANNEL_ACTIVE@@${ReduxTypes.UPDATE_VALUE}`: {
          const data = action.payload;
          draft.channelActive = data;
          setLocalStorage(KEY_LOCAL_STORY, action.payload.value);
          // console.log('CHANNEL_ACTIVE', data);
          return;
        }

        case `${PREFIX}@@CHANNEL_ACTIVE_BY_ID@@${ReduxTypes.UPDATE_VALUE}`: {
          const channel = state.channels.map[action.payload];

          if (channel) {
            draft.channelActive = channel;
            setLocalStorage(KEY_LOCAL_STORY, channel.key);
          }
          // console.log('CHANNEL_ACTIVE', data);
          return;
        }

        case `${PREFIX}@@CHANNEL_ACTIVE_FROM_URL@@${ReduxTypes.UPDATE_VALUE}`: {
          const channelId = action.payload;
          // console.log('CHANNEL_ACTIVE_FROM_URL', action.payload, state.channels.map[channelId]);
          const activateChannel =
            Number(channelId) === ALL_CHANNEL.key
              ? ALL_CHANNEL
              : state.channels.map[channelId];
          if (activateChannel) {
            draft.channelActive = activateChannel;
            setLocalStorage(KEY_LOCAL_STORY, channelId);
          }

          return;
        }

        case `${PREFIX}${ReduxTypes.GET_DATA_JOURNEY_FROM_PACKAGE}`: {
          if (Object.keys(action.payload).length) {
            Object.keys(action.payload).forEach(key => {
              draft[key] = action.payload[key];
            });
          }
          return;
        }
        case `${PREFIX}@@CREATING_JOURNEY_INFO${ReduxTypes.UPDATE_VALUE}`: {
          draft.creatingJourneyInfo = action.payload;
          return;
        }
        case `${PREFIX}@@IS_OPEN_SUB_DRAWER${ReduxTypes.UPDATE_VALUE}`: {
          draft.drawerJourneyInfo.isOpenSubDrawer = action.payload;
          return;
        }
        case `${PREFIX}@@CREATING_JOURNEY_INFO${ReduxTypes.RESET}`: {
          draft.creatingJourneyInfo = initialState.creatingJourneyInfo;
          return;
        }

        case `${PREFIX}@@RESET_DRAWER${ReduxTypes.RESET}`: {
          draft.drawerJourneyInfo = initialState.drawerJourneyInfo;
          draft.cacheCreateCopy3rdParty = {};
          return;
        }

        case `${PREFIX}@@TOGGLE_CREATE_JOURNEY${ReduxTypes.UPDATE_VALUE}`: {
          draft.drawerJourneyInfo.isOpenCreateJourney = action.payload;
          return;
        }

        case `${PREFIX}@@TOGGLE_LAYOUT_JOURNEY${ReduxTypes.UPDATE_VALUE}`: {
          draft.drawerJourneyInfo.isOpenLayoutJourney = action.payload;
          return;
        }

        case `${PREFIX}@@DRAWER_JOURNEY_INFO${ReduxTypes.UPDATE_VALUE}`: {
          draft.drawerJourneyInfo = {
            ...state.drawerJourneyInfo,
            ...action.payload,
          };
          return;
        }

        case `${PREFIX}@@TOGGLE_MODAL_JOURNEY_INFO@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.isOpenModalJourneyInfo = action.payload;
          return;
        }

        case `${PREFIX}@@TOGGLE_MODAL_CREATE_COPY_JOURNEY@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.drawerJourneyInfo.isOpenModalConfirmCreateCopy = action.payload;
          return;
        }
        case `${PREFIX}@@LOADING_ON_STEP_TWO${ReduxTypes.UPDATE_VALUE}`: {
          draft.drawerJourneyInfo.loadingOnStepTwo = action.payload;
          return;
        }

        case `${PREFIX}@@UPDATE_STATUS_RENAME${ReduxTypes.UPDATE_VALUE}`: {
          draft.isRenameSuccess = action.payload;
          return;
        }

        case `${PREFIX}@@TOGGLE_FULL_SCREEN@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.isFullScreen = action.payload;
          return;
        }

        case `${PREFIX}@@UPDATE_CHANNEL_ACTIVE_BY_SEARCH_PARAMS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.drawerJourneyInfo.channelActive = action.payload;
          return;
        }

        case `${PREFIX}@@ON_UPDATE_THIRD_PARTY_CAMPAIGNS${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const newData = action.payload;
          draft.thirdPartyCampaigns = newData;
          return;
        }
        case `${PREFIX}@@RESET_CACHE_3RD_PARTY_CAMPAIGN${ReduxTypes.RESET}`: {
          draft.cacheCreateCopy3rdParty = {};
          return;
        }
        case `${PREFIX}@@CACHE_COPY_3RD_PARTY_CAMPAIGN${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const newCache = action.payload;
          if (newCache) {
            draft.cacheCreateCopy3rdParty = newCache;
          }
          return;
        }
        case `${PREFIX}@@CACHE_ZALO_ZNS_TEMPLATE${ReduxTypes.UPDATE_VALUE}`: {
          const newCache = action.payload || {};

          if (newCache) {
            const { cacheKey, ...restCache } = newCache;

            if (cacheKey) {
              draft.cacheZaloZNSTemplate[cacheKey] = {
                ...(state.cacheZaloZNSTemplate[cacheKey] || {}),
                ...restCache,
              };
            }
          }
          return;
        }
        case `${PREFIX}@@CACHE_WHATSAPP_TEMPLATE${ReduxTypes.UPDATE_VALUE}`: {
          const { status, cacheKey, cacheData } = action.payload || {};

          draft.cacheWhatsappTemplate.status = status || 'idle';

          if (cacheKey) {
            draft.cacheWhatsappTemplate.data[cacheKey] = cacheData;
          }
          return;
        }
        default:
          return state;
      }
    });
  return mainReducer;
};

// export default customerReducer;
export default combineReducers({
  main: mainReducerFor(),
  // filters: filtersReducerFor(PREFIX),
});
