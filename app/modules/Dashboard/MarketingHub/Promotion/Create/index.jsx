/* eslint-disable indent */
import React, { useEffect, useMemo, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { compose } from 'redux';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import UILinearProgress from 'components/common/UILinearProgress';
import LayoutContent from '../../../../../components/Templates/LayoutContent';
import { MODULE_CONFIG, TABS_CONFIG } from './config';
import {
  makeSelectPromotionCodeStructure,
  makeSelectPromotionSettingAlert,
  makeSelectPromotionSettingExpiration,
  makeSelectPromotionSettingGeneralPool,
  makeSelectPromotionSettingMain,
  makeSelectPromotionSettingMatchingFile,
  makeSelectPromotionSettingRectriction,
  makeSelectPromotionSettingSource,
  makeSelectPromotionShopifyDiscount,
} from './selectors';
import reducer from './reducer';
import saga from './saga';
import { DisplayUI, Wrapper } from './styled';
import {
  update,
  reset,
  updateValue,
  getDetail,
} from '../../../../../redux/actions';
import ModalPullCode from '../../../../../containers/modals/ModalPullCode';
import useToggle from '../../../../../hooks/useToggle';
import { Button, EditableName, Scrollbars } from '@antscorp/antsomi-ui';
import HeaderDrawer from '../../../../../components/common/HeaderDrawer';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import UIHelmet from '../../../../../components/Templates/LayoutContent/Helmet';
import ShareAccessView from './_UI/ShareAccessView';
import CreatePromotionSettings from './Settings';
import { POOL_CREATE_METHOD } from '../config';

/* -------------------------------- TRANSLATE ------------------------------- */
const MAP_TITLE = {
  defaultTitle: getTranslateMessage(
    TRANSLATE_KEY._,
    'Create new promotion pool',
  ),
  save: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
};

/* ---------------------------------- MAIN ---------------------------------- */
const CreatePool = props => {
  const [isOpenModalPool, toggleModalPool] = useToggle(false);

  // Thông tin 2 reducer con và 1 reducer mẹ
  const {
    sourceId,
    onAfterSave,

    main,
    settingGeneralPool,
    settingExpiration,
    settingMatchingFilePool,
    settingAlert,
    settingRestriction,
    settingSource,
    settingCodeStructure,
    settingShopifyDiscount,

    onChangeDataConfig,
    onReset,
  } = props;
  const { isLoading, promotionIntegration } = main;

  // States
  const [title, setTitle] = useState(
    settingGeneralPool?.dataConfig?.poolName?.value?.[
      settingGeneralPool?.dataConfig?.poolName?.value?.DEFAULT_LANG || 'EN'
    ] || MAP_TITLE.defaultTitle,
  );

  useEffect(() => {
    if (
      sourceId !== POOL_CREATE_METHOD.BULK_UPLOAD.value &&
      sourceId !== POOL_CREATE_METHOD.RULE_BASE_GENERATION.value
    ) {
      props.onGetPromotionIntegration(sourceId);
    }
  }, [sourceId]);

  useEffect(
    () => () => {
      onReset();
    },
    [],
  );
  // const onSave = () => {
  //   props.onSave({ id: sourceId, data: { status: false } });

  //   // if (id === 'manual') {
  //   //   props.onSave({ id, data: { status: false } });
  //   // } else {
  //   //   toggleModalPool();
  //   // }
  // };
  const callback = (type, data) => {
    switch (type) {
      case 'ACTION_POOL_CODES': {
        props.onSave({ id: sourceId, data });
        break;
      }

      case 'UPDATE_SHARE_ACCESS': {
        props.onChangeShareAccessInfo(data);
        break;
      }

      default:
        break;
    }
  };
  // Sử dụng validate của 2 reducer còn để  check disable
  // -> Nếu cả 2 đều isValidate(xác thực) = true thì mở button save
  const disableButtonSave = useMemo(() => {
    if (isLoading) return true;
    let isDisTmp = true;
    if (
      settingExpiration.isValidate &&
      settingGeneralPool.isValidate &&
      settingMatchingFilePool.isValidate &&
      settingAlert.isValidate &&
      settingRestriction.isValidate &&
      (() => {
        if (sourceId === POOL_CREATE_METHOD.RULE_BASE_GENERATION.value) {
          return settingCodeStructure.isValidate;
        }
        if (sourceId === 'manual') {
          return true;
        }
        if (promotionIntegration.connector_code === 'shopify') {
          return settingShopifyDiscount.isValidate;
        }
        return settingSource.isValidate;
      })()
    ) {
      isDisTmp = false;
    }
    return isDisTmp;
  }, [
    isLoading,
    settingRestriction.isValidate,
    settingExpiration.isValidate,
    settingGeneralPool.isValidate,
    settingMatchingFilePool.isValidate,
    settingAlert.isValidate,
    settingSource.isValidate,
    settingCodeStructure.isValidate,
    settingShopifyDiscount.isValidate,
  ]);

  // Handlers
  const handleChangeName = newValue => {
    if (typeof onChangeDataConfig === 'function') {
      onChangeDataConfig({
        name: 'poolName',
        value: {
          DEFAULT_LANG: 'EN',
          EN: newValue,
        },
      });
    }
  };

  const handleAfterSave = () => {
    if (typeof onAfterSave === 'function') {
      onAfterSave();
    }
  };

  const handleSave = () => {
    if (typeof props.onSave === 'function') {
      props.onSave({
        id: sourceId,
        promotionIntegration,
        data: { status: false },
        callback: handleAfterSave,
      });
    }
  };

  const handleChangeTitle = () => {
    const newTitle =
      settingGeneralPool?.dataConfig?.poolName?.value?.[
        settingGeneralPool?.dataConfig?.poolName?.value?.DEFAULT_LANG || 'EN'
      ];

    if (!newTitle) {
      setTitle(MAP_TITLE.defaultTitle);
    } else if (
      newTitle &&
      !settingGeneralPool?.dataConfig?.poolName?.errors?.[0]
    ) {
      setTitle(newTitle);
    }
  };

  // const renderContent = () => {
  //   switch (main.activeTab) {
  //     case TABS_CONFIG.SETTINGS.key:
  //       return (
  //         <CreatePromotion
  //           sourceId={sourceId}
  //           onAfterChangeSource={props.onAfterChangeSource}
  //         />
  //       );

  //     case TABS_CONFIG.SHARE_ACCESS.key:
  //       return (
  //         <ShareAccessView moduleConfig={MODULE_CONFIG} callback={callback} />
  //       );

  //     default:
  //       return (
  //         <CreatePromotion
  //           sourceId={sourceId}
  //           onAfterChangeSource={props.onAfterChangeSource}
  //         />
  //       );
  //   }
  // };

  return (
    <LayoutContent padding="0">
      {/* <Spin isLoading={isLoading}> */}
      <UIHelmet title={title} />
      <HeaderDrawer
        style={{ height: 50 }}
        extraContent={
          <Button
            type="primary"
            onClick={() => handleSave()}
            disabled={disableButtonSave}
            style={{ height: 30 }}
          >
            {MAP_TITLE.save}
          </Button>
        }
      >
        <EditableName
          name={MODULE_CONFIG.key}
          defaultValue={
            settingGeneralPool?.dataConfig?.poolName?.value?.[
              settingGeneralPool?.dataConfig?.poolName?.value?.DEFAULT_LANG ||
                'EN'
            ]
          }
          value={
            settingGeneralPool?.dataConfig?.poolName?.value?.[
              settingGeneralPool?.dataConfig?.poolName?.value?.DEFAULT_LANG ||
                'EN'
            ]
          }
          error={
            settingGeneralPool?.dataConfig?.poolName?.errors?.length
              ? settingGeneralPool?.dataConfig?.poolName?.errors?.[0]
              : undefined
          }
          className={`${MODULE_CONFIG.key}__name`}
          // isLoading={main?.isLoading}
          onChange={handleChangeName}
          onBlur={handleChangeTitle}
          required
        />
      </HeaderDrawer>
      <Scrollbars style={{ width: '100%', height: 'calc(100% - 50px)' }}>
        <DisplayUI isShow={main.activeTab === TABS_CONFIG.SETTINGS.key}>
          <CreatePromotionSettings
            sourceId={sourceId}
            moduleConfig={MODULE_CONFIG}
            promotionIntegration={promotionIntegration}
            onAfterChangeSource={props.onAfterChangeSource}
          />
        </DisplayUI>
        <DisplayUI isShow={main.activeTab === TABS_CONFIG.SHARE_ACCESS.key}>
          <Wrapper>
            <ShareAccessView
              design="create"
              moduleConfig={MODULE_CONFIG}
              callback={callback}
            />
          </Wrapper>
        </DisplayUI>
      </Scrollbars>
      {/* </Spin> */}
      <UILinearProgress isShow={isLoading} />
      <ModalPullCode
        callback={callback}
        isOpen={isOpenModalPool}
        toggle={toggleModalPool}
      />
    </LayoutContent>
  );
};

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

/* ----------------------------- STATE TO PROPS ----------------------------- */
const mapStateToProps = createStructuredSelector({
  main: makeSelectPromotionSettingMain(),
  settingExpiration: makeSelectPromotionSettingExpiration(),
  settingGeneralPool: makeSelectPromotionSettingGeneralPool(),
  settingMatchingFilePool: makeSelectPromotionSettingMatchingFile(),
  settingAlert: makeSelectPromotionSettingAlert(),
  settingRestriction: makeSelectPromotionSettingRectriction(),
  settingSource: makeSelectPromotionSettingSource(),
  settingCodeStructure: makeSelectPromotionCodeStructure(),
  settingShopifyDiscount: makeSelectPromotionShopifyDiscount(),
});

/* --------------------------------- ACTION TO PROPS --------------------------------- */
function mapDispatchToProps(dispatch) {
  return {
    onSave: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@ON_SAVE`, params));
    },
    onReset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
      dispatch(reset(`${MODULE_CONFIG.key}@@EXPERITION`, params));
      dispatch(reset(`${MODULE_CONFIG.key}@@RESTRICTION`, params));
      dispatch(reset(`${MODULE_CONFIG.key}@@SETTING_POOL`, params));
      dispatch(reset(`${MODULE_CONFIG.key}@@SETTING_SOURCE`, params));
      dispatch(reset(`${MODULE_CONFIG.key}@@SETTING_POOL_ALERT`, params));
      dispatch(reset(`${MODULE_CONFIG.key}@@PROMOTION_SHARE_ACCESS`, params));
    },
    onChangeDataConfig: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@SETTING_POOL@@DATA_CONFIG@@`, value),
      ),
    onChangeShareAccessInfo: value => {
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@PROMOTION_SHARE_ACCESS`, value),
      );
    },
    onGetPromotionIntegration: sourceId => {
      dispatch(
        getDetail(`${MODULE_CONFIG.key}@@PROMOTION_INTEGRATION@@`, {
          sourceId,
        }),
      );
    },
  };
}

/* -------------------------------- PROPTYPES ------------------------------- */
CreatePool.propTypes = {
  sourceId: PropTypes.string,
  onAfterChangeSource: PropTypes.func,
  onAfterSave: PropTypes.func,

  settingExpiration: PropTypes.object,
  settingGeneralPool: PropTypes.object,
  settingMatchingFilePool: PropTypes.object,
  settingRestriction: PropTypes.object,
  settingAlert: PropTypes.object,
  main: PropTypes.object,
  settingSource: PropTypes.object,
  settingCodeStructure: PropTypes.object,
  settingShopifyDiscount: PropTypes.object,

  onSave: PropTypes.func,
  onReset: PropTypes.func,
  onChangeDataConfig: PropTypes.func,
  onChangeShareAccessInfo: PropTypes.func,
  onGetPromotionIntegration: PropTypes.func,
};

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);
export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(CreatePool);

// Một sô dispatch sau này có thể sử dụng
// init: params => {
//   dispatch(init(MODULE_CONFIG.key, params));
// },
// reset: params => {
//   dispatch(reset(MODULE_CONFIG.key, params));
// },
// onChangeTab: params => {
//   dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANGE_TAB@@`, params));
// },
// onChangeChannel: params => {
//   dispatch(updateValue(`${MODULE_CONFIG.key}@@CHANNEL_ACTIVE@@`, params));
// },
