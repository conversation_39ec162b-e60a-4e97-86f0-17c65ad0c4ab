// Libraries
import _ from 'lodash';
import { format } from 'date-fns';
import momentTimeZone from 'moment-timezone';

// Utils
import { getDefaultVal } from '../../../../../components/common/InputLanguage/utils';
import { getPortalTimeZone } from '../../../../../utils/web/portalSetting';
import { PortalDate } from '../../../../../utils/date';

// Constants
import { POOL_CREATE_METHOD } from '../config';
import { METHOD_OPTIONS_MAP } from './_UI/FieldShopifyDiscount/constant';

/* -------------------------- MAP DATA CREATE POOL -------------------------- */
export const mapDataToAPI = ({
  reducerMatchingFile,
  reducerSettingExpiration,
  reducerGeneralPool,
  reducerSettingAlert,
  reducerSettingRectriction,
  reducerSettingSource,
  id,
  status,
  reducerAccessInfo,
  reducerCodeStructure,
  reducerShopifyDiscount,
  promotionIntegration,
}) => {
  if (typeof reducerMatchingFile !== 'object' || _.isEmpty(reducerMatchingFile))
    return null;
  if (
    typeof reducerSettingExpiration !== 'object' ||
    _.isEmpty(reducerSettingExpiration)
  )
    return null;
  if (typeof reducerGeneralPool !== 'object' || _.isEmpty(reducerGeneralPool))
    return null;
  if (typeof reducerSettingAlert !== 'object' || _.isEmpty(reducerSettingAlert))
    return null;
  if (
    typeof reducerSettingRectriction !== 'object' ||
    _.isEmpty(reducerSettingRectriction)
  )
    return null;
  const mappingFields = [];
  const {
    filePath,
    fileName,
    extension,
    headerFiles,
    originFileName,
    mappingAttributes,
    delimiterCode,
    attributeOptional,
    importType,
  } = reducerMatchingFile;
  const { selectOption } = attributeOptional;
  const { settingDateTime, optionValue } = reducerSettingExpiration;
  const optionValueRectriction = reducerSettingRectriction.optionValue;
  const allocationTypes = getValueListEvery(
    reducerSettingRectriction.selectedEvery.list,
  );
  // console.log('settingDateTime', settingDateTime.value.getTime());
  const { poolName, poolCode, description } = reducerGeneralPool.dataConfig;
  const {
    alertOnExpireDay,
    dayThresholdNumber,
    alertOnCodeNumber,
    codeThresholdNumber,
  } = reducerSettingAlert.dataConfig;
  const alertAccountIds = reducerSettingAlert.data;
  mappingAttributes.forEach(each => {
    const item = {
      headerCode: each.headerValue.headerCode,
      headerIndex: each.headerValue.headerIndex,
      itemAttributeCode: each.currentValue.value,
      allowNull: false,
    };
    mappingFields.push(item);
  });
  Object.keys(selectOption.map).forEach(key => {
    const each = selectOption.map[key];
    const item = {
      headerCode: each.mapValueAttribute.headerValue.headerCode,
      headerIndex: each.mapValueAttribute.headerValue.headerIndex,
      itemAttributeCode: each.itemPropertyName,
      allowNull: false,
    };
    mappingFields.push(item);
  });

  let data;

  switch (id) {
    case 'manual': {
      data = {
        filePath: filePath || '',
        fileName: fileName || '',
        originFileName: originFileName || '',
        extension,
        delimiterCode,
        headers: headerFiles || '',
        mappingFields: mappingFields || [],
        pool_code: poolCode.value,
        pool_name: getDefaultVal(poolName.value),
        expiration: {
          type: optionValue,
          endTime:
            optionValue !== 'none' ? settingDateTime.value.getTime() : null,
        },
        description: getDefaultVal(description.value),
        description_multilang: description.value,
        pool_name_multilang: poolName.value,
        alertSetting: {
          alertOnExpireDay: alertOnExpireDay.value,
          dayThresholdNumber: parseInt(dayThresholdNumber.value),
          alertOnCodeNumber: alertOnCodeNumber.value,
          codeThresholdNumber: parseInt(codeThresholdNumber.value),
          alertAccountIds,
        },
        restrictionType: optionValueRectriction === 'unique' ? 1 : 2,
        allocationTypes:
          optionValueRectriction === 'unique' ? allocationTypes : null,
        type: 1,
        isRun: status,
        // importType: importType.value.value,
      };

      break;
    }
    case POOL_CREATE_METHOD.RULE_BASE_GENERATION.value: {
      data = {
        pool_code: poolCode.value,
        pool_name: getDefaultVal(poolName.value),
        description: getDefaultVal(description.value),
        description_multilang: description.value,
        pool_name_multilang: poolName.value,
        type: 3,
        alertSetting: {
          alertOnExpireDay: alertOnExpireDay.value,
          dayThresholdNumber: parseInt(dayThresholdNumber.value),
          alertOnCodeNumber: alertOnCodeNumber.value,
          codeThresholdNumber: parseInt(codeThresholdNumber.value),
          alertAccountIds,
        },
        expiration: {
          type: optionValue,
          endTime:
            optionValue !== 'none' ? settingDateTime.value.getTime() : null,
        },
        allocationTypes:
          optionValueRectriction === 'unique' ? allocationTypes : null,
        restrictionType: optionValueRectriction === 'unique' ? 1 : 2,
      };

      data.generateCodeSettings = {
        prefix: reducerCodeStructure.dataConfig.prefix,
        suffix: reducerCodeStructure.dataConfig.suffix,
        infix_type: reducerCodeStructure.dataConfig.characterType, // 'numbers', 'letters', or 'both'
        separator: reducerCodeStructure.dataConfig.separator, // 'hyphen', 'under_score', 'dot'
        quantity: reducerCodeStructure.dataConfig.quantity,
        total_predicted: reducerCodeStructure.dataConfig.totalPredicted,

        ...(reducerCodeStructure.dataConfig.characterType === 'both' && {
          num_digits: reducerCodeStructure.dataConfig?.digitalNumbers,
          num_letters: reducerCodeStructure.dataConfig?.alphabetLetters,
          letter_case: reducerCodeStructure.dataConfig?.capitalization,
          order: reducerCodeStructure.dataConfig?.characterOrder,
        }),
        ...(reducerCodeStructure.dataConfig.characterType === 'numbers' && {
          num_digits: reducerCodeStructure.dataConfig?.digitalNumbers,
        }),
        ...(reducerCodeStructure.dataConfig.characterType === 'letters' && {
          num_letters: reducerCodeStructure.dataConfig?.alphabetLetters,
          letter_case: reducerCodeStructure.dataConfig?.capitalization,
        }),
        ...(reducerCodeStructure.activeRow.generate_code_settings
          ?.total_generated && {
          total_generated:
            reducerCodeStructure.activeRow.generate_code_settings
              .total_generated,
        }),
        ...(reducerCodeStructure.dataConfig?.refillInterval && {
          interval:
            reducerCodeStructure.dataConfig.refillInterval === 'manual'
              ? {
                  type: 'manual',
                }
              : {
                  type: 'auto',
                  conditions: {
                    unit: reducerCodeStructure.dataConfig.refillUnit,
                    num: reducerCodeStructure.dataConfig.refillThreshold || 0,
                  },
                },
        }),
      };

      break;
    }

    default: {
      const useShopify = promotionIntegration?.connector_code === 'shopify';

      const { inputs } = reducerSettingSource;

      if (useShopify) {
        data = {
          pool_code: poolCode.value,
          pool_name: getDefaultVal(poolName.value),
          expiration: {
            type: optionValue,
            endTime:
              optionValue !== 'none' ? settingDateTime.value.getTime() : null,
          },
          description: getDefaultVal(description.value),
          description_multilang: description.value,
          pool_name_multilang: poolName.value,
          alertSetting: {
            alertOnExpireDay: alertOnExpireDay.value,
            dayThresholdNumber: parseInt(dayThresholdNumber.value),
            alertOnCodeNumber: alertOnCodeNumber.value,
            codeThresholdNumber: parseInt(codeThresholdNumber.value),
            alertAccountIds,
          },
          restrictionType: optionValueRectriction === 'unique' ? 1 : 2,
          allocationTypes:
            optionValueRectriction === 'unique' ? allocationTypes : null,
          type: 2,

          // SOURCE SHOPIFY
          sourceId: reducerShopifyDiscount?.sourceId,
          settings: {
            codeDestination:
              reducerShopifyDiscount?.dataConfig?.codeDestination?.value,
            method: reducerShopifyDiscount?.dataConfig?.method?.value,
            discount:
              reducerShopifyDiscount?.dataConfig?.discount?.value?.value,
          },
          isRun: status,
          // importType: importType.value.value,
        };

        // Method Rule-based
        if (
          reducerShopifyDiscount?.dataConfig?.method?.value ===
          METHOD_OPTIONS_MAP.RULE_BASED_GENERATOR.value
        ) {
          data.generateCodeSettings = {
            prefix: reducerCodeStructure.dataConfig.prefix,
            suffix: reducerCodeStructure.dataConfig.suffix,
            infix_type: reducerCodeStructure.dataConfig.characterType, // 'numbers', 'letters', or 'both'
            separator: reducerCodeStructure.dataConfig.separator, // 'hyphen', 'under_score', 'dot'
            quantity: reducerCodeStructure.dataConfig.quantity,
            total_predicted: reducerCodeStructure.dataConfig.totalPredicted,

            ...(reducerCodeStructure.dataConfig.characterType === 'both' && {
              num_digits: reducerCodeStructure.dataConfig?.digitalNumbers,
              num_letters: reducerCodeStructure.dataConfig?.alphabetLetters,
              letter_case: reducerCodeStructure.dataConfig?.capitalization,
              order: reducerCodeStructure.dataConfig?.characterOrder,
            }),
            ...(reducerCodeStructure.dataConfig.characterType === 'numbers' && {
              num_digits: reducerCodeStructure.dataConfig?.digitalNumbers,
            }),
            ...(reducerCodeStructure.dataConfig.characterType === 'letters' && {
              num_letters: reducerCodeStructure.dataConfig?.alphabetLetters,
              letter_case: reducerCodeStructure.dataConfig?.capitalization,
            }),
            ...(reducerCodeStructure.activeRow.generate_code_settings
              ?.total_generated && {
              total_generated:
                reducerCodeStructure.activeRow.generate_code_settings
                  .total_generated,
            }),
            ...(reducerCodeStructure.dataConfig?.refillInterval && {
              interval:
                reducerCodeStructure.dataConfig.refillInterval === 'manual'
                  ? {
                      type: 'manual',
                    }
                  : {
                      type: 'auto',
                      conditions: {
                        unit: reducerCodeStructure.dataConfig.refillUnit,
                        num:
                          reducerCodeStructure.dataConfig.refillThreshold || 0,
                      },
                    },
            }),
          };
        }

        // Method Bulk Upload
        if (
          reducerShopifyDiscount?.dataConfig?.method?.value ===
          METHOD_OPTIONS_MAP.BULK_UPLOAD.value
        ) {
          data.filePath = filePath || '';
          data.fileName = fileName || '';
          data.originFileName = originFileName || '';
          data.extension = extension;
          data.delimiterCode = delimiterCode;
          data.headers = headerFiles || '';
          data.mappingFields = mappingFields || [];
        }
      } else {
        data = {
          pool_code: poolCode.value,
          pool_name: getDefaultVal(poolName.value),
          expiration: {
            type: optionValue,
            endTime:
              optionValue !== 'none' ? settingDateTime.value.getTime() : null,
          },
          description: getDefaultVal(description.value),
          description_multilang: description.value,
          pool_name_multilang: poolName.value,
          alertSetting: {
            alertOnExpireDay: alertOnExpireDay.value,
            dayThresholdNumber: parseInt(dayThresholdNumber.value),
            alertOnCodeNumber: alertOnCodeNumber.value,
            codeThresholdNumber: parseInt(codeThresholdNumber.value),
            alertAccountIds,
          },
          restrictionType: optionValueRectriction === 'unique' ? 1 : 2,
          allocationTypes:
            optionValueRectriction === 'unique' ? allocationTypes : null,
          type: 2,
          sourceId: reducerSettingSource.sourceId,
          settings: {
            inputs: {
              ...inputs,
              quantity: reducerSettingSource.codeQuantity,
            },
            optionInterval: reducerSettingSource.optionInterval,
          },
          isRun: status,
          // importType: importType.value.value,
        };
      }

      if (reducerSettingSource.optionInterval !== 'manually') {
        data.settings.repeatType = reducerSettingSource.repeatBy.type;
        data.settings.repeatValue = reducerSettingSource.repeatBy.value;
        data.settings.repeatStartTime = {
          value: reducerSettingSource.settingDateTime.value,
          hour: Number(
            new Date(reducerSettingSource.settingDateTime.value).getHours(),
          ),
          minute: Number(
            new Date(reducerSettingSource.settingDateTime.value).getMinutes(),
          ),
          day: format(reducerSettingSource.settingDateTime.value, 'dd/MM/yyyy'),
        };
        const portalTimezone = getPortalTimeZone();
        data.settings.startTime = momentTimeZone(
          new PortalDate(reducerSettingSource.settingDateTime.value),
        )
          .tz(portalTimezone)
          .format();
        data.settings.repeatOnValue =
          reducerSettingSource.optionWeek.optionsSelected;
        data.settings.andRemain = {
          ...reducerSettingSource.andRemain,
        };
      }
      break;
    }
  }

  // Reducer Access Info
  const { listAccess, isPublic, publicRole } =
    reducerAccessInfo.dataConfig || {};
  const mappingListAccess = (listAccess || []).map(access => ({
    user_id: +access.userId,
    email: access.email,
    avatar: access.avatar,
    role: +access.role,
    allow_view: +access.allowView,
    allow_edit: +access.allowEdit,
    allow_comment: +access.allowComment,
  }));

  data.shareAccess = {
    is_public: +isPublic,
    public_role: +publicRole,
    list_access: mappingListAccess,
  };

  return data;
};

/* ------------------------- MAP value khi trường lỗi (API trả: pool_name, FE cần: poolName) ------------------------ */
export const MAP_VALUE_ERROR = {
  pool_name: 'poolName',
  pool_code: 'poolCode',
};
export const getValueListEvery = dataIn => {
  const dataOut = [];
  dataIn.forEach(item => {
    if (item.value) {
      dataOut.push(item.value);
    }
  });
  return dataOut;
};
