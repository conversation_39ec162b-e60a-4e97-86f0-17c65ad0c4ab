// Libraries
import React, { memo, useCallback, useEffect } from 'react';
import { translate, translations } from '@antscorp/antsomi-locales';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { uniq } from 'lodash';

// Selectors
import {
  makeSelectShopifyDiscountDataConfig,
  makeSelectShopifyDiscountSettings,
} from './selectors';

// Actions
import { init, reset, updateValue } from '../../../../../../../redux/actions';

// Components
import { Title } from '../../styled';
import { Box, Grid } from '@material-ui/core';
import { StyleContent, WrapperContent } from './styles';
import { UILoading as Loading } from '@xlab-team/ui-components';
import ErrorBoundary from '../../../../../../../components/common/ErrorBoundary';

const MAP_TITLE = {
  TITLE_SHOPIFY_DISCOUNT: translate(
    translations._POOL_SHOPIFY_INFO,
    'Shopify Discount Information',
  ),
};

const InputComponent = memo(data => {
  if (data.hidden) return null;

  return (
    <Grid container spacing={2}>
      {data.componentEl(data)}
    </Grid>
  );
});

const FieldShopifyInfomation = props => {
  const {
    main,
    dataConfig,
    design,
    isViewMode,
    sourceId,

    onChangeDataConfig,
    onInit,
    onReset,
  } = props;
  const { isLoading } = main;

  const onChangeData = useCallback(
    name => value => {
      if (value !== dataConfig[name].value) {
        onChangeDataConfig({ name, value });
      }
    },
    [dataConfig],
  );

  useEffect(() => {
    onInit({ design, sourceId });
    return () => onReset();
  }, []);

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Promotion/Create/_UI/FieldShopifyInfomation/index.jsx">
      <WrapperContent>
        <StyleContent>
          <Loading isLoading={isLoading} />
          <Box
            className="d-flex flex-column width-100 m-x-0"
            style={{ gap: 15 }}
          >
            <Title>{MAP_TITLE.TITLE_SHOPIFY_DISCOUNT}</Title>
            <Grid container>
              <Grid item xs={12} md={7}>
                <Grid container spacing={2} alignItems="center">
                  {dataConfig.infoFields.map(each => (
                    <Grid key={each} item xs={12}>
                      <InputComponent
                        {...dataConfig[each]}
                        onChange={onChangeData}
                        key={each}
                        isViewMode={isViewMode}
                        errors={uniq([...dataConfig[each].errors])}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Grid>
          </Box>
        </StyleContent>
      </WrapperContent>
    </ErrorBoundary>
  );
};

const mapStateToProps = createStructuredSelector({
  main: makeSelectShopifyDiscountSettings(),
  dataConfig: makeSelectShopifyDiscountDataConfig(),
});

export function mapDispatchToProps(dispatch, ownProps) {
  const PREFIX = ownProps.moduleConfig.key;
  return {
    onChangeDataConfig: value =>
      dispatch(
        updateValue(`${PREFIX}@@SHOPIFY_DISCOUNT@@DATA_CONFIG@@`, value),
      ),
    onReset: value => dispatch(reset(`${PREFIX}@@SHOPIFY_DISCOUNT`, value)),
    onInit: value => dispatch(init(`${PREFIX}@@SHOPIFY_DISCOUNT`, value)),
  };
}

FieldShopifyInfomation.propTypes = {
  isViewMode: PropTypes.bool,
  design: PropTypes.string,
  sourceId: PropTypes.string,

  main: PropTypes.any,
  dataConfig: PropTypes.object,

  onChangeDataConfig: PropTypes.func.isRequired,
  onReset: PropTypes.func,
  onInit: PropTypes.func,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(FieldShopifyInfomation);
