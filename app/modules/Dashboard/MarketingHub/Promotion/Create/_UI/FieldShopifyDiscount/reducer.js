/* eslint-disable no-param-reassign */
// Libraries
import produce from 'immer';

// Constants
import ReduxTypes from '../../../../../../../redux/constants';
import { CODE_DESTINATION_OPTIONS } from './constant';

// Utils
import { initDefaultDataConfig, mapValueToFE, validateAll } from './utils';

export const initStateCreateSettings = () => ({
  isInitDone: false,
  activeRow: {},
  isValidate: false,
  isLoading: false,
  isCheckOnChange: false,
  design: '',
  sourceId: undefined,

  dataConfig: initDefaultDataConfig(),
});

const settingShopifyDiscountReducer = PREFIX => {
  const mainReducer = (state = initStateCreateSettings(), action) => {
    return produce(state, draft => {
      switch (action.type) {
        /* ---------------------------------- INIT --------------------------------- */
        case `${PREFIX}@@SHOPIFY_DISCOUNT${ReduxTypes.INIT}`: {
          const { design = 'create', sourceId } = action.payload;
          draft.design = design;
          draft.sourceId = sourceId;
          return draft;
        }

        /* ---------------------------------- RESET --------------------------------- */
        case `${PREFIX}@@SHOPIFY_DISCOUNT${ReduxTypes.RESET}`: {
          return initStateCreateSettings();
        }

        /* ----------------------------- GET_DETAIL_DONE ---------------------------- */
        case `${PREFIX}@@SHOPIFY_DISCOUNT${ReduxTypes.GET_DETAIL_DONE}`: {
          const activeRow = action.payload;
          const { mapValue, fields } = mapValueToFE({ activeRow });

          fields.forEach(each => {
            draft.dataConfig[each].value = mapValue[each].value;
            draft.dataConfig[each].initValue = mapValue[each].value;
            draft.dataConfig[each].isValidate = true;
            draft.dataConfig[each].disabled = mapValue[each].disabled;
            draft.dataConfig[each].errors = [];
            draft.dataConfig[each].isChanged = false;
          });
          draft.activeRow = activeRow;
          draft.isValidate = true;

          if (activeRow?.total_row_count) {
            draft.dataConfig.discount.disabled = true;
          }

          return draft;
        }

        case `${PREFIX}@@SHOPIFY_DISCOUNT@@DATA_CONFIG@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const {
            name,
            value,
            notUpdateCheckOnChange = false,
          } = action.payload;
          draft.dataConfig[name].value = value;
          const { errors, isValidate } = draft.dataConfig[name].validate(
            draft.dataConfig[name],
          );
          draft.dataConfig[name].errors = errors;
          draft.dataConfig[name].isValidate = isValidate;
          draft.dataConfig[name].isChanged = true;

          if (name === 'codeDestination') {
            draft.dataConfig.discount.hidden =
              value === CODE_DESTINATION_OPTIONS[1].value;
          }

          draft.dataConfig.isValidate = validateAll(state, draft);
          draft.isValidate = validateAll(state, draft);

          if (!notUpdateCheckOnChange) {
            draft.isCheckOnChange = !state.isCheckOnChange;
          }

          return draft;
        }

        case `${PREFIX}@@SHOPIFY_DISCOUNT@@DISCOUNT_OPTIONS${
          ReduxTypes.GET_LIST_DONE
        }`: {
          const { data = [] } = action.payload;
          draft.dataConfig.discount.options = data;

          return draft;
        }

        /* --------------------------------- DEFAULT -------------------------------- */
        default:
          return state;
      }
    });
  };
  return mainReducer;
};

export default settingShopifyDiscountReducer;
