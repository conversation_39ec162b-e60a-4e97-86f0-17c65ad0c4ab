import { translate, translations } from '@antscorp/antsomi-locales';

export const CODE_DESTINATION_OPTIONS = [
  {
    label: translate(
      translations._POOL_SHOPIFY_INFO_CODE_EST_DIS,
      'Existing discount',
    ),
    value: 'existing_discount',
  },
  {
    label: translate(
      translations._POOL_SHOPIFY_INFO_CODE_NEW_DIS,
      'New discount',
    ),
    value: 'new_discount',
    disabled: true,
  },
];

export const METHOD_OPTIONS_MAP = {
  RULE_BASED_GENERATOR: {
    label: translate(
      translations._CREATE_POOL_METHOD_RULE,
      'Rule-based Generator',
    ),
    value: 'rule_based_generator',
  },
  BULK_UPLOAD: {
    label: translate(translations._CREATE_POOL_METHOD_UPLOAD, 'Bulk Upload'),
    value: 'bulk_upload',
  },
};

export const METHOD_OPTIONS = Object.values(METHOD_OPTIONS_MAP);
