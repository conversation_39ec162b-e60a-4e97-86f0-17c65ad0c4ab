// Libraries
import React, { memo, useCallback } from 'react';
import PropTypes from 'prop-types';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { uniq } from 'lodash';

// Selectors
import { makeSelectShopifyDiscountDataConfig } from './selectors';

// Actions
import { updateValue } from '../../../../../../../redux/actions';

// Constants
import { METHOD_OPTIONS_MAP } from './constant';

// Components
import { Box, Grid } from '@material-ui/core';
import { StyleContent, WrapperContent } from './styles';
import FieldCodeStructure from '../FieldCodeStructure';
import UploadMatchingFile from '../UploadMatchingFile';
import ErrorBoundary from '../../../../../../../components/common/ErrorBoundary';

const InputComponent = memo(data => {
  if (data.hidden) return null;

  return (
    <Grid container spacing={2}>
      {data.componentEl(data)}
    </Grid>
  );
});

const FieldShopifyConfiguration = props => {
  const {
    design,
    isViewMode,
    moduleConfig,
    dataConfig,
    onChangeDataConfig,
  } = props;

  const onChangeData = useCallback(
    name => value => {
      if (value !== dataConfig[name].value) {
        onChangeDataConfig({ name, value });
      }
    },
    [dataConfig],
  );

  const renderConfiguration = () => {
    switch (dataConfig.method.value) {
      case METHOD_OPTIONS_MAP.RULE_BASED_GENERATOR.value: {
        return (
          <FieldCodeStructure
            design={design}
            moduleConfig={moduleConfig}
            isViewMode={isViewMode}
          />
        );
      }
      case METHOD_OPTIONS_MAP.BULK_UPLOAD.value: {
        if (design === 'create') {
          return (
            <UploadMatchingFile
              design={design}
              disabled={isViewMode}
              moduleConfig={moduleConfig}
            />
          );
        }
        return null;
      }
      default:
        return null;
    }
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Promotion/Create/_UI/FieldShopifyConfiguration/index.jsx">
      <WrapperContent>
        <StyleContent>
          <Box
            className="d-flex flex-column width-100 m-x-0"
            style={{ gap: 20 }}
          >
            {design === 'create' && (
              <Grid container>
                <Grid item xs={12} md={7}>
                  <Grid container spacing={2} alignItems="center">
                    {dataConfig.configFields.map(each => (
                      <Grid key={each} item xs={12}>
                        <InputComponent
                          {...dataConfig[each]}
                          onChange={onChangeData}
                          key={each}
                          isViewMode={isViewMode}
                          errors={uniq([...dataConfig[each].errors])}
                        />
                      </Grid>
                    ))}
                  </Grid>
                </Grid>
              </Grid>
            )}

            {renderConfiguration()}
          </Box>
        </StyleContent>
      </WrapperContent>
    </ErrorBoundary>
  );
};

const mapStateToProps = createStructuredSelector({
  dataConfig: makeSelectShopifyDiscountDataConfig(),
});

export function mapDispatchToProps(dispatch, ownProps) {
  const PREFIX = ownProps.moduleConfig.key;
  return {
    onChangeDataConfig: value =>
      dispatch(
        updateValue(`${PREFIX}@@SHOPIFY_DISCOUNT@@DATA_CONFIG@@`, value),
      ),
    // onReset: value => dispatch(reset(`${PREFIX}@@SHOPIFY_DISCOUNT`, value)),
    // onInit: value => dispatch(init(`${PREFIX}@@SHOPIFY_DISCOUNT`, value)),
  };
}

FieldShopifyConfiguration.propTypes = {
  isViewMode: PropTypes.bool,
  design: PropTypes.string,
  moduleConfig: PropTypes.object,

  dataConfig: PropTypes.object,

  onChangeDataConfig: PropTypes.func.isRequired,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(FieldShopifyConfiguration);
