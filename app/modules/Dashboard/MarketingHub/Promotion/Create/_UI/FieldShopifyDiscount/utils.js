/* eslint-disable camelcase */
import { translate, translations } from '@antscorp/antsomi-locales';
import { MAP_INPUT_TEMPLATE } from '../../../../../../../utils/web/renderForm';
import { CODE_DESTINATION_OPTIONS, METHOD_OPTIONS } from './constant';

const MAP_TITLE = {
  CODE_DESTINATION: translate(
    translations._POOL_SHOPIFY_INFO_CODE_DESTINATION,
    'Code destination',
  ),
  DISCOUNT: translate(
    translations._POOL_SHOPIFY_INFO_CODE_DISCOUNT,
    'Discount',
  ),
  METHOD: translate(translations._TITL_METHOD, 'Method'),
};

export const initDefaultDataConfig = () => ({
  isValidate: false,
  disabled: false,
  validFields: ['codeDestination', 'discount', 'method'],
  infoFields: ['codeDestination', 'discount'],
  configFields: ['method'],
  codeDestination: {
    ...MAP_INPUT_TEMPLATE.radioGroup,
    value: CODE_DESTINATION_OPTIONS[0].value,
    name: 'codeDestination',
    isRequired: true,
    label: MAP_TITLE.CODE_DESTINATION,
    classNameTitle: 'title-pool',
    options: CODE_DESTINATION_OPTIONS,
    radioGroupProps: {
      row: true,
    },
    radioProps: {
      size: 'small',
    },
    labelStyle: {
      fontSize: '12px',
    },
  },
  discount: {
    ...MAP_INPUT_TEMPLATE.selectDropdown,
    // value: ,
    name: 'discount',
    isRequired: true,
    label: MAP_TITLE.DISCOUNT,
    classNameTitle: 'title-pool',
    inputWrapperStyle: { maxWidth: 465 },
    options: [],
    disabled: false,
  },
  method: {
    ...MAP_INPUT_TEMPLATE.radioGroup,
    value: METHOD_OPTIONS[0].value,
    name: 'method',
    label: MAP_TITLE.METHOD,
    classNameTitle: 'title-pool',
    options: METHOD_OPTIONS,
    radioGroupProps: {
      row: true,
    },
    radioProps: {
      size: 'small',
    },
    labelStyle: {
      fontSize: '12px',
    },
  },
});

export const validateAll = (state, draft) => {
  const {
    dataConfig: { validFields },
  } = state;

  return [...validFields].every(each => {
    return (
      draft.dataConfig[each].hidden ||
      draft.dataConfig[each].isValidate === true
    );
  });
};

export const mapValueToFE = ({ activeRow }) => {
  const { settings } = activeRow || {};
  const data = {
    codeDestination: { value: settings?.codeDestination },
    // discount: { value: { value: settings?.discount }, disabled: true },
    method: { value: settings?.method, disabled: true },
  };
  const fields = ['codeDestination', 'method'];
  return { mapValue: data, fields };
};
