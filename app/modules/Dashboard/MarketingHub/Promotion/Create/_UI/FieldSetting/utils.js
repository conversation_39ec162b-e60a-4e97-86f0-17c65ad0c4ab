/* eslint-disable camelcase */
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { getUntitledName } from '../../../../../../../utils/web/properties';
import { MAP_INPUT_TEMPLATE } from '../../../../../../../utils/web/renderForm';
import { serializeLabelToCodeAttr } from '../../../../../../../utils/web/utils';

/* -------------------------------- TRANSLATE ------------------------------- */
const MAP_TITLE = {
  poolName: getTranslateMessage(TRANSLATE_KEY._TITL_POOL_NAME, 'Pool Name'),
  poolCode: getTranslateMessage(TRANSLATE_KEY._, 'Pool Internal Code'),
  description: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESCRIPTION,
    'Description',
  ),
};

/* -------------------------- INIT DATA CONFIG OF FIELD ------------------------- */
export const initDefaultDataConfig = () => ({
  isValidate: true,
  disabled: false,
  validFields: ['poolName', 'poolCode', 'description'],
  infoFields: ['poolCode', 'description'],
  poolName: {
    ...MAP_INPUT_TEMPLATE.singleLineHeading,
    value: {
      DEFAULT_LANG: 'EN',
      EN: getUntitledName('Untitled Pool'),
    },
    name: 'poolName',
    isRequired: true,
    label: MAP_TITLE.poolName,
    isValidate: true,
    classNameTitle: 'title-pool',
  },
  poolCode: {
    ...MAP_INPUT_TEMPLATE.code,
    value: serializeLabelToCodeAttr(getUntitledName('Untitled Pool')),
    name: 'poolCode',
    isRequired: true,
    label: MAP_TITLE.poolCode,
    isValidate: true,
    classNameTitle: 'title-pool',
    inputWrapperStyle: { maxWidth: 465 },
  },
  description: {
    ...MAP_INPUT_TEMPLATE.multiLangInput,
    name: 'description',
    label: MAP_TITLE.description,
    isValidate: true,
    isRequired: false,
    classNameTitle: 'title-pool',
    inputWrapperStyle: { maxWidth: 465 },
  },
});

/* --------------------------- VALIDATE ALL FIELD --------------------------- */
export const validateAll = (state, draft) => {
  const {
    dataConfig: { validFields },
  } = state;

  return [...validFields].every(each => {
    return draft.dataConfig[each].isValidate === true;
  });
};

/* ----------------------------- MAP VALUE TO FE ---------------------------- */
export const mapValueToFE = ({ activeRow }) => {
  const {
    pool_name_multilang = {},
    pool_code = '',
    description_multilang = {},
  } = activeRow;
  const data = {
    poolName: { value: pool_name_multilang, disabled: false },
    poolCode: { value: pool_code, disabled: true },
    description: { value: description_multilang, disabled: false },
  };
  const fields = ['poolName', 'poolCode', 'description'];
  return { mapValue: data, fields };
};

// export const autoFillCode = ({ value, state, draft, name }) => {
//   const dataTmp = { errors: '', isValidate: false, value: '' };
//   // const draftTmp = draft;
//   const newDefaultVal = getDefaultVal(value);
//   const oldDefaultVal = getDefaultVal(state.dataConfig[name].value);
//   if (newDefaultVal !== oldDefaultVal) {
//     const temptCode = serializeLabelToCodeAttr(newDefaultVal);
//     dataTmp.value = temptCode.substring(0, MAX_LCODE);
//   }
//   const dataValidTmp = draft.dataConfig.poolCode.validate(
//     draftTmp.dataConfig.poolCode,
//   );
//   return dataTmp;
// };
