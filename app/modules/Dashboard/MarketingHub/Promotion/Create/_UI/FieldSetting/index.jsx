import React, { useEffect, memo, useCallback } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import Grid from '@material-ui/core/Grid';
import { UILoading as Loading } from '@xlab-team/ui-components';
import { Box, makeStyles } from '@material-ui/core';
import ErrorBoundary from 'components/common/ErrorBoundary';
import PropTypes from 'prop-types';

// import Box from '../../../../../../../components/Templates/LayoutContent/Box';
import { init, reset, updateValue } from '../../../../../../../redux/actions';
import {
  makeSelectCreateSettingsDataConfig,
  makeSelectCreateSettings,
} from './selectors';

import { WrapperContent, StyleContent, StyleContentLoading } from './styles';
import { Title } from '../../styled';
import { uniq } from 'lodash';
import { getErrorMessageByCode } from '../../../../Journey/Create/_UI/JourneyTemplate/utils/helper';
import { translate, translations } from '@antscorp/antsomi-locales';

const MAP_TITLE = {
  TITLE_GENERAL_INFORMATION: translate(
    translations._TITL_GENERAL_INFORMATION,
    'General Information',
  ),
};

const useStyles = makeStyles(() => ({
  item: {
    padding: '5px 0',
  },
}));
const InputComponent = memo(data => {
  return (
    <Grid container spacing={2}>
      {data.componentEl(data)}
    </Grid>
  );
});
function ContentSettings(props) {
  const classes = useStyles();

  const { main, dataConfig, onInit, design, isViewMode, errors } = props;
  const { isLoading } = main;

  const onChangeData = useCallback(
    name => value => {
      if (value !== dataConfig[name].value) {
        props.onChangeDataConfig({ name, value });
      }
    },
    [dataConfig],
  );

  useEffect(() => {
    onInit({ design });
    return () => props.onReset();
  }, []);

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Promotion/Create/FieldSetting/index.jsx">
      <WrapperContent>
        <StyleContent>
          <StyleContentLoading isViewMode={isViewMode}>
            <Loading isLoading={isLoading} />
            <Box
              className="d-flex flex-column width-100 m-x-0"
              style={{ gap: 15 }}
            >
              <Title>{MAP_TITLE.TITLE_GENERAL_INFORMATION}</Title>
              <Grid container>
                <Grid item xs={12} md={7}>
                  <Grid container spacing={2} alignItems="center">
                    {dataConfig.infoFields.map(each => (
                      <Grid key={each} item xs={12}>
                        <InputComponent
                          {...dataConfig[each]}
                          onChange={onChangeData}
                          key={each}
                          classes={classes}
                          isViewMode={isViewMode}
                          errors={uniq([
                            ...dataConfig[each].errors,
                            getErrorMessageByCode(errors, each),
                          ])}
                        />
                      </Grid>
                    ))}
                  </Grid>
                </Grid>
              </Grid>
            </Box>
          </StyleContentLoading>
        </StyleContent>
      </WrapperContent>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  dataConfig: makeSelectCreateSettingsDataConfig(),
  main: makeSelectCreateSettings(),
});

export function mapDispatchToProps(dispatch, ownProps) {
  const PREFIX = ownProps.moduleConfig.key;
  return {
    onChangeDataConfig: value =>
      dispatch(updateValue(`${PREFIX}@@SETTING_POOL@@DATA_CONFIG@@`, value)),
    onReset: value => dispatch(reset(`${PREFIX}@@SETTING_POOL`, value)),
    onInit: value => dispatch(init(`${PREFIX}@@SETTING_POOL`, value)),
  };
}

ContentSettings.propTypes = {
  main: PropTypes.any,
  dataConfig: PropTypes.object,
  onChangeDataConfig: PropTypes.func.isRequired,
  onReset: PropTypes.func,
  onInit: PropTypes.func,
  design: PropTypes.string,
  isViewMode: PropTypes.bool,
  errors: PropTypes.array,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ContentSettings);

/* ---------------------------------- NOTE ---------------------------------- */
// init: value => dispatch(init(`${PREFIX}@@SETTING_POOL`, value)),
// onSave: value => dispatch(update(`${PREFIX}@@SETTING_POOL`, value)),
