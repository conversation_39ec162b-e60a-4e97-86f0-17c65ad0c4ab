/* eslint-disable indent */
import styled from 'styled-components';
import { FormControlLabel } from '@material-ui/core';
import colors from 'utils/colors';

export const WrapperButton = styled.div`
  display: flex;
  justify-content: flex-end;
  padding: 16px 0px;
  gap: 20px;
`;
export const Title = styled.div`
  /* width: 144px; */
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 16px;
  letter-spacing: normal;
  text-align: left;
  color: #000;
  /* margin: 18px 0px 0px 15px; */
`;

export const FormControlLabelStyled = styled(FormControlLabel)`
  margin-left: 0 !important;
  gap: 6px;
`;

export const ButtonTag = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;

  height: 22px;
  width: fit-content;
  border: 1px solid;
  border-radius: 1rem;

  font-size: 12px;
  font-weight: 700;
  line-height: 1.17;
  padding: 4px 10px;
  cursor: pointer;

  color: ${props => (!props.disabled ? colors.primary : '#A2A2A2')};
  border-color: ${props =>
    !props.disabled ? colors.primary : colors.disabled};
  background-color: ${props =>
    !props.disabled ? 'transparent' : colors.disabled};
`;

export const ButtonRemove = styled.div`
  width: 20px;
  height: 20px;
  padding: 4px;
  cursor: pointer;
`;

export const DisplayUI = styled.div`
  display: ${props => (props.isShow ? 'flex' : 'none')};
  flex-direction: column;
  height: 100%;
  /* gap: 15px;
  padding: 20px 15px; */
`;

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;

  width: 100%;
  padding: 20px 15px;
`;
