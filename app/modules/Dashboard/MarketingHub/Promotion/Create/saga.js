/* eslint-disable no-unused-vars */
import { call, put, select, takeLatest } from 'redux-saga/effects';
import { push } from 'react-router-redux';
import PromotionService from '../../../../../services/Promotion';

import { MODULE_CONFIG } from './config';
import ReduxTypes from '../../../../../redux/constants';
import {
  addNotification,
  getDetailDone,
  getList,
  getListDone,
  updateDone,
  updateValue,
} from '../../../../../redux/actions';
import {
  makeSelectPromotionSettingMatchingFile,
  makeSelectPromotionSettingExpiration,
  makeSelectPromotionSettingGeneralPool,
  makeSelectPromotionSettingAlert,
  makeSelectPromotionSettingRectriction,
  makeSelectPromotionSettingSource,
  makeSelectPromotionAccessInfo,
  makeSelectPromotionCodeStructure,
  makeSelectPromotionShopifyDiscount,
} from './selectors';
import { mapDataToAPI, MAP_VALUE_ERROR } from './utils';
import APP from '../../../../../appConfig';
import {
  getCurrentAccessUserId,
  getPortalId,
} from '../../../../../utils/web/cookie';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { safeParse, safeParseFirstEle } from '../../../../../utils/web/utils';
import { getErrorMessageV2Translate } from '../../../../../utils/web/message';
import { makeUrlPermisison } from '../../../../../utils/web/permission';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { translate, translations } from '@antscorp/antsomi-locales';

const PREFIX = MODULE_CONFIG.key;

/* ---------------------------- workerCommonSaga ---------------------------- */
export default function* workerCommonSaga() {
  yield takeLatest(`${PREFIX}@@ON_SAVE${ReduxTypes.UPDATE}`, handleSave);
  yield takeLatest(
    `${PREFIX}@@PROMOTION_INTEGRATION@@${ReduxTypes.GET_DETAIL}`,
    handleDetailPromotionIntegration,
  );
  yield takeLatest(
    `${PREFIX}@@SHOPIFY_DISCOUNT@@DISCOUNT_OPTIONS${ReduxTypes.GET_LIST}`,
    handleGetListDiscountShopify,
  );
}

/* ------------------------------- handleSave ------------------------------- */
function* handleSave(action, args) {
  const { id, promotionIntegration, data, callback } = action.payload;
  const { status } = data;
  // info reducer con Matching File
  const reducerMatchingFile = yield select(
    makeSelectPromotionSettingMatchingFile(),
  );

  // info reducer con setting time run pool
  const reducerSettingExpiration = yield select(
    makeSelectPromotionSettingExpiration(),
  );

  // info reducer con setting thông tin pool
  const reducerGeneralPool = yield select(
    makeSelectPromotionSettingGeneralPool(),
  );
  // info reducer con setting rectriction thông tin pool
  const reducerSettingAlert = yield select(makeSelectPromotionSettingAlert());
  const reducerSettingRectriction = yield select(
    makeSelectPromotionSettingRectriction(),
  );
  const reducerSettingSource = yield select(makeSelectPromotionSettingSource());

  const reducerAccessInfo = yield select(makeSelectPromotionAccessInfo());

  const reducerCodeStructure = yield select(makeSelectPromotionCodeStructure());

  const reducerShopifyDiscount = yield select(
    makeSelectPromotionShopifyDiscount(),
  );

  // Map Data To API
  const dataMap = mapDataToAPI({
    reducerMatchingFile,
    reducerSettingExpiration,
    reducerGeneralPool,
    reducerSettingAlert,
    reducerSettingRectriction,
    reducerSettingSource,
    id,
    status,
    reducerAccessInfo,
    reducerCodeStructure,
    reducerShopifyDiscount,
    promotionIntegration,
  });

  // Gửi thêm thông tin vào API nếu muốn
  const params = {
    data: dataMap,
  };

  const response = yield call(PromotionService.setting.create, params);
  console.log('🚀 ~ function*handleSave ~ response:', response);

  // Code 200 -> go to listing pool
  if (response.code === 200) {
    if (typeof callback === 'function') {
      callback();
    }
    // yield put(
    //   push(
    //     makeUrlPermisison(
    //       `${APP.PREFIX
    //       }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/promotion-center`,
    //     ),
    //   ),
    // );
    yield put(addNotification(NOTI.success));
  } else if (
    response.code === 500 &&
    (response.codeMessage === '_NAME_RULE_DUPLICATED' ||
      response.codeMessage === '_CODE_RULE_DUPLICATED')
  ) {
    const { fieldCode, errorCode } = safeParseFirstEle(response.data, {});
    yield put(
      updateValue(`${MODULE_CONFIG.key}@@SETTING_POOL@@ERROR_API`, {
        name: MAP_VALUE_ERROR[fieldCode] || '',
        errors: [
          getTranslateMessage(
            TRANSLATE_KEY[errorCode],
            'This field has been used, please try another.',
          ),
        ],
      }),
    );
    const noti = NOTI.systemError.fail(response);
    yield put(addNotification(noti));
  } else if (
    response.code === 500 &&
    response.codeMessage === '_LENGTH_RULE_BASED_CODE_IS_TOO_LONG'
  ) {
    const noti = {
      id: 'system-error',
      timeout: 4000,
      timestamp: 1554173349265,
      type: 'danger',
      message: translate(
        translations._POOL_RULE_VALIDATION_CHAC_LIMIT,
        'Code must be under 50 alphanumeric characters.',
      ),
    };

    yield put(addNotification(noti));
  }
  // Show Errors System
  else {
    const noti = NOTI.systemError.fail(response);
    yield put(addNotification(noti));
  }

  // Update isloading
  yield put(updateDone(`${MODULE_CONFIG.key}@@ON_SAVE`));
}

/** ---------------------------- handleDetailPromotionIntegration ------------------------------- */
function* handleDetailPromotionIntegration(action, args) {
  try {
    const { sourceId } = action.payload;
    if (!sourceId) return;

    const res = yield call(PromotionService.promotionSource.data.detail, {
      sourceId,
    });
    if (res.code === 200 && res.data) {
      const shopifyInfo = safeParse(res.data[0], {});
      yield put(
        getDetailDone(`${PREFIX}@@PROMOTION_INTEGRATION@@`, {
          data: shopifyInfo,
        }),
      );

      // Get Shopify Discount if integration is shopify
      if (shopifyInfo.connector_code === 'shopify') {
        yield put(
          getList(`${PREFIX}@@SHOPIFY_DISCOUNT@@DISCOUNT_OPTIONS`, {
            shopifyInfo,
          }),
        );
      }
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Promotion/Create/saga.js',
      func: 'handleDetailPromotionIntegration',
      data: error.stack,
    });
  }
}

function* handleGetListDiscountShopify(action, args) {
  try {
    const { shopifyInfo } = action.payload || {};

    const res = yield call(
      PromotionService.promotionSource.data.shopifyListPriceRule,
      {
        sourceId: shopifyInfo.source_id,
      },
    );

    const priceRules = safeParse(res?.data?.price_rules, []);
    const data = priceRules?.map(item => ({
      id: item.id,
      label: item.title,
      value: item.id,
    }));

    yield put(
      getListDone(`${PREFIX}@@SHOPIFY_DISCOUNT@@DISCOUNT_OPTIONS`, {
        data,
      }),
    );

    // Set default data discount
    if (data.length > 0) {
      yield put(
        updateValue(`${PREFIX}@@SHOPIFY_DISCOUNT@@DATA_CONFIG@@`, {
          name: 'discount',
          value: data[0],
        }),
      );
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Promotion/Create/saga.js',
      func: 'handleGetListDiscountShopify',
      data: error.stack,
    });
  }
}

/* ---------------------------------- Noti ---------------------------------- */
const NOTI = {
  systemError: {
    fail: res => ({
      id: 'system-error',
      ...getErrorMessageV2Translate(res.codeMessage),
      timeout: 4000,
      timestamp: 1554173349265,
      type: 'danger',
    }),
    success: {},
  },
  success: {
    id: 'success',
    message: 'Updates saved!',
    translateCode: TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
    timeout: 1500,
    timestamp: new Date().getTime(),
    type: 'success',
  },
};
