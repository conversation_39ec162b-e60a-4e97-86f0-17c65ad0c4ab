/* eslint-disable no-unreachable */
// Libraries
import React, { useState } from 'react';
import PropTypes from 'prop-types';

// Configs
import { MODULE_CONFIG } from '../../../config';

// Components
import { Flex } from '@antscorp/antsomi-ui';
import { Title, Wrapper } from '../../../styled';
import FieldSetting from '../../../_UI/FieldSetting/Loadable';
import FieldShopifyDiscount from '../../../_UI/FieldShopifyDiscount';
import FieldExpiration from '../../../_UI/FieldExpiration/Loadable';
import FieldRectriction from '../../../_UI/FieldRectriction/Loadable';
import SettingAlert from '../../../_UI/SettingAlert/Loadable';
import StepFlow from '../../../../../../../../components/Molecules/StepFlow';

// Utils
import { translate, translations } from '@antscorp/antsomi-locales';

const ShopifyIntegration = props => {
  const { design = 'create', moduleConfig, sourceId } = props;

  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      key: 0,
      title: translate(
        translations._TITL_GENERAL_INFORMATION,
        'General Information',
      ),
    },
    {
      key: 1,
      title: translate(
        translations._POOL_SHOPIFY_INFO_CODE_CONFIG,
        'Coupon Configuration',
      ),
    },
  ];

  const content = {
    0: (
      <Wrapper>
        <FieldSetting
          design={design}
          moduleConfig={moduleConfig}
          use="source"
        />
        <FieldShopifyDiscount.Infomation
          design={design}
          sourceId={sourceId}
          moduleConfig={moduleConfig}
        />
        <Flex vertical gap={15} style={{ paddingBottom: 15 }}>
          <Title>
            {translate(
              translations._POOL_ADVANCED_SETTINGS,
              'Advanced Settings',
            )}
          </Title>
          <FieldExpiration
            design={design}
            moduleConfig={moduleConfig}
            use="source"
          />
          <FieldRectriction
            design={design}
            moduleConfig={moduleConfig}
            use="source"
          />
          <SettingAlert
            design={design}
            moduleConfig={moduleConfig}
            use="source"
          />
        </Flex>
      </Wrapper>
    ),
    1: (
      <Wrapper>
        <FieldShopifyDiscount.Configuration
          design={design}
          moduleConfig={moduleConfig}
        />
      </Wrapper>
    ),
  };

  const handleBackStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleNextStep = () => {
    setCurrentStep(currentStep + 1);
  };

  return (
    <StepFlow
      current={currentStep}
      items={steps}
      content={content}
      onBack={handleBackStep}
      onNext={handleNextStep}
      onChange={setCurrentStep}
      stepProps={{
        style: {
          maxWidth: 450,
        },
      }}
    />
  );
};

ShopifyIntegration.propTypes = {
  design: PropTypes.string,
  moduleConfig: PropTypes.object,
  sourceId: PropTypes.string,
};

ShopifyIntegration.defaultProps = {
  design: 'create',
  moduleConfig: MODULE_CONFIG,
};

export default ShopifyIntegration;
