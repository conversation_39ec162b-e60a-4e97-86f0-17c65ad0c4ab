// Libraries
import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { isEmpty } from 'lodash';

// Config
import { POOL_CREATE_METHOD } from '../../config';

// Components
import RuleBaseGeneration from './_UI/RuleBaseGeneration';
import BulkUpload from './_UI/BulkUpload';
import ShopifyIntegration from './_UI/ShopifyIntegration';
import PromotionIntegration from './_UI/PromotionIntegration';
import Loading from 'components/common/LoadingLoadable';

const CreatePromotionSettings = props => {
  const { sourceId, promotionIntegration, onAfterChangeSource } = props;

  const renderSettings = useMemo(() => {
    const sourceComponents = {
      [POOL_CREATE_METHOD.RULE_BASE_GENERATION.value]: <RuleBaseGeneration />,
      [POOL_CREATE_METHOD.BULK_UPLOAD.value]: <BulkUpload />,
    };

    if (sourceComponents[sourceId]) return sourceComponents[sourceId];

    if (!isEmpty(promotionIntegration)) {
      return promotionIntegration?.connector_code === 'shopify' ? (
        <ShopifyIntegration sourceId={sourceId} />
      ) : (
        <PromotionIntegration
          sourceId={sourceId}
          onAfterChangeSource={onAfterChangeSource}
        />
      );
    }

    return <Loading />;
  }, [sourceId, promotionIntegration, onAfterChangeSource]);

  return <>{renderSettings}</>;
};

CreatePromotionSettings.propTypes = {
  sourceId: PropTypes.string,
  promotionIntegration: PropTypes.object,
  onAfterChangeSource: PropTypes.func,
};

export default CreatePromotionSettings;
