/* eslint-disable indent */
/* eslint-disable camelcase */
/* eslint-disable consistent-return */

import produce from 'immer';
import { combineReducers } from 'redux';

import { MODULE_CONFIG } from './config';

import ReduxTypes from '../../../../../../redux/constants';
import { getUntitledName } from '../../../../../../utils/web/properties';
import { isCheckAuthent } from './utils';
import { CONNECTOR_CODE } from '../../config';

const PREFIX = MODULE_CONFIG.key;

export const initialState = () => ({
  isInitDone: false,
  isLoading: true,
  isReadyAuthorize: false,
  listConector: [],
  authent: {},
  sourceId: null,
  setting: {},
  sourceName: getUntitledName('Untitled Promotion Integration'),
  isAuthent: false,
  errors: {},

  thirdPartyAuthenUrl: '',
  disabledAuthorize: true,
  disabledSave: true,
});

/* eslint-disable default-case, no-param-reassign */
export const mainReducerFor = () => {
  const mainReducer = (state = initialState(), action) =>
    produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.INIT}`: {
          const { sourceId } = action.payload;
          draft.sourceId = sourceId;
          draft.isLoading = true;

          return;
        }
        case `${PREFIX}${ReduxTypes.INIT_DONE}`: {
          const { data, setting = {} } = action.payload;
          draft.isLoading = false;
          draft.listConector = data;
          draft.isInitDone = true;
          draft.setting = setting;
          return;
        }
        case `${PREFIX}${ReduxTypes.UPDATE_VALUE}`: {
          return { ...state, ...action.payload };
        }
        case `${PREFIX}@@DETAIL${ReduxTypes.INIT_DONE}`: {
          const { data } = action.payload;
          draft.isLoading = false;
          draft.isInitDone = true;
          draft.sourceName = data.source_name;
          draft.authent = { ...data.settings };
          draft.setting = { ...data };
          return;
        }
        case `${PREFIX}@@DETAIL${ReduxTypes.UPDATE_VALUE}`: {
          const { data } = action.payload;
          draft.sourceName = data.source_name;
          draft.authent = { ...data.settings };
          draft.setting = { ...data };
          return;
        }
        case `${PREFIX}${ReduxTypes.RESET}`: {
          return initialState();
        }
        case `${PREFIX}@@AUTHENT${ReduxTypes.RESET}`: {
          draft.sourceName = initialState().sourceName;
          draft.authent = initialState().authent;
          draft.disabledAuthorize = initialState().disabledAuthorize;
          draft.disabledSave = initialState().disabledSave;
          draft.isReadyAuthorize = initialState().isReadyAuthorize;
          draft.thirdPartyAuthenUrl = initialState().thirdPartyAuthenUrl;
          return;
        }
        case `${PREFIX}@@ON_SAVE${ReduxTypes.UPDATE}`: {
          draft.isLoading = true;
          draft.disabledSave = true;
          return;
        }
        case `${PREFIX}@@ON_SAVE${ReduxTypes.UPDATE_DONE}`: {
          draft.isLoading = false;
          draft.disabledSave = false;
          return;
        }
        case `${PREFIX}@@ON_SAVE@@ERRORS${ReduxTypes.UPDATE_DONE}`: {
          draft.isLoading = false;
          draft.disabledSave = false;
          return;
        }
        case `${PREFIX}@@ON_CHANGE${ReduxTypes.UPDATE_VALUE}`: {
          // Uppdate authent
          const { key, value } = action.payload;
          draft.authent[key] = value;

          // Check button disabled
          const authorizationSettings =
            state?.setting?.connector_settings?.authorizationSettings;
          const settings = state?.setting?.connector_settings?.settings;
          const authent = { ...state?.authent, [key]: value };
          const connectorCode = state?.setting?.connector_code;
          const hasMissingRequiredField = authorizationSettings?.some(field => {
            return settings?.[field]?.isRequired && !authent?.[field];
          });
          if (connectorCode === CONNECTOR_CODE.SHOPIFY) {
            draft.disabledAuthorize = hasMissingRequiredField;
            draft.disabledSave = true;
          } else {
            draft.disabledSave = hasMissingRequiredField;
          }
          return;
        }
        case `${PREFIX}@@STORY_NAME@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.sourceName = action.payload;
          return;
        }
        case `${PREFIX}@@AUTHEN${ReduxTypes.UPDATE_DONE}`: {
          draft.isAuthent = true;
          return;
        }
        case `${PREFIX}@@ERRORS_API${ReduxTypes.UPDATE_VALUE}`: {
          const { name, message } = action.payload;
          // console.log(name, message);
          draft.errors[name] = message;
          return;
        }
        case `${PREFIX}@@ERRORS${ReduxTypes.UPDATE_VALUE}`: {
          draft.isLoading = false;
          draft.errors = {
            ...(draft?.errors || {}),
            ...(action.payload || {}),
          };
          return;
        }
        case `${PREFIX}@@ERRORS${ReduxTypes.RESET}`: {
          draft.errors = initialState().errors;
          return;
        }
        case `${PREFIX}@@BACK_LIST${ReduxTypes.UPDATE_VALUE}`: {
          draft.authent = {};
          // console.log(name, message);
          return;
        }
        case `${PREFIX}@@AUTHORIZE_SHOPIFY${ReduxTypes.UPDATE_VALUE}`: {
          draft.isLoading = true;
          draft.disabledSave = true;
          // draft.disabledAuthorize = true;
          draft.isReadyAuthorize = false;
          return;
        }
        case `${PREFIX}@@AUTHORIZE_SHOPIFY${ReduxTypes.UPDATE_DONE}`: {
          const { thirdPartyAuthenUrl } = action.payload;
          draft.isLoading = false;

          draft.isReadyAuthorize = true;
          draft.thirdPartyAuthenUrl = thirdPartyAuthenUrl;
          return;
        }
        case `${PREFIX}@@RECEIVE_AUTHORIZE_SHOPIFY${ReduxTypes.UPDATE_VALUE}`: {
          Object.keys(action.payload).forEach(key => {
            draft.authent[key] = action.payload[key];
          });
          draft.isReadyAuthorize = false;
          draft.thirdPartyAuthenUrl = '';
          draft.disabledSave = false;
          return;
        }
        case `${PREFIX}@@OPEN_AUTHORIZE_SHOPIFY${ReduxTypes.UPDATE_VALUE}`: {
          draft.thirdPartyAuthenUrl = '';
          draft.isReadyAuthorize = false;
          return;
        }

        default:
          return state;
      }
    });
  return mainReducer;
};

// export default customerReducer;
export default combineReducers({
  main: mainReducerFor(),
});
