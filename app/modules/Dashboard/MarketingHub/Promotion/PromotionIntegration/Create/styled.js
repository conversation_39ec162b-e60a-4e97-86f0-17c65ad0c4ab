import styled from 'styled-components';
import COLORS from 'utils/colors';
import { fonts } from '../../../../../../utils/variables';
import { Flex } from '@antscorp/antsomi-ui';

export const WrapperButton = styled.div`
  display: flex;
  justify-content: flex-end;
  padding: 16px 0px;
  gap: 20px;
`;
export const StyleBox = styled.div`
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 0.375em 0px !important;
  margin-top: ${props => (props.isModal ? '0px' : '20px')};
  width: 100%;
  height: calc(100% - 75px);
  background-color: #fff;
  border: 1px solid transparent;
  padding: 15px 20px;
`;
export const WrapperBox = styled.div`
  width: 300px;
  height: 175px;
  margin: 0 15px 15px 0;
  padding: 10px;
  border-radius: 3px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.25);
  background-color: #fff;
  &:hover {
    box-shadow: 1px 1px 2px #dddddd;
    border: 1px solid #32437a;
  }
`;
export const BoxChannel = styled.div`
  margin-bottom: 10px;
`;
export const WrapperBoxIcon = styled.div`
  position: relative;
  display: inline-block;
  padding: 0 20px 0 10px;
`;
export const BoxIcon = styled.img`
  background-image: none;
  position: relative;
  border-radius: 3px;
  margin-right: 15px;
  top: -10px;
  width: 50px;
  max-height: 50px;
  height: auto;
  // &::after {
  //   content: '';
  //   position: relative;
  //   display: block;
  //   width: auto;
  //   max-height: 50px;
  //   height: 50px;
  //   background-color: #fff;
  //   background: url('~Assets/images/logo-antalyser2.png') no-repeat;
  //   background-size: contain;
  // }
`;
export const BoxDescription = styled.div`
  display: inline-block;
  padding: 0;
  margin: 0;
`;
export const Label = styled.label`
  font-weight: bold;
  margin: 0;
  max-width: 185px;
  font-size: 14px;
  height: 18px;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
  color: #222;
`;
export const Text = styled.span`
  display: block;
  opacity: 0.8;
  max-width: 185px;
  height: 18px;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.33;
  letter-spacing: normal;
  text-align: left;
  color: #666;
`;
export const LabelDescription = styled.label`
  height: 18px;
  max-width: 270px;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.33;
  letter-spacing: normal;
  text-align: left;
  color: #222;
`;
export const Title = styled.div`
  width: 122px;
  height: 21px;
  margin: 10px 48px 10px 0;
  font-family: Roboto;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: normal;
  text-align: left;
  color: #222;
`;
export const Line = styled.div`
  width: 2px;
  height: 38px;
  position: relative;

  &::after {
    content: '..........';
    font-weight: bold;
    font-size: 15px;
    color: #e5e6f3;
    transform: rotate(90deg);
    height: 8px;
    width: 38px;
    display: inline-block;
    overflow: hidden;
    line-height: 0;
    letter-spacing: 0;
    border: none;
    outline: none;
    position: absolute;
    left: -18px;
    top: calc(50% - 4px);
  }
  &.h-30 {
    &::after {
      width: 30px;
      left: -14px;
    }
  }
  &.grey-d2 {
    &::after {
      color: #d2d2d2;
    }
  }
`;
export const ContentTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
  font-weight: bold;
`;
export const WrapperContent = styled.div`
  display: flex;
  flex-basis: 300px;
  flex-wrap: wrap;
  margin-top: 20px;
  position: relative;
`;
export const StyleDiv = styled.div`
  &:last-child {
    a,
    .MuiInputBase-input {
      color: ${COLORS.black};
      font-family: ${fonts.roBold};
      max-width: ${props => props.maxWidth};
      font-size: 1.25rem !important;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
`;

export const BodyDrawer = styled(Flex)`
  padding: 15px;
`;
