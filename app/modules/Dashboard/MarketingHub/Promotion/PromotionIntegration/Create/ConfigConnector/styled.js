import styled from 'styled-components';

export const TableContent = styled.div`
  height: calc(100% - 55px);
  border: solid 1px #c0daf0;
  background-color: #fff;
  display: flex;
  margin-top: '10px';
`;
export const WrapperAuthen = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
`;
export const CellAuthen = styled.div`
  font-size: 14px;
  font-weight: 700;
  line-height: 1.17;
  color: #000000;

  /* width: 250px;
  height: 40px;
  padding: 10px 15px;
  background-color: #fafafa;
  border-right: 1px solid #c0daf0;
  border-bottom: 1px solid #c0daf0;
  font-size: 14px;
  font-weight: bold;
  color: #222222; */
`;
export const WrapperContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;

  max-width: 60%;
`;
export const Text = styled.div`
  font-size: 12px;
  font-weight: 400;
  color: #222222;
  line-height: 1.17;
`;

export const Label = styled.label``;

export const ContentWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 15px;
`;

export const AuthorizeWrapper = styled.div`
  .authorize-btn {
    width: fit-content;
    margin-top: 15px;
  }
  .required-mark {
    color: red;
    margin-left: 5px;
  }
  .shop-name {
    margin-left: 100px;
  }
`;
