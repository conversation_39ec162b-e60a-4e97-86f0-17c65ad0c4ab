/* eslint-disable prefer-const */
/* eslint-disable no-restricted-globals */
/* eslint-disable react/no-unescaped-entities */
/* eslint-disable camelcase */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';

// Styled
import { BodyDrawer } from '../styled';

// Components
import Authencation from './Authencation';
import LayoutContent from '../../../../../../../components/Templates/LayoutContent';
import HeaderDrawer from '../../../../../../../components/common/HeaderDrawer';
import { Button, EditableName, Scrollbars } from '@antscorp/antsomi-ui';
import { Box } from '@material-ui/core';
import WrapperDisable from '../../../../../../../components/common/WrapperDisable';
import VendorCard from '../../../../../../../components/Organisms/VendorCard';
import UIHelmet from '../../../../../../../components/Templates/LayoutContent/Helmet';

// Utils
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';

// Constants
import TRANSLATE_KEY from '../../../../../../../messages/constant';

// Constants
import { translate, translations } from '@antscorp/antsomi-locales';

const MAP_TITLE = {
  defaultTitle: getTranslateMessage(
    TRANSLATE_KEY._,
    'Create new Promotion Integration',
  ),
  shopifyDescription: getTranslateMessage(
    TRANSLATE_KEY._,
    "Easily integrate your Shopify Store to start syncing data. To get started, you'll need to authorize your store.",
  ),
  shopifyAuthorize: getTranslateMessage(
    TRANSLATE_KEY._,
    'Authorize Shopify Store',
  ),
  save: translate(translations._ACT_SAVE, 'Save'),
};

export const ConfigConnector = props => {
  let {
    dataSelect,
    initData,
    design,
    sourceName,
    isLoading,
    isLoadingSaveName,
    errors,
    disabled,
    isReadyAuthorize,
    thirdPartyAuthenUrl,

    disabledAuthorize,
    disabledSave,
  } = props;

  let {
    connector_settings,
    connector_code: connectorCode,
    connector_name: connectorName,
  } = dataSelect;

  // States
  const [titlePage, setTitlePage] = useState(sourceName);

  const onChangeValue = (key, value) => {
    props.callback('ON_CHANGE_DATA', { key, value });
  };
  const onAuthenticate = () => {
    props.callback('AUTHENTICATE');
  };

  const onChangeSourceName = name => {
    props.callback('CHANGE_SOURCE_NAME', name);
  };
  const onSaveSourceName = () => {
    props.callback('SAVE_SOURCE_NAME');
  };

  const onSave = () => {
    props.callback('SAVE');
  };

  const handleTitlePage = () => {
    if (!sourceName) {
      setTitlePage(MAP_TITLE.defaultTitle);
    } else if (sourceName && !errors?.name) {
      setTitlePage(sourceName);
    }
  };

  const handleSaveName = () => {
    onSaveSourceName();
    handleTitlePage();
  };

  const onClickAuthorize = () => {
    props.callback('AUTHORIZE_SHOPIFY');
  };

  useEffect(() => {
    const bc = new BroadcastChannel('cdp-promotion-authen-channel');
    bc.onmessage = event => {
      const { type, accessToken, shop } = event.data;

      if (type === 'AUTHEN_SHOPIFY_RESULT') {
        props.callback('RECEIVE_AUTHEN_SHOPIFY', { accessToken, shop });
      }
    };

    return () => {
      bc.close();
    };
  }, []);

  useEffect(() => {
    if (!titlePage) {
      setTitlePage(sourceName);
    }
  }, [sourceName]);

  const popupCenter = (url, title, w, h) => {
    // Fixes dual-screen position                             Most browsers      Firefox
    const dualScreenLeft =
      window.screenLeft !== undefined ? window.screenLeft : window.screenX;
    const dualScreenTop =
      window.screenTop !== undefined ? window.screenTop : window.screenY;

    const width =
      window.innerWidth || document.documentElement.clientWidth || screen.width;
    const height =
      window.innerHeight ||
      document.documentElement.clientHeight ||
      screen.height;

    const systemZoom = width / window.screen.availWidth;
    const left = (width - w) / 2 / systemZoom + dualScreenLeft;
    const top = (height - h) / 2 / systemZoom + dualScreenTop;
    const newWindow = window.open(
      url,
      title,
      `
      scrollbars=yes,
      width=${w / systemZoom}, 
      height=${h / systemZoom}, 
      top=${top}, 
      left=${left}
      `,
    );

    if (window.focus) newWindow.focus();
  };

  useEffect(() => {
    if (isReadyAuthorize && thirdPartyAuthenUrl) {
      popupCenter(thirdPartyAuthenUrl, 'Shopify Authentication', 500, 500);
      props.callback('OPEN_AUTHORIZE_SHOPIFY');
    }
  }, [isReadyAuthorize]);

  const renderSetting = () => {
    if (!connectorName) return null;

    return (
      <>
        {/* <VendorCard
          logo={dataSelect.logo_url}
          name={dataSelect.connector_name}
          author={dataSelect?.properties?.author}
          header
        /> */}
        <Box>
          <Authencation
            initData={initData}
            settings={connector_settings}
            errors={errors}
            connectorCode={connectorCode}
            disabledAuthorize={disabledAuthorize}
            disabledSave={disabledSave}
            onChangeValue={onChangeValue}
            onAuthenticate={onAuthenticate}
            onClickAuthorize={onClickAuthorize}
          />
        </Box>
      </>
    );
  };

  return (
    <LayoutContent padding={0} justifyContent="flex-start">
      <UIHelmet title={titlePage} />
      <HeaderDrawer
        style={{ height: 50 }}
        extraContent={
          <Button
            type="primary"
            loading={isLoading}
            disabled={disabledSave}
            onClick={onSave}
          >
            {MAP_TITLE.save}
          </Button>
        }
      >
        <EditableName
          isLoading={isLoadingSaveName || isLoading}
          value={sourceName}
          onChange={onChangeSourceName}
          error={errors?.name}
          onBlur={handleSaveName}
          readonly={disabled}
          required
        />
      </HeaderDrawer>
      <Scrollbars>
        <WrapperDisable disabled={disabled || isLoading}>
          <BodyDrawer className="body" vertical gap={25}>
            {renderSetting()}
          </BodyDrawer>
        </WrapperDisable>
      </Scrollbars>
    </LayoutContent>
  );
};

export default ConfigConnector;
