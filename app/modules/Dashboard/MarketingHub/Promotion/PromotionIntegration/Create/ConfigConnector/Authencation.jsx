/* eslint-disable indent */
/* eslint-disable array-callback-return */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/prop-types */
import React from 'react';
import Grid from '@material-ui/core/Grid';
import PropTypes from 'prop-types';

import {
  Cell<PERSON><PERSON>en,
  W<PERSON>perA<PERSON>en,
  WrapperContent,
  Text,
  Label,
} from './styled';
import { Input, Button, Typography } from '@antscorp/antsomi-ui';

// Constants
import { CONNECTOR_CODE } from '../../../config';

// Translate
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { translate, translations } from '@antscorp/antsomi-locales';

const MAP_TITLE = {
  shopName: getTranslateMessage(TRANSLATE_KEY._SHOP_NAME, 'Shop name'),
  authorize: getTranslateMessage(TRANSLATE_KEY._AUTHORIZE, 'Authorize'),
};

export const Authencation = props => {
  const {
    settings = {},
    initData,
    errors,
    connectorCode,
    disabledAuthorize,
    onClickAuthorize,
  } = props;
  const onChange = (value, key) => {
    props.onChangeValue(value, key);
  };
  const onAuthenticate = () => {
    props.onAuthenticate();
  };
  const renderContent = (data, key) => {
    if (!data || data?.hidden) return null;

    switch (data.dataType) {
      case 'string': {
        return (
          <Label key={key}>
            <Grid container alignItems="center">
              <Grid item xs={3}>
                <Text>
                  {translate(translations?.[data?.labelCode], data?.label)}
                  {data?.isRequired && (
                    <span style={{ color: '#ff0000' }}> *</span>
                  )}
                </Text>
              </Grid>
              <Grid item xs={9}>
                <Input
                  value={initData[key]}
                  maxLength={255}
                  debounce={500}
                  errorArchive={errors?.[key] || ''}
                  onAfterChange={e => onChange(key, e)}
                />
              </Grid>
            </Grid>
          </Label>
        );
      }

      default: {
        return null;
      }
    }
  };

  const renderSetting = () => {
    switch (connectorCode) {
      case CONNECTOR_CODE.SHOPIFY: {
        return (
          <>
            {settings?.authorizationSettings &&
              settings?.authorizationSettings.map(item => {
                return renderContent(settings.settings[item], item);
              })}
            {errors?.accessToken && (
              <Typography.Text type="danger">
                {errors?.accessToken}
              </Typography.Text>
            )}
            <Button
              disabled={disabledAuthorize}
              type="primary"
              style={{ width: 'fit-content' }}
              onClick={onClickAuthorize}
            >
              {MAP_TITLE.authorize}
            </Button>
          </>
        );
      }
      default:
        return (
          settings.authorizationSettings &&
          settings.authorizationSettings.map(item => {
            return renderContent(settings.settings[item], item);
          })
        );
    }
  };

  return (
    <WrapperAuthen>
      <CellAuthen>Authentication</CellAuthen>
      <WrapperContent>{renderSetting()}</WrapperContent>
    </WrapperAuthen>
  );
};

Authencation.propTypes = {
  initData: PropTypes.object,
  settings: PropTypes.object,
  errors: PropTypes.object,
  connectorCode: PropTypes.string,
  disabledAuthorize: PropTypes.bool,
  onClickAuthorize: PropTypes.func,
};

export default Authencation;
