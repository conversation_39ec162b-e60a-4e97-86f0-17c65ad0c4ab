// Libraries
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { translate, translations } from '@antscorp/antsomi-locales';
import { Flex } from '@antscorp/antsomi-ui';

// Configs
import { MODULE_CONFIG } from '../../config';

// Components
import FieldSetting from '../../../../Create/_UI/FieldSetting';
import { Title } from '../../../../Create/styled';
import FieldExpiration from '../../../../Create/_UI/FieldExpiration';
import FieldRectriction from '../../../../Create/_UI/FieldRectriction';
import SettingAlert from '../../../../Create/_UI/SettingAlert';
import FieldShopifyDiscount from '../../../../Create/_UI/FieldShopifyDiscount';
import StepFlow from '../../../../../../../../components/Molecules/StepFlow';

// Constants
import { METHOD_OPTIONS_MAP } from '../../../../Create/_UI/FieldShopifyDiscount/constant';

const ShopifyIntegration = props => {
  const {
    design,
    moduleConfig,
    isViewMode,
    errors,
    activeRow,
    sourceId,
  } = props;

  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      key: 0,
      title: translate(
        translations._TITL_GENERAL_INFORMATION,
        'General Information',
      ),
    },
    {
      key: 1,
      title: translate(translations._, 'Coupon Configuration'),
    },
  ];

  const content = {
    0: (
      <Flex vertical style={{ padding: '20px 15px', width: '100%' }}>
        <FieldSetting
          design={design}
          moduleConfig={moduleConfig}
          isViewMode={isViewMode}
          errors={errors}
          use="manual"
        />

        <Flex vertical gap={15} style={{ padding: '15px 0px' }}>
          <FieldShopifyDiscount.Infomation
            sourceId={sourceId}
            design={design}
            moduleConfig={moduleConfig}
            isViewMode={isViewMode}
            use="source"
          />

          <Title>
            {translate(
              translations._POOL_ADVANCED_SETTINGS,
              'Advanced Settings',
            )}
          </Title>
          <FieldExpiration
            design={design}
            moduleConfig={MODULE_CONFIG}
            isViewMode={isViewMode}
            use="manual"
          />
          <FieldRectriction
            design={design}
            moduleConfig={MODULE_CONFIG}
            isViewMode={isViewMode}
            use="manual"
          />
          <SettingAlert
            design={design}
            moduleConfig={MODULE_CONFIG}
            isViewMode={isViewMode}
            use="manual"
          />
        </Flex>
      </Flex>
    ),
    1: (
      <Flex vertical style={{ padding: '20px 15px', width: '100%' }}>
        <FieldShopifyDiscount.Configuration
          design={design}
          moduleConfig={moduleConfig}
          isViewMode={isViewMode}
          use="source"
        />
      </Flex>
    ),
  };

  const handleBackStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleNextStep = () => {
    setCurrentStep(currentStep + 1);
  };

  return activeRow?.settings?.method ===
    METHOD_OPTIONS_MAP.BULK_UPLOAD.value ? (
    content?.[0]
  ) : (
    <StepFlow
      current={currentStep}
      items={steps}
      content={content}
      onBack={handleBackStep}
      onNext={handleNextStep}
      onChange={setCurrentStep}
      stepProps={{
        style: {
          maxWidth: 450,
        },
      }}
    />
  );
};

ShopifyIntegration.propTypes = {
  design: PropTypes.string,
  moduleConfig: PropTypes.object,
  isViewMode: PropTypes.bool,
  errors: PropTypes.array,
  activeRow: PropTypes.object,
  sourceId: PropTypes.string,
};

ShopifyIntegration.defaultProps = {
  design: 'update',
  moduleConfig: MODULE_CONFIG,
};

export default ShopifyIntegration;
