// Libraries
import { call, put, select, takeLatest } from 'redux-saga/effects';

// Configs
import { MODULE_CONFIG } from './config';

// Constants
import ReduxTypes from '../../../../../../redux/constants';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { METHOD_OPTIONS_MAP } from '../../Create/_UI/FieldShopifyDiscount/constant';

// Actions
import {
  addNotification,
  getDetailDone,
  getList,
  getListDone,
  initDone,
  updateDone,
  updateValue,
} from '../../../../../../redux/actions';

// Selectors
import {
  makeSelectPromotionSettingExpiration,
  makeSelectPromotionSettingGeneralPool,
  makeSelectPromotionSettingMain,
  makeSelectPromotionSettingAlert,
  makeSelectPromotionSettingRectriction,
  makeSelectPromotionSettingSource,
  makeSelectPromotionAccessInfo,
  makeSelectPromotionCodeStructure,
  makeSelectPromotionShopifyDiscount,
} from './selectors';

// Services
import PromotionService from '../../../../../../services/Promotion';

// Utils
import { mapDataToAPI } from './utils';
import { mapDataToAPI as mapDataToAPIGetDetails, mapDataToFE } from '../utils';
import { getDefaultVal } from '../../../../../../components/common/InputLanguage/utils';
import { MAP_VALUE_ERROR } from '../../Create/utils';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import { MODULE_CONFIG as MODULE_CONFIG_PARENT } from '../config';
import { addMessageToQueue } from '../../../../../../utils/web/queue';
import { getErrorMessageV2Translate } from '../../../../../../utils/web/message';
import { safeParse } from '../../../../../../utils/common';
import { translate, translations } from '@antscorp/antsomi-locales';

const PREFIX = MODULE_CONFIG.key;

/* ---------------------------------- SAGA ---------------------------------- */
export default function* workerCommonSaga() {
  yield takeLatest(`${PREFIX}${ReduxTypes.INIT}`, handleInit);
  yield takeLatest(`${PREFIX}@@SAVE_NAME${ReduxTypes.UPDATE}`, handleSaveName);
  yield takeLatest(`${PREFIX}@@ON_SAVE${ReduxTypes.UPDATE}`, handleSave);
  yield takeLatest(
    `${PREFIX}@@PROMOTION_INTEGRATION${ReduxTypes.GET_DETAIL}`,
    handleGetDetailPromotionIntegration,
  );
  yield takeLatest(
    `${PREFIX}@@SHOPIFY_DISCOUNT@@DISCOUNT_OPTIONS${ReduxTypes.GET_LIST}`,
    handleGetListDiscountShopify,
  );
}

/* -------------------------------- Init Data ------------------------------- */
function* handleInit(action) {
  try {
    const { activeRow } = action.payload;
    let integrationInfo;

    // Get Promotion Integration IF type 3
    if (Number(activeRow.type) === 2 && activeRow?.source_id) {
      integrationInfo = yield call(handleGetDetailPromotionIntegration, {
        payload: { sourceId: activeRow.source_id, activeRow },
      });
    }

    // INIT DONE SET DATA
    yield put(initDone(`${MODULE_CONFIG.key}`, activeRow));
    // PUSH DATA cho reducer con của setting pool
    yield put(getDetailDone(`${MODULE_CONFIG.key}@@SETTING_POOL`, activeRow));

    // Push data cho reducer expiration pool
    yield put(getDetailDone(`${MODULE_CONFIG.key}@@EXPERITION`, activeRow));
    // Push data cho reducer restriction pool

    yield put(getDetailDone(`${MODULE_CONFIG.key}@@RESTRICTION`, activeRow));

    if (
      Number(activeRow.type) === 3 ||
      activeRow?.settings?.method ===
        METHOD_OPTIONS_MAP.RULE_BASED_GENERATOR?.value
    ) {
      yield put(
        getDetailDone(`${MODULE_CONFIG.key}@@CODE_STRUCTURE`, activeRow),
      );
    }

    // Push data cho reducer setting alert
    if (activeRow.alertSetting != null) {
      yield put(
        getDetailDone(`${MODULE_CONFIG.key}@@SETTING_POOL_ALERT`, activeRow),
      );
    }

    // Push data cho Integration Source
    if (
      Number(activeRow.type) === 2 &&
      integrationInfo &&
      integrationInfo?.connector_code !== 'shopify' &&
      activeRow.settings != null
    ) {
      yield put(
        getDetailDone(`${MODULE_CONFIG.key}@@SETTING_SOURCE`, activeRow),
      );
    }

    // Push data cho Integration Shopify
    if (integrationInfo && integrationInfo?.connector_code === 'shopify') {
      yield put(
        getDetailDone(`${MODULE_CONFIG.key}@@SHOPIFY_DISCOUNT`, activeRow),
      );
    }

    // Push data cho reducer share access
    yield put(
      getDetailDone(`${MODULE_CONFIG.key}@@PROMOTION_SHARE_ACCESS`, activeRow),
    );
  } catch (error) {
    console.log('error', error);
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Promotion/Detail/Settings/saga.js',
      func: 'handleInit',
      data: error.stack,
    });
  }
}

/* -------------------------------- SAVE NAME ------------------------------- */
function* handleSaveName(action) {
  const { callback } = action.payload;
  try {
    // Get Data của reducer main
    const reducerMainSetting = yield select(makeSelectPromotionSettingMain());

    // Get Data của reducer con -> expriration pool
    const reducerGeneralPool = yield select(
      makeSelectPromotionSettingGeneralPool(),
    );
    const { poolName } = reducerGeneralPool?.dataConfig;

    if (
      poolName?.errors?.length ||
      !poolName?.isValidate ||
      !poolName?.isChanged
    ) {
      return yield put(updateDone(`${MODULE_CONFIG.key}@@SAVE_NAME`));
    }

    // PARAMS Call API
    const params = {
      poolId: reducerMainSetting.poolId,
      data: {
        poolName: getDefaultVal(poolName?.value),
      },
    };

    // Serivice API
    const response = yield call(
      PromotionService.setting.updateNameById,
      params,
    );
    // Success
    if (response.code === 200) {
      callback('AFTER_SAVE_NAME', getDefaultVal(poolName?.value));
    } else if (response.code === 500) {
      yield call(handleSaveError, {
        dataErros: {
          fieldCode: 'pool_name',
          errorCode: response.codeMessage || '_NOTIFICATION_ERROR',
        },
      });
    } else {
      yield call(handleSaveError, {
        dataErros: {
          fieldCode: 'pool_name',
          errorCode: '_NOTIFICATION_ERROR',
        },
      });
    }
    return yield put(updateDone(`${MODULE_CONFIG.key}@@SAVE_NAME`));
  } catch (error) {
    console.error(error);
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Promotion/Detail/Settings/saga.js',
      func: 'handleSaveName',
      data: error.stack,
    });
    yield call(handleSaveError, {
      dataErros: {
        fieldCode: 'pool_name',
        errorCode: '_NOTIFICATION_ERROR',
      },
    });
    return yield put(updateDone(`${MODULE_CONFIG.key}@@SAVE_NAME`));
  }
}

/* -------------------------------- SAVE DATA ------------------------------- */
function* handleSave(action) {
  const { type, data, callback, promotionIntegration } = action.payload || {};
  const { status } = data;
  try {
    // Get Data của reducer main
    const reducerMainSetting = yield select(makeSelectPromotionSettingMain());

    // Get Data của reducer con -> setting pool
    const reducerSettingExpiration = yield select(
      makeSelectPromotionSettingExpiration(),
    );

    // Get Data của reducer con -> expriration pool
    const reducerGeneralPool = yield select(
      makeSelectPromotionSettingGeneralPool(),
    );
    // Get Data của reducer con -> set up alert
    const reducerSettingAlert = yield select(makeSelectPromotionSettingAlert());
    const reducerSettingRectriction = yield select(
      makeSelectPromotionSettingRectriction(),
    );
    const reducerSettingSource = yield select(
      makeSelectPromotionSettingSource(),
    );

    // Get Data của reducer con -> share access
    const reducerAccessInfo = yield select(makeSelectPromotionAccessInfo());

    const reducerCodeStructure = yield select(
      makeSelectPromotionCodeStructure(),
    );

    const reducerShopifyDiscount = yield select(
      makeSelectPromotionShopifyDiscount(),
    );

    // Map Data Call API
    const dataMap = mapDataToAPI({
      expiration: reducerSettingExpiration,
      settingPool: reducerGeneralPool.dataConfig,
      setUpAlert: reducerSettingAlert,
      settingRectriction: reducerSettingRectriction,
      settingSource: reducerSettingSource,
      type,
      status,
      reducerAccessInfo,
      reducerCodeStructure,
      reducerShopifyDiscount,
      promotionIntegration:
        promotionIntegration || reducerMainSetting.promotionIntegration,
    });

    // PARAMS Call API
    const params = {
      data: dataMap,
      poolId: reducerMainSetting.poolId,
    };

    // Serivice API
    const response = yield call(PromotionService.setting.updateById, params);
    // Success
    if (response.code === 200) {
      yield call(handleSaveSuccess, { reducerGeneralPool, reducerMainSetting });
      if (typeof callback === 'function') {
        callback();
      }
    }
    // Check errors field
    else if (
      response.code === 500 &&
      (response.codeMessage === '_NAME_RULE_DUPLICATED' ||
        response.codeMessage === '_CODE_RULE_DUPLICATED')
    ) {
      yield call(handleSaveError, { dataErros: response.data[0] });
    } else if (
      response.code === 500 &&
      response.codeMessage === '_LENGTH_RULE_BASED_CODE_IS_TOO_LONG'
    ) {
      const noti = {
        id: 'system-error',
        timeout: 4000,
        timestamp: 1554173349265,
        type: 'danger',
        message: translate(
          translations._POOL_RULE_VALIDATION_CHAC_LIMIT,
          'Code must be under 50 alphanumeric characters.',
        ),
      };

      yield put(addNotification(noti));
    } else {
      const noti = NOTI.systemError.fail(response);
      yield put(addNotification(noti));
    }

    // Update DONE
    yield put(updateDone(`${MODULE_CONFIG.key}@@ON_SAVE`));
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Promotion/Detail/Settings/saga.js',
      func: 'handleSave',
      data: error.stack,
    });
  }
}

/* ---------------------------- SAGA GET DETAILS BY POOL ID---------------------------- */

function* handleGetDetail(action) {
  const { poolId } = action;
  const params = {
    data: mapDataToAPIGetDetails({ poolId }),
  };
  const response = yield call(PromotionService.setting.getByIds, params);
  yield put(
    updateValue(
      `${MODULE_CONFIG_PARENT.key}@@INFO_PROMOTION_CODE@@`,
      mapDataToFE(response.data[0]),
    ),
  );
}

/* ------------------------ HANDLE AFTER SAVE SUCCESS ----------------------- */
function* handleSaveSuccess(action) {
  const { reducerMainSetting } = action;
  // NOTI SUCCESS
  yield put(addNotification(NOTI.success));

  // Go to list
  // yield put(
  //   push(
  //     makeUrlPermisison(
  //       `${
  //         APP.PREFIX
  //       }/${getPortalId()}/${getCurrentAccessUserId()}/marketing-hub/promotion-center?detail-promotion-codes=${
  //         reducerMainSetting.poolId
  //       }&detail-tab=promotion-codes`,
  //     ),
  //   ),
  // );

  // Update Tab
  // yield put(
  //   updateValue(
  //     `${MODULE_CONFIG_PARENT.key}@@CHANGE_TAB`,
  //     TABS_CONFIG.PROMOTION_CODES.key,
  //   ),
  // );

  // get details
  yield call(handleGetDetail, { poolId: reducerMainSetting.poolId });
}

/* ------------------------- HANDLE AFTER SAVE ERROR ------------------------ */
function* handleSaveError(action) {
  const { fieldCode, errorCode } = action.dataErros;

  // Push errors field
  yield put(
    updateValue(`${MODULE_CONFIG.key}@@SETTING_POOL@@ERROR_API`, {
      name: MAP_VALUE_ERROR[fieldCode] || '',
      errors: [
        getTranslateMessage(
          TRANSLATE_KEY[errorCode],
          'This name has been used, please try another name.',
        ),
      ],
    }),
  );
}

/* ------------------------- HANDLE GET DETAIL INTEGRATION ------------------------ */
function* handleGetDetailPromotionIntegration(action) {
  try {
    const { sourceId, activeRow } = action.payload;
    if (!sourceId) return undefined;

    const res = yield call(PromotionService.promotionSource.data.detail, {
      sourceId,
    });

    const shopifyInfo = safeParse(res?.data?.[0], undefined);
    if (res?.code === 200 && res?.data && shopifyInfo) {
      yield put(
        getDetailDone(`${PREFIX}@@PROMOTION_INTEGRATION`, {
          data: shopifyInfo,
        }),
      );

      // Get Shopify Discount if integration is shopify
      if (shopifyInfo.connector_code === 'shopify') {
        yield put(
          getList(`${PREFIX}@@SHOPIFY_DISCOUNT@@DISCOUNT_OPTIONS`, {
            shopifyInfo,
            defaultValue: activeRow?.settings?.discount,
          }),
        );
      }
    }
    return shopifyInfo;
  } catch (error) {
    console.log('error', error);
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Promotion/Detail/Settings/saga.js',
      func: 'handleGetDetailPromotionIntegration',
      data: error.stack,
    });
    return undefined;
  }
}

/* ------------------------- HANDLE GET LIST DISCOUNT SHOPIFY ------------------------ */
function* handleGetListDiscountShopify(action) {
  try {
    const { shopifyInfo, defaultValue } = action.payload || {};

    // Call api
    const res = yield call(
      PromotionService.promotionSource.data.shopifyListPriceRule,
      {
        sourceId: shopifyInfo.source_id,
      },
    );

    const priceRules = safeParse(res?.data?.price_rules, []);
    const data = priceRules?.map(item => ({
      label: item.title,
      value: item.id,
    }));

    yield put(
      getListDone(`${PREFIX}@@SHOPIFY_DISCOUNT@@DISCOUNT_OPTIONS`, {
        data,
      }),
    );

    const valueSelected = (data || []).find(
      item => item.value === defaultValue,
    );
    if (valueSelected) {
      yield put(
        updateValue(`${PREFIX}@@SHOPIFY_DISCOUNT@@DATA_CONFIG@@`, {
          name: 'discount',
          value: valueSelected,
          notUpdateCheckOnChange: true,
        }),
      );
    } else {
      yield put(
        updateValue(`${PREFIX}@@SHOPIFY_DISCOUNT@@DATA_CONFIG@@`, {
          name: 'discount',
          value: {
            value: defaultValue,
            label: translate(translations.FIELD_REMOVE_BY_3RD_LABEL, 'Unknown'),
          },
          notUpdateCheckOnChange: true,
        }),
      );
    }
  } catch (error) {
    console.log('error', error);
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Promotion/Detail/Settings/saga.js',
      func: 'handleGetListDiscountShopify',
      data: error.stack,
    });
  }
}

/* ---------------------------------- NOTI ---------------------------------- */
const NOTI = {
  systemError: {
    fail: res => ({
      id: 'system-error',
      ...getErrorMessageV2Translate(res.codeMessage),
      timeout: 4000,
      timestamp: 1554173349265,
      type: 'danger',
    }),
    success: {},
  },
  success: {
    id: 'success',
    message: 'Updates saved!',
    translateCode: TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
    timeout: 1500,
    timestamp: new Date().getTime(),
    type: 'success',
  },
};
