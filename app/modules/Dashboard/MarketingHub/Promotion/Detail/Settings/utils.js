import { getDefaultVal } from '../../../../../../components/common/InputLanguage/utils';
import { getValueListEvery } from '../../Create/utils';
import { METHOD_OPTIONS_MAP } from '../../Create/_UI/FieldShopifyDiscount/constant';

/* -------------------------- MAP DATA BODY TO API -------------------------- */
export const mapDataToAPI = ({
  expiration,
  settingPool,
  setUpAlert,
  settingRectriction,
  settingSource,
  type,
  status = false,
  reducerAccessInfo,
  reducerCodeStructure,
  reducerShopifyDiscount,
  promotionIntegration,
}) => {
  const { settingDateTime, optionValue } = expiration;
  const { poolName, poolCode, description } = settingPool;
  const {
    alertOnExpireDay,
    dayThresholdNumber,
    alertOnCodeNumber,
    codeThresholdNumber,
  } = setUpAlert.dataConfig;
  const alertAccountIds = setUpAlert.data;
  const optionValueRectriction = settingRectriction.optionValue;
  const allocationTypes = getValueListEvery(
    settingRectriction.selectedEvery.list,
  );
  const endTimeTmp =
    typeof settingDateTime.value === 'number'
      ? settingDateTime.value
      : settingDateTime.value.getTime();

  let dataTmp;
  // if (type === '2') {
  //   const { inputs } = settingSource;
  //   dataTmp = {
  //     pool_code: poolCode.value,
  //     pool_name: getDefaultVal(poolName.value),
  //     expiration: {
  //       type: optionValue,
  //       endTime: optionValue !== 'none' ? endTimeTmp : null,
  //     },
  //     description: getDefaultVal(description.value),
  //     description_multilang: description.value || {},
  //     pool_name_multilang: poolName.value,
  //     alertSetting: {
  //       alertOnExpireDay: alertOnExpireDay.value,
  //       dayThresholdNumber: parseInt(dayThresholdNumber.value),
  //       alertOnCodeNumber: alertOnCodeNumber.value,
  //       codeThresholdNumber: parseInt(codeThresholdNumber.value),
  //       alertAccountIds,
  //     },
  //     restrictionType: optionValueRectriction === 'unique' ? 1 : 2,
  //     allocationTypes:
  //       optionValueRectriction === 'unique' ? allocationTypes : null,
  //     type: 2,
  //     sourceId: settingSource.sourceId,
  //     settings: {
  //       inputs: {
  //         ...inputs,
  //         quantity: settingSource.codeQuantity,
  //       },
  //       optionInterval: settingSource.optionInterval,
  //     },
  //     isRun: status,
  //     // importType: importType.value.value,
  //   };
  //   if (settingSource.optionInterval !== 'manually') {
  //     dataTmp.settings.repeatType = settingSource.repeatBy.type;
  //     dataTmp.settings.repeatValue = settingSource.repeatBy.value;
  //     dataTmp.settings.repeatStartTime = {
  //       value: settingSource.settingDateTime.value.toISOString(),
  //       hour: Number(settingSource.settingDateTime.value.getHours()),
  //       minute: Number(settingSource.settingDateTime.value.getMinutes()),
  //       day: format(settingSource.settingDateTime.value, 'dd/MM/yyyy'),
  //     };
  //     const portalTimezone = getPortalTimeZone();

  //     dataTmp.settings.startTime = momentTimeZone(
  //       new PortalDate(settingSource.settingDateTime.value),
  //     )
  //       .tz(portalTimezone)
  //       .format();
  //     dataTmp.settings.repeatOnValue = settingSource.optionWeek.optionsSelected;
  //     dataTmp.settings.andRemain = {
  //       ...settingSource.andRemain,
  //     };
  //   }
  // } else {
  //   dataTmp = {
  //     pool_code: poolCode.value,
  //     pool_name: getDefaultVal(poolName.value),
  //     expiration: {
  //       type: optionValue,
  //       endTime: optionValue !== 'none' ? endTimeTmp : null,
  //     },
  //     description: getDefaultVal(description.value),
  //     description_multilang: description.value || {},
  //     pool_name_multilang: poolName.value,
  //     alertSetting: {
  //       alertOnExpireDay: alertOnExpireDay.value,
  //       dayThresholdNumber: parseInt(dayThresholdNumber.value),
  //       alertOnCodeNumber: alertOnCodeNumber.value,
  //       codeThresholdNumber: parseInt(codeThresholdNumber.value),
  //       alertAccountIds,
  //     },
  //     restrictionType: optionValueRectriction === 'unique' ? 1 : 2,
  //     allocationTypes:
  //       optionValueRectriction === 'unique' ? allocationTypes : null,
  //     isRun: status,
  //   };
  // }

  switch (type) {
    case '2': {
      const useShopify = promotionIntegration?.connector_code === 'shopify';

      const { inputs } = settingSource;

      if (useShopify) {
        dataTmp = {
          pool_code: poolCode.value,
          pool_name: getDefaultVal(poolName.value),
          expiration: {
            type: optionValue,
            endTime: optionValue !== 'none' ? endTimeTmp : null,
          },
          description: getDefaultVal(description.value),
          description_multilang: description.value || {},
          pool_name_multilang: poolName.value,
          alertSetting: {
            alertOnExpireDay: alertOnExpireDay.value,
            dayThresholdNumber: parseInt(dayThresholdNumber.value),
            alertOnCodeNumber: alertOnCodeNumber.value,
            codeThresholdNumber: parseInt(codeThresholdNumber.value),
            alertAccountIds,
          },
          restrictionType: optionValueRectriction === 'unique' ? 1 : 2,
          allocationTypes:
            optionValueRectriction === 'unique' ? allocationTypes : null,
          type: 2,
          sourceId: reducerShopifyDiscount?.sourceId,
          settings: {
            codeDestination:
              reducerShopifyDiscount?.dataConfig?.codeDestination?.value,
            method: reducerShopifyDiscount?.dataConfig?.method?.value,
            discount:
              reducerShopifyDiscount?.dataConfig?.discount?.value?.value,
          },
          isRun: status,
          // importType: importType.value.value,
        };

        // Method Rule-based
        if (
          reducerShopifyDiscount?.dataConfig?.method?.value ===
            METHOD_OPTIONS_MAP.RULE_BASED_GENERATOR.value &&
          reducerCodeStructure
        ) {
          dataTmp.generateCodeSettings = {
            prefix: reducerCodeStructure.dataConfig.prefix,
            suffix: reducerCodeStructure.dataConfig.suffix,
            infix_type: reducerCodeStructure.dataConfig.characterType, // 'numbers', 'letters', or 'both'
            separator: reducerCodeStructure.dataConfig.separator, // 'hyphen', 'under_score', 'dot'
            quantity: reducerCodeStructure.dataConfig.quantity,
            total_predicted: reducerCodeStructure.dataConfig.totalPredicted,

            ...(reducerCodeStructure.dataConfig.characterType === 'both' && {
              num_digits: reducerCodeStructure.dataConfig?.digitalNumbers,
              num_letters: reducerCodeStructure.dataConfig?.alphabetLetters,
              letter_case: reducerCodeStructure.dataConfig?.capitalization,
              order: reducerCodeStructure.dataConfig?.characterOrder,
            }),
            ...(reducerCodeStructure.dataConfig.characterType === 'numbers' && {
              num_digits: reducerCodeStructure.dataConfig?.digitalNumbers,
            }),
            ...(reducerCodeStructure.dataConfig.characterType === 'letters' && {
              num_letters: reducerCodeStructure.dataConfig?.alphabetLetters,
              letter_case: reducerCodeStructure.dataConfig?.capitalization,
            }),
            ...(reducerCodeStructure.activeRow.generate_code_settings
              ?.total_generated && {
              total_generated:
                reducerCodeStructure.activeRow.generate_code_settings
                  .total_generated,
            }),
            ...(reducerCodeStructure.dataConfig?.refillInterval && {
              interval:
                reducerCodeStructure.dataConfig.refillInterval === 'manual'
                  ? {
                      type: 'manual',
                    }
                  : {
                      type: 'auto',
                      conditions: {
                        unit: reducerCodeStructure.dataConfig.refillUnit,
                        num:
                          reducerCodeStructure.dataConfig.refillThreshold || 0,
                      },
                    },
            }),
          };
        }
      } else {
        dataTmp = {
          pool_code: poolCode.value,
          pool_name: getDefaultVal(poolName.value),
          expiration: {
            type: optionValue,
            endTime: optionValue !== 'none' ? endTimeTmp : null,
          },
          description: getDefaultVal(description.value),
          description_multilang: description.value || {},
          pool_name_multilang: poolName.value,
          alertSetting: {
            alertOnExpireDay: alertOnExpireDay.value,
            dayThresholdNumber: parseInt(dayThresholdNumber.value),
            alertOnCodeNumber: alertOnCodeNumber.value,
            codeThresholdNumber: parseInt(codeThresholdNumber.value),
            alertAccountIds,
          },
          restrictionType: optionValueRectriction === 'unique' ? 1 : 2,
          allocationTypes:
            optionValueRectriction === 'unique' ? allocationTypes : null,
          type: 2,
          sourceId: settingSource.sourceId,
          settings: {
            inputs: {
              ...inputs,
              quantity: settingSource.codeQuantity,
            },
            optionInterval: settingSource.optionInterval,
          },
          isRun: status,
          // importType: importType.value.value,
        };
      }

      break;
    }
    case '3':
      dataTmp = {
        pool_code: poolCode.value,
        pool_name: getDefaultVal(poolName.value),
        description: getDefaultVal(description.value),
        description_multilang: description.value,
        pool_name_multilang: poolName.value,
        type: 3,
        alertSetting: {
          alertOnExpireDay: alertOnExpireDay.value,
          dayThresholdNumber: parseInt(dayThresholdNumber.value),
          alertOnCodeNumber: alertOnCodeNumber.value,
          codeThresholdNumber: parseInt(codeThresholdNumber.value),
          alertAccountIds,
        },
        expiration: {
          type: optionValue,
          endTime:
            optionValue !== 'none' ? settingDateTime.value.getTime() : null,
        },
        allocationTypes:
          optionValueRectriction === 'unique' ? allocationTypes : null,
        restrictionType: optionValueRectriction === 'unique' ? 1 : 2,
        isRun: status,
      };

      if (reducerCodeStructure) {
        dataTmp.generateCodeSettings = {
          prefix: reducerCodeStructure.dataConfig.prefix,
          suffix: reducerCodeStructure.dataConfig.suffix,
          infix_type: reducerCodeStructure.dataConfig.characterType, // 'numbers', 'letters', or 'both'
          separator: reducerCodeStructure.dataConfig.separator, // 'hyphen', 'under_score', 'dot'
          quantity: reducerCodeStructure.dataConfig.quantity,
          total_predicted: reducerCodeStructure.dataConfig.totalPredicted,

          ...(reducerCodeStructure.dataConfig.characterType === 'both' && {
            num_digits: reducerCodeStructure.dataConfig?.digitalNumbers,
            num_letters: reducerCodeStructure.dataConfig?.alphabetLetters,
            letter_case: reducerCodeStructure.dataConfig?.capitalization,
            order: reducerCodeStructure.dataConfig?.characterOrder,
          }),
          ...(reducerCodeStructure.dataConfig.characterType === 'numbers' && {
            num_digits: reducerCodeStructure.dataConfig?.digitalNumbers,
          }),
          ...(reducerCodeStructure.dataConfig.characterType === 'letters' && {
            num_letters: reducerCodeStructure.dataConfig?.alphabetLetters,
            letter_case: reducerCodeStructure.dataConfig?.capitalization,
          }),
          ...(reducerCodeStructure.activeRow.generate_code_settings
            ?.total_generated && {
            total_generated:
              reducerCodeStructure.activeRow.generate_code_settings
                .total_generated,
          }),
          ...(reducerCodeStructure.dataConfig?.refillInterval && {
            interval:
              reducerCodeStructure.dataConfig.refillInterval === 'manual'
                ? {
                    type: 'manual',
                  }
                : {
                    type: 'auto',
                    conditions: {
                      unit: reducerCodeStructure.dataConfig.refillUnit,
                      num: reducerCodeStructure.dataConfig.refillThreshold || 0,
                    },
                  },
          }),
        };
      }

      break;
    default:
      dataTmp = {
        pool_code: poolCode.value,
        pool_name: getDefaultVal(poolName.value),
        expiration: {
          type: optionValue,
          endTime: optionValue !== 'none' ? endTimeTmp : null,
        },
        description: getDefaultVal(description.value),
        description_multilang: description.value || {},
        pool_name_multilang: poolName.value,
        alertSetting: {
          alertOnExpireDay: alertOnExpireDay.value,
          dayThresholdNumber: parseInt(dayThresholdNumber.value),
          alertOnCodeNumber: alertOnCodeNumber.value,
          codeThresholdNumber: parseInt(codeThresholdNumber.value),
          alertAccountIds,
        },
        restrictionType: optionValueRectriction === 'unique' ? 1 : 2,
        allocationTypes:
          optionValueRectriction === 'unique' ? allocationTypes : null,
        isRun: status,
      };
      break;
  }

  // Reducer Access Info
  const { listAccess, isPublic, publicRole } =
    reducerAccessInfo?.dataConfig || {};
  const mappingListAccess = (listAccess || []).map(access => ({
    user_id: +access.userId,
    email: access.email,
    avatar: access.avatar,
    role: +access.role,
    allow_view: +access.allowView,
    allow_edit: +access.allowEdit,
    allow_comment: +access.allowComment,
  }));
  dataTmp.shareAccess = {
    is_public: +isPublic,
    public_role: +publicRole,
    list_access: mappingListAccess,
  };

  return dataTmp;
};
