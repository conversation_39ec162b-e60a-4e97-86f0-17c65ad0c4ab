/* eslint-disable indent */

// Libraries
import React, { useCallback, useEffect } from 'react';
import { withRouter } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';
import { compose } from 'redux';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { Box } from '@material-ui/core';
import { Button, EditableName, Scrollbars } from '@antscorp/antsomi-ui';
import { isEmpty } from 'lodash';

// Redux
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import reducer from './reducer';
import saga from './saga';

// Config
import { MODULE_CONFIG } from './config';
import { MODULE_CONFIG as MODULE_CONFIG_PARENT, TABS_CONFIG } from '../config';

// Selectors
import {
  makeSelectPromotionSettingExpiration,
  makeSelectPromotionSettingGeneralPool,
  makeSelectPromotionSettingMain,
  makeSelectPromotionSettingAlert,
  makeSelectPromotionSettingRectriction,
  makeSelectPromotionSettingSource,
  makeSelectPromotionAccessInfo,
  makeSelectPromotionCodeStructure,
  makeSelectPromotionShopifyDiscount,
} from './selectors';
import { makeSelectCheckPermissionPool } from '../selectors';

// Utils
import { init, update, updateValue } from '../../../../../../redux/actions';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import { mapDataToAPI } from './utils';
import { isModifiedRules } from '../../../../../../containers/Segment/util';
import { getDefaultVal } from '../../../../../../components/common/InputLanguage/utils';

// Constants
import TRANSLATE_KEY from '../../../../../../messages/constant';

// Hokks
import useToggle from '../../../../../../hooks/useToggle';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import UILinearProgress from 'components/common/UILinearProgress';
import ModalPullCode from '../../../../../../containers/modals/ModalPullCode';

import HeaderDrawer from '../../../../../../components/common/HeaderDrawer';
import WrapperDisable from '../../../../../../components/common/WrapperDisable';
import BulkUpload from './_UI/BulkUpload';
import RuleBaseGeneration from './_UI/RuleBaseGeneration';
import ShopifyIntegration from './_UI/ShopifyIntegration';
import PromotionIntegration from './_UI/PromotionIntegration';
import Loading from 'components/common/LoadingLoadable';

const MAP_TITLE = {
  title: getTranslateMessage(TRANSLATE_KEY._TAB_OBJECT_SETTING, 'Settings'),
  save: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
  cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
};

function SettingPool(props) {
  const [isOpenModalPool, toggleModalPool] = useToggle(false);
  // Thông tin 2 reducer con và 1 reducer mẹ
  const {
    settingExpiration,
    settingGeneralPool,
    settingRestriction,
    settingAlert,
    accessInfo,
    settingCodeStructure,
    settingShopifyDiscount,
    main,
    onInit,
    onSave,
    activeRow,
    // onCancel,
    activeTab,
    onUpdateDisableSave,
    settingSource,
    isViewMode,
    errors,
    onAfterSave,
    permission,
  } = props;
  const {
    isInitDone,
    isLoading,
    isDisableSave,
    isLoadingSaveName,
    promotionIntegration,
  } = main;
  const { isEdit = true } = permission;
  const { dataConfig } = settingGeneralPool || {};

  useEffect(() => {
    if (
      activeTab === TABS_CONFIG.SETTINGS.key ||
      activeTab === TABS_CONFIG.SHARE_ACCESS.key
    ) {
      onInit({ activeRow });
    }
    // return () => {
    //   onReset();
    // };
  }, []);

  // Sử dụng validate của 2 reducer còn để  check disable
  // -> Nếu cả 2 đều isValidate(xác thực) = true thì mở button save
  useEffect(() => {
    // let isDisTmp = true;
    if (
      settingExpiration.isValidate &&
      settingGeneralPool.isValidate &&
      settingAlert.isValidate &&
      settingRestriction.isValidate &&
      (() => {
        if (Number(activeRow.type) === 3) {
          return settingCodeStructure.isValidate;
        }
        if (promotionIntegration) {
          if (promotionIntegration?.connector_code === 'shopify') {
            return settingShopifyDiscount.isValidate;
          }
          if (
            Number(activeRow.type) === 2 &&
            promotionIntegration?.connector_code !== 'shopify'
          ) {
            return settingSource.isValidate;
          }
        }
        return true;
      })()
    ) {
      onUpdateDisableSave(false);
    } else {
      onUpdateDisableSave(true);
    }

    // ACTION UPDATE DISABLE REDUCER
  }, [
    settingRestriction.isCheckOnChange,
    settingExpiration.isCheckOnChange,
    settingGeneralPool.isCheckOnChange,
    settingRestriction.isCheckOnChange,
    settingSource.isCheckOnChange,
    settingCodeStructure.isCheckOnChange,
    accessInfo.isCheckOnChange,
    settingGeneralPool.optionValue,
    settingAlert.isValidate,
    settingAlert.isDoing,
    settingShopifyDiscount.isCheckOnChange,
  ]);

  const handleAfterSave = () => {
    if (typeof onAfterSave === 'function') {
      onAfterSave();
    }
  };

  // Dispatch actiono Save Data
  const handleSaveData = () => {
    if (isChange && !(promotionIntegration?.connector_code === 'shopify')) {
      toggleModalPool();
    } else {
      onSave({
        type: activeRow.type,
        data: { status: false },
        promotionIntegration,
        callback: handleAfterSave,
      });
    }
  };
  const dataMap = mapDataToAPI({
    expiration: settingExpiration,
    settingPool: settingGeneralPool.dataConfig,
    setUpAlert: settingAlert,
    settingRectriction: settingRestriction,
    settingSource,
    type: activeRow.type,
    status: false,
    reducerAccessInfo: accessInfo,
  });
  const excludeCheckedModified = [
    'pool_name',
    'process_status',
    'pool_code',
    'isRun',
    'pool_name_multilang',
    'description_multilang',
    'description',
    'sourceId',
  ];
  const isChange = isModifiedRules(activeRow, dataMap, {
    excludeProperty: excludeCheckedModified,
  });
  // Dispatch action change tab
  // const handleCancel = () => {
  //   onCancel(TABS_CONFIG.PROMOTION_CODES.key);
  // };
  const callback = (type, data) => {
    switch (type) {
      case 'ACTION_POOL_CODES': {
        onSave({ type: activeRow.type, data, promotionIntegration });
        break;
      }

      case 'AFTER_SAVE_NAME': {
        if (typeof props.onAfterSaveName === 'function') {
          props.onAfterSaveName(data);
        }
        break;
      }

      default:
        break;
    }
  };

  const onChangeData = useCallback(
    name => value => {
      if (value !== dataConfig[name].value) {
        props.onChangeDataConfig({ name, value });
      }
    },
    [dataConfig],
  );

  const handleSaveName = () => {
    if (!dataConfig.poolName.isChanged) return;
    props.onSaveName({ callback });
  };

  const renderContent = () => {
    if (activeRow?.type == null) return null;

    if (Number(activeRow.type) === 1)
      return (
        <BulkUpload
          moduleConfig={MODULE_CONFIG}
          sourceId={activeRow.source_id}
          isViewMode={isViewMode}
          errors={errors}
        />
      );
    if (Number(activeRow.type) === 3)
      return (
        <RuleBaseGeneration
          moduleConfig={MODULE_CONFIG}
          sourceId={activeRow.source_id}
          isViewMode={isViewMode}
          errors={errors}
        />
      );
    if (Number(activeRow.type) === 2) {
      if (isEmpty(promotionIntegration)) return <Loading />;
      return promotionIntegration.connector_code === 'shopify' ? (
        <ShopifyIntegration
          moduleConfig={MODULE_CONFIG}
          sourceId={activeRow.source_id}
          isViewMode={isViewMode}
          errors={errors}
          activeRow={activeRow}
        />
      ) : (
        <PromotionIntegration
          moduleConfig={MODULE_CONFIG}
          sourceId={activeRow.source_id}
          isViewMode={isViewMode}
          errors={errors}
        />
      );
    }

    return null;
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Promotion/Detail/Settings/index.jsx">
      <Box position="relative">
        {!isViewMode && (
          <HeaderDrawer
            style={{ height: 50 }}
            extraContent={
              <Button
                type="primary"
                style={{ height: 30, width: 'max-content' }}
                onClick={handleSaveData}
                disabled={isDisableSave || !isEdit}
              >
                {MAP_TITLE.save}
              </Button>
            }
          >
            <EditableName
              isLoading={isLoadingSaveName}
              value={
                isInitDone
                  ? getDefaultVal(dataConfig.poolName.value) || ''
                  : activeRow?.pool_name || ''
              }
              error={dataConfig.poolName?.errors?.[0]}
              onChange={newValue => {
                if (activeRow.pool_name !== newValue) {
                  onChangeData('poolName')({
                    DEFAULT_LANG: 'EN',
                    EN: newValue,
                    VI: newValue,
                    JA: newValue,
                  });
                }
              }}
              onBlur={handleSaveName}
              readonly={!isEdit}
              required
            />
          </HeaderDrawer>
        )}
        <WrapperDisable disabled={!isEdit}>
          <Scrollbars
            style={{
              width: '100%',
              height: 'calc(100vh - 50px)',
            }}
          >
            {renderContent()}
          </Scrollbars>
        </WrapperDisable>
      </Box>
      <UILinearProgress isShow={isLoading} />
      <ModalPullCode
        callback={callback}
        isOpen={isOpenModalPool}
        toggle={toggleModalPool}
      />
    </ErrorBoundary>
  );
}

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

const mapStateToProps = createStructuredSelector({
  main: makeSelectPromotionSettingMain(),
  settingExpiration: makeSelectPromotionSettingExpiration(),
  settingGeneralPool: makeSelectPromotionSettingGeneralPool(),
  settingAlert: makeSelectPromotionSettingAlert(),
  settingRestriction: makeSelectPromotionSettingRectriction(),
  settingSource: makeSelectPromotionSettingSource(),
  settingCodeStructure: makeSelectPromotionCodeStructure(),
  settingShopifyDiscount: makeSelectPromotionShopifyDiscount(),
  accessInfo: makeSelectPromotionAccessInfo(),
  permission: makeSelectCheckPermissionPool(),
});

function mapDispatchToProps(dispatch) {
  return {
    onInit: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    // onReset: params => {
    //   dispatch(reset(MODULE_CONFIG.key, params));
    //   dispatch(reset(`${MODULE_CONFIG.key}@@EXPERITION`, params));
    //   dispatch(reset(`${MODULE_CONFIG.key}@@SETTING_POOL`, params));
    //   dispatch(reset(`${MODULE_CONFIG.key}@@PROMOTION_SHARE_ACCESS`, params));
    // },
    onSave: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@ON_SAVE`, params));
    },
    onCancel: params => {
      dispatch(updateValue(`${MODULE_CONFIG_PARENT.key}@@CHANGE_TAB`, params));
    },
    onUpdateDisableSave: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DISABLE_SAVE`, params));
    },
    onChangeDataConfig: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@SETTING_POOL@@DATA_CONFIG@@`, value),
      ),
    onSaveName: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@SAVE_NAME`, params));
    },
  };
}
SettingPool.propTypes = {
  main: PropTypes.object,
  settingExpiration: PropTypes.object,
  settingGeneralPool: PropTypes.object,
  settingAlert: PropTypes.object,
  settingRestriction: PropTypes.object,
  settingSource: PropTypes.object,
  accessInfo: PropTypes.object,
  settingCodeStructure: PropTypes.object,
  settingShopifyDiscount: PropTypes.object,
  activeRow: PropTypes.object,
  onAfterSave: PropTypes.func,
  onAfterSaveName: PropTypes.func,

  onInit: PropTypes.func.isRequired,
  // onReset: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  // onCancel: PropTypes.func,
  onUpdateDisableSave: PropTypes.func,
  activeTab: PropTypes.string,
  isViewMode: PropTypes.bool,
  errors: PropTypes.array,
  onChangeDataConfig: PropTypes.func,
  onSaveName: PropTypes.func,

  permission: PropTypes.object,
};

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);
export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(SettingPool);
