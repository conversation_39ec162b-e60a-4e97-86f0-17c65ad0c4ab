/* eslint-disable no-unused-vars */
import {
  all,
  call,
  delay,
  put,
  select,
  takeLatest,
  race,
  take,
} from 'redux-saga/effects';
// import { push } from 'react-router-redux';
// import { delay } from 'redux-saga';
// import delay from '@redux-saga/delay-p';
import { OrderedMap } from 'immutable';
import PromotionServices from '../../../../../../services/Promotion';
import { encodeURL, safeParse } from 'utils/common';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import ReduxTypes from '../../../../../../redux/constants';
import BusinessObject from 'services/BusinessObject';
import { MODULE_CONFIG } from './config';
import selectListPromotionDomain, {
  makeSelectListPromotionColumn,
  makeSelectListPromotionFilter,
  makeSelectListPromotionDomainMain,
  makeSelectListPromotionTable,
  makeSelectListPromotionAttributes,
  makeSelectListPromotionDomainMainData,
  makeSelectListPromotionDomainMainModuleConfig,
  makeSelectModuleConfigColumn,
  makeSelectPromotionSettingGeneralPool,
} from './selectors';

import { buildTableGroupColumns } from './utils';
import {
  getListDone,
  updateValue,
  onTablePaging,
  updateDone,
  addNotification,
  initDone,
  getList,
  getDetailDone,
} from '../../../../../../redux/actions';
import {
  toConditionAPI,
  toConditionUI,
} from '../../../../../../containers/Filters/utils';
import { makeSelectPortal } from '../../../../selector';
import APP from '../../../../../../appConfig';
import { getPortalId } from '../../../../../../utils/web/cookie';
import {
  commonHandleGetLastFilter,
  commonHandleFilterSearchAddItemCallback,
} from '../../../../../../containers/Filters/saga';
import { initRules } from '../../../../../../containers/Filters/action';
import {
  OPERATORS_OPTION,
  OPERATORS,
} from '../../../../../../containers/Filters/_UI/operators';
import {
  commonHandleGetColumns,
  // commonHandleGetListColumns,
} from '../../../../../../containers/ModifyColumn/saga';
import { getLocationOrigin } from '../../../../../../utils/web/utils';
import { addMessageToQueue } from '../../../../../../utils/web/queue';
import { getErrorMessageV2Translate } from '../../../../../../utils/web/message';
import { getDefaultVal } from '../../../../../../components/common/InputLanguage/utils';
import { MAP_VALUE_ERROR } from '../../Create/utils';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import { makeSelectIsEditPool } from '../selectors';
import { PROMOTION_QUERY_KEY } from '../../config';
import { push } from 'react-router-redux';
import { getItemAttributeDecryptFields } from '../../../../../../utils/web/attribute';

const PATH = 'app/modules/Dashboard/MarketingHub/Promotion/Detail/List/saga.js';

const PREFIX = MODULE_CONFIG.key;
const getReducer = state => state.get(PREFIX);

export default function* workerCustomerSaga(args) {
  yield takeLatest(`${PREFIX}${ReduxTypes.INIT}`, handleInit);
  yield takeLatest(`${PREFIX}@@SAVE_NAME${ReduxTypes.UPDATE}`, handleSaveName);
  yield takeLatest(`${PREFIX}${ReduxTypes.GET_LIST}`, handleGetListData);

  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_TABLE_COLUMN${ReduxTypes.UPDATE_VALUE}`,
    handleBuildTableColumn,
  );

  /** FILTER HANDLE */
  yield takeLatest(
    `${PREFIX}@@COMMON_RESET_PAGING${ReduxTypes.UPDATE_VALUE}`,
    handleResetPaging,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_FILTER_SEARCH_GOTO${ReduxTypes.UPDATE_VALUE}`,
    handleFilterSearchGoto,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_SEARCH_ADD_ITEM${ReduxTypes.UPDATE_VALUE}`,
    handleFilterSearchAddItem,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_FILTER_RULES${ReduxTypes.UPDATE_VALUE}`,
    buildFilterRules,
  );
  yield takeLatest(
    `${PREFIX}@@COMMON_CHANGE_RULES${ReduxTypes.UPDATE_VALUE}`,
    handelChangeRuleFilter,
  );
  /** END FILTER HANDLE */

  yield takeLatest(
    `${PREFIX}@@COMMON_BUILD_FILTER_RULES_WITH_LOOKUP${ReduxTypes.UPDATE_VALUE
    }`,
    buildFilterRulesWithLookupInfo,
  );
  yield takeLatest(`${PREFIX}@@DELETE_ROW${ReduxTypes.UPDATE}`, handleDelete);
}

export function* handleInit(action) {
  try {
    const { activeRow } = action.payload;

    yield put(initDone(PREFIX));
    yield put(getDetailDone(`${MODULE_CONFIG.key}@@SETTING_POOL`, activeRow));
    yield call(handleInitTable, action);
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'handleInit',
      data: error.stack,
    });
    console.warn(error);
  }
}

export function* handleInitTable(action, args) {
  // console.log('handleInitTable', action, itemTypeId);
  // get data init cho table
  const main = yield select(makeSelectListPromotionDomainMain());
  const { moduleConfig, moduleConfigColumn } = main;

  yield call(handleGetListAttributes);
  const groupAttributes = yield select(makeSelectListPromotionAttributes());
  moduleConfigColumn.columnsDefault = groupAttributes.requires;
  moduleConfigColumn.tableColumnDefault = groupAttributes.defaults;

  // filter khác nhau giữa các itemTypeId, còn modify column thì giống nhau

  yield all([
    call(commonHandleGetColumns, PREFIX, moduleConfigColumn),
    call(
      commonHandleGetLastFilter,
      PREFIX,
      moduleConfig,
      buildFilterRulesWithLookupInfo,
    ),
  ]);
  yield call(handleGetListData, action);
  yield race([
    // call(intervalHandleGetData),
    take(`${PREFIX}${ReduxTypes.RESET}`),
  ]);
}

export function* handleGetListData() {
  try {
    const reducer = yield select(makeSelectListPromotionDomainMain());
    const reducerFilter = yield select(makeSelectListPromotionFilter());
    const reducerColumm = yield select(makeSelectListPromotionColumn());
    const reducerTable = yield select(makeSelectListPromotionTable());
    const {
      groupAttributes: { map },
      searchQuery,
      poolId,
    } = reducer;
    const { columnObj } = reducerColumm;
    const { rules } = reducerFilter;
    const { paging, sort } = reducerTable;
    const { key, by } = sort;

    const columnsAlias = [...columnObj.columns.columnsAlias];
    const columns = [];
    const perfColumns = [];
    columnsAlias.forEach(each => {
      columns.push(each);
    });
    [
      'id',
      'code_status',
      'story_id',
      'audience_type',
      'allocated_audience',
      'allocated_time',
      'last_used_time',
      'last_used_source',
      'date_created',
      'last_updated',
    ].forEach(each => {
      if (!columns.includes(each)) {
        columns.push(each);
      }
    });

    const apiRules = toConditionAPI(rules);

    const params = {
      poolId,
      data: {
        // poolId: parseInt(poolId),
        filters: apiRules,
        properties: columns,
        // perf_columns: perfColumns,
        limit: paging.limit,
        page: paging.page,
        sort: safeParse(key, 'last_updated'),
        sd: safeParse(by, 'desc'),
      },
    };
    // console.log('params', params);
    const response = yield call(
      PromotionServices.promotionCode.getList,
      params,
    );
    if (response !== null) {
      const data = {
        totalRecord: response.totalRecord,
      };

      const portal = yield select(state => makeSelectPortal(state));
      const isEdit = yield select(makeSelectIsEditPool());
      yield put(
        getListDone(PREFIX, {
          data: response.data,
          portalId: portal.portalId,
          isEdit,
        }),
      );
      yield put(onTablePaging(PREFIX, { data }));
    }

    // yield put(setTotalList(response));
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleGetListData',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleGetListAttributes() {
  try {
    const responseBO = yield call(
      PromotionServices.promotionCode.getListGroupAttrsPromotionCode,
      { itemTypeId: -100 },
    );
    const response = yield call(
      PromotionServices.promotionCode.getListGroupAttrs,
    ); // modify column
    yield put(
      getListDone(`${PREFIX}@@GROUP_ATTRS`, {
        dataBO: safeParse(responseBO.data, []),
        data: safeParse(response.data, []),
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleGetListAttributes',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleBuildTableColumn(action) {
  try {
    const { columns, isFetch } = action.payload;
    ['id', 'name', 'code_status'].forEach(each => {
      if (!columns.includes(each)) {
        columns.push(each);
      }
    });
    const reducerMain = yield select(makeSelectListPromotionDomainMain());
    const { groupAttributes } = reducerMain;
    const list = [];
    let columnID = {};
    // console.log('groupAttributes.map', groupAttributes.map);
    columns.forEach(col => {
      if (groupAttributes.map[col] !== undefined) {
        if (col === 'id') {
          columnID = groupAttributes.map[col];
        } else {
          list.push(groupAttributes.map[col]);
        }
      }
    });

    // if (Object.keys(columnName).length === 0) {
    //   columnName = groupAttributes.map.id;
    // }
    // if (Object.keys(columnStatus).length === 0) {
    //   columnStatus = groupAttributes.map.code_status;
    // }

    const feColumns = buildTableGroupColumns(columnID, list);
    yield put(updateValue(`${PREFIX}@@COMMON_COLUMN_TABLE`, feColumns));
    if (isFetch) {
      yield call(handleGetListData);
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleBuildTableColumn',
      data: err.stack,
    });
    console.error(err);
  }
}

export function* handleResetPaging() {
  yield put(onTablePaging(PREFIX, { data: { page: 1 } }));
}

export function* handleFilterSearchGoto(action) {
  const data = action.payload;

  try {
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set(PROMOTION_QUERY_KEY.DETAIL_CODE_ID, data.id);
    searchParams.set(PROMOTION_QUERY_KEY.DETAIL_CODE_STATUS, data.status);
    const newSearch = searchParams.toString();

    yield put(
      push({
        search: newSearch,
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleFilterSearchGoto',
      data: err.stack,
    });
    console.error(err);
  }
}

// export function* intervalHandleGetData() {
//   try {
//     while (true) {
//       yield delay(10000);
//       yield call(handleGetDataInterval);
//     }
//   } catch (err) {
//     addMessageToQueue({
//       path:
//         PATH,
//       func: 'intervalHandleGetData',
//       data: err.stack,
//     });
//     console.error(err);
//   }
// }

// export function* handleGetDataInterval() {
//   try {
//     const { main } = yield select(getReducer);
//     const reducer = yield select(makeSelectListCollectionDomainMain());
//     const { dateRange } = reducer;
//     const {
//       data,
//       groupAttributes: { map },
//     } = main;
//     const reducerColumm = yield select(makeSelectListCollectionColumn());
//     const { columnObj } = reducerColumm;
//     // const columns = [...columnObj.columns.columnsAlias];
//     const columnsAlias = [...columnObj.columns.columnsAlias];

//     const perfColumns = [];
//     const columns = [];
//     columnsAlias.forEach(each => {
//       if (map[each].type == '2') {
//         perfColumns.push(each);
//       } else {
//         columns.push(each);
//       }
//     });

//     [
//       'status',
//       'segment_display',
//       'segment_display',
//       'process_status',
//       'segment_size',
//     ].forEach(each => {
//       if (!columns.includes(each)) {
//         columns.push(each);
//       }
//     });

//     if (data.length > 0) {
//       const ids = [];
//       data.forEach(item => {
//         ids.push(item.segment_id);
//       });
//       const params = {
//         itemTypeId: Number(reducer.moduleConfig.objectId),
//         segmentIds: ids,
//         columns,
//       };
//       // console.log('params', params);
//       const { portalId } = yield select(state => makeSelectPortal(state));
//       const res = yield call(PromotionServices.BOCollection.getByIds, params);
//       yield put(
//         updateValue(`${PREFIX}@@UPDATE_DATA_INTERVAL`, {
//           data: res.data,
//           portalId,
//         }),
//       );
//     }
//   } catch (err) {
//     addMessageToQueue({
//       path:
//         PATH,
//       func: 'handleGetListData',
//       data: err.stack,
//     });
//     console.error(err);
//   }
// }

export function* buildFilterRules(action) {
  const { rules } = action.payload;
  const reducer = yield select(makeSelectListPromotionDomainMain());
  const moduleConfig = yield select(
    makeSelectListPromotionDomainMainModuleConfig(),
  );
  const { groupAttributes, mapInfo } = reducer;
  const tmp = toConditionUI(
    rules,
    groupAttributes.map,
    mapInfo.itemAttribute,
    moduleConfig.objectId,
  );
  yield put(initRules(PREFIX, { data: tmp }));
}

export function* buildFilterRulesWithLookupInfo(action) {
  const { rules, isFetch } = action.payload;
  const reducer = yield select(makeSelectListPromotionDomainMain());
  const moduleConfig = yield select(
    makeSelectListPromotionDomainMainModuleConfig(),
  );
  const { groupAttributes, mapInfo } = reducer;
  const tmp = toConditionUI(
    rules,
    groupAttributes.map,
    mapInfo.itemAttribute,
    moduleConfig.objectId,
  );
  yield put(initRules(PREFIX, { data: tmp }));
  if (isFetch) {
    yield call(handleGetListData);
  }
}

export function* handelChangeRuleFilter(action) {
  try {
    const reducerFilter = yield select(makeSelectListPromotionFilter());
    const moduleConfig = yield select(
      makeSelectListPromotionDomainMainModuleConfig(),
    );
    const customFilterId = reducerFilter.config.library.filterCustom.filterId;
    yield put(
      updateValue(`${PREFIX}@@COMMON_FILTER_OBJ`, {
        filterId: customFilterId,
      }),
    );

    const data = {
      type: 'apply',
      filterId: customFilterId,
      rules: toConditionAPI(reducerFilter.rules),
    };
    yield put(
      updateValue(`${PREFIX}@@TO_COMMON_FILTER`, {
        data,
        callbackPrefix: PREFIX,
        moduleConfig,
      }),
    );
  } catch (err) {
    console.error(err);
    addMessageToQueue({
      path: PATH,
      func: 'handelChangeRuleFilter',
      data: err.stack,
    });
  }
}

export function* handleFilterSearchAddItem(action) {
  try {
    const { data } = action.payload;
    const moduleConfig = yield select(
      makeSelectListPromotionDomainMainModuleConfig(),
    );
    const reducerAttribute = yield select(makeSelectListPromotionAttributes());
    const property = reducerAttribute.map.id;
    if (property) {
      const itemFilter = OrderedMap({
        value: data,
        property,
        operator: OPERATORS_OPTION.CONTAINS,
        operators: OPERATORS.string,
        dataType: property.dataType,
      });
      yield call(
        commonHandleFilterSearchAddItemCallback,
        PREFIX,
        itemFilter,
        makeSelectListPromotionFilter,
        moduleConfig,
      );
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleFilterSearchAddItem',
      data: err.stack,
    });
    console.error(err);
  }
}
export function* handleDelete(action) {
  try {
    const reducerFilter = yield select(makeSelectListPromotionFilter());
    const reducerTable = yield select(makeSelectListPromotionTable());
    const reducer = yield select(makeSelectListPromotionDomainMain());

    const { rules } = reducerFilter;
    const { poolId } = reducer;
    const promotionCodes = [];
    reducerTable.selectedRows.forEach(each => {
      promotionCodes.push(each.id);
    });
    const params = {
      data: {
        promotionCodes,
        sizes: promotionCodes.length,
      },
      poolId,
    };
    if (reducerTable.isSelectedAll) {
      params.data.filters = toConditionAPI(rules);
      delete params.data.promotionCodes;
      params.data.scope = 'remove_all';
      params.data.sizes = reducerTable.paging.totalRecord;
    }
    const res = yield call(
      PromotionServices.promotionSource.data.removeCode,
      params,
    );

    if (res.code === 200) {
      yield put(getList(`${PREFIX}`));
      yield call(handleGetListData);
    } else {
      const noti = NOTI.updateStatus.fail(res);
      yield put(addNotification(noti));
    }
    // yield put(updateDone(`${PREFIX}@@CELL_STATUS`, data));
  } catch (err) {
    // addMessageToQueue(messageObject('handleDelete', err));
    console.error(err);
  }
}

/* -------------------------------- SAVE NAME ------------------------------- */
function* handleSaveName(action) {
  const { callback } = action.payload;
  try {
    // Get Data của reducer main
    const reducerMainSetting = yield select(
      makeSelectListPromotionDomainMain(),
    );

    // Get Data của reducer con -> general pool
    const reducerGeneralPool = yield select(
      makeSelectPromotionSettingGeneralPool(),
    );
    const { poolName } = reducerGeneralPool.dataConfig;

    if (
      poolName?.errors?.length ||
      !poolName?.isValidate ||
      !poolName?.isChanged
    ) {
      return yield put(updateDone(`${MODULE_CONFIG.key}@@SAVE_NAME`));
    }

    const data = {
      poolName: getDefaultVal(poolName.value),
    };

    // PARAMS Call API
    const params = {
      poolId: reducerMainSetting.poolId,
      data,
    };

    // Serivice API
    const response = yield call(
      PromotionServices.setting.updateNameById,
      params,
    );

    // Success
    if (response.code === 200) {
      // yield put(updateDone(`${MODULE_CONFIG.key}@@SAVE_NAME`));
      callback('AFTER_SAVE_NAME', getDefaultVal(poolName?.value));
    } else if (response.code === 500) {
      yield call(handleSaveError, {
        dataErros: {
          fieldCode: 'pool_name',
          errorCode: response.codeMessage || '_NOTIFICATION_ERROR',
        },
      });
    } else {
      yield call(handleSaveError, {
        dataErros: {
          fieldCode: 'pool_name',
          errorCode: '_NOTIFICATION_ERROR',
        },
      });
    }
    return yield put(updateDone(`${MODULE_CONFIG.key}@@SAVE_NAME`));
  } catch (error) {
    console.error(error);
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Promotion/Detail/Settings/saga.js',
      func: 'handleSaveName',
      data: error.stack,
    });
    yield call(handleSaveError, {
      dataErros: {
        fieldCode: 'pool_name',
        errorCode: '_NOTIFICATION_ERROR',
      },
    });
    return yield put(updateDone(`${MODULE_CONFIG.key}@@SAVE_NAME`));
  }
}

/* ------------------------- HANDLE AFTER SAVE ERROR ------------------------ */
function* handleSaveError(action) {
  const { fieldCode, errorCode } = action.dataErros;

  // Push errors field
  yield put(
    updateValue(`${MODULE_CONFIG.key}@@SETTING_POOL@@ERROR_API`, {
      name: MAP_VALUE_ERROR[fieldCode] || '',
      errors: [
        getTranslateMessage(
          TRANSLATE_KEY[errorCode],
          'This name has been used, please try another name.',
        ),
      ],
    }),
  );
}

const NOTI = {
  updateStatus: {
    fail: res => ({
      id: 'edit-name-error',
      ...getErrorMessageV2Translate(res.codeMessage),
      timeout: 4000,
      timestamp: 1554173349265,
      type: 'danger',
    }),
    success: {},
  },
};
