/* eslint-disable indent */
/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
/* eslint-disable no-param-reassign */
import React, { useEffect, useState } from 'react';
import { PropTypes } from 'prop-types';
import { connect } from 'react-redux';

import {
  UIModal,
  UIModalHeader,
  UIModalBody,
  UIModalFooter,
} from '@xlab-team/ui-components';
import { safeParse } from 'utils/common';
import { useServices } from 'hooks/useServices';
import PromotionServices from 'services/Promotion';
import UISelect from 'components/form/UISelectCondition';
import styled from 'styled-components';
import ModalFooter from 'components/Molecules/ModalFooter';
import { UIButton as Button } from '@xlab-team/ui-components';
import { UILoading as Loading } from '@xlab-team/ui-components';
import { getTranslateMessage } from 'containers/Translate/util';
import TRANSLATE_KEY from 'messages/constant';
import { addNotification } from 'redux/actions';
import { useImmer } from 'use-immer';
import { mapLibraryFilterForActiveFilter } from '../ControlTable/utils';
import { getErrorMessageV2Translate } from '../../../../../../../utils/web/message';
import { Flex, Spin } from '@antscorp/antsomi-ui';
import { LabelMove, ModalStyled } from './styled';
// import { serializeDataGroup } from '../utils';

// import { MAP_JOURNEY_ACCEPTED_ACTIONS as MAA } from '../../../utils/web/permission';

const MAP_TITLE = {
  actCancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
  actMove: getTranslateMessage(TRANSLATE_KEY._ACT_MOVE, 'Move'),
  actDelete: getTranslateMessage(TRANSLATE_KEY._ACT_REMOVE, 'Remove'),
  boxTitle: x =>
    getTranslateMessage(
      TRANSLATE_KEY._BOX_TITL_MOVE_CODE,
      'Move {x} code(s) to another pool',
      { x },
    ),
  moveToPool: getTranslateMessage(
    TRANSLATE_KEY._TITL_MOVE_TO_POOL,
    `Move to pool`,
  ),
  selectPool: getTranslateMessage(TRANSLATE_KEY._, 'Select pool'),
  selectAPool: getTranslateMessage(
    TRANSLATE_KEY._TITL_SELECT_POOL,
    'Select a pool',
  ),
};

const NOTI = {
  fail: res => ({
    id: 'clone-name-error',
    ...getErrorMessageV2Translate(res.codeMessage),
    timeout: 4000,
    type: 'danger',
  }),
  success: () => ({
    id: 'clone-name-success',
    message: `Save!`,
    translateCode: TRANSLATE_KEY._NOTIFICATION_SUCCESS,
    timeout: 4000,
    type: 'success',
  }),
  serviceNotFound: () => ({
    id: 'object-server-error',
    message: 'Service not found',
    timeout: 4000,
    type: 'danger',
  }),
};

export const ButtonStyle = styled(Button)`
  margin: 0rem 0.25rem;
`;
export const ContainerSelect = styled.div`
  display: flex;
  align-items: center;
  margin-top: 1rem;
`;
export const TitleWarnDelete = styled.p`
  flex: none;
  margin: 0 16px 0 0;
`;
export const WrapperListItem = styled.div`
  display: flex;
  position: relative;
  flex-direction: column;
  height: 2.5rem;
  overflow-y: ${({ overflowY }) => overflowY};
`;

export const ModalFooterStyle = styled(ModalFooter)`
  justify-content: flex-end;
  padding-right: 2rem;
`;

const ContainerUISelect = styled.div`
  width: 100%;
`;

const initState = () => ({
  total: [],
  accepted: [],
  denied: [],
  isDisabledDelete: false,
  isLoading: false,
  isDoing: false,
  titleWarnDelete: '',
  listTitleAccepted: [],
  // value: {},
  selectedValue: {},
  res: [],
  options: [],
  disabled: true,
});

function ModalMoveTo(props) {
  const [state, setState] = useImmer(initState());
  // console.log('state', state)
  const {
    label,
    isOpen,
    poolId,
    toggle,
    fetchData,
    selectedGroup,
    selectedRows,
    callback,
    isSelectedAll,
    activeRow,
  } = props;
  // console.log('props modal', props);

  useEffect(() => {
    if (isOpen) {
      setState(draft => {
        draft.isLoading = true;
      });

      const params = {
        data: {
          objectType: 'PROMOTION_POOL',
          filters: {
            OR: [
              {
                AND: [
                  {
                    // type: 1,
                    column: 'pool_name',
                    data_type: 'string',
                    operator: 'not_matches',
                    value: [props.activeRow.pool_name],
                  },
                ],
              },
            ],
          },
          limit: null,
          page: null,
          search: '',
          isSnakeCase: 1,
        },
      };

      PromotionServices.promotionCode.selectorPool(params).then(res => {
        if (res.code === 200) {
          const options = [];
          res.data.forEach(e => {
            options.push({
              name: e.pool_name,
              label: e.pool_name,
              value: e.pool_id,
            });
          });
          // console.log('res.data', res.data);
          setState(draft => {
            draft.res = res.data;
            draft.options = options;
          });
        } else {
          const notification = NOTI.fail(res);
          props.addNotification(notification);
        }
        setState(draft => {
          draft.isLoading = false;
        });
      });
    }
  }, [isOpen]);

  const onCancel = () => {
    toggle(false);
    setState(draft => initState());
  };

  const onApply = () => {
    setState(draft => {
      draft.isLoading = true;
    });
    const promotionCode = [];
    const selectedRowsValues = [...selectedRows.values()];
    const itemPropertyNames = selectedRowsValues.map(
      item => item.item_property_name,
    );
    props.selectedRows.forEach(each => {
      promotionCode.push(each.id);
    });
    // console.log('promotion_code', promotionCode);

    let params = {};

    const paramsMoveTo = {
      targetPoolId: parseInt(state.selectedValue.value),
      sourcePoolId: parseInt(poolId),
      data: {
        promotionCodes: promotionCode,
      },
    };

    const paramsMoveAll = {
      targetPoolId: parseInt(state.selectedValue.value),
      sourcePoolId: parseInt(poolId),
      data: {
        scope: 'move_all',
        sizes: props.libraryFilters.totalRecord,
        filters: mapLibraryFilterForActiveFilter(
          props.libraryFilters.filterActive.filterId,
          props.libraryFilters.library,
        ),
      },
    };
    // console.log('paramsMoveAll', paramsMoveAll);
    // console.log('params', params);

    if (isSelectedAll) {
      params = paramsMoveAll;
    } else {
      params = paramsMoveTo;
    }

    PromotionServices.promotionCode.moveTo(params).then(res => {
      setState(draft => {
        draft.isLoading = false;
      });

      if (res.code === 200) {
        setState(draft => initState());
        toggle();
        fetchData();
      } else {
        const notification = NOTI.fail(res);
        props.addNotification(notification);
      }
    });
  };

  const onChangeSelect = value =>
    setState(draft => {
      draft.selectedValue = value;
      draft.disabled = false;
    });

  return (
    <ModalStyled
      width={350}
      open={isOpen}
      title={
        isSelectedAll
          ? MAP_TITLE.boxTitle(props.libraryFilters.totalRecord)
          : MAP_TITLE.boxTitle(props.selectedRows.size)
      }
      centered
      okText={MAP_TITLE.actMove}
      onOk={onApply}
      onCancel={onCancel}
    >
      <Spin size="small" spinning={state.isLoading}>
        <Flex vertical gap={5}>
          <LabelMove>{MAP_TITLE.selectPool}</LabelMove>
          <ContainerUISelect>
            <UISelect
              isSearchable
              use="tree"
              options={state.options}
              value={state.selectedValue}
              onChange={onChangeSelect}
              placeholder={MAP_TITLE.selectAPool}
              // className="w-50"
            />
          </ContainerUISelect>
        </Flex>
      </Spin>
    </ModalStyled>
  );

  // return (
  //   <UIModal isOpen={isOpen} toggle={toggle}>
  //     <UIModalHeader style={{ color: '#005fb8' }}>
  //       {isSelectedAll ? (
  //         <div>{MAP_TITLE.boxTitle(props.libraryFilters.totalRecord)}</div>
  //       ) : (
  //         <div>{MAP_TITLE.boxTitle(props.selectedRows.size)}</div>
  //       )}
  //     </UIModalHeader>
  //     <UIModalBody style={{ paddingTop: 0 }}>
  //       <div>
  //         <Loading isLoading={state.isLoading} />
  //         <ContainerSelect>
  //           <TitleWarnDelete>{MAP_TITLE.moveToPool}</TitleWarnDelete>
  //           <ContainerUISelect>
  //             <UISelect
  //               isSearchable={true}
  //               use="tree"
  //               options={state.options}
  //               value={state.selectedValue}
  //               onChange={onChangeSelect}
  //               placeholder={MAP_TITLE.selectAPool}
  //               className="w-50"
  //             />
  //           </ContainerUISelect>
  //         </ContainerSelect>
  //       </div>
  //     </UIModalBody>
  //     <UIModalFooter>
  //       <ButtonStyle theme="outline" onClick={onCancel}>
  //         {MAP_TITLE.actCancel}
  //       </ButtonStyle>
  //       <ButtonStyle
  //         color="secondary"
  //         onClick={onApply}
  //         disabled={state.disabled}
  //       >
  //         {MAP_TITLE.actMove}
  //       </ButtonStyle>
  //     </UIModalFooter>
  //   </UIModal>
  // );
}

function mapDispatchToProps(dispatch) {
  return {
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
  };
}

ModalMoveTo.propTypes = {
  isOpen: PropTypes.bool,
  label: PropTypes.string,
  addNotification: PropTypes.func,
  poolId: PropTypes.string,
  toggle: PropTypes.func,
  moduleName: PropTypes.string,
  fetchData: PropTypes.func,
  selectedGroup: PropTypes.number,
};
ModalMoveTo.defaultProps = {
  // label: x => getTranslateMessage(
  //   TRANSLATE_KEY._BOX_TITL_MOVE_CODE,
  //   'Move {x} code(s) to another pool',
  //   { x }
  // ),
  isOpen: false,
  toggle: () => {},
  fetchData: () => {},
  selectedGroup: 1,
};
export default connect(
  null,
  mapDispatchToProps,
)(ModalMoveTo);
