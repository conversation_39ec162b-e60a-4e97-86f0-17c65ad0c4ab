/* eslint-disable indent */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react/prop-types */

// Libraries
import React, { useState, useEffect, useCallback } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter, useHistory, useLocation } from 'react-router-dom';
import { createStructuredSelector } from 'reselect';

// Services
import PromotionServices from '../../../../../../services/Promotion';

// Reducers, Sagas
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import reducer from './reducer';
import saga from './saga';

// Hooks
import useNotificationBar from 'hooks/useNotificationBar';
import useToggle from '../../../../../../hooks/useToggle';

// Selectors
import {
  makeSelectListPromotionTable,
  makeSelectListPromotionFilter,
  makeSelectListPromotionColumn,
  makeSelectListPromotionDomainMain,
  makeSelectListPromotionDomainMainData,
  makeSelectPromotionSettingGeneralPool,
} from './selectors';
import {
  makeSelectCheckPermissionPool,
  makeSelectPromotionIntegration,
} from '../selectors';

// Config
import { MODULE_CONFIG } from './config';
import { TABS_CONFIG } from '../config';
import { KEY_UPLOAD_POOL_CODE } from '../UploadPoolCode/config';

// Actions
import {
  init,
  getList,
  updateValue,
  update,
  reset,
  addNotification,
} from '../../../../../../redux/actions';

// Utils
import { getItemAttributeDecryptFields } from '../../../../../../utils/web/attribute';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import { getDefaultVal } from '../../../../../../components/common/InputLanguage/utils';

// Constant
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { METHOD_OPTIONS_MAP } from '../../Create/_UI/FieldShopifyDiscount/constant';

// Styled
import { WrapActionTable } from '../../../../../../containers/Table/styles';
import { TableWrapper, TableRelative } from './styles';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import TableContainer from 'containers/Table';
import {
  UIIconButton as IconButton,
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import Filters from '../../../../../../containers/Filters';
import ModifyColumn from '../../../../../../containers/ModifyColumn';
import Search from '../../../../../../containers/Search';
// import TryVersion from '../../../../../../containers/TryVersion';
import ControlTable from './ControlTable';
import ModalMoveTo from './ModalMoveTo';
import LayoutContent, {
  LayoutContentLoading,
  StyledWrapperLoading,
} from '../../../../../../components/Templates/LayoutContent';
import ModalExport from '../../../../../../containers/modals/ModalExport';
import DrawerPromotionCodeInfo from '../../../../../../containers/modals/ModalPromotionCodeInfo/DrawerPromotionCodeInfo';
import ModalDeleteV2 from '../../../../../../containers/modals/ModalDeleteV2';
import { Box } from '@material-ui/core';
import HeaderDrawer from '../../../../../../components/common/HeaderDrawer';
import { EditableName, Scrollbars } from '@antscorp/antsomi-ui';
import UploadPoolCodeDrawer from '../UploadPoolCode/Drawer';

const MAP_TITLE = {
  title: getTranslateMessage(
    TRANSLATE_KEY._TAB_PROMOTION_CODE,
    'Promotion Codes',
  ),
  action: {
    cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
    apply: getTranslateMessage(TRANSLATE_KEY._ACT_APPLY, 'Cancel'),
    viewAll: getTranslateMessage(TRANSLATE_KEY._ACT_VIEW_ALL, 'View all'),
    addFilter: getTranslateMessage(TRANSLATE_KEY._ACT_ADD_FILTER, 'Cancel'),
    save: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Save'),
    reset: getTranslateMessage(TRANSLATE_KEY._ACT_RESET, 'Reset'),
    search: getTranslateMessage(TRANSLATE_KEY._ACT_SEARCH, 'SEARCH'),
    column: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT_COLUMN, 'COLUMN'),
    download: getTranslateMessage(TRANSLATE_KEY._ACT_DOWNLOAD, 'DOWNLOAD'),
    actExport: getTranslateMessage(TRANSLATE_KEY._ACT_EXPORT, 'EXPORT'),
    actUpload: getTranslateMessage(TRANSLATE_KEY._ACT_UPLOAD, 'UPLOAD'),

    more: getTranslateMessage(TRANSLATE_KEY._ACT_MORE, 'MORE'),
  },
};

// const PARENT_PREFIX = MODULE_CONFIG_PARENT.parentPrefix;

// const layoutStyle = {
//   overflow: 'hidden',
//   height: 'calc(100vh - 108px)',
// };

export function PromotionList(props) {
  // console.log('props', props);
  const history = useHistory();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);

  const {
    activeRow,
    activeTab,
    poolId,
    main,
    table,
    filter,
    column,
    settingGeneralPool,
    permission,
    promotionIntegration,
  } = props;
  const {
    isInitDone,
    moduleConfig,
    moduleConfigColumn,
    isLoadingSaveName,
  } = main;
  const { isEdit = true } = permission;

  const { dataConfig } = settingGeneralPool;

  // States
  const [isOpenModalMoveTo, toggleModalMoveTo] = useToggle(false);
  const [isOpenModalDetail, setIsOpenModalDetail] = useState(false);
  const [isOpenModalDownload, toggleModalDownload] = useToggle(false);
  const [isOpenModalDelete, toggleModalDelete] = useToggle(false);

  const [promotionInfo, setPromotionInfo] = useState({});
  const [width, setWidth] = useState(window.innerWidth);
  const { isShow } = useNotificationBar();

  useEffect(() => {
    if (activeTab === TABS_CONFIG.PROMOTION_CODES.key) {
      props.init({ poolId, activeRow });
    }
    return () => {
      props.reset();
    };
  }, [poolId, activeTab]);

  const handleClickImportPool = () => {
    searchParams.set(KEY_UPLOAD_POOL_CODE, poolId);
    history.push({
      search: searchParams.toString(),
    });
  };
  // useInjectReducer({ key: MODULE_CONFIG.key, reducer });
  // useInjectSaga({ key: MODULE_CONFIG.key, saga, args: MODULE_CONFIG.key });
  const callback = (type, data) => {
    // console.log('callback', type, data);
    switch (type) {
      case 'FETCH_DATA': {
        props.fetchData();
        break;
      }
      // case 'ACTION_TABLE_CLONE': {
      //   // setSelectedRows(data);
      //   setIsOpenModalClone(true);
      //   break;
      // }
      case 'ACTION_TABLE_DELETE': {
        // setSelectedRows(data);
        toggleModalDelete();
        break;
      }
      case 'DELETE_CONFIRM': {
        props.onChangeDelete(data);
        break;
      }
      case 'ACTION_TABLE_MOVE_TO': {
        toggleModalMoveTo();
        break;
      }
      case 'UPDATE_TOGGLE_CELL': {
        props.onChangeStatus(data);
        break;
      }
      case 'TOGGLE_MODAL_DETAIL': {
        setIsOpenModalDetail(!isOpenModalDetail);
        setPromotionInfo(data);
        break;
      }
      // case 'UPDATE_TOGGLE_CELL_DONE': {
      //   const { isSuccessed, res } = data;
      //   if (!isSuccessed) {
      //     const noti = NOTI.toggle.fail(res);
      //     props.addNotification(noti);
      //   }
      //   props.onChangeStatusDone(data);
      //   break;
      // }
      case 'EDIT_CELL_NAME': {
        props.editCellName(data);
        break;
      }

      case 'AFTER_SAVE_NAME': {
        if (typeof props.onAfterSaveName === 'function') {
          props.onAfterSaveName(data);
        }
        break;
      }

      default:
        break;
    }
  };

  const onChangeData = useCallback(
    name => value => {
      if (value !== dataConfig[name].value) {
        props.onChangeDataConfig({ name, value });
      }
    },
    [dataConfig],
  );

  const handleSaveName = () => {
    if (!dataConfig.poolName.isChanged) return;
    props.onSaveName({ callback });
  };

  const tableColumns = React.useMemo(() => column.tableColumns, [
    column.tableColumns,
  ]);

  const handleOpenModalDetail = () => {
    setIsOpenModalDetail(!isOpenModalDetail);
  };
  // console.log({ props, main, table });
  const libraryFilters = {
    library: filter.config.library.filters,
    filterActive: filter.config.filterObj,
    totalRecord: table.paging.totalRecord,
  };
  // console.log({ props, main, table, elliot: filter.config.filterObj });
  return (
    <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Promotion/Detail/List/index.jsx">
      <HeaderDrawer style={{ height: 50 }}>
        <EditableName
          isLoading={isLoadingSaveName}
          defaultValue={activeRow.pool_name}
          value={getDefaultVal(dataConfig.poolName.value)}
          error={dataConfig?.poolName?.errors?.[0]}
          onChange={newValue => {
            if (activeRow.pool_name !== newValue) {
              onChangeData('poolName')({
                DEFAULT_LANG: 'EN',
                EN: newValue,
                VI: newValue,
                JA: newValue,
              });
            }
          }}
          onBlur={handleSaveName}
          readonly={!isEdit}
          required
        />
      </HeaderDrawer>
      <Scrollbars style={{ width: '100%', height: 'calc(100% - 50px)' }}>
        <Box padding="0px 15px 15px">
          {isInitDone ? (
            <>
              <LayoutContent
                padding="0px"
                height={
                  isShow ? 'calc(100vh - 65px - 54px)' : 'calc(100vh - 65px)'
                }
              >
                <LayoutContentLoading
                  classNameLoading="m-top-2"
                  isLoading={!isInitDone}
                >
                  <TableRelative className="m-top-2">
                    <TableWrapper>
                      <TableContainer
                        columnActive={column.columnObj}
                        table={table}
                        isLoading={main.isLoading}
                        // version={MODULE_CONFIG.key}
                        moduleConfig={moduleConfig}
                        selectedIds={table.selectedIds}
                        selectedRows={table.selectedRows}
                        isSelectedAll={table.isSelectedAll}
                        isSelectedAllPage={table.isSelectedAllPage}
                        columns={tableColumns}
                        data={props.data}
                        callback={callback}
                        noDataText="No data"
                        resizeColName="id"
                        widthFirstColumns={143}
                        initialWidthColumns={423}
                        ComponentControlTable={ControlTable}
                        libraryFilters={libraryFilters}
                        disabledHeaderCheckbox={!isEdit}
                        // NoDataComponent={() => (
                        //   <NoDataComponent
                        //     AddComponent={() => (
                        //       <AddComponent
                        //         // itemTypeId={itemTypeId}
                        //         className="m-left-0"
                        //       />
                        //     )}
                        //     guideNoYet={MAP_TITLE.guideNoDestYet}
                        //   />
                        // )}
                      >
                        <>
                          <Filters
                            use="list"
                            rules={filter.rules}
                            moduleConfig={moduleConfig}
                            filterActive={filter.config.filterObj}
                            filterCustom={filter.config.library.filterCustom}
                            libraryFilters={filter.config.library.filters}
                            groups={main.groupAttributes.groupsFilter}
                            // callback={callback}
                            isFilter={filter.config.design.isFilter}
                            isLoading={filter.config.isLoading}
                          />
                          <WrapActionTable
                            show={!filter.config.design.isFilter}
                            className="p-x-4"
                          >
                            <div className="actionTable__inner">
                              <WrapperDisable>
                                <Search
                                  moduleConfig={moduleConfig}
                                  config={{
                                    limit: 20,
                                    page: 1,
                                    // sd: 'asc',
                                    // search: '',
                                    // feKey: '1-1-undefined-name',
                                    poolId: Number(poolId),
                                    // pool_code: activeRow.pool_code,
                                    scope: 1,
                                    itemTypeId: -100,
                                    propertyCode: 'id',
                                    itemPropertyName: 'id',
                                    itemTypeName: 'promotion_code',
                                    // systemDefined: 0,
                                    // isPk: 0,
                                    decryptFields: getItemAttributeDecryptFields(
                                      -100,
                                    ),
                                  }}
                                  suggestionType="dataTable"
                                  moduleLabel={MAP_TITLE.title}
                                  isAddFilter
                                  isGoTo={false}
                                />
                              </WrapperDisable>
                              <ModifyColumn
                                sort={table.sort}
                                moduleConfig={moduleConfigColumn}
                                columnActive={column.columnObj}
                                columnCustom={column.library.columnCustom}
                                libraryColumns={column.library.columns}
                                defaults={moduleConfigColumn.columnsDefault}
                                defaultSortColumns={
                                  moduleConfigColumn.defaultSortColumns
                                }
                                columns={column.columnObj.columns.columnsAlias}
                                groups={main.groupAttributes.groups}
                                isLoading={column.isLoading}
                              />
                              <IconButton
                                // upload
                                iconName="file_upload"
                                size="20px"
                                onClick={
                                  activeRow.pool_status !== 1
                                    ? handleClickImportPool
                                    : () => {}
                                }
                                isVertical
                                disabled={
                                  activeRow.pool_status === 1 ||
                                  !isEdit ||
                                  Number(activeRow?.type) === 3 ||
                                  (promotionIntegration?.connector_code ===
                                    'shopify' &&
                                    activeRow?.settings?.method !==
                                      METHOD_OPTIONS_MAP.BULK_UPLOAD.value)
                                }
                              >
                                {MAP_TITLE.action.actUpload.toUpperCase()}
                              </IconButton>
                              {table.paging.totalRecord > 0 && (
                                <IconButton
                                  iconName="file_download"
                                  size="20px"
                                  onClick={
                                    table.paging.totalRecord === 0
                                      ? () => {}
                                      : toggleModalDownload
                                  }
                                  disabled={table.paging.totalRecord === 0}
                                  isVertical
                                >
                                  {MAP_TITLE.action.actExport.toUpperCase()}
                                </IconButton>
                              )}
                              {/* <IconButton
                              iconName="more-vertical"
                              size="20px"
                              onClick={() => {}}
                              isVertical
                              disabled
                            >
                              {MAP_TITLE.action.more.toUpperCase()}
                            </IconButton> */}
                            </div>
                          </WrapActionTable>
                        </>
                      </TableContainer>
                    </TableWrapper>
                  </TableRelative>
                </LayoutContentLoading>
              </LayoutContent>
              <ModalMoveTo
                isOpen={isOpenModalMoveTo}
                toggle={toggleModalMoveTo}
                poolId={poolId}
                selectedRows={table.selectedRows}
                fetchData={props.fetchData}
                callback={callback}
                isSelectedAll={table.isSelectedAll}
                activeRow={activeRow}
                libraryFilters={libraryFilters}
              />

              {/* Drawer promotion code info */}
              <DrawerPromotionCodeInfo poolId={props.poolId} />

              {/* Drawer uploads */}
              <UploadPoolCodeDrawer
                onAfterSave={() => callback('FETCH_DATA')}
              />
            </>
          ) : (
            <StyledWrapperLoading
              className="m-top-2"
              style={{ height: 'calc(100vh - 120px)' }}
            >
              <Loading isLoading={!isInitDone} />
            </StyledWrapperLoading>
          )}
        </Box>

        <ModalExport
          isOpen={isOpenModalDownload}
          objectName="Promotion"
          toggle={toggleModalDownload}
          paging={table.paging}
          object_type={MODULE_CONFIG.objectType}
          // decryptFields={getItemAttributeDecryptFields(itemTypeId)}
          sort={table.sort}
          filters={filter}
          itemTypeId={-100}
          itemTypeName="customer"
          columns={column.columnObj.columns.columnsAlias}
        />
        <ModalDeleteV2
          isOpenModal={isOpenModalDelete}
          setOpenModal={toggleModalDelete}
          label="Confirm your action"
          activeRows={table.selectedRows}
          rules={filter.rules}
          isSelectedAll={table.isSelectedAll}
          callback={callback}
          params={{
            poolId,
            data: {
              promotionCodes: [],
            },
          }}
          ObjectServicesFn={
            PromotionServices.promotionSource.data.checkRemoveCode
          }
          type="code"
          filed="promotionCodes"
        />
      </Scrollbars>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  main: makeSelectListPromotionDomainMain(),
  // common: makeSelectCustomerCommon(),
  table: makeSelectListPromotionTable(),
  filter: makeSelectListPromotionFilter(),
  column: makeSelectListPromotionColumn(),
  data: makeSelectListPromotionDomainMainData(),
  settingGeneralPool: makeSelectPromotionSettingGeneralPool(),

  permission: makeSelectCheckPermissionPool(),
  promotionIntegration: makeSelectPromotionIntegration(),
});

function mapDispatchToProps(dispatch) {
  return {
    init: params => {
      dispatch(init(MODULE_CONFIG.key, params));
    },
    reset: params => {
      dispatch(reset(MODULE_CONFIG.key, params));
    },
    fetchData: params => {
      dispatch(getList(MODULE_CONFIG.key, params));
    },

    editCellName: params => {
      dispatch(updateValue(`${MODULE_CONFIG.key}@@EDIT_CELL_NAME`, params));
    },
    onChangeStatus: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    },
    // onChangeStatusDone: params => {
    //   dispatch(updateDone(`${MODULE_CONFIG.key}@@CELL_STATUS`, params));
    // },
    addNotification: noti => {
      dispatch(addNotification(noti));
    },
    onChangeDelete: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@DELETE_ROW`, params));
    },
    onChangeDataConfig: value =>
      dispatch(
        updateValue(`${MODULE_CONFIG.key}@@SETTING_POOL@@DATA_CONFIG@@`, value),
      ),
    onSaveName: params => {
      dispatch(update(`${MODULE_CONFIG.key}@@SAVE_NAME`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({ key: MODULE_CONFIG.key, reducer });
const withSaga = injectSaga({ key: MODULE_CONFIG.key, saga });

export default compose(
  withRouter,
  withReducer,
  withSaga,
  withConnect,
)(PromotionList);
