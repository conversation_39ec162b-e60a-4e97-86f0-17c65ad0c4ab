import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';

export const STEP_NAME = {
  CATALOG: 0,
  GENERAL: 1,
};

export const MAP_STEP_NAME = {
  [STEP_NAME.CATALOG]: 'choose_catalog',
  [STEP_NAME.GENERAL]: 'general_setting_step',
};

export const STEPS = [
  getTranslateMessage(TRANSLATE_KEY._, 'Choose a catalog'),
  getTranslateMessage(TRANSLATE_KEY._, 'General Information'),
];

export const CHANNEL_TYPE = {
  EMAIL: 1,
  WEB_PERSONALIZATION: 2,
  WEB_PUSH: 3,
  APP_PUSH: 4,
  CONVERSATION: 5,
  WEBHOOK: 6,
  SMS: 7,
  ORCHES: 8,
  VIBER: 9,
};

export const CATALOG_CODE = {
  ONE_SIGNAL_APP_PUSH: 'onesignal_app_push',
  FIRE_BASE_APP_PUSH: 'firebase_app_push',
  ONEWAY_SMS: 'oneway_sms',
  LINE: 'line_app',
  LINE_RICH_MENU: 'line_rich_menu',
  ZALO_OA: 'zalo_official_account',
  ANTSOMI_APP_PUSH: 'antsomi_app_push',
  SMART_INBOX: 'smart_inbox_app',
  ZNS: 'zalo_notification_service',
  SMS_IMEDIA: 'sms_imedia',
  ZALO_IMEDIA: 'imedia_zns',
  WEBHOOK_GOLDEN_GATE: 'webhook_golden_gate',
  AEON_MALL_IN_APP_MESSAGE: 'aeon_mall_in_app_message',
};
