/* eslint-disable indent */
import _ from 'lodash';
import _isEmpty from 'lodash/isEmpty';
import { getDefaultVal } from 'components/common/InputLanguage/utils';
import {
  toAPIFrequencyCapping,
  toUIFrequencyCapping,
} from 'components/common/UIFrequencyCapping/utils';
import {
  MAP_VALIDATE,
  MAP_INPUT_TYPE,
  MAP_COMPONENT_ALIAS,
  getTypeRender,
} from './utils.map';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import {
  CellText,
  CellRelatedObject,
  CellMainLimitDestination,
} from '../../../../../containers/Table/Cell';
import { getObjectPropSafely } from '../../../../../utils/common';
import APP from '../../../../../appConfig';
import { BREADCRUMDATA } from './config';
import { MAP_LABEL_CHANNEL } from '../utils';
import { addMessageToQueue } from '../../../../../utils/web/queue';

const MAP_TITLE = {
  titlDestName: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_NAME,
    'Destination Name',
  ),
  titlDescription: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESCRIPTION,
    'Description',
  ),
  titlDestCatalog: getTranslateMessage(
    TRANSLATE_KEY._TITL_DESTINATION_CATALOG,
    'Destination Catalog',
  ),
  titlMethod: getTranslateMessage(TRANSLATE_KEY._TITL_METHOD, 'Method'),
  labelNumberRecord: getTranslateMessage(
    TRANSLATE_KEY._DES_BY_NUMBER_RECORD,
    'By number of records',
  ),
  labelEveryTimeCompleted: getTranslateMessage(
    TRANSLATE_KEY._DES_BY_EVERY_TIME_JOURNEY_COMPLETED,
    'By every time Journey Completed',
  ),
  labelByDays: getTranslateMessage(TRANSLATE_KEY._DES_BY_DAY, 'By day(s)'),
  labelByHours: getTranslateMessage(TRANSLATE_KEY._DES_BY_HOUR, 'By hour(s)'),
  labelByMinute: getTranslateMessage(
    TRANSLATE_KEY._DES_BY_MINUTE,
    'By minute(s)',
  ),
};

export const MAX_TO_SEND_BROADCAST = 1000000;
export const CHANNEL_FILE_TRANSFER = 11;
export const TYPE_DELIVERY_INTERVAL = {
  NUM_RECORDS: 'num_records',
  AFTER_COMPLETED: 'after_journey_completed',
  DAY: 'day',
  HOUR: 'hour',
  MINUTE: 'minute',
};

export const EXTRA_EMAIL_FIELDS = {
  CC: 'ccEmail',
  BCC: 'bccEmail',
};

export const mapLabelExtraEmail = (extraField = '') => {
  if (extraField === EXTRA_EMAIL_FIELDS.CC) return 'Cc Email';
  if (extraField === EXTRA_EMAIL_FIELDS.BCC) return 'Bcc Email';
  return 'Unknown';
};

export const mapLabelDeliveryInterval = {
  [TYPE_DELIVERY_INTERVAL.NUM_RECORDS]: getTranslateMessage(
    TRANSLATE_KEY._RECORD,
    'record(s)',
  ),
  [TYPE_DELIVERY_INTERVAL.DAY]: getTranslateMessage(
    TRANSLATE_KEY._DAY,
    'day(s)',
  ),
  [TYPE_DELIVERY_INTERVAL.HOUR]: getTranslateMessage(
    TRANSLATE_KEY._HOUR,
    'hour(s)',
  ),
  [TYPE_DELIVERY_INTERVAL.MINUTE]: getTranslateMessage(
    TRANSLATE_KEY._MINUTE,
    'minute(s)',
  ),
};

const DELIVERY_INTERVAL_OPTIONS = [
  {
    label: MAP_TITLE.labelNumberRecord,
    value: TYPE_DELIVERY_INTERVAL.NUM_RECORDS,
  },
  {
    label: MAP_TITLE.labelEveryTimeCompleted,
    value: TYPE_DELIVERY_INTERVAL.AFTER_COMPLETED,
  },
  {
    label: MAP_TITLE.labelByDays,
    value: TYPE_DELIVERY_INTERVAL.DAY,
  },
  {
    label: MAP_TITLE.labelByHours,
    value: TYPE_DELIVERY_INTERVAL.HOUR,
  },
  {
    label: MAP_TITLE.labelByMinute,
    value: TYPE_DELIVERY_INTERVAL.MINUTE,
  },
];

export const MINUTE_OPTIONS = [
  {
    label: 15,
    value: 15,
  },
  {
    label: 30,
    value: 30,
  },
  {
    label: 45,
    value: 45,
  },
];

export const initDefaultDataConfig = () => ({
  isValidate: false,
  isExistedDestination: false,
  configFields: [],
  // generalFields: [],
  infoFields: ['destinationName', 'description', 'destinationCatalog'],
  extraInfoFields: [],
  destinationName: {
    ...initInputElement(
      'destinationName',
      {},
      255,
      true,
      MAP_TITLE.titlDestName,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  description: {
    ...initInputElement(
      'description',
      {},
      255,
      false,
      MAP_TITLE.titlDescription,
      null,
      MAP_VALIDATE.multiLangInput,
    ),
    componentEl: MAP_INPUT_TYPE.multiLangInput,
  },
  destinationCatalog: {
    ...initInputElement(
      'destinationCatalog',
      null,
      null,
      true,
      MAP_TITLE.titlDestCatalog,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_COMPONENT_ALIAS.destinationCatalog,
    options: [],
    mapOptions: {},
    placeHolder: 'sdnnsjdfg',
  },
  method: {
    ...initInputElement(
      'method',
      null,
      null,
      true,
      MAP_TITLE.titlMethod,
      null,
      MAP_VALIDATE.selectDropdown,
    ),
    componentEl: MAP_INPUT_TYPE.selectDropdown,
    options: [],
    mapOptions: {},
  },
  frequencyCapping: { value: {}, disabled: false },
  deliveryInterval: {
    options: DELIVERY_INTERVAL_OPTIONS,
    mapOptions: {
      label: MAP_TITLE.labelNumberRecord,
      value: TYPE_DELIVERY_INTERVAL.NUM_RECORDS,
    },
    value: 100,
    disabled: false,
  },
  deliveryRate: { type: 'normal', disabled: false },
  deliveryBroadcast: {
    canSendBroadcast: false,
    isBroadcast: false,
    from: 1,
    to: 1000,
    disabled: false,
  },
});
const initInputElement = (
  name,
  value,
  maxLength,
  isRequired,
  label,
  errors,
  validate,
) => ({
  name: name || '',
  value: value || '',
  maxLength: maxLength || null,
  isRequired,
  label: label || '',
  errors: errors || [],
  isValidate: !isRequired,
  validate: validate || (() => ({ errors: [], isValidate: false })),
  initValue: value || null,
});

export const mapMethods = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.name,
    value: item.name,
    label: item.label,
  }));

export const mapDestCatalog = data =>
  mapBluePrint(data, item => ({
    ...item,
    id: item.catalogId,
    value: item.catalogId,
    label: item.translateLabel || item.catalogName,
  }));

const mapBluePrint = (dataIn, bluePrintFn) => {
  const data = {
    list: [],
    map: {},
  };
  dataIn.forEach(item => {
    const objItem = {
      ...item,
      ...bluePrintFn(item),
    };
    data.list.push(objItem);
    data.map[objItem.id] = objItem;
  });
  return data;
};
export const mapConfigureField = (fields, settings) => {
  // console.log('fields, settings: ', fields, settings);
  const data = {};

  fields.forEach(each => {
    const item = settings[each];
    // const initValue = (initData || {})[each] || '';
    const value = '';
    const typeRender = getTypeRender(item);
    const label = getTranslateMessage(item.labelCode, item.label);
    const temp = {
      ...item,
      ...initInputElement(
        each,
        value,
        item.maxLength,
        item.isRequired,
        label,
        null,
        MAP_VALIDATE[typeRender],
      ),
      isValidate: !item.isRequired,
      componentEl: MAP_INPUT_TYPE[typeRender],
    };

    if (typeRender === 'radioGroup') {
      if (item?.default) {
        _.set(temp, 'value', item?.default);
        _.set(temp, 'initValue', item?.default);
        _.set(temp, 'isValidate', true);
      }
    }

    data[each] = temp;
  });
  return data;
};

export const toEntryAPI = (dataIn, channelId) => {
  // console.log('toEntryAPI', dataIn, channelId);
  const {
    destinationCatalog,
    description,
    destinationName,
    method,
    frequencyCapping,
    configFields,
    deliveryRate,
    deliveryInterval,
    deliveryBroadcast,
  } = dataIn;
  const destinationSetting = {};
  configFields.forEach(each => {
    destinationSetting[each] = dataIn[each].value;
  });
  destinationSetting.method = method.value.value;
  // destinationSetting.frequencyCapping =
  //   toAPIFrequencyCapping(frequencyCapping.value) || {};
  destinationSetting.frequencyCapping = frequencyCapping.value;

  if (+channelId === CHANNEL_FILE_TRANSFER) {
    destinationSetting.formatFile = dataIn.formatFile.value.value;
    if (
      deliveryInterval.mapOptions.value ===
      TYPE_DELIVERY_INTERVAL.AFTER_COMPLETED
    ) {
      destinationSetting.intervalSetting = {
        type: deliveryInterval.mapOptions.value,
      };
    } else {
      destinationSetting.intervalSetting = {
        type: deliveryInterval.mapOptions.value,
        value: +deliveryInterval.value,
      };
    }
  } else {
    destinationSetting.deliveryRate =
      deliveryRate.type === 'normal'
        ? {
            type: deliveryRate.type,
          }
        : {
            type: deliveryRate.type,
            limit: deliveryRate.limit,
          };
  }

  const canSendBroadcast = getObjectPropSafely(
    () => deliveryBroadcast.canSendBroadcast,
    false,
  );

  if (canSendBroadcast && _.has(deliveryBroadcast, 'isSendMultiple')) {
    destinationSetting.isSendMultiple = deliveryBroadcast.isSendMultiple;
  }

  if (
    !_isEmpty(deliveryBroadcast) &&
    canSendBroadcast &&
    getObjectPropSafely(() => Boolean(deliveryBroadcast.isBroadcast), false)
  ) {
    const { from, to } = deliveryBroadcast;
    destinationSetting.intervalSetting = { from, to };
  }

  destinationSetting.isBroadcast = canSendBroadcast
    ? getObjectPropSafely(() => Boolean(deliveryBroadcast.isBroadcast), false)
    : false;

  const data = {
    catalogId: destinationCatalog.value.value,
    destinationName: getDefaultVal(destinationName.value),
    destinationNameMultilang: destinationName.value || {},
    destinationSetting,
    description: getDefaultVal(description.value),
    descriptionMultilang: description.value || {},
  };
  // console.log('toEntryAPI out', data);
  return data;
};

export const mapValueToFE = ({ activeRow, dataConfig }) => {
  // console.log('mapValueToFE', activeRow, dataConfig);
  const {
    destinationNameMultilang,
    descriptionMultilang,
    catalogId,
    destinationSetting,
  } = activeRow;
  const {
    frequencyCapping,
    method,
    deliveryRate,
    isBroadcast = false,
    intervalSetting = {},
  } = destinationSetting;
  const { configFields } = dataConfig;

  let data = {
    destinationName: destinationNameMultilang,
    description: descriptionMultilang,
    // frequencyCapping: toUIFrequencyCapping(frequencyCapping),
    frequencyCapping,
    destinationCatalog: dataConfig.destinationCatalog.mapOptions[catalogId],
    method: dataConfig.method.mapOptions[method],
    deliveryRate: { ...deliveryRate, disabled: false },
  };
  configFields.forEach(each => {
    data[each] = destinationSetting[each] || '';
  });

  if (typeof isBroadcast === 'boolean') {
    data = {
      ...data,
      deliveryBroadcast: {
        isBroadcast,
        canSendBroadcast: true,
        from: intervalSetting.from || 1,
        to: intervalSetting.to || 1000,
      },
    };

    if (_.has(destinationSetting, 'isSendMultiple')) {
      data.deliveryBroadcast.isSendMultiple = destinationSetting.isSendMultiple;
    }
  }

  if (!_isEmpty(intervalSetting) && isBroadcast === null) {
    const temp = DELIVERY_INTERVAL_OPTIONS.find(
      option => option.value === intervalSetting.type,
    );
    const formatFileValueAPI = getObjectPropSafely(
      () => destinationSetting.formatFile,
      '',
    );
    const formatFile = [...(dataConfig.formatFile.options || [])].find(
      format => format.value === formatFileValueAPI,
    );

    data = {
      ...data,
      formatFile,
      deliveryInterval: {
        options: DELIVERY_INTERVAL_OPTIONS,
        mapOptions: !_isEmpty(temp) ? temp : DELIVERY_INTERVAL_OPTIONS[0],
        value: intervalSetting.value,
        disabled: false,
      },
    };
  }
  // console.log('toEntryAPI out', data);
  return data;
};

export const columnsDestination = [
  {
    Header: getTranslateMessage(
      TRANSLATE_KEY._WARN_TITLE_DES_NAME,
      'Destination Name',
    ),
    id: 'destinationName',
    accessor: 'destinationName',
    Cell: CellMainLimitDestination,
    sticky: 'left',
    width: 280,
    // disableResizing: true,
    disableSortBy: true,
  },
  {
    Header: 'Info Remaining',
    columns: [
      {
        Header: getTranslateMessage(
          TRANSLATE_KEY._WARN_TITLE_DES_CREATED_BY,
          'Created by',
        ),
        // Header: 'Type',
        id: 'createdBy',
        accessor: 'createdBy',
        disableSortBy: true,
        Cell: CellText,
      },
      // {
      //   Header: getTranslateMessage(
      //     TRANSLATE_KEY._WARN_TITLE_DES_RELATED_OBJECTS,
      //     'Related Objects',
      //   ),
      //   id: 'relatedObjects',
      //   accessor: 'relatedObjects',
      //   Cell: CellRelatedObject,
      //   placement: 'left',
      //   disableSortBy: true,
      // },
    ],
  },
];

export const convertDataToUI = data => {
  try {
    const dataOut = [];
    data.forEach(each => {
      // const relatedMapObject =
      //   each.relatedObjects &&
      //   each.relatedObjects.map(related => ({
      //     ...related,
      //     objectName: related.storyName,
      //   }));
      const item = {
        ...each,
        id: each.destination_id,
        destinationName: each.destination_name,
        createdBy: each.c_user_id,
        // relatedObjects: each.relatedObjects ? relatedMapObject : null,
      };
      dataOut.push(item);
    });
    return dataOut;
  } catch (error) {
    return [];
  }
};
export function getBreadcrums(channelActive) {
  const channel = {
    key: '1',
    display: MAP_LABEL_CHANNEL[channelActive],
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: `/marketing-hub/destinations/${channelActive}`,
    },
  };
  const allChannel = {
    key: 2,
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: '/marketing-hub/destinations',
    },
    display: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_ALL_DESTINATION,
      'All Destinations',
    ),
  };
  const tmp = {
    key: 3,
    display: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_CREATE_NEW_DESTINATION,
      'Create new destination',
    ),
  };
  return [allChannel, channel, tmp];
}

export const checkIsHideField = ({
  channelId = '',
  name = '',
  extraEmailBtn = [],
}) => {
  try {
    if (!name || !channelId) return false;

    switch (+channelId) {
      case 1: {
        if (!Array.isArray(extraEmailBtn)) return false;

        return (
          Object.values(EXTRA_EMAIL_FIELDS).includes(name) &&
          Array.isArray(extraEmailBtn) &&
          extraEmailBtn.includes(name)
        );
      }
      default: {
        return false;
      }
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Destination/Create/utils.js',
      func: 'checkIsHideField',
      data: error.stack,
    });
    // eslint-disable-next-line no-console
    console.log(error);
  }
};
