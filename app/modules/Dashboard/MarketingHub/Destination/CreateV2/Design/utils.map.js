/* eslint-disable no-param-reassign */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable indent */
/* eslint-disable prefer-const */
/* eslint-disable arrow-body-style */
/* eslint-disable react/prop-types */
// Libraries
import React, { useMemo } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _ from 'lodash';
import classNames from 'classnames';
import produce from 'immer';

// Components
import {
  UITextField,
  UIWrapperDisable as WrapperDisable,
  UICheckbox,
  UINumber,
} from '@xlab-team/ui-components';
import { UploadImage, Input, Typography } from '@antscorp/antsomi-ui';
import UISelect from 'components/form/UISelectCondition';
import PairKeyValue from 'components/common/UIPairKeyValue';
import { getDefaultVal } from 'components/common/InputLanguage/utils';
import FormHelperText from '@material-ui/core/FormHelperText';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import IconXlab from 'components/common/UIIconXlab';
import InputLanguage from '../../../../../../components/common/InputLanguage';
import InputPreview from '../../../../../../components/Atoms/InputPreview';
import { ListTemplate } from './_UI';

// Styled
import {
  WrapperCenterFlexStart,
  WrapperCenterFlexEnd,
  WrapperIcon,
  Text,
  WrapperFlex,
  WrapperTextArea,
  WrapperPasswordField,
  WrapperListTemplate,
} from './styles';
import {
  BlockLeft,
  BlockRight,
  Title,
} from '../../../../../../containers/Drawer/DrawerIntegration/components/Content/styled';

// Utils
import { isProduction, validateEmail } from '../../../../../../utils/web/utils';
import { getCurrentUserId, getToken } from '../../../../../../utils/web/cookie';

// Translations
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';

// Constants
import { TEMPLATES } from 'containers/UIPreview/AppPushPreview/AppPushTemplate/constants';

const { Password } = Input;

const MAP_TRANSLATE = {
  nameEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EMPTY_NAME,
    `Name can't be empty`,
  ),
  inputEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_REQUIRED_INPUT,
    'Input can’t be empty',
  ),
  requiredField: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EMPTY_NAME,
    `This field can't be empty`,
  ),
};

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';
const DOMAIN_MEDIA_PROD = 'https://media-template.antsomi.com';

export const MAP_VALIDATE = {
  preventValidate: () => ({ errors: [], isValidate: true }),
  templateSetting: () => ({ errors: [], isValidate: true }),
  singleLineText: ({ value, isRequired, maxLength }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (value.trim().length === 0 && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.spaceError];
    } else if (maxLength !== null && value.length > maxLength) {
      isValidate = false;
      errors = [`Invalid: maximum ${maxLength} characters`];
    }
    return { errors, isValidate };
  },
  imageUrl: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  editor: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (typeof value === 'string' && value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      !Object.prototype.hasOwnProperty.call(value, 'html') &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      Object.prototype.hasOwnProperty.call(value, 'html') &&
      value.html.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  htmlEditor: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (typeof value === 'string' && value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      !Object.prototype.hasOwnProperty.call(value, 'html') &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (
      typeof value === 'object' &&
      Object.prototype.hasOwnProperty.call(value, 'html') &&
      value.html.trim() === '' &&
      isRequired
    ) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  multiLineText: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  selectDropdown: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  email: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
    } else {
      isValidate = validateEmail(value);
      errors = isValidate ? [] : ['Invalid email'];
    }
    return { errors, isValidate };
  },
  password: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.requiredField];
    }
    return { errors, isValidate };
  },
  keyvalue: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else {
      isValidate = Object.keys(value).every(
        each => each.trim() !== '' && value[each].trim() !== '',
      );
      errors = isValidate ? [] : [MAP_TRANSLATE.inputEmpty];
    }

    return { errors, isValidate };
  },
  checkbox: () => ({ errors: [], isValidate: true }),
  default: () => ({ errors: [], isValidate: true }),
  number: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (Number.isNaN(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  multiLangInput: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(getDefaultVal(value).trim()) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  singleLineAddPersonalize: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
};

export const MAP_INPUT_TYPE = {
  singleLineText: props => {
    return (
      <>
        <BlockLeft
          item
          sm={props.isGridV2 ? 4 : 2}
          className={props.classes.title}
          style={{
            display: props.isHidden ? 'none' : 'block',
          }}
        >
          <WrapperCenterFlexEnd>
            <Title>
              {props.label}
              {props.isRequired && !props.isViewMode && (
                <span style={{ marginLeft: 3, color: '#ff0000' }}> *</span>
              )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight
          item
          sm={10}
          style={{ display: props.isHidden ? 'none' : 'block' }}
        >
          {props.subLabel && (
            <Typography.Text style={{ paddingTop: 6, display: 'block' }}>
              {props.subLabel}
            </Typography.Text>
          )}
          {props.encrypt ? (
            <>
              <Password
                id={props.name}
                value={props.value}
                disabled={props.disabled || props.isViewMode}
                placeholder={props.placeholder}
                status={props.errors[0] ? 'error' : undefined}
                onChange={e => {
                  if (e?.target) {
                    const valueOut = e.target?.value;
                    props.onChange(props.name)(valueOut);
                  }
                }}
                visibilityToggle={props.isOwner}
                onFocus={e => {
                  if (
                    props.encrypt &&
                    !props.isOwner &&
                    props.isHasPermissionEdit &&
                    e.target.type === 'password'
                  ) {
                    e.target.oldValue = e.target.value;
                    props.onChange(props.name)('');
                    e.target.type = 'text';
                  }
                }}
                onBlur={e => {
                  if (
                    props.encrypt &&
                    !props.isOwner &&
                    props.isHasPermissionEdit
                  ) {
                    if (!e.target.value) {
                      props.onChange(props.name)(e.target.oldValue);
                      e.target.type = 'password';
                    }
                  }
                }}
              />
              <FormHelperText
                id="component-helper-text"
                error={!!props.errors[0]}
                style={{ fontSize: '11px', marginTop: 4 }}
              >
                {props.errors}
              </FormHelperText>
            </>
          ) : (
            <UITextField
              id={props.name}
              value={props.value}
              onChange={props.onChange(props.name)}
              firstText={props.errors[0]}
              placeholder={props.placeholder}
              textFieldProps={{
                disabled: props.disabled,
                size: 'small',
                multiline: false,
                rowsMax: 1,
                className: 'width-100',
                // id: 'standard-basic',
                error: !!props.errors[0],
              }}
            />
          )}
        </BlockRight>
      </>
    );
  },
  password: props => {
    return (
      <>
        <BlockLeft
          item
          sm={props.isGridV2 ? 4 : 2}
          className={props.classes.title}
          style={{
            display: props.isHidden ? 'none' : 'block',
          }}
        >
          <WrapperCenterFlexEnd>
            <Title>
              {props.label}
              {props.isRequired && !props.isViewMode && (
                <span style={{ marginLeft: 3, color: '#ff0000' }}> *</span>
              )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight
          item
          sm={10}
          style={{ display: props.isHidden ? 'none' : 'block' }}
        >
          <WrapperPasswordField>
            <Password
              disabled={props.disabled || props.isViewMode}
              placeholder={props.placeholder}
              id={props.name}
              value={props.value}
              status={props.errors[0] ? 'error' : undefined}
              onChange={e => {
                if (e?.target) {
                  const valueOut = e.target?.value;
                  props.onChange(props.name)(valueOut);
                }
              }}
              visibilityToggle={props.encrypt ? props.isOwner : true}
              onFocus={e => {
                if (
                  props.encrypt &&
                  !props.isOwner &&
                  props.isHasPermissionEdit &&
                  e.target.type === 'password'
                ) {
                  e.target.oldValue = e.target.value;
                  props.onChange(props.name)('');
                  e.target.type = 'text';
                }
              }}
              onBlur={e => {
                if (
                  props.encrypt &&
                  !props.isOwner &&
                  props.isHasPermissionEdit
                ) {
                  if (!e.target.value) {
                    props.onChange(props.name)(e.target.oldValue);
                    e.target.type = 'password';
                  }
                }
              }}
            />
            <FormHelperText
              id="component-helper-text"
              error={!!props.errors[0]}
              style={{ fontSize: '11px', marginTop: 4 }}
            >
              {props.errors}
            </FormHelperText>
          </WrapperPasswordField>
        </BlockRight>
      </>
    );
  },
  imageUrl: props => {
    // console.log('imageUrl', props);
    // Constants
    const userId = getCurrentUserId();
    const token = getToken();

    return (
      <>
        <BlockLeft item sm={2}>
          <WrapperCenterFlexEnd>
            <Title className={props.classes.title}>
              {props.label}
              <span style={{ marginLeft: 3, color: '#ff0000' }}>
                {props.isRequired && `*`}
              </span>
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={10}>
          <WrapperDisable disabled={props.disabled}>
            <UploadImage
              isInputMode
              domainMedia={
                isProduction() ? DOMAIN_MEDIA_PROD : DOMAIN_MEDIA_SANDBOX
              }
              slug="api/v1"
              paramConfigs={{
                token,
                userId,
                accountId: userId,
              }}
              width="100%"
              selectedImage={{
                url: props.value,
              }}
              onChangeImage={image => {
                props.onChange(props.name)((image && image.url) || '');
              }}
              onRemoveImage={() => {
                props.onChange(props.name)('');
              }}
            />
          </WrapperDisable>
        </BlockRight>
      </>
    );
  },
  selectDropdown: props => {
    const { value = {}, options = [] } = props;
    let valueParsed = value;

    // Vì hiện tại có một số case đặc biệt value === string nên phải map lại data từ options
    if (typeof valueParsed === 'string' && Array.isArray(options)) {
      valueParsed = options.find(option => option.value === value);
    }

    return (
      <>
        <BlockLeft item sm={2}>
          <WrapperCenterFlexEnd>
            <Title className={props.classes.title}>
              {props.label}
              <span style={{ marginLeft: 3, color: '#ff0000' }}>
                {props.isRequired && `*`}
              </span>
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={9}>
          <UISelect
            onlyParent
            use="tree"
            isSearchable
            options={props.options}
            value={valueParsed || value}
            onChange={props.onChange(props.name)}
            placeholder={getTranslateMessage(
              TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
              'Select an item',
            )}
            fullWidthPopover
            disabled={props.disabled}
          />
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  multiLineText: props => {
    return (
      <>
        <BlockLeft
          item
          sm={props.colLeft || 4}
          className={classNames(
            props.classes.title,
            props.classes.styledChannel,
          )}
        >
          <WrapperCenterFlexStart>
            <Title>
              {props.label}
              <span style={{ marginLeft: 3, color: '#ff0000' }}>
                {props.isRequired && `*`}
              </span>
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </BlockLeft>
        <BlockRight item sm={props.colRight || 8}>
          <InputPreview
            id={props.id}
            name={props.name}
            className={props.className}
            borderStyle={props.borderStyle}
            height={props.height}
            minHeight={props.minHeight}
            maxHeight={props.maxHeight}
            isViewMode={props.isViewMode}
            value={props.value}
          >
            <WrapperDisable disabled={props.disabled}>
              <WrapperDisable disabled={props.disabled}>
                <TinymceEditor
                  {...props}
                  typeComponent="input"
                  onChange={value => {
                    props.onChange(props.name)(value);
                  }}
                  // onChangeOthers={props.onChangeOthers(props.name)}
                  initData={props.initValue}
                  value={props.value}
                  isChannelEmail={false}
                  hasEmoji
                />
              </WrapperDisable>
              <WrapperFlex
                style={{
                  marginTop: 10,
                  justifyContent: 'flex-end',
                }}
              >
                <WrapperFlex>
                  <IconXlab name="info" fontSize="20px" color="#595959" />
                  <Text
                    style={{
                      color: '#a2a2a2',
                      fontSize: '11px',
                      marginLeft: 5,
                    }}
                  >
                    {`${props.value.length}/${
                      props.maxLength
                    } characters | SMS: 1`}
                  </Text>
                </WrapperFlex>
              </WrapperFlex>
            </WrapperDisable>
          </InputPreview>
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  editor: props => {
    return (
      <>
        <BlockLeft item sm={2}>
          <WrapperCenterFlexStart>
            <Title>
              {props.label}
              <span style={{ marginLeft: 3, color: '#ff0000' }}>
                {props.isRequired && `*`}
              </span>
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </BlockLeft>
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <TinymceEditor
              {...props}
              initData={props.initValue}
              onChange={props.onChange(props.name)}
              typeComponent="editor"
              // showPopupEditorHTML={false}
              showPopupEditorHTML
              showPersonalization
              showObjectWidget
            />
          </WrapperDisable>
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  keyvalue: props => {
    return (
      <>
        <BlockLeft item sm={2}>
          <WrapperCenterFlexStart>
            <Title>
              {props.label}
              <span style={{ marginLeft: 3, color: '#ff0000' }}>
                {props.isRequired && `*`}
              </span>
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </BlockLeft>
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <PairKeyValue
              {...props}
              global={{ ...props }}
              errors={props.errors}
              isRequired={props.isRequired}
              initData={props.initValue}
              onChange={value => props.onChange(props.name)(value)}
            />
          </WrapperDisable>
        </BlockRight>
      </>
    );
  },
  checkbox: props => {
    const { isHiddenLeft = false } = props;

    return (
      <>
        {!isHiddenLeft && <BlockLeft item sm={2} />}
        <BlockRight item sm={9} style={{ marginLeft: isHiddenLeft ? 50 : 0 }}>
          <WrapperDisable disabled={props.disabled}>
            <UICheckbox
              name={props.name}
              checked={!!props.value}
              onChange={value => props.onChange(props.name)(value)}
            >
              <span style={{ color: '#000000', fontSize: '12px' }}>
                {props.label}
              </span>
            </UICheckbox>
          </WrapperDisable>
        </BlockRight>
      </>
    );
  },
  htmlEditor: props => {
    return (
      <>
        <BlockLeft item sm={props.isGridV2 ? 4 : 2}>
          <WrapperCenterFlexEnd>
            <Title className={props.classes.title}>
              {props.label}
              <span style={{ marginLeft: 3, color: '#ff0000' }}>
                {props.isRequired && `*`}
              </span>
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <WrapperTextArea style={{ position: 'relative', height: 70 }}>
              <TinymceEditor
                {...props}
                initData={props.initValue}
                onChange={props.onChange(props.name)}
                typeComponent="htmlEditor"
                // showPopupEditorHTML={false}
                showPopupEditorHTML
                showPersonalization
                showObjectWidget
              />
            </WrapperTextArea>
            <WrapperFlex style={{ marginTop: 10 }}>
              <WrapperIcon>
                <IconXlab name="settings" fontSize="24px" color="#005fb8" />
              </WrapperIcon>
              <WrapperFlex>
                <IconXlab name="info" fontSize="20px" color="#595959" />
                <Text
                  style={{
                    color: '#a2a2a2',
                    fontSize: '11px',
                    marginLeft: 5,
                  }}
                >
                  49/459 characters | SMS: 1
                </Text>
              </WrapperFlex>
            </WrapperFlex>
          </WrapperDisable>
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  multiLangInput: props => {
    return (
      <>
        <BlockLeft item sm={2} className={props.classes.title}>
          <WrapperCenterFlexEnd>
            <Title>
              {props.label}
              {props.isRequired && !props.isViewMode && (
                <span style={{ marginLeft: 3, color: '#ff0000' }}> *</span>
              )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={9}>
          <InputLanguage
            isShowLabelFlagSelected
            disabled={props.disabled}
            initData={props.initValue}
            onChange={props.onChange(props.name)}
            errors={props.errors}
          />
        </BlockRight>
      </>
    );
  },
  number: props => {
    return (
      <>
        <BlockLeft item sm={2}>
          <WrapperCenterFlexStart>
            <Title className={props.classes.title}>
              {props.label}
              <span style={{ marginLeft: 3, color: '#ff0000' }}>
                {props.isRequired && `*`}
              </span>
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexStart>
        </BlockLeft>
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <UINumber
              type="number"
              onChange={props.onChange(props.name)}
              value={props.value}
              min={props.minLength}
              max={props.maxLength}
              width="4rem"
            />
          </WrapperDisable>
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  singleLineAddPersonalize: props => {
    return (
      <>
        <BlockLeft item sm={2} className={props.classes.title}>
          <WrapperCenterFlexEnd>
            <Title className={props.classes.title}>
              {props.label}
              {props.isRequired && !props.isViewMode && (
                <span style={{ marginLeft: 3, color: '#ff0000' }}> *</span>
              )}
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={9}>
          <WrapperDisable disabled={props.disabled}>
            <TinymceEditor
              {...props}
              typeComponent="input"
              onChange={props.onChange(props.name)}
              initData={props.value}
            />
          </WrapperDisable>
          <FormHelperText
            style={{ fontSize: '11px', marginTop: 4 }}
            id="component-helper-text"
            error={!!props.errors[0]}
          >
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  templateSetting: props => {
    const { onChange, name, value } = props;
    const { templates, selected } = value;

    const addMoreListMemoized = useMemo(() => {
      return Object.values(TEMPLATES)
        .filter(template => !templates.includes(template.value))
        .map(item => ({ label: item.label, id: item.value }));
    }, [templates]);

    const listMemoized = useMemo(() => {
      if (!Array.isArray(templates)) return [];

      return templates.map(templateId => {
        const template = TEMPLATES[templateId];

        return {
          label: template.label,
          id: template.value,
        };
      });
    }, [templates]);

    const handleAdd = newTemplate => {
      if (newTemplate) {
        onChange(name)(
          produce(value, draft => {
            draft.templates = [...templates, newTemplate];
          }),
        );
      }
    };

    const handleRemove = templateId => {
      onChange(name)(
        produce(value, draft => {
          const newTemplates = templates.filter(
            template => template !== templateId,
          );

          draft.templates = newTemplates;

          if (templateId === selected) {
            draft.selected = newTemplates[0] || null;
          }
        }),
      );
    };

    const handleSelect = templateId => {
      onChange(name)(
        produce(value, draft => {
          draft.selected = templateId;
        }),
      );
    };

    const callbackListTemplate = dataOut => {
      const { type = '', value: valueOut = {} } = dataOut || {};
      const templateId = _.get(valueOut, 'id', '');

      switch (type) {
        case 'ACTIVE_TEMPLATE': {
          if (templateId) {
            handleSelect(templateId);
          }
          break;
        }
        case 'REMOVE_TEMPLATE': {
          if (templateId) {
            handleRemove(templateId);
          }
          break;
        }
        case 'ADD_TEMPLATE': {
          if (templateId) {
            handleAdd(templateId);
          }
          break;
        }
        default: {
          break;
        }
      }
    };
    const disabledAdd = props.disabled || addMoreListMemoized.length === 0;

    return (
      <WrapperListTemplate>
        <ListTemplate
          list={listMemoized}
          addMoreList={addMoreListMemoized}
          activeId={selected}
          disabledAdd={disabledAdd}
          isShowBtnAdd={addMoreListMemoized.length > 0}
          isViewMode={props.design === 'preview'}
          callback={callbackListTemplate}
        />
      </WrapperListTemplate>
    );
  },
};

export const getTypeRender = ({
  inputType,
  inputFormat,
  canImportFromMediaLibrary,
  canAddPersonalization,
}) => {
  // console.log(
  //   'typeRender: ',
  //   inputType,
  //   inputFormat,
  //   canImportFromMediaLibrary,
  //   canAddPersonalization,
  // );
  let typeRender = 'singleLineText';
  if (inputType === 'multitext') {
    typeRender = 'multiLineText';
  } else if (inputType === 'keyvalue') {
    typeRender = 'keyvalue';
  } else if (inputType === 'select') {
    typeRender = 'selectDropdown';
  } else if (inputType === 'editor') {
    typeRender = 'editor';
  } else if (inputType === 'text') {
    if (inputFormat === 'password') {
      typeRender = 'password';
    } else if (canAddPersonalization) {
      typeRender = 'singleLineAddPersonalize';
    } else if (canImportFromMediaLibrary) {
      typeRender = 'imageUrl';
    }
  }

  // console.log('typeRender: ', typeRender);
  return typeRender;
};
