/* eslint-disable indent */
/* eslint-disable import/order */
/* eslint-disable arrow-body-style */
/* eslint-disable react/prop-types */
// Libraries
import React from 'react';
import _isEmpty from 'lodash/isEmpty';

// Components
import {
  UITextField,
  UITippy,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import Grid from '@material-ui/core/Grid';
import ToggleButton from 'components/Atoms/ToggleButton/index';
import UISelect from 'components/form/UISelectCondition';
import InputLanguage from 'components/common/InputLanguage';
import TextAreaBorder from 'components/Atoms/Text/TextAreaBorder';
import { getDefaultVal } from 'components/common/InputLanguage/utils';
import FormHelperText from '@material-ui/core/FormHelperText';
import ErrorOutlineIcon from '@material-ui/icons/ErrorOutline';
import TinymceEditor from 'components/common/UIEditorPersonalization/WrapperPersonalization';
import IconXlab from 'components/common/UIIconXlab';
import RadioBox from './Content/components/RadioBox';
import RadioLabel from './Content/components/RadioLabel';
import CheckboxLabel from './Content/components/CheckboxLabel';
import DnsRecords from './Content/components/DnsRecords';

// Styled
import {
  WrapperCenterFlexEnd,
  WrapperCenter,
  WrapperIcon,
  WrapperExtraBtn,
  ButtonPre,
} from './styles';
import { BlockContent, Desc, P } from './Content/styles';
import {
  BlockLeft,
  BlockRight,
  Title,
} from 'containers/Drawer/DrawerIntegration/components/Content/styled';

// Utils
import {
  getObjectPropSafely,
  safeParse,
  safeParseInt,
  validateEmail,
} from '../../../../../../../utils/web/utils';
import { EXTRA_EMAIL_FIELDS, mapLabelExtraEmail } from '../../../Create/utils';

// Translations
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../../../containers/Translate/util';

const flexStart = {
  alignItems: 'flex-start',
  justifyContent: 'flex-start',
  marginTop: 10,
};

const MAP_TRANSLATE = {
  nameEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EMPTY_NAME,
    `Name can't be empty`,
  ),
  inputEmpty: getTranslateMessage(
    TRANSLATE_KEY._NOTI_REQUIRED_INPUT,
    'Input can’t be empty',
  ),
};

const MAP_IDENTITY_STATUS = {
  PENDING: 'Pending',
  SUCCESS: 'Success',
  FAILED: 'Failed',
  TEMPORARY_FAILURE: 'Temporary failure',
  NOT_STARTED: 'Not started',
};

const LeftTitle = props => {
  const { classes = {}, isRequired, label, errors = [] } = props;
  return (
    <WrapperCenterFlexEnd>
      <Title className={classes.title}>
        {label}
        <span style={{ marginLeft: 3, color: '#ff0000' }}>
          {isRequired && `*`}
        </span>
      </Title>
      {!!errors[0] && <div style={{ height: '1.5rem', minWidth: '1.75rem' }} />}
    </WrapperCenterFlexEnd>
  );
};

export const MAP_VALIDATE = {
  preventValidate: () => ({ errors: [], isValidate: true }),
  singleLineText: ({ value, isRequired, maxLength }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    } else if (value.trim().length === 0 && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.spaceError];
    } else if (maxLength !== null && value.length > maxLength) {
      isValidate = false;
      errors = [`Invalid: maximum ${maxLength} characters`];
    }
    return { errors, isValidate };
  },
  multiLineText: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  selectDropdown: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(value) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  email: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
    } else {
      isValidate = validateEmail(value);
      errors = isValidate ? [] : ['Invalid email'];
    }
    return { errors, isValidate };
  },
  default: () => ({ errors: [], isValidate: true }),
  multiLangInput: ({ value, isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (_isEmpty(getDefaultVal(value).trim()) && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
  singleLineAddPersonalize: ({ value = '', isRequired }) => {
    let [isValidate, errors] = [true, []];
    if (value.trim() === '' && isRequired) {
      isValidate = false;
      errors = [MAP_TRANSLATE.nameEmpty];
    }
    return { errors, isValidate };
  },
};

export const MAP_INPUT_TYPE = {
  singleLineText: props => {
    const { extraEmailBtn = [], infoCreateDomain = {}, design = '' } = props;
    const isChannelEmail = safeParseInt(props.channelId) === 1;
    const isExtraEmailField =
      isChannelEmail && Object.values(EXTRA_EMAIL_FIELDS).includes(props.name);

    // Force hide some field of channel email
    if (
      isExtraEmailField &&
      Array.isArray(extraEmailBtn) &&
      extraEmailBtn.includes(props.name)
    )
      return null;

    return (
      <>
        <BlockLeft
          item
          sm={props.isGridV2 ? 4 : 2}
          className={props.classes.title}
          style={{ display: props.isHidden ? 'none' : 'block' }}
        >
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <BlockRight
          item
          sm={10}
          style={{
            position: isExtraEmailField ? 'relative' : 'unset',
            display: props.isHidden ? 'none' : 'block',
          }}
        >
          <UITextField
            id={props.name}
            value={props.value}
            onChange={props.onChange(props.name)}
            firstText={
              props.errors[0] ||
              (props.name === 'emailIdentity' &&
                design === 'create' &&
                infoCreateDomain &&
                infoCreateDomain.errorIdentity)
            }
            placeholder={props.placeholder}
            textFieldProps={{
              disabled: props.disabled,
              size: 'small',
              multiline: false,
              rowsMax: 1,
              className: 'width-100',
              // id: 'standard-basic',
              error:
                !!props.errors[0] ||
                (props.name === 'emailIdentity' &&
                  design === 'create' &&
                  infoCreateDomain &&
                  !infoCreateDomain.isValid),
            }}
          />
          {isChannelEmail && isExtraEmailField && (
            <WrapperDisable disabled={props.disabled}>
              <WrapperIcon
                style={{ bottom: '0px', right: '-35px' }}
                onClick={() => {
                  if (typeof props.onChangeExtraEmailBtn === 'function') {
                    props.onChangeExtraEmailBtn({
                      type: 'REMOVE',
                      btnName: props.name,
                    });
                  }
                }}
              >
                <IconXlab name="close" color="#005eb8" fontSize="18px" />
              </WrapperIcon>
            </WrapperDisable>
          )}
        </BlockRight>
      </>
    );
  },
  selectDropdown: props => {
    return (
      <>
        <BlockLeft item sm={2}>
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <BlockRight item sm={9}>
          <UISelect
            onlyParent
            use="tree"
            isSearchable
            options={props.options}
            value={props.value}
            onChange={props.onChange(props.name)}
            placeholder={getTranslateMessage(
              TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
              'Select an item',
            )}
            fullWidthPopover
            disabled={props.disabled}
          />
          <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
            {props.errors}
          </FormHelperText>
        </BlockRight>
      </>
    );
  },
  singleLineAddPersonalize: props => {
    const { extraEmailBtn = [] } = props;
    const isChannelEmail = safeParseInt(props.channelId) === 1;

    return (
      <>
        <BlockLeft item sm={2} className={props.classes.title}>
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <BlockRight item sm={9} className="pos-relative">
          <WrapperDisable disabled={props.disabled}>
            <TinymceEditor
              {...props}
              typeComponent="input"
              onChange={props.onChange(props.name)}
              initData={props.initValue}
            />
            {isChannelEmail &&
              props.name === 'email' &&
              Array.isArray(extraEmailBtn) && (
                <WrapperExtraBtn>
                  {extraEmailBtn.map(each => (
                    <ButtonPre
                      key={each}
                      iconName="add"
                      iconSize="20px"
                      reverse
                      theme="outline"
                      isNoBackround
                      style={{ border: 'none' }}
                      onClick={() => {
                        if (typeof props.onChangeExtraEmailBtn === 'function') {
                          props.onChangeExtraEmailBtn({
                            type: 'ADD',
                            btnName: each,
                          });
                        }
                      }}
                    >
                      {mapLabelExtraEmail(each)}
                    </ButtonPre>
                  ))}
                </WrapperExtraBtn>
              )}
          </WrapperDisable>
        </BlockRight>
      </>
    );
  },
  textarea: props => {
    return (
      <>
        <BlockLeft
          item
          sm={props.isGridV2 ? 4 : 2}
          className={props.classes.title}
          style={{ display: props.isHidden ? 'none' : 'block' }}
        >
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <BlockRight item sm={10} className="d-flex flex-column">
          <WrapperDisable disabled={props.disabled}>
            <TextAreaBorder
              aria-label="minimum height"
              value={props.value}
              placeholder={props.placeholder}
              onChange={e => props.onChange(props.name)(e.target.value)}
              style={{ width: '100%' }}
            />
          </WrapperDisable>
          <Desc className="m-top-1">
            Private key must use from 1024-bit up to 2048-bit RSA encryption,
            and be PEM-encoded (base64).
          </Desc>
        </BlockRight>
      </>
    );
  },
  checkbox: props => {
    return (
      <>
        <BlockLeft item sm={2}>
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <BlockRight item sm={9}>
          <CheckboxLabel
            checked={!!props.value}
            onChange={props.onChange(props.name)}
            label={props.title}
            disabled={props.disabled}
          />
          {props.description ? (
            <Desc className="m-top-1">{props.description}</Desc>
          ) : null}
        </BlockRight>
      </>
    );
  },
  multiLangInput: props => {
    return (
      <>
        <BlockLeft item sm={2} className={props.classes.title}>
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <BlockRight item sm={9}>
          <InputLanguage
            isShowLabelFlagSelected
            disabled={props.disabled}
            initData={props.initValue}
            onChange={props.onChange(props.name)}
            errors={props.errors}
          />
        </BlockRight>
      </>
    );
  },
  selectRadioBox: props => {
    return (
      <>
        <BlockLeft item sm={2}>
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <BlockRight item sm={10}>
          <div className="d-flex">
            {props.options.map((option, index) => (
              <RadioBox
                key={option.value}
                value={option.value}
                checked={props.value === option.value}
                label={option.label}
                onChange={props.onChange(props.name)}
                description={option.description}
                disabled={option.disabled || props.disabled}
                style={{ marginRight: index < props.options.length ? 10 : 0 }}
              />
            ))}
          </div>
        </BlockRight>
      </>
    );
  },
  selectRadio: props => {
    return (
      <>
        <BlockLeft item sm={2}>
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <BlockRight item sm={10}>
          {props.description ? (
            <Desc className="m-bottom-1">{props.description}</Desc>
          ) : null}
          <div className="d-flex flex-column">
            {props.options.map((option, index) => (
              <RadioLabel
                key={option.value}
                value={option.value}
                checked={props.value === option.value}
                label={option.label}
                onChange={props.onChange(props.name)}
                disabled={option.disabled || props.disabled}
                style={{
                  marginBottom: index < props.options.length ? 5 : 0,
                  marginLeft: 0,
                }}
              />
            ))}
          </div>
        </BlockRight>
      </>
    );
  },
  selectorName: props => {
    const domainName = getObjectPropSafely(
      () => props.dataConfig.emailIdentity.value,
      '',
    );
    return (
      <>
        <BlockLeft
          item
          sm={2}
          className={props.classes.title}
          style={{ display: props.isHidden ? 'none' : 'block' }}
        >
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <BlockRight item sm={10} container>
          <div style={{ width: '100%' }}>
            <UITextField
              id={props.name}
              value={props.value}
              onChange={props.onChange(props.name)}
              firstText={props.errors[0]}
              placeholder={props.placeholder}
              textFieldProps={{
                disabled: props.disabled,
                size: 'small',
                multiline: false,
                rowsMax: 1,
                className: 'width-100',
                error: !!props.errors[0],
              }}
            />
            <Desc className="m-top-1">Maximum length of 63 characters.</Desc>
          </div>
          <P className="m-left-2 m-top-4">
            ._domainkey.{domainName || 'example.com'}
          </P>
        </BlockRight>
      </>
    );
  },
  text: props => {
    return (
      <>
        <BlockLeft item xs={2}>
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <BlockRight item xs={10}>
          <P style={{ paddingTop: 4 }}>{MAP_IDENTITY_STATUS[props.value]}</P>
        </BlockRight>
      </>
    );
  },
  tableState: props => {
    return (
      <>
        <BlockLeft item xs={2}>
          <LeftTitle
            classes={props.classes}
            isRequired={props.isRequired}
            label={props.label}
            errors={props.errors}
          />
        </BlockLeft>
        <Grid item xs={10} className="pos-relative" style={{ maxWidth: 999 }}>
          <DnsRecords data={props.value} />
        </Grid>
      </>
    );
  },
};

export const getTypeRender = ({
  inputType,
  inputFormat,
  canImportFromMediaLibrary,
  canAddPersonalization,
}) => {
  let typeRender = 'singleLineText';
  if (inputType === 'multitext') {
    typeRender = 'multiLineText';
  } else if (inputType === 'keyvalue') {
    typeRender = 'keyvalue';
  } else if (inputType === 'editor') {
    typeRender = 'editor';
  } else if (inputType === 'text') {
    if (inputFormat === 'password') {
      typeRender = 'password';
    } else if (canAddPersonalization) {
      typeRender = 'singleLineAddPersonalize';
    } else if (canImportFromMediaLibrary) {
      typeRender = 'imageUrl';
    }
  }

  return typeRender;
};

const renderToggleButton = data => {
  const {
    label = '',
    name = '',
    value = false,
    isHiddenLabel = false,
    styleLabel,
    handleClick = () => {},
    ...restProps
  } = data;

  const styles = { marginLeft: 10, fontSize: '12px', color: '#222222' };

  return (
    <WrapperCenter style={flexStart}>
      <ToggleButton
        {...restProps}
        isToggle={value}
        handleClick={() => handleClick({ name, value })}
      />
      {!isHiddenLabel && (
        <span style={!styleLabel ? styles : styleLabel}>{label}</span>
      )}
    </WrapperCenter>
  );
};

export const MAP_COMPONENT_ALIAS = {
  destinationCatalog: props => {
    return (
      <>
        <BlockLeft item sm={2}>
          <WrapperCenterFlexEnd>
            <Title className={props.classes.title}>
              {props.label}
              <span style={{ marginLeft: 3, color: '#ff0000' }}>
                {props.isRequired && `*`}
              </span>
            </Title>
            {!!props.errors[0] && (
              <div style={{ height: '1.75rem', width: '100%' }} />
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={9}>
          <div style={{ position: 'relative' }}>
            <UISelect
              // display="label"
              onlyParent
              use="tree"
              isSearchable
              options={props.options}
              value={props.value}
              onChange={props.onChange(props.name)}
              placeholder={getTranslateMessage(
                TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                'Select an item',
              )}
              fullWidthPopover
              disabled={props.disabled}
            />
            <div style={{ position: 'absolute', right: '-2rem', top: '0rem' }}>
              <UITippy content={props.tooltip || ''} arrow distance={10}>
                <ErrorOutlineIcon />
              </UITippy>
            </div>
          </div>
          <FormHelperText id="component-helper-text" error={!!props.errors[0]}>
            {props.errors}
          </FormHelperText>
        </BlockRight>
        {/* <Grid item sm={1}> */}

        {/* </Grid> */}
      </>
    );
  },
  actionToggle: props => {
    const {
      isHiddenTitle = false,
      label = '',
      subLabel = '',
      toggleProps = {},
    } = props;

    return (
      <>
        <BlockLeft item sm={props.isGridV2 ? 4 : 2}>
          <WrapperCenterFlexEnd>
            {!isHiddenTitle && (
              <Title>
                {label}
                <span style={{ marginLeft: 3, color: '#ff0000' }}>
                  {props.isRequired && `*`}
                </span>
                <br />
                {subLabel}
              </Title>
            )}
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={10}>
          <WrapperCenter style={{ marginTop: 2 }}>
            {renderToggleButton(toggleProps)}
          </WrapperCenter>
        </BlockRight>
      </>
    );
  },
  behavior: props => {
    const { label = '' } = props;

    return (
      <>
        <BlockLeft item sm={2}>
          <WrapperCenterFlexEnd>
            <Title>{label}</Title>
          </WrapperCenterFlexEnd>
        </BlockLeft>
        <BlockRight item sm={10}>
          <BlockContent container>
            <Grid item sm={12}>
              <Title>
                {getTranslateMessage(
                  TRANSLATE_KEY._DES_MATCHING_STRATEGY,
                  'Matching Strategy',
                )}
              </Title>
            </Grid>
            <Grid item sm={12}>
              <WrapperDisable disabled={props.disabled}>
                <UISelect
                  // display="label"
                  onlyParent
                  use="tree"
                  isSearchable={false}
                  options={safeParse(
                    props.settings.matchingStrategy.options,
                    [],
                  )}
                  value={safeParse(props.settings.matchingStrategy.value, {})}
                  onChange={data => {
                    props.onChange(props.name)({
                      ...data,
                      type: 'matchingStrategy',
                    });
                  }}
                  placeholder={getTranslateMessage(
                    TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                    'Select an item',
                  )}
                  fullWidthPopover
                  disabled={false}
                />
              </WrapperDisable>
            </Grid>
            <Grid item sm={12}>
              <Title>
                {getTranslateMessage(
                  TRANSLATE_KEY._DES_ACTION_STRATEGY,
                  'Action Strategy',
                )}
              </Title>
            </Grid>
            <Grid item sm={12}>
              <WrapperDisable disabled={props.disabled}>
                <UISelect // display="label" onlyParent
                  use="tree"
                  isSearchable={false}
                  options={safeParse(props.settings.actionStrategy.options, [])}
                  value={safeParse(props.settings.actionStrategy.value, {})}
                  onChange={data => {
                    props.onChange(props.name)({
                      ...data,
                      type: 'actionStrategy',
                    });
                  }}
                  placeholder={getTranslateMessage(
                    TRANSLATE_KEY._USER_GUIDE_SELECT_ITEM,
                    'Select an item',
                  )}
                  fullWidthPopover
                  disabled={false}
                />
              </WrapperDisable>
            </Grid>
          </BlockContent>
        </BlockRight>
      </>
    );
  },
};

export const createTooltip = ({
  basicSettings,
  settings,
  basicInputs,
  inputs,
}) => (
  <>
    <p>
      <b>Config Field: </b>
      {basicSettings.map(each => settings[each].label).join(', ')}
    </p>
    <p>
      <b>Content Field: </b>
      {basicInputs.map(each => inputs[each].label).join(', ')}
    </p>
  </>
);
