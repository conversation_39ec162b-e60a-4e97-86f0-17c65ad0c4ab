/* eslint-disable camelcase */
import { useCallback, useEffect, useRef } from 'react';
import isEmpty from 'lodash/isEmpty';
import {
  init,
  reset,
  getList,
  getListDone,
  initDone,
  getDetailDone,
} from '../../../../../../../../../redux/actions';
import SelectorService from '../../../../../../../../../services/Selector';
import { buildExtendUrlGetSource, getEAEC } from './utils';
import { getInputLookupFromRule } from '../../../../../../../../../components/common/ConditionAttributes/utils';
import { AM_TYPE } from '../../../../../../../../../services/Abstract.data';

export const useInit = ({ reducer, dispatch, componentKey }) => {
  const sendRequest = useCallback(
    async params => {
      try {
        // Xóa dữ liệu cũ và init dữ liệu mới tránh cache dữ liệu
        await dispatch.put(reset(''));
        await dispatch.put(init('', params));

        // Lookup cho các fields Event, Source, Attribute trường hợp các input này bị xóa hoặc unassign khỏi chủ thể gốc
        // Phần lookup đọc thêm trong README.md
        const {
          activeRow,
          globalData: { insightPropertyIds },
        } = params;
        const {
          event = {},
          attribute = {},
          source = [],
          condition = {},
        } = activeRow;
        const { comp_attr } = getInputLookupFromRule(condition.OR);

        if (!isEmpty(event)) {
          let paramsEvent = {};
          let paramsSource = {};
          let paramsAttribute = {};
          let paramsCondition = {};
          let resEvent = {};
          let resSource = {};
          let resAttribute = {};
          let resCondition = {};
          if (
            !isEmpty(event) &&
            !!event.eventActionId &&
            !!event.eventCategoryId
          ) {
            paramsEvent = {
              data: {
                insightPropertyIds: insightPropertyIds.concat(source),
                events: [
                  {
                    eventCategoryId: event.eventCategoryId,
                    eventActionId: event.eventActionId,
                  },
                ],
              },
            };
          }
          if (!isEmpty(event) && !isEmpty(source)) {
            paramsSource = {
              insightPropertyIds: buildExtendUrlGetSource(source),
            };
          }
          // debugger;
          if (
            !isEmpty(attribute) &&
            (attribute.eventPropertyName || attribute.encryptCode) &&
            event.eventActionId &&
            event.eventCategoryId
          ) {
            //
            paramsAttribute = {
              insightPropertyId: buildExtendUrlGetSource(insightPropertyIds),
              eventCategoryId: event.eventCategoryId,
              eventActionId: event.eventActionId,
              data: {
                eventIds: [
                  {
                    eventPropertyName:
                      attribute.eventPropertyName || attribute.encryptCode,
                    itemTypeId:
                      parseInt(attribute.itemTypeId) === 0
                        ? null
                        : attribute.itemTypeId,
                  },
                ],
              },
            };
          }
          if (!isEmpty(comp_attr)) {
            paramsCondition = {
              insightPropertyId: buildExtendUrlGetSource(insightPropertyIds),
              eventCategoryId: event.eventCategoryId,
              eventActionId: event.eventActionId,
              data: {
                eventIds: comp_attr.map(each => ({
                  eventPropertyName: each.itemPropertyName,
                  itemTypeId:
                    parseInt(each.itemTypeId) === 0 ? null : each.itemTypeId,
                })),
              },
            };
          }
          [resEvent, resSource, resAttribute, resCondition] = await Promise.all(
            [
              isEmpty(paramsEvent)
                ? paramsEvent
                : SelectorService.event.lookupByIdsV2(paramsEvent),
              isEmpty(paramsSource)
                ? paramsSource
                : SelectorService.source.lookupByIds(paramsSource),
              isEmpty(paramsAttribute)
                ? paramsAttribute
                : SelectorService.attribute.lookupByIds(paramsAttribute),
              isEmpty(paramsCondition)
                ? paramsCondition
                : SelectorService.attribute.lookupByIds(paramsCondition),
            ],
          );
          dispatch.put(
            getDetailDone(`@@LOOKUP_DATA`, {
              resEvent: resEvent.data || [],
              resSource: resSource.data || [],
              resAttribute: resAttribute.data || {},
              resCondition: resCondition.data || {},
              componentKey,
              newAttribute: attribute || {},
            }),
          );
        }
        dispatch.put(initDone('', params));
      } catch (err) {
        throw err;
      }
    },
    [reducer, dispatch, componentKey],
  );

  return [sendRequest];
};
export const useFetchEvent = ({ reducer, dispatch }) => {
  const sendRequest = useCallback(async () => {
    try {
      dispatch.put(getList(`@@EVENT`));
      const { insightPropertyIds, itemTypeIds, modelType } = reducer;
      let params = {};
      let res = {};
      if (
        parseInt(modelType) === AM_TYPE.EVENT_ORIENTED ||
        parseInt(modelType) === AM_TYPE.VISITOR_PROFILE ||
        parseInt(modelType) === AM_TYPE.CUSTOMER_PROFILE
      ) {
        // Event Oriented AM thì Event phục thuộc vào General Source (insightPropertyIds)
        params = {
          data: {
            insightPropertyIds,
            eventPropertyInfo: [],
          },
        };
        res = await SelectorService.event.getListBySource(params);
      } else if (parseInt(modelType) === AM_TYPE.OBJECT_ORIENTED) {
        // BO Oriented AM thì Event phục thuộc vào General BO (itemTypeIds)
        params = {
          data: {
            itemTypeIds,
          },
        };
        res = await SelectorService.event.getListByBO(params);
      }
      const { data = [] } = res;
      dispatch.put(getListDone(`@@EVENT`, data));
    } catch (err) {
      throw err;
    }
  }, [reducer, dispatch]);

  return [sendRequest];
};

export const useFetchSource = ({ reducer, dispatch }) => {
  const sendRequest = useCallback(async () => {
    try {
      await dispatch.put(getList(`@@SOURCE`));
      const {
        dataConfig: { event },
        insightPropertyIds,
        itemTypeIds,
        modelType,
      } = reducer;
      const { eventActionId, eventCategoryId } = event.value;
      let params = {};
      let res = {};
      if (
        parseInt(modelType) === AM_TYPE.EVENT_ORIENTED ||
        parseInt(modelType) === AM_TYPE.VISITOR_PROFILE ||
        parseInt(modelType) === AM_TYPE.CUSTOMER_PROFILE
      ) {
        // Event Oriented AM thì Source phục thuộc vào Event và giới hạn bởi General Source (insightPropertyIds)
        params = {
          data: {
            insightPropertyIds,
            eventConfig: getEAEC(event.value),
          },
          // sourceUrl: buildExtendUrlGetSource(insightPropertyIds),
          // eventActionId,
          // eventCategoryId,
        };
        res = await SelectorService.source.getSourceByEventAndSource(params);
      } else if (parseInt(modelType) === AM_TYPE.OBJECT_ORIENTED) {
        // BO Oriented AM thì Source phục thuộc vào Event và phải có liên kết tới General BO (itemTypeIds)
        params = {
          itemTypeIdsUrl: buildExtendUrlGetSource(itemTypeIds),
          eventActionId,
          eventCategoryId,
        };
        res = await SelectorService.source.getSourceByEventAndBO(params);
      }
      // console.log('res', res);
      const { data = [] } = res;
      dispatch.put(getListDone(`@@SOURCE`, data));
    } catch (err) {
      throw err;
    }
  }, [reducer, dispatch]);

  return [sendRequest];
};

export const useFetchAttribute = ({ reducer, dispatch }) => {
  const sendRequest = useCallback(async () => {
    try {
      await dispatch.put(getList(`@@ATTRIBUTE`));
      const {
        dataConfig: { event, source },
      } = reducer;
      const { eventActionId, eventCategoryId } = event.value;
      const insightPropertyIds = source.value;
      // Attribute chỉ phục thuộc vào Event và Source của Fields
      const params = {
        data: {
          insightPropertyIds,
          eventConfig: getEAEC(event.value),
        },
        // sourceUrl: buildExtendUrlGetSource(insightPropertyIds),
        // eventActionId,
        // eventCategoryId,
        // objectType: 'ANALYTIC_MODEL',
      };
      const res = await SelectorService.eventAttribute.getListAttrMultiEvent(
        params,
      );
      const { data = [] } = res;
      dispatch.put(getListDone(`@@ATTRIBUTE`, data));
    } catch (err) {
      throw err;
    }
  }, [reducer, dispatch]);

  return [sendRequest];
};

export const useFetchData = ({ reducer, dispatch, componentKey }) => {
  const [fetchEvent] = useFetchEvent({
    reducer,
    dispatch,
  });
  const [fetchSource] = useFetchSource({
    reducer,
    dispatch,
  });
  const [fetchAttribute] = useFetchAttribute({
    reducer,
    dispatch,
  });
  const [onInit] = useInit({
    reducer,
    dispatch,
    componentKey,
  });
  return { fetchEvent, fetchSource, fetchAttribute, onInit };
};
