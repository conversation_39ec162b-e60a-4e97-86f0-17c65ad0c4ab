/* eslint-disable no-unused-expressions */
/* eslint-disable indent */
/* eslint-disable react/prop-types */
import React, { memo, useState, useMemo, useCallback } from 'react';
import Grid from '@material-ui/core/Grid';
import {
  UIButton,
  UICheckbox,
  UIWrapperDisable as WrapperDisable,
  UILoading as Loading,
  UILoading,
  UIWrapperDisable,
} from '@xlab-team/ui-components';
import ErrorBoundary from 'components/common/ErrorBoundary';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import {
  Preview,
  SettingInputTypeDatePicker,
  SettingInputTypeRadio,
  SettingsInputType,
} from '@antscorp/form-design';
import '@antscorp/form-design/main.css';
// import Box from 'components/Templates/LayoutContent/Box';
import WarningIcon from '@material-ui/icons/WarningOutlined';
import ChipSelect from 'components/common/ChipSelect';
import colors from 'utils/colors';
import { getTranslateMessage } from 'containers/Translate/util';
import TRANSLATE_KEY from 'messages/constant';

import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Paper,
  Typography,
} from '@material-ui/core';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import _, { get, uniq, uniqBy } from 'lodash';
import moment from 'moment';
import classnames from 'classnames';
import {
  buildDisableHours,
  getDateFromComputeSchedule,
} from 'components/Organisms/CalendarTable/utils';
import { getObjectPropSafely } from '../../../../../../../../../../utils/common';
import ConfigFieldsCustomFunction from './ConfigFieldsCustomFunction';
import {
  DivMessageComputing,
  useStyles,
  WrapperLoading,
  RootContainer,
  WrapperDialog,
  WrapperTitleDialog,
  WrapperAssociate,
  StyledWrapperDisable,
  WrapperSettingsInputType,
} from './styles';
import { MAP_DATA_DATE_TIME } from '../../../../../../../../MarketingHub/Journey/Create/Content/Nodes/constant';
import {
  ARCHIVE_STATUS,
  TYPE_ATTRIBUTE,
  CONFIG_OBJECT_STATUS,
} from '../../../../../../../../../../utils/constants';
import ModalEncrypt from '../../../../../../../../../../containers/modals/ModalEncrypt';
import useToggle from '../../../../../../../../../../hooks/useToggle';
import ModalWarningEncrypt from '../../../../../../../../../../containers/modals/ModalWarningEncrypt';
import { checkPermisionDecrypt } from '../utils';
import { TitleViewMode } from './ConfigFieldsCustomFunction/styles';
import { getErrorMessageByCode } from '../../../../../../../../MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/helper';
import { fieldGroup, getFuncSnippetsFormula } from '../CustomAttribute/utils';
import ConfigFieldsCustomAttribute from '../../../../../../../EventSources/EventAttributes/Create/Content/ConfigFieldsCustomAttribute';

const itemStyle = {
  padding: '5px 0',
};
const MAP_TITLE = {
  titlGeneralInfomation: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_INFORMATION,
    'General Information',
  ),
  titlSetting: getTranslateMessage(
    TRANSLATE_KEY._TAB_DESTINATION_SETTING,
    'Setting',
  ),
  titlComputationSchedule: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Computation schedule',
  ),
  notiAlertEdit: getTranslateMessage(
    TRANSLATE_KEY._NOTI_ATTRIBUTE_BEING_COMPUTED,
    "This attribute is being computed, you can't change its condition until the building process finished",
  ),
  notificationSetup: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Notification Setup',
  ),
  archive: getTranslateMessage(
    TRANSLATE_KEY.KHONG_CO,
    'This computed attribute is archived, you can’t change its condition',
  ),
};

const InputComponent = memo(props => {
  // console.log('InputComponent', props);
  return (
    <Grid container style={itemStyle}>
      {props.componentEl(props)}
    </Grid>
  );
});
function FieldContent(props) {
  const classes = useStyles();
  const {
    customReducer,
    onChangeData,
    Footer,
    dispatchAction,
    callback,
    globalData,
    callbackInputVia,
    eventValue,
    onControlModal,
    createCopy,
    user,
    isViewMode,
    errors = [],
  } = props;
  const { activeRow, copyId } = globalData;
  const { process_status, item_property_name } = activeRow;
  const { decryptPermission } = user;
  const isCheckPermision = checkPermisionDecrypt(decryptPermission, activeRow);
  const [open, setOpen] = useState(false);
  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const {
    isLoading,
    dataConfig,
    isInitDone,
    dataInputVia = {},
    customInputs = [],
    isOpenEncry = false,
    fomularFunction = [],
  } = customReducer;
  // console.log('dataInputVia', dataInputVia, dataConfig);

  const {
    infoFields,
    configFields = [],
    settingFields = [],
    additionFields = [],
    computationScheduleFields = [],
    notificationFields = [],
    isInputViaUI,
    event,
    formula,
  } = dataConfig;
  const isEmptyDataInputVia = _.isEmpty(dataInputVia);

  const isShowPreviewInputVia =
    isInputViaUI &&
    isInputViaUI.value &&
    !isEmptyDataInputVia &&
    dataConfig.selectViaInputType &&
    ['datePicker', 'radio', 'dropdown'].includes(
      dataConfig.selectViaInputType.value.value,
    );

  const isShowSettingsInputVia =
    isInputViaUI &&
    isInputViaUI.value &&
    dataConfig &&
    dataConfig.selectViaInputType;

  const callbackComputationSchedule = (type, data) => {
    switch (type) {
      case 'TOGGLE_MODAL_CALENDAR_TABLE': {
        const modalCalendarTable = getObjectPropSafely(
          () => dataConfig.modalCalendarTable.value,
          {},
        );
        const {
          dataEventsGroupByDate,
          limitHour,
          timeRange,
        } = modalCalendarTable;

        let newModalCalendarTable = {
          ...modalCalendarTable,
          isOpen: !modalCalendarTable.isOpen,
        };

        // call khi open modal
        if (!modalCalendarTable.isOpen) {
          const computeSchedule = getObjectPropSafely(
            () => dataConfig.updateSchedule.value,
            {},
          );

          // rebuild disableTimes for current computeSchedule
          const { selectedTime, currentDate } = getDateFromComputeSchedule(
            computeSchedule,
          );

          const disableHours = buildDisableHours({
            computeSchedule,
            dataEventsGroupByDate,
            limitHour,
            timeRange,
          });

          newModalCalendarTable = {
            ...newModalCalendarTable,
            disableTimes: disableHours,
            currentDate,
            selectedTime,
            isFetchPlans: true,
          };
        }
        onChangeData('modalCalendarTable')({
          ...newModalCalendarTable,
        });
        break;
      }
      default:
        break;
    }
  };

  const renderComputationSchedule = () => {
    const computeSchedule = getObjectPropSafely(
      () => dataConfig.updateSchedule.value,
      {},
    );
    // console.log({ computationScheduleFields, dataConfig });
    return (
      <Grid
        container
        className={classnames(
          'border-all m-bottom-3 bs-all bg-form pos-relative',
          {
            setting: props.isViewMode,
          },
        )}
      >
        <UILoading isLoading={computeSchedule.isLoading} />
        {isViewMode ? (
          <TitleViewMode className="p-top-4">
            {MAP_TITLE.titlComputationSchedule}
          </TitleViewMode>
        ) : (
          <Grid item xs={2} className="p-all-4 p-top-7">
            {MAP_TITLE.titlComputationSchedule}
          </Grid>
        )}
        <Grid
          item
          sm={isViewMode ? 12 : 10}
          className={classnames('p-top-4 p-right-4 p-bottom-4', {
            'p-left-4': !isViewMode,
          })}
        >
          {computationScheduleFields.map(
            each =>
              dataConfig[each] && (
                <InputComponent
                  {...dataConfig[each]}
                  onChange={onChangeData}
                  key={each}
                  dataConfig={dataConfig}
                  callback={callbackComputationSchedule}
                  isViewMode={isViewMode}
                />
              ),
          )}
        </Grid>
      </Grid>
    );
  };

  const selectViaInputTypeValue = get(
    dataInputVia,
    get(dataConfig, 'selectViaInputType.value.value', ''),
    {},
  );
  const {
    isAssociate = false,
    isMultiSelect = false,
    associateWith = [],
    value: selectViaInputValue = [],
  } = selectViaInputTypeValue;

  const associateOptions = useMemo(() => {
    if (dataInputVia.type === 'datePicker') return [];

    return customInputs
      .filter(
        ({ itemPropertyName, inputViaUiValue }) =>
          inputViaUiValue.input_type === 'dropdown' &&
          itemPropertyName !== item_property_name && // filter current attribute
          itemPropertyName !== 'dat_client', // filter error attributes
      )
      .map(({ itemPropertyName, translateLabel, inputViaUiValue, status }) => {
        const inputViaUiValueOptions = inputViaUiValue.value || [];

        // luu lai cac option da bi xoa khoi parent attributes
        const selectedAssociates = [];
        selectViaInputValue.forEach(({ associates }) => {
          if (typeof associates === 'object') {
            Object.entries(associates).forEach(([k, v]) => {
              if (k === itemPropertyName) {
                selectedAssociates.push({
                  value: v[0],
                  label: v[0],
                });
              }
            });
          }
        });

        const uniqInputViaValue = uniqBy(
          [...inputViaUiValueOptions, ...selectedAssociates],
          'value',
        );

        const options = uniqInputViaValue.map(({ value, label }) => {
          const isRemove =
            inputViaUiValueOptions.findIndex(
              option => option.value === value,
            ) === -1;

          // settings attribute children
          return {
            value,
            label,
            errorMsg:
              // uu tien chipColor nen check status
              +status === CONFIG_OBJECT_STATUS.ENABLE && isRemove
                ? "This value isn't available"
                : '',
            chipColor:
              +status === CONFIG_OBJECT_STATUS.DISABLE ? '#f0f0f0' : '',
          };
        });

        return {
          value: itemPropertyName,
          label: translateLabel,
          options,
          errorMsg:
            +status === CONFIG_OBJECT_STATUS.DISABLE
              ? "This attribute isn't available"
              : '',
        };
      });
  }, [customInputs, item_property_name, dataInputVia.type]);

  const onChangeInputVia = values => {
    callbackInputVia('SETTING_INPUT_TYPE', {
      ...values,
    });
  };

  const renderConfigFields = () => {
    if (typeRender === 'custom_function') {
      return (
        <ConfigFieldsCustomFunction
          globalData={globalData}
          configFields={configFields}
          dataConfig={dataConfig}
          onChangeData={onChangeData}
          InputComponent={InputComponent}
          isViewMode={isViewMode}
        />
      );
    }
    if (typeRender === 'custom_attribute' && configFields.includes('formula')) {
      return (
        <Grid
          container
          className={classnames('border-all m-bottom-3 bg-form bs-all', {
            setting: isViewMode,
          })}
        >
          {isViewMode ? (
            <TitleViewMode className="p-top-4">
              {MAP_TITLE.titlSetting}
            </TitleViewMode>
          ) : (
            <Grid item xs={!isViewMode && 2} className="p-all-4 p-top-7">
              {MAP_TITLE.titlSetting}
            </Grid>
          )}
          <Grid
            item
            xs={isViewMode ? 12 : 10}
            className={classnames(' p-top-4 p-bottom-4 p-right-8', {
              'p-left-4': !props.isViewMode,
            })}
          >
            {configFields.map(each => {
              if (!fieldGroup.includes(each)) {
                return (
                  <InputComponent
                    {...dataConfig[each]}
                    onChange={onChangeData}
                    key={each}
                    isViewMode={isViewMode}
                  />
                );
              }
            })}
            <Grid container>
              <Grid style={{ marginTop: '15px' }} item xs={9}>
                {dataConfig.configFields.map(each => {
                  if (fieldGroup.includes(each)) {
                    return (
                      <InputComponent
                        {...dataConfig[each]}
                        girdContent={8}
                        girdLabel={4}
                        onChange={onChangeData}
                        key={each}
                        classes={classes}
                        snippets={[...getFuncSnippetsFormula(fomularFunction)]}
                        isViewMode={isViewMode}
                      />
                    );
                  }
                })}
              </Grid>
              {dataConfig.configFields.includes('formula') && !isViewMode && (
                <Grid style={{ marginTop: '15px' }} item xs={3}>
                  <ConfigFieldsCustomAttribute
                    configFields={dataConfig.configFields}
                    dataConfig={dataConfig}
                    onChangeData={onChangeData}
                    InputComponent={InputComponent}
                    fomularFunction={fomularFunction}
                  />
                </Grid>
              )}
            </Grid>
          </Grid>
        </Grid>
      );
    }
    return (
      <Grid
        container
        className={classnames('border-all m-bottom-3 bg-form bs-all', {
          setting: isViewMode,
        })}
      >
        {isViewMode ? (
          <TitleViewMode className="p-top-4">
            {MAP_TITLE.titlSetting}
          </TitleViewMode>
        ) : (
          <Grid item xs={!isViewMode && 2} className="p-all-4 p-top-7">
            {MAP_TITLE.titlSetting}
          </Grid>
        )}
        <Grid
          item
          xs={isViewMode ? 12 : 10}
          className={classnames(' p-top-4 p-bottom-4 p-right-8', {
            'p-left-4': !props.isViewMode,
          })}
        >
          {configFields.map(
            each =>
              dataConfig[each] && (
                <InputComponent
                  {...dataConfig[each]}
                  onChange={onChangeData}
                  key={each}
                  isViewMode={isViewMode}
                />
              ),
          )}

          {dataConfig.selectViaInputType &&
            dataConfig.selectViaInputType.value.value === 'dropdown' && (
              <Grid container style={{ margin: '5px 0' }}>
                <Grid item xs={3} />
                <Grid item xs={9}>
                  <WrapperAssociate>
                    <div className="m-bottom-4">
                      <UICheckbox
                        checked={isMultiSelect}
                        onChange={() =>
                          onChangeInputVia({ isMultiSelect: !isMultiSelect })
                        }
                        label="Multi-Select"
                        disabled={isViewMode}
                      />
                    </div>
                    <UICheckbox
                      checked={isAssociate}
                      onChange={() =>
                        onChangeInputVia({ isAssociate: !isAssociate })
                      }
                      label="Enable Associate"
                      disabled={isViewMode}
                    />
                    {isAssociate ? (
                      <>
                        <p>
                          <span style={{ color: 'red' }}>*</span>
                          Associated with:
                        </p>
                        <ChipSelect
                          value={associateWith}
                          onChange={val =>
                            onChangeInputVia({ associateWith: val })
                          }
                          options={associateOptions}
                          label="+ Add attribute"
                        />
                      </>
                    ) : null}
                  </WrapperAssociate>
                </Grid>
              </Grid>
            )}
          {/* Show Preview */}
          {isShowPreviewInputVia && (
            <Grid container style={{ margin: '12px 0' }}>
              <Grid item xs={3} />
              <Grid item xs={9}>
                <span>Settings Input Type</span>
                <MoreVertIcon />
                <UIButton theme="text-link" onClick={handleClickOpen}>
                  Preview
                </UIButton>
                <Dialog
                  open={open}
                  onClose={handleClose}
                  aria-labelledby="alert-dialog-title"
                  aria-describedby="alert-dialog-description"
                  // style={{ width: '400px' }}
                >
                  <WrapperDialog>
                    <DialogTitle
                      id="customized-dialog-title"
                      onClose={handleClose}
                      style={{ paddingBottom: 0 }}
                    >
                      <WrapperTitleDialog>
                        <Typography variant="h6" component="p">
                          Preview
                        </Typography>
                        <div>
                          <IconButton aria-label="close" onClick={handleClose}>
                            <CloseIcon />
                          </IconButton>
                        </div>
                      </WrapperTitleDialog>
                    </DialogTitle>
                    <DialogContent style={{ padding: '16px 24px' }}>
                      <UIWrapperDisable disabled={isViewMode}>
                        <Preview
                          design="input"
                          dateType={
                            dataInputVia[
                              dataConfig.selectViaInputType.value.value
                            ].value.dateType
                          }
                          callback={() => {}}
                          momentDateTime={moment(
                            dataInputVia[
                              dataConfig.selectViaInputType.value.value
                            ].value.momentDateTime,
                            MAP_DATA_DATE_TIME[
                              dataInputVia[
                                dataConfig.selectViaInputType.value.value
                              ].value.dateType
                            ],
                          ).format()}
                          type={dataConfig.selectViaInputType.value.value}
                          label=""
                          initData={_.cloneDeep(
                            dataInputVia[
                              dataConfig.selectViaInputType.value.value
                            ].value,
                          )}
                          alignment={
                            dataConfig.selectViaInputType.value.value ===
                              'radio' &&
                            Object.values(
                              dataInputVia[
                                dataConfig.selectViaInputType.value.value
                              ].alignmentType,
                            ).find(each => each.isChecked).value
                          }
                        />
                      </UIWrapperDisable>
                    </DialogContent>
                    <Divider />
                    <DialogActions style={{ justifyContent: 'flex-start' }}>
                      <UIButton onClick={handleClose} theme="outline">
                        Close
                      </UIButton>
                    </DialogActions>
                  </WrapperDialog>
                </Dialog>
              </Grid>
            </Grid>
          )}

          {/* Show Setting Input Via */}
          {isShowSettingsInputVia && (
            <Grid container>
              <Grid item xs={3} />
              <Grid item xs={9}>
                {dataConfig.selectViaInputType &&
                  dataConfig.selectViaInputType.value.value ===
                    'datePicker' && (
                    <Paper variant="outlined" style={{ padding: '16px' }}>
                      <UIWrapperDisable disabled={isViewMode}>
                        <SettingInputTypeDatePicker
                          dateType={
                            dataInputVia[
                              dataConfig.selectViaInputType.value.value
                            ].value.dateType
                          }
                          callback={callbackInputVia}
                          momentDateTime={
                            dataInputVia[
                              dataConfig.selectViaInputType.value.value
                            ].value.momentDateTime
                          }
                          isUseTitle
                        />
                      </UIWrapperDisable>
                    </Paper>
                  )}
                {dataConfig.selectViaInputType &&
                  dataConfig.selectViaInputType.value.value === 'radio' && (
                    <Paper variant="outlined" style={{ padding: '16px' }}>
                      <UIWrapperDisable disabled={isViewMode}>
                        <SettingInputTypeRadio
                          initData={
                            dataInputVia[
                              dataConfig.selectViaInputType.value.value
                            ].value
                          }
                          callback={callbackInputVia}
                        />
                      </UIWrapperDisable>
                    </Paper>
                  )}{' '}
                {dataConfig.selectViaInputType &&
                  dataConfig.selectViaInputType.value.value === 'dropdown' && (
                    <WrapperSettingsInputType>
                      <UIWrapperDisable disabled={isViewMode}>
                        <SettingsInputType
                          initData={selectViaInputTypeValue.value}
                          callback={callbackInputVia}
                          isAssociate={isAssociate}
                          // isMultiSelect={isMultiSelect}
                          associateWith={associateWith}
                          associateOptions={associateOptions}
                          Components={{
                            ChipSelect,
                          }}
                        />
                      </UIWrapperDisable>
                    </WrapperSettingsInputType>
                  )}
              </Grid>
            </Grid>
          )}
        </Grid>
      </Grid>
    );
  };

  const renderNotificationFields = () => (
    <Grid
      container
      className={classnames('border-all m-bottom-3 bs-all bg-form', {
        setting: props.isViewMode,
      })}
    >
      {isViewMode ? (
        <TitleViewMode className="p-top-4">
          {MAP_TITLE.notificationSetup}
        </TitleViewMode>
      ) : (
        <Grid item xs={2} className="p-all-4 p-top-7">
          {MAP_TITLE.notificationSetup}
        </Grid>
      )}
      <Grid
        item
        xs={isViewMode ? 12 : 10}
        className={classnames(' p-top-4 p-bottom-4 p-right-8', {
          'p-left-4': !props.isViewMode,
        })}
      >
        {notificationFields.map(
          each =>
            dataConfig[each] && (
              <InputComponent
                {...dataConfig[each]}
                onChange={onChangeData}
                key={each}
                isViewMode={props.isViewMode}
              />
            ),
        )}
      </Grid>
    </Grid>
  );
  const renderMessage = () => {
    if (
      activeRow.type === TYPE_ATTRIBUTE.COMPUTED &&
      activeRow.status === ARCHIVE_STATUS &&
      !createCopy
    ) {
      return (
        <DivMessageComputing bgColor="#e5f5f8">
          <WarningIcon className="m-x-2" />
          {MAP_TITLE.archive}
        </DivMessageComputing>
      );
    }

    if (parseInt(process_status) === 1 && !copyId) {
      return (
        <DivMessageComputing bgColor={colors.selectedYellow}>
          <WarningIcon className="m-x-2" />
          {MAP_TITLE.notiAlertEdit}
        </DivMessageComputing>
      );
    }

    return null;
  };

  const isDisable =
    activeRow.type === TYPE_ATTRIBUTE.COMPUTED &&
    activeRow.status === ARCHIVE_STATUS &&
    !createCopy;
  const isDisableProcessStatus =
    activeRow.type === TYPE_ATTRIBUTE.COMPUTED &&
    (activeRow.process_status === 1 || activeRow.process_status === 5) &&
    !createCopy;

  const { typeRender } = globalData.attrType;

  return (
    <ErrorBoundary path="app/modules/Dashboard/Settings/BusinessObject/Detail/Attributes/Create/Design/Settings/_UI/FieldContent.jsx">
      {/* <div id="setting-container" className="style-container jc-center"> */}
      {/* <WrapperContent className="pos-relative"> */}
      {/* <StyleContent className="p-x-3"> */}
      {/* <StyleContentLoading> */}

      {!isInitDone && (
        <WrapperLoading
          className={classnames({
            'bs-all': !props.isViewMode,
          })}
        >
          <Loading isLoading />
        </WrapperLoading>
      )}
      {isInitDone && (
        <RootContainer
          className={classnames(classes.root, {
            'is-view-mode': props.isViewMode,
          })}
        >
          <Loading isLoading={isLoading} />
          <Grid
            container
            // boxShadow={false}
            className="row width-100 m-x-0 m-bottom-1"
          >
            <StyledWrapperDisable
              className="width-100"
              disabled={!props.isViewMode && isDisable}
            >
              <Grid
                container
                className={classnames('bg-form  m-bottom-3 bs-all border-all', {
                  'general-information': props.isViewMode,
                })}
              >
                <StyledWrapperDisable
                  className="width-100"
                  disabled={!isViewMode && isDisableProcessStatus}
                >
                  {!createCopy && !isViewMode && renderMessage()}
                  {props.isViewMode ? (
                    <TitleViewMode>
                      {MAP_TITLE.titlGeneralInfomation}
                    </TitleViewMode>
                  ) : (
                    <Grid item xs={2} className={classnames(' p-top-7')}>
                      {MAP_TITLE.titlGeneralInfomation}
                    </Grid>
                  )}
                </StyledWrapperDisable>
                <Grid
                  item
                  xs={isViewMode ? 12 : 10}
                  className={classnames(' p-top-4 p-bottom-4 p-right-8', {
                    'p-left-4': !props.isViewMode,
                  })}
                >
                  {infoFields.map(
                    each =>
                      dataConfig[each] && (
                        <InputComponent
                          {...dataConfig[each]}
                          onChange={onChangeData}
                          key={each}
                          disabled={
                            dataConfig[each].disabled
                              ? dataConfig[each].disabled
                              : dataConfig[each].name === 'groupAttribute' &&
                                isDisableProcessStatus
                          }
                          isViewMode={isViewMode}
                          errors={
                            isViewMode
                              ? uniq([
                                  ...dataConfig[each].errors,
                                  getErrorMessageByCode(errors, each),
                                ])
                              : dataConfig[each].errors
                          }
                        />
                      ),
                  )}
                </Grid>
              </Grid>
              <StyledWrapperDisable
                className="width-100"
                disabled={!props.isViewMode && isDisableProcessStatus}
              >
                {configFields.length > 0 && renderConfigFields()}
                {settingFields.length > 0 && (
                  <Grid
                    container
                    className={classnames(
                      'border-all m-bottom-3 bs-all  bg-form',
                      {
                        setting: props.isViewMode,
                      },
                    )}
                  >
                    {isViewMode ? (
                      <TitleViewMode className="p-top-4">
                        {MAP_TITLE.titlSetting}
                      </TitleViewMode>
                    ) : (
                      <Grid
                        item
                        xs={!isViewMode && 2}
                        className="p-all-4 p-top-7"
                      >
                        {MAP_TITLE.titlSetting}
                      </Grid>
                    )}
                    <Grid
                      item
                      xs={isViewMode ? 12 : 10}
                      className={classnames(' p-top-4 p-bottom-4 p-right-8', {
                        'p-left-4': !props.isViewMode,
                      })}
                    >
                      {settingFields.map(
                        each =>
                          dataConfig[each] && (
                            <InputComponent
                              {...dataConfig[each]}
                              onChange={onChangeData}
                              key={each}
                              isViewMode={isViewMode}
                            />
                          ),
                      )}
                    </Grid>
                  </Grid>
                )}
                {additionFields.length > 0 && (
                  <Grid
                    container
                    className={classnames(
                      'border-all m-bottom-3 bs-all  bg-form',
                      {
                        setting: props.isViewMode,
                      },
                    )}
                  >
                    {isViewMode ? (
                      <TitleViewMode className="p-top-4">
                        {MAP_TITLE.titlSetting}
                      </TitleViewMode>
                    ) : (
                      <Grid
                        item
                        xs={!isViewMode && 2}
                        className="p-all-4 p-top-7"
                      >
                        {MAP_TITLE.titlSetting}
                      </Grid>
                    )}
                    <Grid
                      item
                      xs={isViewMode ? 12 : 10}
                      className={classnames(' p-top-4 p-bottom-4 p-right-8', {
                        'p-left-4': !props.isViewMode,
                      })}
                    >
                      {additionFields.map(
                        each =>
                          dataConfig[each] && (
                            <InputComponent
                              {...dataConfig[each]}
                              onChange={onChangeData}
                              key={each}
                              isViewMode={isViewMode}
                            />
                          ),
                      )}
                    </Grid>
                  </Grid>
                )}
                {computationScheduleFields.length > 0 &&
                  renderComputationSchedule()}
                {notificationFields.length > 0 && renderNotificationFields()}
              </StyledWrapperDisable>
              {!props.isViewMode && (
                <Footer
                  main={customReducer}
                  dispatchAction={dispatchAction}
                  callback={callback}
                  globalData={globalData}
                />
              )}
            </StyledWrapperDisable>
          </Grid>
        </RootContainer>
      )}
      {/* </StyleContentLoading> */}
      {/* </StyleContent> */}
      {/* </WrapperContent> */}
      {/* </div> */}
      {isCheckPermision ? (
        <>
          <ModalEncrypt
            callback={callbackInputVia}
            onControlModal={onControlModal}
            isOpen={isOpenEncry}
          />
        </>
      ) : (
        <>
          <ModalWarningEncrypt
            isOpen={isOpenEncry}
            onControlModal={onControlModal}
          />
        </>
      )}
    </ErrorBoundary>
  );
}

export default memo(FieldContent);
