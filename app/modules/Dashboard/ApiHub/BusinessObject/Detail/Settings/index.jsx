/* eslint-disable react/prop-types */
import React, {
  useEffect,
  memo,
  useCallback,
  useRef,
  useState,
  useMemo,
} from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';

import { createStructuredSelector } from 'reselect';
import injectSaga from 'utils/injectSaga';
import injectReducer from 'utils/injectReducer';
import { withRouter, useLocation, useParams } from 'react-router-dom';

import Grid from '@material-ui/core/Grid';
import {
  UILoading as Loading,
  UIWrapperDisable as WrapperDisable,
  UIButton,
  TabPanel,
} from '@xlab-team/ui-components';
import WarningIcon from '@material-ui/icons/WarningOutlined';
import colors from 'utils/colors';

import queryString from 'query-string';
// import ModalCreateEventCategory from 'containers/modals/ModalCreateEventCategory';
import ErrorBoundary from 'components/common/ErrorBoundary';

import ModalConfirmExit from 'containers/modals/ModalConfirmExit';
import Box from '../../../../../../components/Templates/LayoutContent/Box';
import {
  init,
  reset,
  update,
  updateValue,
} from '../../../../../../redux/actions';
import { MODULE_CONFIG } from './config';
import Footer from './Footer';
import {
  makeSelectCreateSettingsDataConfig,
  makeSelectCreateSettings,
} from './selectors';

import reducer from './reducer';
import saga from './saga';

import {
  WrapperContent,
  StyleContent,
  // useStyles,
  StyleFooter,
  DivMessageComputing,
  StyleContentLoading,
  ButtonWorkspace,
  DialogIconClose,
  DialogHeader,
  UIDialog,
  WorkspaceWrapper,
} from './styles';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { updateUrl } from '../../../../../../utils/common';
import { checkPermisionSetting, toEntryAPI } from './utils';
import {
  DialogActions,
  DialogContent,
  DialogTitle,
  Icon,
  makeStyles,
} from '@material-ui/core';
import { Workspace } from '@antscorp/form-design';
import { serializeDataInputAttrs } from '../../../../MarketingHub/Journey/Create/Content/Nodes/constant';

import { useImmer } from 'use-immer';
import _, { isEmpty, uniq } from 'lodash';
import { TabsSetting } from '../../ModalCreateObject/styled';
import ShareAccess from '../../../../../../components/common/ShareAccess';
import { MENU_CODE } from '../../../../../../utils/web/permission';
import { BrDivide, WrapperButtonAdd } from '../../SidebarObjects/styled';
import { getErrorMessageByCode } from '../../../../MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/helper';

const itemStyle = {
  padding: '4px 0',
};
const useStyles = makeStyles(() => ({
  item: {
    padding: '5px 0',
  },
  container: {
    // Fix 2 scroll
    // maxHeight: '530px',
    // overflowY: 'auto',
  },
}));

const MAP_TITLE = {
  notiAlertEdit: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EDIT_SOURCE,
    'If you edit this source will affect the input of other features.',
  ),
  titlGeneralInfomation: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_INFORMATION,
    'General Information',
  ),
  notiDiscardChange: getTranslateMessage(
    TRANSLATE_KEY._WARN_DISCARD_CHANGE_INTRO,
    "Change you created won't be saved if you leave this site",
  ),
  notiCancel: getTranslateMessage(
    TRANSLATE_KEY._TITL_CANCEL_EDIT_SEGMENT,
    'Change will be lost',
  ),
};

const InputComponent = memo(props => {
  const { isShow = true } = props;
  return (
    <Grid
      container
      style={{ ...itemStyle, display: `${isShow ? '' : 'none'}` }}
    >
      {props.componentEl(props)}
    </Grid>
  );
});

function ContentSettings(props) {
  const classes = useStyles();
  const location = useLocation();

  const [state, setState] = useImmer({
    dataWorkspace: [],
    isOpenModal: false,
    isUpdateWorkspace: true,
    activeTab: 'general',
  });

  const { itemTypeId } = props;

  const {
    main,
    dataConfig,
    activeId,
    sourceType,
    activeRow,
    initData,
    typeBO,
    isViewMode,
    errors = [],
    settings,
  } = props;

  const {
    isLoading,
    isInitDone,
    isSucessSave,
    actionDone,
    dataDone,
    design,
    disabledSave,
    dataInputVia,
    positions,
    accessInfo,
    isLoadingDetail,
  } = main;

  const { workspaces } = dataInputVia;

  useEffect(() => {
    setState(draft => {
      draft.dataWorkspace = _.cloneDeep(workspaces);
    });
  }, [workspaces]);

  const onChangeData = useCallback(
    name => value => {
      if (value !== dataConfig[name].value) {
        props.onChangeDataConfig({
          name,
          value,
        });
      }
    },
    [dataConfig],
  );

  useEffect(() => {
    if (settings) {
      props.init({ settings });

      return;
    }

    props.init({
      itemTypeId,
    });
  }, [itemTypeId, settings?.item_type_id]);

  useEffect(() => () => props.reset(), []);

  const callback = (type, data) => {
    switch (type) {
      case 'ON_SAVE':
        props.onSave();
        break;
      case 'DATA_WORKSPACE_DND':
        setState(draft => {
          draft.dataWorkspace = data;
        });
        break;

      default:
        props.callback(type, data);
        break;
    }
  };

  const handleEditWorkspace = () => {
    setState(draft => {
      draft.isEditModalWorkspace = true;
      draft.isUpdateWorkspace = false;
    });
  };

  const handleCloseWorkspace = () => {
    setState(draft => {
      draft.isEditModalWorkspace = false;
      draft.dataWorkspace = _.cloneDeep(workspaces);
      draft.isUpdateWorkspace = true;
    });
  };

  const handleSaveWorkspace = () => {
    props.onUpdatePositions({
      workspaces: state.dataWorkspace,
    });
    setState(draft => {
      draft.isEditModalWorkspace = false;
      draft.isUpdateWorkspace = true;
    });
  };
  const onChangeTab = data => {
    setState(draft => {
      draft.activeTab = data;
    });
  };
  const callbackComponent = (type, data) => {
    switch (type) {
      case 'ON_OPEN_CHOOSE_REPORT':
        break;

      case 'SELECT_TEMPLATE':
        break;

      case 'UPDATE_SHARE_ACCESS':
        props.onUpdateAccess(data.accessInfo);
        break;

      case 'ON_SAVE_NAME_DUPLICATE':
        break;

      case 'CONFIRM_MODAL_ACCESS':
        break;

      default:
        break;
    }
  };

  return (
    <ErrorBoundary path="app/modules/Dashboard/ApiHub/BusinessObject/Detail/Settings">
      <WrapperContent style={{ height: '100%' }} className="pos-relative">
        <Loading
          isLoading={isLoading || isLoadingDetail}
          isWhite={isLoadingDetail}
        />
        <StyleContent style={{ width: '100%', height: '100%' }}>
          <StyleContentLoading
            style={{ height: '100%', backgroundColor: 'rgb(255, 255, 255)' }}
            loadingScroll={isLoading}
          >
            <div
              style={{ backgroundColor: 'rgb(255,255,255)' }}
              // height={state.activeTab === 'general' && '100%'}
              // className="row width-100 m-x-0"
            >
              <Grid container className={classes.container}>
                {/* <Grid item xs={1} className="p-all-4 p-top-6">
                  {MAP_TITLE.titlGeneralInfomation}
                </Grid> */}
                <Grid item xs={12} className="p-all-4">
                  {dataConfig.infoFields.map(each =>
                    each === 'constantly' && !dataConfig.sdk.value ? (
                      <></>
                    ) : (
                      <InputComponent
                        {...dataConfig[each]}
                        onChange={onChangeData}
                        key={each}
                        classes={classes}
                        isViewMode={isViewMode}
                        errors={uniq([
                          ...dataConfig[each].errors,
                          getErrorMessageByCode(errors, each),
                        ])}
                      />
                    ),
                  )}
                </Grid>
                {!isViewMode && (
                  <>
                    <Grid style={{ padding: '0px !important' }} item xs={12}>
                      <BrDivide />
                    </Grid>
                    <Grid item xs={12} className="p-all-4">
                      <div className="wrapper-common assign-attributes">
                        <TabsSetting
                          activeTab={state.activeTab}
                          onChange={onChangeTab}
                          className="tab-nav-assign-attributes"
                          isBoxShadow={false}
                        >
                          <TabPanel
                            marginTop
                            style={{ fontSize: '12px' }}
                            styleCss="font-size: 12px;"
                            label="General Settings"
                            eventKey="general"
                          >
                            {dataConfig.generalSettings.map(each =>
                              each === 'constantly' && !dataConfig.sdk.value ? (
                                <></>
                              ) : (
                                <InputComponent
                                  {...dataConfig[each]}
                                  onChange={onChangeData}
                                  key={each}
                                  classes={classes}
                                />
                              ),
                            )}
                            {state.dataWorkspace.length > 0 && (
                              <Grid container>
                                <Grid
                                  item
                                  xs={12}
                                  style={{
                                    color: '#7f7f7f',
                                  }}
                                >
                                  Custom Input
                                </Grid>
                                <Grid
                                  item
                                  xs={12}
                                  style={{
                                    background: '#e7eff2',
                                    margin: '16px 0',
                                    position: 'relative',
                                  }}
                                >
                                  {state.isUpdateWorkspace && (
                                    <WorkspaceWrapper>
                                      <Workspace
                                        workspaces={workspaces}
                                        callback={() => {}}
                                      />
                                    </WorkspaceWrapper>
                                  )}
                                </Grid>
                                <ButtonWorkspace>
                                  <UIButton
                                    onClick={handleEditWorkspace}
                                    theme="primary"
                                  >
                                    Edit
                                  </UIButton>
                                </ButtonWorkspace>
                              </Grid>
                            )}
                          </TabPanel>
                          <TabPanel
                            style={{ fontSize: '12px' }}
                            marginTop
                            styleCss="font-size: 12px;"
                            label="Share Access"
                            eventKey="share"
                          >
                            <ShareAccess
                              accessInfo={accessInfo}
                              callback={callbackComponent}
                            />
                          </TabPanel>
                        </TabsSetting>
                      </div>
                    </Grid>
                  </>
                )}
              </Grid>
            </div>
            {/* <WrapperButtonAdd className="p-y-1">
              <Footer main={main} dataConfig={dataConfig} callback={callback} />
            </WrapperButtonAdd> */}
          </StyleContentLoading>
          {!isViewMode && (
            <>
              <StyleFooter>
                <Footer
                  main={main}
                  dataConfig={dataConfig}
                  callback={callback}
                />
              </StyleFooter>
              <UIDialog
                onClose={handleCloseWorkspace}
                open={state.isEditModalWorkspace}
              >
                <DialogHeader>
                  <DialogTitle>Custom Inputs</DialogTitle>
                  <DialogIconClose>
                    <Icon onClick={handleCloseWorkspace}>cancel</Icon>
                  </DialogIconClose>
                </DialogHeader>
                <DialogContent>
                  {state.dataWorkspace.length > 0 && (
                    <div className="dialog-edit-workspace">
                      <Workspace
                        workspaces={state.dataWorkspace}
                        callback={callback}
                      />
                    </div>
                  )}
                </DialogContent>
                <DialogActions>
                  <UIButton theme="primary" onClick={handleSaveWorkspace}>
                    Save
                  </UIButton>
                </DialogActions>
              </UIDialog>
            </>
          )}
        </StyleContent>
      </WrapperContent>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  dataConfig: makeSelectCreateSettingsDataConfig(),
  main: makeSelectCreateSettings(),
});

export function mapDispatchToProps(dispatch, props) {
  return {
    init: value => dispatch(init(MODULE_CONFIG.key, value)),
    onSave: value => dispatch(update(MODULE_CONFIG.key, value)),
    onChangeDataConfig: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATA_CONFIG@@`, value)),
    reset: value => dispatch(reset(`${MODULE_CONFIG.key}`, value)),
    onUpdatePositions: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@POSITION_INPUT_VIA`, value)),
    onUpdateAccess: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ACCESS_INFO`, value)),
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({
  key: MODULE_CONFIG.key,
  reducer,
});
const withSaga = injectSaga({
  key: MODULE_CONFIG.key,
  saga,
});

ContentSettings.defaultProps = {
  sourceType: '1',
};

export default compose(
  withReducer,
  withRouter,
  withConnect,
  withSaga,
)(ContentSettings);
