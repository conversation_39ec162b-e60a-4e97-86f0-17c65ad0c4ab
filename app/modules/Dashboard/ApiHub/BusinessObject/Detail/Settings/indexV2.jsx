/* eslint-disable no-unused-vars */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
/* eslint-disable react/prop-types */
// libraries
import React, { useEffect, memo, useCallback, useMemo } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { useImmer } from 'use-immer';
import _, { uniq } from 'lodash';
import { createStructuredSelector } from 'reselect';
import injectSaga from 'utils/injectSaga';
import injectReducer from 'utils/injectReducer';
import { withRouter } from 'react-router-dom';
import { createPortal } from 'react-dom';
import {
  DialogActions,
  DialogContent,
  DialogTitle,
  Icon,
  makeStyles,
} from '@material-ui/core';
import Grid from '@material-ui/core/Grid';
import { UILoading as Loading } from '@xlab-team/ui-components';
import { Workspace } from '@antscorp/form-design';
import { Button } from '@antscorp/antsomi-ui';

// Components
import ErrorBoundary from 'components/common/ErrorBoundary';
import ShareAccessLib from '../../../../../../components/common/ShareAccessAntsomiUI';
import Footer from './Footer';

// Constants
import { MODULE_CONFIG } from './config';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import { HEADER_RIGHT_ID } from '../../../../../../containers/Drawer/DrawerDetailDataObject/constants';

// Actions
import {
  init,
  reset,
  update,
  updateValue,
} from '../../../../../../redux/actions';
import {
  makeSelectCreateSettingsDataConfig,
  makeSelectCreateSettings,
} from './selectors';

import reducer from './reducer';
import saga from './saga';

// Styles
import {
  WrapperContent,
  StyleContent,
  StyleContentLoading,
  ButtonWorkspace,
  DialogIconClose,
  DialogHeader,
  UIDialog,
  WorkspaceWrapper,
  Title,
} from './styles';

// Utils
import { getErrorMessageByCode } from '../../../../MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/helper';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import { getCurrentUserId } from '../../../../../../utils/web/cookie';

const itemStyle = {
  padding: '4px 0',
};
const useStyles = makeStyles(() => ({
  item: {
    padding: '5px 0',
  },
  container: {
    // Fix 2 scroll
    // maxHeight: '530px',
    // overflowY: 'auto',
  },
}));

const InputComponent = memo(props => {
  const { isShow = true } = props;

  return (
    <Grid
      container
      style={{ ...itemStyle, display: `${isShow ? '' : 'none'}` }}
    >
      {props.componentEl(props)}
    </Grid>
  );
});

const MAP_TITLE = {
  titlGeneralInfomation: getTranslateMessage(
    TRANSLATE_KEY._TITL_GENERAL_INFORMATION,
    'General Information',
  ),
};

function ContentSettings(props) {
  const classes = useStyles();

  const [state, setState] = useImmer({
    dataWorkspace: [],
    isOpenModal: false,
    isUpdateWorkspace: true,
    activeTab: 'general',
  });

  const { itemTypeId } = props;

  const { main, dataConfig, isViewMode, errors = [], settings } = props;

  const { isLoading, dataInputVia, accessInfo, isLoadingDetail } = main;

  const { workspaces } = dataInputVia;

  useEffect(() => {
    setState(draft => {
      draft.dataWorkspace = _.cloneDeep(workspaces);
    });
  }, [workspaces]);

  const onChangeData = useCallback(
    name => value => {
      if (value !== dataConfig[name].value) {
        props.onChangeDataConfig({
          name,
          value,
        });
      }
    },
    [dataConfig],
  );

  useEffect(() => {
    if (settings) {
      props.init({ settings });

      return;
    }

    props.init({
      itemTypeId,
    });
  }, [itemTypeId, settings?.item_type_id]);

  useEffect(() => () => props.reset(), []);

  const callback = (type, data) => {
    switch (type) {
      case 'ON_SAVE':
        props.onSave();
        break;
      case 'DATA_WORKSPACE_DND':
        setState(draft => {
          draft.dataWorkspace = data;
        });
        break;

      default:
        props.callback(type, data);
        break;
    }
  };

  const handleEditWorkspace = () => {
    setState(draft => {
      draft.isEditModalWorkspace = true;
      draft.isUpdateWorkspace = false;
    });
  };

  const handleCloseWorkspace = () => {
    setState(draft => {
      draft.isEditModalWorkspace = false;
      draft.dataWorkspace = _.cloneDeep(workspaces);
      draft.isUpdateWorkspace = true;
    });
  };

  const handleSaveWorkspace = () => {
    props.onUpdatePositions({
      workspaces: state.dataWorkspace,
    });
    setState(draft => {
      draft.isEditModalWorkspace = false;
      draft.isUpdateWorkspace = true;
    });
  };

  const onChangeTab = data => {
    setState(draft => {
      draft.activeTab = data;
    });
  };

  const callbackComponent = (type, data) => {
    switch (type) {
      case 'ON_OPEN_CHOOSE_REPORT':
        break;

      case 'SELECT_TEMPLATE':
        break;

      case 'UPDATE_SHARE_ACCESS':
        props.onUpdateAccess({ ...data.accessInfo, type: data.type });
        break;

      case 'ON_SAVE_NAME_DUPLICATE':
        break;

      case 'CONFIRM_MODAL_ACCESS':
        break;

      default:
        break;
    }
  };

  const rightEle = document.getElementById(HEADER_RIGHT_ID);

  return (
    <ErrorBoundary path="app/modules/Dashboard/ApiHub/BusinessObject/Detail/Settings">
      <WrapperContent
        style={{ height: '100%', padding: '15px' }}
        className="pos-relative"
      >
        <Loading
          isLoading={isLoading || isLoadingDetail}
          isWhite={isLoadingDetail}
        />
        <StyleContent style={{ height: '100%' }}>
          <StyleContentLoading
            style={{
              height: '100%',
              backgroundColor: 'rgb(255, 255, 255)',
            }}
            loadingScroll={isLoading}
            isDetailV2
          >
            <div style={{ backgroundColor: 'rgb(255,255,255)' }}>
              <Grid container className={classes.container}>
                <Grid item xs={12}>
                  <Title>{MAP_TITLE.titlGeneralInfomation}</Title>
                </Grid>
                <Grid item xs={10}>
                  {dataConfig.infoFields.map(each =>
                    (each === 'constantly' && !dataConfig.sdk.value) ||
                    each === 'objectName' ? (
                      <></>
                    ) : (
                      <InputComponent
                        {...dataConfig[each]}
                        onChange={onChangeData}
                        key={each}
                        classes={classes}
                        isViewMode={isViewMode}
                        errors={uniq([
                          ...dataConfig[each].errors,
                          getErrorMessageByCode(errors, each),
                        ])}
                      />
                    ),
                  )}
                </Grid>

                <Grid item xs={10}>
                  <div className="wrapper-common assign-attributes">
                    {dataConfig.generalSettings.map(each =>
                      each === 'constantly' && !dataConfig.sdk.value ? (
                        <></>
                      ) : (
                        <InputComponent
                          {...dataConfig[each]}
                          onChange={onChangeData}
                          key={each}
                          classes={classes}
                          styleRightContent={{
                            display: 'flex',
                            alignItems: 'center',
                          }}
                          styleTitle={{
                            width: '90%',
                          }}
                        />
                      ),
                    )}
                    {state.dataWorkspace.length > 0 && (
                      <Grid container>
                        <Grid
                          item
                          xs={12}
                          style={{
                            color: '#7f7f7f',
                          }}
                        >
                          Custom Input
                        </Grid>
                        <Grid
                          item
                          xs={12}
                          style={{
                            background: '#e7eff2',
                            margin: '16px 0',
                            position: 'relative',
                          }}
                        >
                          {state.isUpdateWorkspace && (
                            <WorkspaceWrapper>
                              <Workspace
                                workspaces={workspaces}
                                callback={() => {}}
                              />
                            </WorkspaceWrapper>
                          )}
                        </Grid>
                        <ButtonWorkspace>
                          <Button onClick={handleEditWorkspace} type="primary">
                            Edit
                          </Button>
                        </ButtonWorkspace>
                      </Grid>
                    )}
                  </div>
                </Grid>
                <Grid item xs={10}>
                  <Title className="m-top-5">Share access</Title>
                </Grid>
                <Grid container xs={10} style={{ marginBottom: '50px' }}>
                  <Grid item xs={2}>
                    Share data object
                  </Grid>
                  <Grid item xs={6} style={{ maxWidth: '400px' }}>
                    <ShareAccessLib
                      accessInfo={accessInfo}
                      callback={callbackComponent}
                      design="update"
                      hideTransferOwnership={
                        Number(getCurrentUserId()) !== accessInfo.ownerId
                      }
                    />
                  </Grid>
                </Grid>
              </Grid>
            </div>
          </StyleContentLoading>
          {rightEle &&
            createPortal(
              <Footer
                main={main}
                dataConfig={dataConfig}
                callback={callback}
                isShowDrawer
              />,
              rightEle,
            )}
          <UIDialog
            onClose={handleCloseWorkspace}
            open={state.isEditModalWorkspace}
          >
            <DialogHeader>
              <DialogTitle>Custom Inputs</DialogTitle>
              <DialogIconClose>
                <Icon onClick={handleCloseWorkspace}>cancel</Icon>
              </DialogIconClose>
            </DialogHeader>
            <DialogContent>
              {state.dataWorkspace.length > 0 && (
                <div className="dialog-edit-workspace">
                  <Workspace
                    workspaces={state.dataWorkspace}
                    callback={callback}
                  />
                </div>
              )}
            </DialogContent>
            <DialogActions>
              <Button type="primary" onClick={handleSaveWorkspace}>
                Save
              </Button>
            </DialogActions>
          </UIDialog>
        </StyleContent>
      </WrapperContent>
    </ErrorBoundary>
  );
}

const mapStateToProps = createStructuredSelector({
  dataConfig: makeSelectCreateSettingsDataConfig(),
  main: makeSelectCreateSettings(),
});

export function mapDispatchToProps(dispatch, props) {
  return {
    init: value => dispatch(init(MODULE_CONFIG.key, value)),
    onSave: value => dispatch(update(MODULE_CONFIG.key, value)),
    onChangeDataConfig: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATA_CONFIG@@`, value)),
    reset: value => dispatch(reset(`${MODULE_CONFIG.key}`, value)),
    onUpdatePositions: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@POSITION_INPUT_VIA`, value)),
    onUpdateAccess: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ACCESS_INFO`, value)),
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({
  key: MODULE_CONFIG.key,
  reducer,
});
const withSaga = injectSaga({
  key: MODULE_CONFIG.key,
  saga,
});

ContentSettings.defaultProps = {
  sourceType: '1',
};

export default compose(
  withReducer,
  withRouter,
  withConnect,
  withSaga,
)(ContentSettings);
