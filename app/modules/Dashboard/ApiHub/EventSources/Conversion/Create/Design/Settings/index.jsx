/* eslint-disable react/prop-types */
import React, { useEffect, memo, useCallback } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import classnames from 'classnames';
import { createStructuredSelector } from 'reselect';
import injectSaga from 'utils/injectSaga';
import injectReducer from 'utils/injectReducer';
import {
  withRouter,
  useLocation,
  useParams,
  useHistory,
} from 'react-router-dom';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { FormControlLabel, Radio, RadioGroup, Grid } from '@material-ui/core';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import {
  UILoading as Loading,
  UIButton,
  UIWrapperDisable as WrapperDisable,
} from '@xlab-team/ui-components';
import WarningIcon from '@material-ui/icons/WarningOutlined';
import colors from 'utils/colors';
import { Dropdown } from 'reactstrap';
import queryString from 'query-string';
import BarChartIcon from '@material-ui/icons/BarChart';
import Box from '../../../../../../../../components/Templates/LayoutContent/Box';
import {
  init,
  reset,
  update,
  updateValue,
} from '../../../../../../../../redux/actions';
import { MODULE_CONFIG } from './config';
import Footer from './Footer';
import {
  makeSelectCreateSettingsDataConfig,
  makeSelectCreateSettings,
} from './selectors';

import reducer from './reducer';
import saga from './saga';

import {
  WrapperContent,
  StyleContent,
  Label,
  useStyles,
  StyleFooter,
  DivMessageComputing,
  StyleContentLoading,
  ContentOption,
  Command,
  Text,
  CirclePoint,
} from './styles';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { updateUrl } from '../../../../../../../../utils/common';
import UIPerformEventV2 from '../../../../../../../../components/common/UIPerformEventV2';
import { Span } from '../../../../../../MarketingHub/Promotion/Create/_UI/FieldExpiration/styles';
import { LableSelect } from '../../../../../../MarketingHub/Promotion/Create/_UI/FieldRectriction/styles';
import { WrapperStyle } from '../../../../../../MarketingHub/Promotion/Create/_UI/FieldSettingSource/styles';
import UISelectCondition from '../../../../../../../../components/form/UISelectCondition';
import { UINumberStyled } from '../../../../../../../../components/common/UIFrequencyCapping/styled';
import {
  MAX_OCCURS,
  PERIOD_LIST_REPEAT,
  PERIOD_MAP_REPEAT,
  toEntryAPI,
  validateEvent,
} from './utils';
import useToggle from '../../../../../../../../hooks/useToggle';
import ModalConfirmConversion from '../../../../../../../../containers/modals/ModalConfirmConversion';
import { isModifiedRules } from '../../../../../../../../containers/Segment/util';
// import { validateRulesConditionPerformEvent } from '../../../../../../../../components/common/UIPerformEventV2/utils.validate';
import { WrapperDropdownToggle } from '../../../../../../Profile/PredictiveModel/Create/components/DataUpdate/styled';
import _, { uniq } from 'lodash';
import { getErrorMessageByCode } from '../../../../../../MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/helper';
import {
  Flex,
  DrawerDetail,
  Icon,
  EditableName,
  Button,
  Alert,
} from '@antscorp/antsomi-ui';
import APP from '../../../../../../../../appConfig';
import { getPortalId } from '../../../../../../../../utils/web/cookie';
import HeaderDrawer from '../../../../../../../../components/common/HeaderDrawer';
import { makeSelectVersionDetail } from '../../../Detail/DetailVersion/selectors';
import {
  BoxHeaderLeft,
  BoxHeaderRight,
  WrapperHeader,
} from '../../../../../../../../containers/Drawer/DrawerDetailDataObject/styled';
import { makeSelectDetailDomainMain } from '../../../Detail/selectors';

const itemStyle = {
  padding: '4px 0',
};
const MAP_TITLE = {
  notiAlertEdit: getTranslateMessage(
    TRANSLATE_KEY._NOTI_EDIT_EVENT,
    'If you edit this destination will affect the input of other features.',
  ),
  titlGeneralInfomation: getTranslateMessage(
    TRANSLATE_KEY._,
    'General Information',
  ),
  titlAttribute: getTranslateMessage(TRANSLATE_KEY._, 'Attribution Rule'),
  notiDiscardChange: getTranslateMessage(
    TRANSLATE_KEY._,
    'Do you want to recalculate the recorded data according to this new change?',
  ),
  notiModal: getTranslateMessage(TRANSLATE_KEY._, 'Confirm Recalculate Data'),
  titleOccurs: getTranslateMessage(
    TRANSLATE_KEY._,
    'When conversion event occurs, Conversion will be calculated:',
  ),
  immediately: getTranslateMessage(TRANSLATE_KEY._, 'Immediately'),
  specific: getTranslateMessage(TRANSLATE_KEY._, 'Specific'),
  actEdit: getTranslateMessage(TRANSLATE_KEY._ACT_EDIT, 'Edit'),
  labelCancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
  labelSave: getTranslateMessage(TRANSLATE_KEY._ACT_SAVE, 'Build'),
  saveAndAssgin: getTranslateMessage(TRANSLATE_KEY._, 'Save'),
  restoreThisVersion: getTranslateMessage(
    TRANSLATE_KEY.DATLE,
    'Restore this version',
  ),
  message: getTranslateMessage(
    TRANSLATE_KEY._,
    'If you edit this conversion, it will affect the input of other features.',
  ),
};

const InputComponent = memo(props => {
  return (
    <Grid container style={itemStyle}>
      {props.componentEl(props)}
    </Grid>
  );
});

function ContentSettings(props) {
  const classes = useStyles();
  const location = useLocation();
  const history = useHistory();
  const [isOpenConversion, setIsOpenConversion] = useToggle(false);
  const [isOpenDropdown, setIsOpenDropdown] = useToggle(false);
  const searchParams = new URLSearchParams(location?.search);
  const itemTypeId = searchParams.get('create') || '';
  const versionId = searchParams.get('version') || null;
  const conversionIdUrl = searchParams.get('detail') || null;
  const versionRestoreId = searchParams.get('versionRestore') || null;
  const { ...restParams } = useParams();

  const {
    main,
    dataConfig,
    activeRow,
    isViewMode,
    errors,
    eventPropertyService,
    getFullEventTrackingMapService,
    getFullEventTrackingService,
    isUsingJourneyTemplate,
    showAllSource,
    versionDetail = {},
    mainDetail = {},
  } = props;
  const {
    isLoading,
    isSucessSave,
    actionDone,
    dataDone,
    listContribution,
    contributeEvent = [],
    isDoing,
    isLoadingConfigFields,
    design,
  } = main;
  const { valueObjectName = {} } = mainDetail;
  const conversionId = props.conversionId || conversionIdUrl;

  const onChangeData = useCallback(
    name => value => {
      if (name === 'iconType') {
        props.onChangeTypeIcon({ name, value });
      } else if (value !== dataConfig[name].value) {
        props.onChangeDataConfig({ name, value });
      }
    },
    [dataConfig],
  );
  useEffect(() => {
    if (design === 'update') {
      props.onChangeDataConfig({
        name: 'conversionName',
        value: valueObjectName?.value,
      });
    }
  }, [valueObjectName?.value.EN]);
  // useEffect(() => {
  //   const el = document.getElementById('setting-container');
  //   el.scrollTo({
  //     top: 0,
  //     left: 0,
  //     behavior: 'smooth', // or can get `auto` variable
  //   });
  // }, [main.design]);

  useEffect(() => {
    // setDesign(queryDesign);

    if (props.use === 'create') {
      props.init({ design: props.use, itemTypeId });
    } else {
      props.init({
        design: props.use,
        activeRow,
        conversionId,
        versionId,
        versionRestoreId,
      });
    }

    return () => props.reset();
  }, [conversionId, itemTypeId]);

  const callback = (type, data) => {
    switch (type) {
      case 'ON_CANCEL': {
        props.goToList();
        break;
      }
      // case 'ON_SAVE':
      //   const { status } = validateEvent(main.performEvent);
      //   if (status) {
      //     if (main.design === 'create') {
      //       props.onSave();
      //     } else if (main.design === 'update') {
      //       const dataIn = {
      //         ...toEntryAPI(
      //           dataConfig,
      //           main.performEvent,
      //           main.occurs,
      //           itemTypeId,
      //         ),
      //       };
      //       const excludeCheckedModified = [
      //         'conversionName',
      //         'description',
      //         'linkImage',
      //         'conversionCode',
      //         'isRecalculate',
      //         'descriptionMultilang',
      //         'conversionNameMultilang',
      //         'audienceType',
      //         'iconUrl',
      //         'properties',
      //       ];
      //       const isChange = isModifiedRules(activeRow, dataIn, {
      //         excludeProperty: excludeCheckedModified,
      //       });
      //       if (isChange || versionRestoreId) {
      //         setIsOpenConversion();
      //       } else {
      //         props.onSave();
      //       }
      //     }
      //   } else {
      //     props.onValidateEvent();
      //   }

      //   break;
      case 'ON_CHANGE_DESIGN_TYPE':
        // if (data.design === 'update') {
        // props.init({ design: 'update', activeRow });
        // }
        updateUrl(`${location.pathname}?design=${data.design}`);
        props.onChangeDesign(data);
        break;
      case 'CONFIRM_CONVERSION':
        if (data !== 'editting') {
          props.onSave(data);
        }

        break;
      default:
        break;
    }
  };
  const onChangePerformEventContribute = data => {
    props.onChangeEvent({ name: 'eventContribute', value: data });
  };
  const onChangePerformEventConversion = data => {
    props.onChangeEvent({ name: 'eventConversion', value: data });
  };
  const handleCloseCreate = useCallback(() => {
    history.push(
      `${APP.PREFIX}/${getPortalId()}/api-hub/event-sources/conversion`,
    );
  }, []);
  const onSave = () => {
    const { status } = validateEvent(main.performEvent);
    if (status) {
      if (main.design === 'create') {
        props.onSave();
      } else if (main.design === 'update') {
        const dataIn = {
          ...toEntryAPI(dataConfig, main.performEvent, main.occurs, itemTypeId),
        };
        const excludeCheckedModified = [
          'conversionName',
          'description',
          'linkImage',
          'conversionCode',
          'isRecalculate',
          'descriptionMultilang',
          'conversionNameMultilang',
          'audienceType',
          'iconUrl',
          'properties',
        ];
        const isChange = isModifiedRules(activeRow, dataIn, {
          excludeProperty: excludeCheckedModified,
        });
        if (isChange || versionRestoreId) {
          setIsOpenConversion();
        } else {
          props.onSave();
        }
      }
    } else {
      props.onValidateEvent();
    }
  };
  const settingContent = () => {
    return (
      <ErrorBoundary path="app/modules/Dashboard/ApiHub/EventSources/Events/Create/Design/Settings/index.jsx">
        <div id="setting-container" className="style-container jc-center">
          <WrapperContent className="pos-relative">
            <StyleContent>
              {/* <HeaderDrawer
                extraContent={
                  versionId ? (
                    <UIButton
                      disabled={
                        Number(versionHistoryListing.data[0]?.version) ==
                        versionId
                      }
                      onClick={onRestore}
                      theme="primary"
                    >
                      {MAP_TITLE.restoreThisVersion}
                    </UIButton>
                  ) : (
                    <UIButton
                      disabled={
                        isDoing ||
                        isLoadingConfigFields ||
                        !dataConfig.isValidate
                      }
                      theme="primary"
                      onClick={() => onSave()}
                      isLoading={isDoing}
                    >
                      <span>
                        {_.some(main.activeRow, _.isEmpty)
                          ? MAP_TITLE.labelSave
                          : MAP_TITLE.saveAndAssgin}
                      </span>
                    </UIButton>
                  )
                }
              >
                {dataConfig.conversionName.componentEl({
                  ...dataConfig.conversionName,
                  onChange: onChangeData,
                })}
              </HeaderDrawer> */}
              <StyleContentLoading>
                <Loading isLoading={isLoading} />

                {main.design === 'update' && !isViewMode && (
                  <Alert
                    message={MAP_TITLE.message}
                    showIcon
                    type="warning"
                    variant="outline"
                  />
                )}
                <Label
                  style={{ marginTop: '10px' }}
                  className={classnames({ 'm-left-0': isViewMode })}
                >
                  {MAP_TITLE.titlGeneralInfomation}
                </Label>
                <Grid container>
                  <Grid
                    item
                    xs={isViewMode || versionId ? 12 : 10}
                    className={classnames('p-top-2 p-right-2 p-bottom-2', {
                      // 'p-left-2': !isViewMode,
                    })}
                  >
                    <Grid container>
                      <Grid
                        style={{ textAlign: isViewMode ? 'start' : 'start' }}
                        item
                        xs={2}
                      >
                        <Span
                          style={{
                            marginRight: '30px',
                            color: isViewMode ? '#000000' : '#666666',
                          }}
                        >
                          Event group
                          <span style={{ color: 'rgb(255, 0, 0)' }}>* </span>
                        </Span>
                      </Grid>
                      <Grid item sm={10}>
                        {isViewMode ? (
                          <Text
                            style={{ marginLeft: '0px', color: '#000000' }}
                            className="text-dropdown"
                          >
                            Conversion
                          </Text>
                        ) : (
                          <div style={{ marginTop: '-7px' }}>
                            <Dropdown
                              disabled
                              isOpen={isOpenDropdown}
                              direction="down"
                              toggle={setIsOpenDropdown}
                            >
                              <WrapperDropdownToggle
                                disabled
                                widthDropdown="230px"
                              >
                                <ContentOption>
                                  <Text
                                    style={{ marginLeft: '0px' }}
                                    className="text-dropdown"
                                  >
                                    Conversion
                                  </Text>
                                </ContentOption>
                              </WrapperDropdownToggle>
                            </Dropdown>
                          </div>
                        )}
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid
                    item
                    xs={isViewMode || versionId ? 12 : 10}
                    className={classnames('p-top-2 p-right-2 p-bottom-2', {
                      // 'p-left-2': !isViewMode,
                    })}
                  >
                    {dataConfig.infoFields.map(each => {
                      // console.log(dataConfig, each);

                      return (
                        <ErrorBoundary path="each">
                          <InputComponent
                            {...dataConfig[each]}
                            onChange={onChangeData}
                            key={each}
                            classes={classes}
                            // starFirst
                            textAlign={!isViewMode}
                            isViewMode={isViewMode}
                            errors={uniq([
                              ...dataConfig[each].errors,
                              getErrorMessageByCode(errors, each),
                            ])}
                          />
                        </ErrorBoundary>
                      );
                    })}
                  </Grid>
                </Grid>
                <Label className={classnames({ 'm-left-0': isViewMode })}>
                  {MAP_TITLE.titlAttribute}
                </Label>
                <Grid container>
                  <Grid
                    item
                    xs={isViewMode || versionId ? 12 : 10}
                    className={classnames('p-top-2 p-right-2 p-bottom-2', {
                      // 'p-left-2': !isViewMode,
                    })}
                  >
                    <Grid container>
                      <Grid
                        style={{ textAlign: isViewMode ? 'start' : 'start' }}
                        item
                        xs={2}
                      >
                        <Span style={{ marginRight: '30px', color: '#666666' }}>
                          Conversion event
                        </Span>
                      </Grid>
                      <Grid item sm={10}>
                        <WrapperDisable disabled={versionId}>
                          <UIPerformEventV2
                            moduleConfig={props.moduleConfig}
                            validateKey={main.validateKey}
                            onChange={onChangePerformEventConversion}
                            showHeader={false}
                            isUIConversion
                            isLoading={isLoading}
                            // callback={callback}
                            initData={
                              main.performEvent.eventConversion &&
                              main.performEvent.eventConversion
                            }
                            componentId={props.componentId}
                            showCustomeTitle={false}
                            showTitle={false}
                            defaultOption={[
                              'purchase_product',
                              'transaction_event',
                            ]}
                            // event engament
                            limitEvent={[2]}
                            hasOpenModalConfirm={props.hasOpenModalConfirm}
                            // paramsFetchEvent={{ objectType: 'STORIES' }}
                            isViewMode={isViewMode}
                            eventPropertyService={eventPropertyService}
                            getFullEventTrackingMapService={
                              getFullEventTrackingMapService
                            }
                            getFullEventTrackingService={
                              getFullEventTrackingService
                            }
                            isUsingJourneyTemplate={isUsingJourneyTemplate}
                            showAllSource={showAllSource}
                          />
                        </WrapperDisable>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid
                    item
                    xs={isViewMode || versionId ? 12 : 10}
                    className={classnames('p-top-2 p-right-2 p-bottom-2', {
                      // 'p-left-2': !isViewMode,
                    })}
                  >
                    <Grid container>
                      <Grid item sm={2} />
                      <Grid item sm={10}>
                        <WrapperDisable
                          disabled={versionId}
                          // disabled={main.design === 'preview' && !isViewMode}
                        >
                          <Span>{MAP_TITLE.titleOccurs}</Span>

                          {isViewMode ? (
                            <>
                              {main.occurs.options === 'immediately' ? (
                                <ContentOption>
                                  <CirclePoint />
                                  <Text>{MAP_TITLE.immediately}</Text>
                                </ContentOption>
                              ) : (
                                <ContentOption>
                                  <CirclePoint />
                                  <Text>{MAP_TITLE.immediately}</Text>
                                </ContentOption>
                              )}
                            </>
                          ) : (
                            <RadioGroup
                              aria-label="expriration"
                              name="expriration"
                              value={main.occurs.options}
                              onChange={e =>
                                props.onChangeOccurs({
                                  name: 'options',
                                  value: e.target.value,
                                })
                              }
                            >
                              <FormControlLabel
                                value="immediately"
                                control={<Radio color="primary" />}
                                label={
                                  <LableSelect>
                                    {MAP_TITLE.immediately}
                                  </LableSelect>
                                }
                                disabled={isViewMode}
                              />
                              <FormControlLabel
                                value="specific"
                                control={<Radio color="primary" />}
                                label={
                                  <LableSelect>
                                    {MAP_TITLE.specific}
                                  </LableSelect>
                                }
                                disabled={isViewMode}
                              />
                              {main.occurs.options === 'specific' && (
                                <WrapperStyle
                                  style={{
                                    marginLeft: '25px',
                                    width: '200px',
                                  }}
                                >
                                  <Span style={{ width: '50px' }}>After</Span>

                                  {isViewMode ? (
                                    <Span>{main.occurs.duration}</Span>
                                  ) : (
                                    <UINumberStyled
                                      name="times"
                                      onChange={e =>
                                        props.onChangeOccurs({
                                          name: 'duration',
                                          value: e,
                                        })
                                      }
                                      value={main.occurs.duration}
                                      min={1}
                                      max={MAX_OCCURS[main.occurs.type]}
                                      defaultValue={1}
                                      width={65}
                                    />
                                  )}

                                  <UISelectCondition
                                    use="tree"
                                    isSearchable={false}
                                    options={PERIOD_LIST_REPEAT}
                                    value={PERIOD_MAP_REPEAT[main.occurs.type]}
                                    onChange={value =>
                                      props.onChangeOccurs({
                                        name: 'type',
                                        value: value.value,
                                      })
                                    }
                                    fullWidthPopover={false}
                                    labelWidth="auto"
                                    isViewMode={isViewMode}
                                    // width={100}
                                    // placeholderTranslateCode={props.placeholderTranslateCode}
                                  />
                                </WrapperStyle>
                              )}
                            </RadioGroup>
                          )}
                        </WrapperDisable>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid
                    item
                    xs={isViewMode || versionId ? 12 : 10}
                    className={classnames('p-top-2 p-right-2 p-bottom-2', {
                      // 'p-left-2': !isViewMode,
                    })}
                  >
                    <Grid container>
                      <Grid
                        style={{ textAlign: isViewMode ? 'start' : 'start' }}
                        item
                        xs={2}
                      >
                        <Span style={{ marginRight: '30px', color: '#666666' }}>
                          Attribution model
                        </Span>
                      </Grid>
                      <Grid item sm={10}>
                        <Span>
                          The attribution model determines how much credit each
                          ad interaction gets for your conversions.
                        </Span>
                      </Grid>
                      <Grid
                        style={{ textAlign: isViewMode ? 'start' : 'end' }}
                        item
                        xs={2}
                      >
                        <Span
                          style={{ marginRight: '30px', color: '#666666' }}
                        />
                      </Grid>
                      <Grid item sm={10}>
                        <div>
                          {isViewMode ? (
                            <ContentOption className="m-top-2">
                              <BarChartIcon />
                              <Text className="text-dropdown">Data-driven</Text>
                              <Command
                                className="text-dropdown"
                                style={{
                                  color: '#0F5900',
                                  backgroundColor: '#12B800',
                                  padding: '0px 4px',
                                }}
                              >
                                Recommended
                              </Command>
                            </ContentOption>
                          ) : (
                            <Dropdown
                              disabled
                              isOpen={isOpenDropdown}
                              direction="down"
                              toggle={setIsOpenDropdown}
                            >
                              {/* <Tooltip
                          title={'Select an item'}
                          placement="top-start"
                        > */}
                              <WrapperDropdownToggle
                                disabled
                                widthDropdown="230px"
                                // messageErrors={messageErrors}
                                // disabled={disabled}
                              >
                                <ContentOption>
                                  <BarChartIcon />
                                  <Text className="text-dropdown">
                                    Data-driven
                                  </Text>
                                  <Command className="text-dropdown">
                                    Recommended
                                  </Command>
                                </ContentOption>

                                <ExpandMoreIcon className="icon" />
                              </WrapperDropdownToggle>
                              {/* </Tooltip> */}
                              {/* <WrapperDropdownMenu>
                          <React.Fragment>
                            <WrapperDropdownItem>
                              <p>123</p>
                            </WrapperDropdownItem>
                          </React.Fragment>
                        </WrapperDropdownMenu> */}
                            </Dropdown>
                          )}
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid
                    item
                    xs={isViewMode || versionId ? 12 : 10}
                    className={classnames('p-top-2 p-right-2 p-bottom-2', {
                      // 'p-left-2': !isViewMode,
                    })}
                  >
                    <Grid container>
                      <Grid
                        style={{ textAlign: isViewMode ? 'start' : 'start' }}
                        item
                        xs={2}
                      >
                        <Span
                          style={{
                            marginRight: '30px',
                            color: '#666666',
                          }}
                        >
                          Contribute events
                        </Span>
                      </Grid>
                      <Grid item sm={10}>
                        <WrapperDisable
                          disabled={versionId}
                          // disabled={main.design === 'preview' && !isViewMode}
                        >
                          {contributeEvent.length > 0 && (
                            <UIPerformEventV2
                              moduleConfig={props.moduleConfig}
                              validateKey={main.validateKey}
                              onChange={onChangePerformEventContribute}
                              // callback={callback}
                              initData={
                                main.performEvent.eventContribute &&
                                main.performEvent.eventContribute
                              }
                              componentId={props.componentId}
                              showCustomeTitle={false}
                              showHeader={false}
                              showTitle={false}
                              isConversion
                              isUIConversion
                              isMultiple
                              limitEvent={contributeEvent}
                              hiddeOption
                              defaultOption={['promotion_code_used_tracking']}
                              // title="Conversion event"
                              // disabledEvent={
                              //   !roleActions.has(
                              //     STORY_SETUP_ACTION.EDIT_TRIGGER_NODE_EVENT,
                              //   )
                              // }
                              // disabledEventConditions={
                              //   !roleActions.has(
                              //     STORY_SETUP_ACTION.EDIT_TRIGGER_EVENT_CONDITIONS,
                              //   )
                              // }
                              hasOpenModalConfirm={props.hasOpenModalConfirm}
                              // paramsFetchEvent={{ objectType: 'STORIES' }}
                              isViewMode={isViewMode}
                              eventPropertyService={eventPropertyService}
                              getFullEventTrackingMapService={
                                getFullEventTrackingMapService
                              }
                              getFullEventTrackingService={
                                getFullEventTrackingService
                              }
                              isUsingJourneyTemplate={isUsingJourneyTemplate}
                              showAllSource={showAllSource}
                            />
                          )}
                        </WrapperDisable>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </StyleContentLoading>

              {/* {!versionId && !isViewMode && (
              <StyleFooter>
                <Footer
                  main={main}
                  dataConfig={dataConfig}
                  callback={callback}
                  isViewMode={isViewMode}
                />
              </StyleFooter>
            )} */}
            </StyleContent>
            <ModalConfirmConversion
              isOpen={isOpenConversion}
              toggle={setIsOpenConversion}
              title={MAP_TITLE.notiModal}
              callback={callback}
            >
              {MAP_TITLE.notiDiscardChange}
            </ModalConfirmConversion>
          </WrapperContent>
        </div>
      </ErrorBoundary>
    );
  };
  const renderContent = () => {
    switch (props.use) {
      case 'update':
        return settingContent();

      case 'create':
        return settingContent();

      default:
        break;
    }
  };
  return renderContent();
}

const mapStateToProps = createStructuredSelector({
  dataConfig: makeSelectCreateSettingsDataConfig(),
  main: makeSelectCreateSettings(),
  versionDetail: makeSelectVersionDetail(),
  mainDetail: makeSelectDetailDomainMain(),
});

function mapDispatchToProps(dispatch, props) {
  return {
    init: value => dispatch(init(MODULE_CONFIG.key, value)),
    onSave: value => dispatch(update(MODULE_CONFIG.key, value)),
    onChangeDataConfig: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DATA_CONFIG@@`, value)),
    reset: value => dispatch(reset(`${MODULE_CONFIG.key}`, value)),
    onChangeDesign: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@DESIGN`, value)),
    goToList: () => dispatch(updateValue(`${MODULE_CONFIG.key}@@GO_TO_LIST`)),
    onChangeEvent: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ON_CHANGE_SOURCE`, value)),
    onChangeOccurs: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ON_CHANGE_OCCURS`, value)),
    onChangeTypeIcon: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ON_CHANGE_TYPE_ICON`, value)),
    onValidateEvent: value =>
      dispatch(updateValue(`${MODULE_CONFIG.key}@@ERROR_EVENT`, value)),
    onChangeTab: params => {
      dispatch(updateValue(`conversion-detail@@CHANGE_TAB`, params));
    },
  };
}

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

const withReducer = injectReducer({
  key: MODULE_CONFIG.key,
  reducer,
});
const withSaga = injectSaga({
  key: MODULE_CONFIG.key,
  saga,
});

export default compose(
  withReducer,
  withRouter,
  withConnect,
  withSaga,
)(ContentSettings);
