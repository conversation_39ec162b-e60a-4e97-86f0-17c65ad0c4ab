import styled, { css } from 'styled-components';
import { makeStyles } from '@material-ui/core/styles';
import ToggleButton from 'components/Atoms/ToggleButton/index';
import React from 'react';
import { UILoading } from '@xlab-team/ui-components';

export const StyleFooter = styled.div`
  /* background-color: #fff; */
  width: 100%;
  display: flex;
  justify-content: flex-end;
`;
export const WrapperContent = styled.main`
  width: 100%;
  display: flex;
  justify-content: center;
`;

export const StyleContent = styled.div`
  display: flex;
  flex-direction: column;
  height: fit-content;
  width: 100%;
  padding: 15px;
`;
export const WrapperAutoSize = styled.div`
  display: flex;
  height: fit-content;
  width: fit-content;
`;

export const StyleContentLoading = styled.div`
  position: relative;
`;

export const DivMessageComputing = styled.div`
  background: ${({ bgColor }) => bgColor};
  display: flex;
  width: 100%;
  padding: 20px 0;
  justify-content: center;
  align-items: center;
  font-weight: 600;
`;

export const ImageIcon = styled.img`
  height: 18px;
  width: 18px;
`;

export const UnderConstruction = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const MenuNodata = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
`;

export const ButtonEditPreviewDesign = styled.div`
  position: absolute;
  bottom: 28px;
  right: 28px;
`;

export const StyleToggleButton = styled(ToggleButton)``;

const styleLoadingCreate = {
  height: 'calc(100vh - 58px)',
  overflow: 'hidden',
  position: 'relative',
  boxShadow: 'rgba(0, 0, 0, 0.2) 0px 0px 0.375em 0px',
};

const styleLoadingUpdate = {
  height: 'calc(100vh - 118px)',
  overflow: 'hidden',
  position: 'relative',
  boxShadow: 'rgba(0, 0, 0, 0.2) 0px 0px 0.375em 0px',
};
export const Label = styled.div`
  font-size: 16px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.88;
  letter-spacing: normal;
  text-align: left;
  color: #222;
  margin: 18px 0px 28px 0px;
`;
export function LayoutLoadingCreateSegment({
  isLoading,
  isWhite = true,
  children,
  design = 'update',
}) {
  if (isLoading === true) {
    return (
      <div
        style={design === 'update' ? styleLoadingUpdate : styleLoadingCreate}
      >
        <UILoading isLoading={isLoading} isWhite={isWhite} />
      </div>
    );
  }
  return children;
}

export const RootContainer = styled.div`
  padding-bottom: 30px;
  .MuiFormControl-marginNormal {
    margin-top: 0 !important;
  }
  .MuiInput-underline.Mui-disabled:before {
    border-bottom-style: solid !important;
  }

  .MuiTypography-body1 {
    font-size: 0.813rem !important;
  }
`;

export const useStyles = makeStyles(() => ({
  item: {
    padding: '5px 0',
  },
}));
export const AddConversion = styled.div`
  font-size: 12px;
  font-weight: bold;
  line-height: 1.5;
  letter-spacing: normal;
  text-align: left;
  color: #005fb8;
  margin-bottom: 10px;
  cursor: pointer;
  width: fit-content;
`;
export const ContentOption = styled.div`
  display: flex;
  align-items: center;
`;
export const Text = styled.span`
  margin: 0px 10px 0px 10px;
`;
export const Command = styled.span`
  border: solid 1px #e5e5e5;
  border-radius: 3px;
  opacity: 0.5;
  background-color: rgba(18, 184, 0, 0.15);
`;

export const CirclePoint = styled.div`
  clip-path: circle(50%);
  width: 4px;
  height: 4px;
  background-color: #aaaaaa;
  margin-right: 10px;
`;
