import APP from '../../../../../appConfig';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { getPortalId } from '../../../../../utils/web/cookie';
import { MODELS } from '../config';
import { CURRENT_MODEL_SUPPORTABLE } from './config';
import ModelMedias from './medias';

export const getBreadcrums = () => [
  {
    key: 2,
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: '/profile/rfms',
    },
    display: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'All Predictive Models',
    ),
  },
  {
    key: 3,
    display: getTranslateMessage(TRANSLATE_KEY.KHONG_CO, 'Create New Model'),
  },
];

export const TRANSLATE_MAP = {
  btn: {
    create: getTranslateMessage(TRANSLATE_KEY._ACT_CREATE, 'Create'),
    cancel: getTranslateMessage(TRANSLATE_KEY._ACT_CANCEL, 'Cancel'),
  },
};

export const CONTENT_BY_MODEL = {
  [MODELS.RFM_MODEL]: {
    Media: ModelMedias.RFMModelMedia,
    title: getTranslateMessage(TRANSLATE_KEY.KHONG_CO, 'RFM Model'),
    descripton: getTranslateMessage(
      TRANSLATE_KEY._,
      'RFM stands for Recency, Frequency, and Monetary value. With this model, you can segment your customer base into meaningful groups based on their purchasing behavior, helping you tailor your marketing strategies, improve customer retention, and maximize revenue.',
    ),
    recommended: [
      {
        title: 'Identify High-Value Customers',
        description:
          'Use RFM analysis to pinpoint your most valuable customers and create personalized marketing campaigns to reward their loyalty and encourage repeat purchases.',
      },
      {
        title: 'Boost Customer Retention',
        description:
          'Detect at-risk customers with low recency scores and re-engage them with targeted promotions or special offers to bring them back.',
      },
      {
        title: 'Optimize Marketing Campaigns',
        description:
          'Segment customers based on RFM scores and tailor your marketing efforts to each group, ensuring higher relevance and effectiveness.',
      },
      {
        title: 'Product Recommendations',
        description:
          'Use the insights from RFM analysis to suggest products that match the preferences and purchasing behavior of different customer segments.',
      },
      {
        title: 'Loyalty Program Development',
        description:
          'Design and implement loyalty programs that cater to the needs and behaviors of your most frequent and highest-spending customers.',
      },
      {
        title: 'Resource Allocation',
        description:
          'Allocate your marketing and customer service resources more efficiently by focusing on segments that offer the highest potential for growth and profitability.',
      },
    ],
  },
  [MODELS.E_RFM_MODEL]: {
    Media: ModelMedias.ERFMModelMedia,
    title: getTranslateMessage(TRANSLATE_KEY.KHONG_CO, 'eRFM Model'),
    descripton: getTranslateMessage(
      TRANSLATE_KEY._,
      'eRFM Model is a broader RFM model analyzing customers based on their',
    ),
    recommended: [],
  },
  [MODELS.PROPENSITY_BUY]: {
    Media: ModelMedias.PropensityBuyModelMedia,
    title: getTranslateMessage(TRANSLATE_KEY.KHONG_CO, 'Propensity to Buy'),
    descripton: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'Analyze the likehood of next purchase',
    ),
    recommended: [],
  },
  [MODELS.LIFECYCLE_STAGE]: {
    Media: ModelMedias.PropensityChurnModelMedia,
    title: getTranslateMessage(TRANSLATE_KEY._, 'Lifecycle Stages'),
    descripton: getTranslateMessage(
      TRANSLATE_KEY._,
      "'Lifecycle stage' model empowers you to understand and optimize each stage of the customer journey, from awareness to advocacy. By leveraging this model, you can create targeted marketing strategies, enhance customer engagement, and maximize customer lifetime value.",
    ),
    recommended: [
      {
        title: 'Boost Brand Awareness',
        description:
          'Use data-driven insights to create impactful marketing campaigns that capture the attention of potential customers.',
      },
      {
        title: 'Enhance Lead Nurturing',
        description:
          'Develop personalized content and communication strategies to guide prospects through the consideration stage.',
      },
      {
        title: 'Increase Conversions',
        description:
          'Optimize your sales funnel with targeted promotions and streamlined purchasing processes to turn prospects into buyers.',
      },
      {
        title: 'Foster Loyalty',
        description:
          'Implement loyalty programs and personalized offers to keep customers engaged and encourage repeat purchases.',
      },
      {
        title: 'Maximize Retention',
        description:
          'Use predictive analytics to identify at-risk customers and implement retention strategies to prevent churn.',
      },
      {
        title: 'Monitor Customer Health',
        description:
          'Track key metrics at each stage of the lifecycle to identify areas for improvement and ensure customer satisfaction.',
      },
    ],
  },
};

export const getModelOptions = () => {
  const portalId = getPortalId();

  const modelSupportable = [
    33167,
    554926187,
    561236459,
    1132,
    564890586,
    564889906,
    564890159,
    564890243,
    564891226,
    564888929,
  ].includes(+portalId)
    ? CURRENT_MODEL_SUPPORTABLE
    : [MODELS.RFM_MODEL];

  return modelSupportable.map(modelType => {
    const { title, descripton, Media, recommended } = CONTENT_BY_MODEL[
      modelType
    ];

    return {
      key: modelType.toString(),
      value: modelType,
      title,
      Media,
      descripton,
      disable: !modelSupportable.includes(modelType),
      recommended,
    };
  });
};
