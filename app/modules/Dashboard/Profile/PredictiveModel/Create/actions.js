import ReduxTypes from '../../../../../redux/constants';
import { MODULE_CONFIG } from './config';

const PREFIX = MODULE_CONFIG.key;

export const Types = {
  updateModelName: `${PREFIX}@@UPDATE_MODEL_NAME${ReduxTypes.UPDATE_VALUE}`,
  updateModelDescription: `${PREFIX}@@UPDATE_MODEL_DESCRIPTION${
    ReduxTypes.UPDATE_VALUE
  }`,

  updateListExistedSegments: `${PREFIX}@@UPDATE_LIST_EXISTED_SEGS`,
  updateListExistedAttributes: `${PREFIX}@@UPDATE_LIST_EXITED_ATTRS`,
  updatelistTriggerEvents: `${PREFIX}@@UPDATE_LIST_TRIGGER_EVENTS${
    ReduxTypes.UPDATE_VALUE
  }`,
  updateActiveStep: `${PREFIX}@@ACTIVE_STEP${ReduxTypes.UPDATE_VALUE}`,
  changeStepSettings: `${PREFIX}@@CHANGE_STEP_SETTINGS${
    ReduxTypes.UPDATE_VALUE
  }`,
  initFromNotiDone: `${PREFIX}@@FROM_NOTI${ReduxTypes.INIT_DONE}`,
  initFromNoti: `${PREFIX}@@FROM_NOTI${ReduxTypes.INIT}`,
  init: `${PREFIX}${ReduxTypes.INIT}`,
  initDone: `${PREFIX}${ReduxTypes.INIT_DONE}`,
  initFail: `${PREFIX}@@INIT_FAIL`,
  reset: `${PREFIX}${ReduxTypes.RESET}`,
  resetViewName: `${PREFIX}@@RESET_VIEW_NAME${ReduxTypes.UPDATE_VALUE}`,
  updateIsSaving: `${PREFIX}@@UPDATE_IS_SAVING${ReduxTypes.INIT}`,
  updateIsLoading: `${PREFIX}@@UPDATE_IS_LOADING${ReduxTypes.INIT}`,
  setViewName: `${PREFIX}@@SET_VIEW_NAME${ReduxTypes.UPDATE_VALUE}`,
  updatePercentModalProgress: `${PREFIX}@@PERCENT_MODAL_PROGRESS${
    ReduxTypes.UPDATE_VALUE
  }`,
  toggleEnableValidate: `${PREFIX}@@TOGGLE_ENABLE_VALIDATE_ERROR${
    ReduxTypes.UPDATE_VALUE
  }`,
  updateModalProgress: `${PREFIX}@@TOGGLE_MODAL_PROGRESS${
    ReduxTypes.UPDATE_VALUE
  }`,
  autoUpdateValidate: `${PREFIX}@@AUTO_UPDATE_VALIDATE_MODEL_SETTINGS`,
  updateModelType: `${PREFIX}@@UPDATE_MODEL_TYPE${ReduxTypes.UPDATE_VALUE}`,
  toggleModalProcess: `${PREFIX}@@TOGGLE_MODAL_PROCESS${
    ReduxTypes.UPDATE_VALUE
  }`,
  toggleModalCancel: `${PREFIX}@@TOGGLE_MODAL_CANCEL${ReduxTypes.UPDATE_VALUE}`,
  validateModelSettings: `${PREFIX}@@VALIDATE_MODEL_SETTINGS`,
  validateModelSettingsDone: `${PREFIX}@@VALIDATE_MODEL_SETTINGS_DONE`,
  updateValidateSettings: `${PREFIX}@@VALIDATE_SETTINGS${
    ReduxTypes.UPDATE_VALUE
  }`,
  saveModel: `${PREFIX}@@SAVE_MODEL`,
  createModelDone: `${PREFIX}@@CREATE_NEW_MODEL_DONE`,
  createModelFail: `${PREFIX}@@CREATE_NEW_MODEL_FAIL`,
  updateModelDone: `${PREFIX}@@UPDATE_MODEL_DONE`,
  updateModelFail: `${PREFIX}@@UPDATE_MODEL_FAIL`,
  initData: `${PREFIX}@@INIT_DATA_DETAIL`,
  saveDesign: `${PREFIX}@@SAVE_DESIGN`,
  prepareData: `${PREFIX}@@PREPARE_DATA`,
  prepareDataSuccess: `${PREFIX}@@PREPARE_DATA_SUCCESS`,
  prepareDataFail: `${PREFIX}@@PREPARE_DATA_FAIL`,
  pollingGetStatusPD: {
    received: `${PREFIX}@@RECEIVED_PPDATA_STATUS`,
    fail: `${PREFIX}@@POLLING_CHECK_PPDATA/GET_ERROR_S`,
    done: `${PREFIX}@@POLLING_CHECK_PPDATA/GET_DONE_S`,
  },
  updateTrainingMode: `${PREFIX}@@UPDATE_TRAINING_MODE`,
  resetTrainModel: `${PREFIX}@@RESET_TRAIN_MODEL`,
  updateTrainModel: `${PREFIX}@@UPDATE_TRAIN_MODEL`,
  toggleModalConfirmSave: `${PREFIX}@@TOGGLE_MODAL_CONFIRM_SAVE${
    ReduxTypes.UPDATE_VALUE
  }`,
  saveAndBuildNow: `${PREFIX}@@SAVE_AND_BUILD_NOW`,
  saveAndBuildSchedule: `${PREFIX}@@SAVE_AND_BUILD_SCHEDULE`,
  cancelModalConfirmSave: `${PREFIX}@@CANCEL_SAVE`,
};

export const Actions = {
  resetTrainModel: () => ({
    type: Types.resetTrainModel,
  }),
  updateTrainModel: data => ({
    type: Types.updateTrainModel,
    payload: { data },
  }),
  updateTrainingMode: mode => ({
    type: Types.updateTrainingMode,
    payload: { mode },
  }),
  prepareData: ({ prepareDataInfo, backupData }) => ({
    type: Types.prepareData,
    payload: { prepareDataInfo, backupData },
  }),
  receivedPPDataStatus: status => ({
    type: Types.pollingGetStatusPD.received,
    payload: { status },
  }),
  prepareDataSuccess: viewName => ({
    type: Types.prepareDataSuccess,
    payload: { viewName },
  }),
  prepareDataFail: () => ({
    type: Types.prepareDataFail,
  }),
  updateModelName: name => ({
    type: Types.updateModelName,
    payload: { name },
  }),
  updateModelDescription: description => ({
    type: Types.updateModelDescription,
    payload: { description },
  }),
  changeStepSettings: stepIndex => ({
    type: Types.changeStepSettings,
    payload: { stepIndex },
  }),
  updateActiveStep: stepIndex => ({
    type: Types.updateActiveStep,
    payload: { stepIndex },
  }),
  resetViewName: () => ({
    type: Types.resetViewName,
  }),
  init: ({ design, modelType, activeRow, isVersionHistory }) => ({
    type: Types.init,
    payload: { design, modelType, activeRow, isVersionHistory },
  }),
  initFromNoti: notificationId => ({
    type: Types.initFromNoti,
    payload: { notificationId },
  }),
  initFromNotiDone: () => ({
    type: Types.initFromNotiDone,
  }),
  initData: data => ({
    type: Types.initData,
    payload: data,
  }),
  updateModelType: modelType => ({
    type: Types.updateModelType,
    payload: { modelType },
  }),
  initFail: () => ({ type: Types.initFail }),
  initDone: ({
    activeRow,
    isVersionHistory,
    isInitFromNoti,
    design,
    main,
  } = {}) => ({
    type: Types.initDone,
    payload: { activeRow, isVersionHistory, isInitFromNoti, design, main },
  }),
  setViewName: viewName => ({
    type: Types.setViewName,
    payload: { viewName },
  }),
  updateModalProgress: updated => ({
    type: Types.updateModalProgress,
    payload: { updated },
  }),
  updateIsSaving: saving => ({
    type: Types.updateIsSaving,
    payload: { saving },
  }),
  updateIsLoading: loading => ({
    type: Types.updateIsLoading,
    payload: { loading },
  }),
  validateModelSettings: (options = {}) => ({
    type: Types.validateModelSettings,
    payload: {
      options,
    },
  }),
  validateModelSettingsDone: errors => ({
    type: Types.validateModelSettingsDone,
    payload: { errors },
  }),
  toggleEnableValidate: enable => ({
    type: Types.toggleEnableValidate,
    payload: { enable },
  }),
  toggleModalCancel: isOpen => ({
    type: Types.toggleModalCancel,
    payload: { isOpen },
  }),
  updateValidateSettings: (errors, options = {}) => {
    const { enable } = options;

    return {
      type: Types.updateValidateSettings,
      payload: {
        errors,
        options: {
          enable,
        },
      },
    };
  },
  createModelDone: res => ({
    type: Types.createModelDone,
    payload: { res },
  }),
  updateModelDone: res => ({
    type: Types.updateModelDone,
    payload: { res },
  }),
  saveModel: ({ serializeSettings }) => ({
    type: Types.saveModel,
    payload: { serializeSettings },
  }),
  saveDesign: data => ({
    type: Types.saveDesign,
    payload: data,
  }),
  reset: ({ reason = '' }) => ({
    type: Types.reset,
    payload: { reason },
  }),
  updatelistTriggerEvents: data => ({
    type: Types.updatelistTriggerEvents,
    payload: { data },
  }),
  updateListExistedAttributes: data => ({
    type: Types.updateListExistedAttributes,
    payload: { data },
  }),
  updateListExistedSegments: data => ({
    type: Types.updateListExistedSegments,
    payload: { data },
  }),
  toggleModalConfirmSave: data => ({
    type: Types.toggleModalConfirmSave,
    payload: { data },
  }),
  saveAndBuildNow: () => ({
    type: Types.saveAndBuildNow,
    payload: { isBuild: true },
  }),
  saveAndBuildSchedule: () => ({
    type: Types.saveAndBuildSchedule,
    payload: { isBuild: false },
  }),

  cancelModalConfirmSave: () => ({
    type: Types.cancelModalConfirmSave,
  }),
};

// Schdule Update
export const ScheduleUpdateTypes = {
  update: `${PREFIX}@@UPDATE_SCHEDULE_UPDATE`,
  updateTriggerEvent: `${PREFIX}@@UPDATE_TRIGGER_EVENT`,
  updateNotiSetup: `${PREFIX}@@NOTIFICATION_SETUP@@${ReduxTypes.UPDATE_VALUE}`,
  updateComputeSchedule: `${PREFIX}@@COMPUTATION_SCHEDULE@@${
    ReduxTypes.UPDATE_VALUE
  }`,
  updateApplyDataByField: `${PREFIX}@@APPLY_DATA_BY_FIELD@@${
    ReduxTypes.UPDATE_VALUE
  }`,
};

export const ScheduleUpdateActions = {
  updateNotiSetup: value => ({
    type: ScheduleUpdateTypes.updateNotiSetup,
    payload: value,
  }),

  updateComputeSchedule: value => ({
    type: ScheduleUpdateTypes.updateComputeSchedule,
    payload: value,
  }),

  updateApplyDataByField: ({ field, data }) => ({
    type: ScheduleUpdateTypes.updateApplyDataByField,
    payload: { field, data },
  }),

  updateTriggerEvent: ({ data }) => ({
    type: ScheduleUpdateTypes.updateTriggerEvent,
    payload: { data },
  }),
};
