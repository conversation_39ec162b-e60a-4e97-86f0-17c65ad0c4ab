/* eslint-disable no-multi-assign */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
/* eslint-disable arrow-body-style */
/* eslint-disable no-restricted-syntax */
import { OrderedMap } from 'immutable';
import { toUIComputeSchedule } from 'containers/Segment/Content/GeneralSettings/utils';
import {
  set,
  get,
  isFunction,
  omit,
  pick,
  xor,
  upperCase,
  isEmpty,
} from 'lodash';
import moment from 'moment';
import isEqual from 'react-fast-compare';
import APP from '../../../../../appConfig';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { omitDeep } from '../../../../../utils/common';
import { DEFAULT_STATUS } from '../../../../../utils/constants';
import {
  getErrorsByCode,
  getErrorsByFieldCodes,
} from '../../../../../utils/validate';
import { getCurrentUserId } from '../../../../../utils/web/cookie';
import { getUserLocaleLanguage } from '../../../../../utils/web/portalSetting';
import { getUntitledName } from '../../../../../utils/web/properties';
import { serializeLabelToCodeAttr } from '../../../../../utils/web/utils';
import {
  getBrowser,
  getClientOS,
} from '../../../MarketingHub/Journey/Create/utils.version';
import { MODEL_LABELS, QS_KEYS } from '../config';
import { OPTION_TYPE } from './components/DataUpdate/utils';
import { STEP_SETTING_KEYS } from './config';
import { getModuleUrl } from '../utils';

export { getErrorsByCode, getErrorsByFieldCodes };

export const NOTI = {
  saveSuccess: design => ({
    id: 'success',
    message: design === 'create' ? 'Created' : 'Updates saved!',
    translateCode:
      design === 'create'
        ? TRANSLATE_KEY._NOTIFICATION_CREATE_SUCCESS
        : TRANSLATE_KEY._NOTIFICATION_SAVED_CHANGES_SUCCESS,
    timeout: 1500,
    timestamp: new Date().getTime(),
    type: 'success',
  }),
  nameExist: {
    id: 'error',
    message: 'This name already existed',
    timeout: 1500,
    timestamp: new Date().getTime(),
    type: 'danger',
  },
};

export const TRANSLATE_MAP = {
  progressPrepareDataSTitle: getTranslateMessage(
    TRANSLATE_KEY.KHONG_CO,
    'It takes a while to prepare data, please wait!',
  ),
  tabs: {
    predictiveModels: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'Predictive Models',
    ),
    computationHistory: getTranslateMessage(
      TRANSLATE_KEY.KHONG_CO,
      'Computation History',
    ),
  },
};

export const REPEAT_BY_OPTIONS = {
  none: { label: 'One', value: 'none' },
  real_time: { label: 'Real-time', value: 'real_time' },
  hours: { label: 'Hour', value: 'hours', min: 1, max: 24 },
  days: { label: 'Day', value: 'days', min: 1, max: 365 },
  weeks: { label: 'Week', value: 'weeks', min: 1, max: 52 },
  months: { label: 'Month', value: 'months', min: 1, max: 12 },
};

const labelDay = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_GRANULARITY_DAY,
  'Day',
);

const labelWeek = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_GRANULARITY_WEEK,
  'Week',
);

const labelMonth = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_GRANULARITY_MONTH,
  'Month',
);

const labelQuarter = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_GRANULARITY_QUARTER,
  'Quarter',
);

const labelYear = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_GRANULARITY_YEAR,
  'Year',
);

export const DAY_OVERWRITE = [
  {
    label: labelDay,
    value: 'day',
  },
  {
    label: labelWeek,
    value: 'week',
  },
  {
    label: labelMonth,
    value: 'month',
  },
  {
    label: labelQuarter,
    value: 'quarter',
  },
  {
    label: labelYear,
    value: 'year',
  },
];

export const defaultRepeatByOptions = [
  REPEAT_BY_OPTIONS.none,
  // REPEAT_BY_OPTIONS.real_time,
  // REPEAT_BY_OPTIONS.hours,
  REPEAT_BY_OPTIONS.days,
  REPEAT_BY_OPTIONS.weeks,
  REPEAT_BY_OPTIONS.months,
];

export const END_TIME_OPTIONS = {
  NEVER: 'none',
  ON: 'on_day',
  AFTER: 'after_num_running',
};

export const FORMAT_FULL_DATE = 'YYYY-MM-DD HH:mm:ss';
export const FORMAT_DATE = 'YYYY-MM-DD';
export const DEFAULT_START_TIME_HOUR = '06:00';

export const currentTime = moment(new Date(), FORMAT_FULL_DATE);
export const defaultFullStartTime = moment(new Date(), FORMAT_FULL_DATE).set({
  hour: 2,
  minute: 0,
  second: 0,
});

export const defaultStartTime = {
  day:
    // If currentTime > currentDate 02:00:00 => currentTime + 1 day
    currentTime > defaultFullStartTime
      ? defaultFullStartTime.add(1, 'days').format(FORMAT_DATE)
      : defaultFullStartTime.format(FORMAT_DATE),
  hour: DEFAULT_START_TIME_HOUR,
};

export const defaultEndTime = {
  day: moment(defaultStartTime.day, FORMAT_DATE)
    .add(7, 'days')
    .format(FORMAT_DATE),
  hour: '02:00',
};

export const defaultComputationSchedule = {
  repeatType: REPEAT_BY_OPTIONS.weeks.value,
  repeatOnValue: [1],
  repeatValue: 1,
  repeatStartTime: defaultStartTime,
  endTime: {
    type: END_TIME_OPTIONS.NEVER,
    numRunning: 1,
    onDay: defaultEndTime,
  },
};

export function initValueComputeSchedule() {
  return {
    type: 'dynamic',
    isLoading: false,
    ...defaultComputationSchedule,
  };
  // {
  //   "time_unit": "day" || "hour" ,
  //   "value": 1,
  //   "type":"static/dynamic"
  // }
}

export const toAPIData = ({ main, settingFields, options = {} }) => {
  const { withVersionInfo = false, versionDescription = '' } = options;

  return {
    modelName: get(main, 'name'),
    modelNameMultilang: {
      EN: get(main, 'name'),
      DEFAULT_LANG: 'EN',
    },
    description: get(main, 'description'),

    ...settingFields,

    ...(withVersionInfo && {
      versionInfo: {
        os: getClientOS(),
        browser: getBrowser(),
        description: versionDescription,
      },
    }),
  };
};

export const getBreadcrumbs = ({ editNameConfig }) => [
  {
    key: 2,
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: '/profile/rfms',
    },
    display: getTranslateMessage(
      TRANSLATE_KEY._PREDICT_MODEL_ALL_PREDICTIVE_MODELS,
      'All Predictive Models',
    ),
  },
  {
    key: 3,
    display: getTranslateMessage(
      TRANSLATE_KEY._PREDICT_MODEL_CREATE_NEW_RFM,
      'Create New RFM',
    ),
    nameEditConfig: {
      ...editNameConfig,
      nameKey: 'name',
    },
  },
];

export const checkIsBuild = (oldModel, newModel) => {
  const excludedPaths = ['alertSetting', 'computeSchedule'];

  return isModifield(oldModel, newModel, excludedPaths);
};

export const checkIsNewVersion = (oldModel, newModel) => {
  return isModifield(oldModel, newModel);
};

export const isModifield = (oldObj, newObj, excludedPaths = []) => {
  const defaultExculdePaths = [
    'modelName',
    'modelType',
    'modelNameMultilang',
    'description',
    'properties',
    'dataCondition.condition',
  ];

  const oldKeys = Object.keys(oldObj);
  const newKeys = Object.keys(newObj);

  const paths = [
    ...xor(oldKeys, newKeys),
    ...excludedPaths,
    ...defaultExculdePaths,
  ];

  const tempOld = omitDeep(oldObj, paths);
  const tempNew = omitDeep(newObj, paths);

  const isSimilar = isEqual(tempOld, tempNew);

  // console.log({ tempOld, tempNew, isSimilar });

  return !isSimilar;
};

export const getNotificationDefault = () => ({
  alertAccountIds: [+getCurrentUserId()],
  alertScopes: {
    success: [],
    failure: ['email', 'app_push'],
  },
});

export const mapRefineUI = data => {
  let temp = OrderedMap({});
  Object.keys(data).forEach(key => {
    temp = temp.set(
      key,
      OrderedMap({
        ...data[key],
      }),
    );
  });

  return temp;
};

export const getAttributeMsg = (attr = {}) => {
  let variant = null;
  let msg = null;

  if (attr.status === DEFAULT_STATUS.ARCHIVED) {
    variant = 'error';
    msg = 'This attribute is archived';
  }

  if (attr.status === DEFAULT_STATUS.REMOVED) {
    variant = 'error';
    msg = 'This attribute is removed. Please choose another attribute';
  }

  if (attr.status === DEFAULT_STATUS.DISABLED) {
    variant = 'warning';
    msg = getTranslateMessage(
      TRANSLATE_KEY._INFO_OBJECT_DISABLED,
      'Data-updating of this {module_name} has been disabled. You still can use the output value of the last time it is processed',
      { module_name: 'attribute' },
    );
  }

  return { variant, msg };
};

export const getErrorsByPath = (errors = [], path = []) =>
  errors.filter(error => path.every(field => error.path.includes(field)));

export const getErrorsByPaths = (errors = [], paths = []) => {
  return paths.reduce((acc, path) => {
    const errorsForPath = getErrorsByPath(errors, path);

    return acc.concat(errorsForPath);
  }, []);
};

export const generateApplyOptionName = ({
  modelType,
  modelName = '',
}) => label => {
  if (isEmpty(modelName)) return label;

  let prefixName = modelName;

  if (prefixName) {
    prefixName = generateModelName(modelType);
  }

  return `[${modelName}] ${label}`;
};

export const generateModelName = modelType =>
  getUntitledName(`Untitled ${MODEL_LABELS[modelType]}`);

export const genAttributesApplyOptions = ({
  options,
  modelType,
  modelName,
}) => {
  let result = generateObjectApplyOptions({ options, modelType, modelName });

  result = result.map(opt => ({
    ...opt,
    itemPropertyName: genAttrCode(opt.value.name),
  }));

  return result;
};

export const generateObjectApplyOptions = ({ options, modelType, modelName }) =>
  options.map(item => ({
    name: item.id,
    label: item.label,
    id: item.id,
    key: item.id,
    option: 'new',
    value: {
      name: generateApplyOptionName({ modelType, modelName })(item.label),
      description: '',
      isReNamed: false,
    },
  }));

export const refinePropertiesAPIToCondtionsAPI = (
  refineWithPropertiesAPI = { OR: [{ AND: [] }] },
) => {
  const conditionAND = get(refineWithPropertiesAPI, 'OR.0.AND').map(
    condition => {
      const conditionType = condition.item_type_id ? 'item' : 'event';

      const temp = {
        ...condition,
        type: conditionType,
        condition_property_name: get(condition, 'metadata.itemPropertyName'),
      };

      return pick(temp, [
        'type',
        'value',
        'operator',
        'data_type',
        'item_type_id',
        'condition_property_name',
      ]);
    },
  );

  return { OR: [{ AND: conditionAND }] };
};

export const toUITriggerEvents = ({
  triggerEvent = [],
  listTriggerEvents = [],
}) => {
  const result = [];

  listTriggerEvents.forEach(apiEvent => {
    const selectedEvent = triggerEvent.find(
      event => apiEvent.eventTrackingName === event.eventTrackingName,
    );

    if (selectedEvent) {
      result.push({
        ...pick(apiEvent, 'eventTrackingName'),
        enabled: !!selectedEvent.enabled,
      });
    }
  });

  return result;
};

export const toAPIDataUpdate = dataUpdate => {
  const mapFn = opt => {
    const { option, value, dataSelect } = opt;

    return {
      id: opt.id,
      name: opt.name,
      option,
      value: pick(value, ['name', 'value']),

      ...(dataSelect && {
        dataSelect,
      }),
    };
  };

  const mapAttrFn = opt => {
    const temp = mapFn(opt);

    const lang = upperCase(getUserLocaleLanguage()) || 'EN';

    return {
      ...temp,
      itemPropertyName: opt.itemPropertyName || genAttrCode(opt.value?.name),
      propertyDisplayMultilang: {
        [lang]: opt.value?.name || '',
        DEFAULT_LANG: lang,
      },
    };
  };

  const result = {
    segments: (dataUpdate.segments || []).map(mapFn),
    attributes: (dataUpdate.attributes || []).map(mapAttrFn),
  };

  return result;
};

export const toUIApplyModel = ({ listTriggerEvents, modelDetail, state }) => {
  const { properties, dataUpdate, computeSchedule } = modelDetail;

  const applyModel = {
    notificationSetup: modelDetail.alertSetting,

    dataUpdate: toUIDataUpdate({
      ...state.dataUpdate,

      ...dataUpdate,
    }),

    computeSchedule: toUIComputeSchedule(computeSchedule),

    triggerEvent: toUITriggerEvents({
      listTriggerEvents,
      triggerEvent:
        properties?.applyModel?.triggerEvent || state.triggerEvent || [],
    }),
  };

  return { applyModel };
};

export const toUIDataUpdate = dataUpdate => {
  const mapFn = opt => {
    const { option, dataSelect, value } = opt;

    return {
      id: opt.id,
      name: opt.name,
      label: opt.label,
      value: pick(value, ['name']),
      option,

      ...(option === OPTION_TYPE.UPDATE && {
        dataSelect: { id: dataSelect?.id },
      }),
    };
  };

  const mapAttrFn = opt => ({
    ...mapFn(opt),
    itemPropertyName: opt.itemPropertyName,
  });

  const result = {
    ...dataUpdate,
    segments: (dataUpdate.segments || []).map(mapFn),
    attributes: (dataUpdate.attributes || []).map(mapAttrFn),
  };

  return result;
};

export const reassignListApply = ({
  listExistedObjs = [],
  listApplyObjs = [],
  allOptions = [],
  opts = {},
}) => {
  const { modelName, updateObjNotExist, mapObjFromExistedObj } = opts;

  const existEqualityFn = obj => existedObj => {
    if (isFunction(opts.existEqualityFn)) {
      return opts.existEqualityFn({ obj, existedObj });
    }

    return existedObj.id === obj.dataSelect?.id;
  };

  const updateObjApplyWithModelName = ({ updatedApplyObj }) => {
    const createOption = allOptions.find(opt => opt.id === updatedApplyObj.id);

    if (modelName && createOption) {
      const newName = generateApplyOptionName({ modelName })(
        createOption.label,
      );

      updatedApplyObj.value.name = newName;
    }
  };

  let result = [...listApplyObjs];

  result = result.map(applyObj => {
    const { option, dataSelect } = applyObj;

    let updatedApplyObj = { ...applyObj };

    if (option === OPTION_TYPE.UPDATE && dataSelect) {
      const existedObj = listExistedObjs.find(existEqualityFn(applyObj));

      if (existedObj && isFunction(mapObjFromExistedObj)) {
        updatedApplyObj = mapObjFromExistedObj({
          obj: updatedApplyObj,
          existedObj,
        });
      }

      if (!existedObj) {
        updatedApplyObj.option = OPTION_TYPE.NEW;
        updatedApplyObj.value.name = '';

        updatedApplyObj = omit(updatedApplyObj, ['dataSelect']);

        updateObjApplyWithModelName({ updatedApplyObj });

        if (isFunction(updateObjNotExist)) {
          updatedApplyObj = updateObjNotExist({
            currentObj: updatedApplyObj,
            initialObj: applyObj,
          });
        }
      }
    }

    if (option === OPTION_TYPE.NEW && !option.isRenamed) {
      updateObjApplyWithModelName({ updatedApplyObj });
    }

    return updatedApplyObj;
  });

  return result;
};

export const reassignListAttrApply = ({
  listApplyAttrs = [],
  listExistedAttrs = [],
  allOptions = [],
  opts = {},
}) => {
  const updateObjNotExist = ({ currentObj }) => {
    const result = { ...currentObj };

    const currentName = currentObj?.value?.name;

    const itemPropertyName = genAttrCode(currentName);

    return { ...result, itemPropertyName };
  };

  const mapObjFromExistedObj = ({ obj, existedObj }) => {
    const { propertyDisplayMultilang: multilang } = existedObj;

    if (isEmpty(multilang)) return obj;

    const { DEFAULT_LANG } = multilang;

    return set({ ...obj }, 'value.name', multilang[DEFAULT_LANG]);
  };

  return reassignListApply({
    listApplyObjs: listApplyAttrs,
    listExistedObjs: listExistedAttrs,
    allOptions,
    opts: {
      ...opts,
      updateObjNotExist,
      mapObjFromExistedObj,
    },
  });
};

export const genAttrCode = label => {
  const result = serializeLabelToCodeAttr(label);

  return String(result).slice(0, 50);
};

export const equalityCheck = (a, b) => {
  if (a === b) return true;

  if (OrderedMap.isOrderedMap(a) && OrderedMap.isOrderedMap(b)) {
    return a.equals(b);
  }

  return isEqual(a, b);
};

export const serializeBasicBarchart = (data, zoombarRangeDefault) => {
  const [min = 0, max = 0] = zoombarRangeDefault;

  const filledData = fillDataBarchart({
    data: Object.values(data),
    min,
    max,
    result: { customers: 0 },
  });

  return filledData.map(item => ({
    min: item.min,
    max: item.max,
    value: item.customers,
  }));
};

export const fillDataBarchart = ({
  data,
  min,
  max,
  result: fillValue = {},
}) => {
  if (!Array.isArray(data)) return [];

  if (min < 0) min = 0;

  const filledData = [...data];
  const dataRange = data[0].max - data[0].min;

  // Fill start part
  let currentMin = data.at(0).min;
  if (typeof currentMin === 'number') {
    while (currentMin > min) {
      const itemMax = currentMin;
      const itemMin = (currentMin =
        currentMin - dataRange < 0 ? 0 : currentMin - dataRange);

      filledData.unshift({
        min: itemMin,
        max: itemMax,
        ...fillValue,
        // value: fillValue,
      });
    }
  }

  // Fill end part
  let currentMax = data.at(-1).max;
  if (typeof currentMax === 'number') {
    while (currentMax < max) {
      const itemMin = currentMax;
      const itemMax = (currentMax =
        currentMax + dataRange > max ? max : currentMax + dataRange);

      filledData.push({
        min: itemMin,
        max: itemMax,
        ...fillValue,
      });
    }
  }

  return filledData;
};

export const getErrorsByStep = (errors, step) =>
  errors.filter(error => error?.params?.step === step);

export const toFePersona = data => {
  const dataOut = {};

  Object.keys(data).forEach(key => {
    dataOut[key] = {
      scores: data[key],
    };
  });

  return dataOut;
};

export const getDisableSteps = (errors, activeStep) => {
  let disableFromStep;

  const stepValues = Object.values(STEP_SETTING_KEYS);

  if (getErrorsByStep(errors, activeStep).length) {
    disableFromStep = activeStep;
  } else {
    disableFromStep = stepValues
      .sort((a, b) => a - b)
      .find(step => getErrorsByStep(errors, step).length > 0);
  }

  if (disableFromStep === undefined) return [];

  const disableSteps = stepValues.filter(step => step > disableFromStep);

  return disableSteps;
};

export const getModelLinkCreate = ({ modelType, copyId }) => {
  const newParams = new URLSearchParams();

  newParams.set(QS_KEYS.create, 1);
  newParams.set(QS_KEYS.modelType, modelType);
  newParams.set(QS_KEYS.copyId, copyId);

  return `${getModuleUrl()}?${newParams.toString()}`;
};

export function isValidProgressRange(range) {
  return (
    range &&
    typeof range.min === 'number' &&
    typeof range.max === 'number' &&
    range.min >= 0 &&
    range.max <= 100 &&
    range.min <= range.max
  );
}
