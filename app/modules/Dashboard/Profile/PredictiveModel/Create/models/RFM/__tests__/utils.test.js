import { OrderedMap } from 'immutable';
import {
  fnGenScoreCard,
  fnGetDisableSteps,
  fnGetMinMaxFromInputRanges,
  fnGetMinMaxOfPersonasConfig,
  fnGetRangesWithInputRanges,
  fnGetScoreWithMaxValue,
  fnSerializeCustomInputRanges,
} from './__mocks__/utils';

import {
  bindValidateToCondition,
  genScoreCard,
  getMinMaxOfPersonasConfigScores,
  getMinMaxRangeFromInputRanges,
  getRangesWithInputRanges,
  getScoreWithMaxValue,
  mapDatatoSelect,
  serializeCustomInputRanges,
  toMapdataMultiple,
} from '../utils';
import { getDisableSteps } from '../../../utils';

describe('test case mapDatatoSelect function', () => {
  it('return [] when data is empty', () => {
    const result = mapDatatoSelect({});
    expect(result).toEqual([]);
  });
  it('return data when type "perf_event"', () => {
    const data = [
      { eventNameDisplay: 'Event 1', eventTrackingCode: 'event-1' },
      { eventNameDisplay: 'Event 2', eventTrackingCode: 'event-2' },
      { eventNameDisplay: 'Event 3', eventTrackingCode: 'event-3' },
    ];
    const expected = [
      {
        eventNameDisplay: 'Event 1',
        eventTrackingCode: 'event-1',
        label: 'Event 1',
        value: 'event-1',
        type: 'perf_event',
      },
      {
        eventNameDisplay: 'Event 2',
        eventTrackingCode: 'event-2',
        label: 'Event 2',
        value: 'event-2',
        type: 'perf_event',
      },
      {
        eventNameDisplay: 'Event 3',
        eventTrackingCode: 'event-3',
        label: 'Event 3',
        value: 'event-3',
        type: 'perf_event',
      },
    ];
    const result = mapDatatoSelect(data);
    expect(result).toEqual(expected);
  });

  it('return data with other types', () => {
    const data = [
      { itemTypeDisplay: 'Item Type 1', itemTypeName: 'item-type-1' },
      { itemTypeDisplay: 'Item Type 2', itemTypeName: 'item-type-2' },
      { itemTypeDisplay: 'Item Type 3', itemTypeName: 'item-type-3' },
    ];
    const expected = [
      {
        itemTypeDisplay: 'Item Type 1',
        itemTypeName: 'item-type-1',
        label: 'Item Type 1',
        value: 'item-type-1',
        type: 'other',
      },
      {
        itemTypeDisplay: 'Item Type 2',
        itemTypeName: 'item-type-2',
        label: 'Item Type 2',
        value: 'item-type-2',
        type: 'other',
      },
      {
        itemTypeDisplay: 'Item Type 3',
        itemTypeName: 'item-type-3',
        label: 'Item Type 3',
        value: 'item-type-3',
        type: 'other',
      },
    ];
    const result = mapDatatoSelect(data, 'other');
    expect(result).toEqual(expected);
  });
});

describe('toMapdataMultiple function', () => {
  test('should return an object with empty arrays if input is not an array', () => {
    const data = '';
    const type = 'perf_event';
    const result = toMapdataMultiple(data, type);
    expect(result).toEqual({
      customer: [],
      date: [],
      order: [],
      revenue: [],
    });
  });

  test('should return an object with expected arrays if input is an array of perf_event data', () => {
    const data = [
      {
        dataType: 'string',
        eventPropertyDisplay: 'Customer',
        eventPropertyName: 'customer_id',
      },
      {
        dataType: 'string',
        itemPropertyDisplay: 'Product',
        itemPropertyName: 'product_id',
      },
      {
        dataType: 'datetime',
        eventPropertyDisplay: 'Date',
        eventPropertyName: 'order_date',
      },
      {
        dataType: 'number',
        displayFormat: { type: 'CURRENCY' },
        eventPropertyDisplay: 'Revenue',
        eventPropertyName: 'total_revenue',
      },
    ];
    const type = 'perf_event';
    const result = toMapdataMultiple(data, type);
    expect(result).toEqual({
      customer: [
        { label: 'Customer', value: 'customer_id', key: 'customer_id' },
        { label: 'Product', value: 'product_id', key: 'product_id' },
      ],
      date: [{ label: 'Date', value: 'order_date', key: 'order_date' }],
      order: [
        { label: 'Customer', value: 'customer_id', key: 'customer_id' },
        { label: 'Product', value: 'product_id', key: 'product_id' },
      ],
      revenue: [
        { label: 'Revenue', value: 'total_revenue', key: 'total_revenue' },
      ],
    });
  });

  test('should return an object with expected arrays if input is an array of comp_attr data', () => {
    const data = [
      {
        data_type: 'string',
        item_property_display: 'Customer',
        item_property_name: 'customer_id',
      },
      {
        data_type: 'string',
        item_property_display: 'Product',
        item_property_name: 'product_id',
      },
      {
        data_type: 'datetime',
        item_property_display: 'Date',
        item_property_name: 'order_date',
      },
      {
        data_type: 'number',
        display_format: { type: 'CURRENCY' },
        item_property_display: 'Revenue',
        item_property_name: 'total_revenue',
      },
    ];
    const type = 'comp_attr';
    const result = toMapdataMultiple(data, type);
    expect(result).toEqual({
      customer: [
        { label: 'Customer', value: 'customer_id', key: 'customer_id' },
        { label: 'Product', value: 'product_id', key: 'product_id' },
      ],
      date: [{ label: 'Date', value: 'order_date', key: 'order_date' }],
      order: [
        { label: 'Customer', value: 'customer_id', key: 'customer_id' },
        { label: 'Product', value: 'product_id', key: 'product_id' },
      ],
      revenue: [
        { label: 'Revenue', value: 'total_revenue', key: 'total_revenue' },
      ],
    });
  });
  test('should return an object with empty arrays if input is an array with type comp_attr', () => {
    const data = [];
    const type = 'comp_attr';
    const result = toMapdataMultiple(data, type);
    expect(result).toEqual({
      customer: [],
      date: [],
      order: [],
      revenue: [],
    });
  });
  test('should return an object with empty arrays if input is an array with type perf_event', () => {
    const data = [];
    const type = 'perf_event';
    const result = toMapdataMultiple(data, type);
    expect(result).toEqual({
      customer: [],
      date: [],
      order: [],
      revenue: [],
    });
  });
});

describe('bindValidateToCondition', () => {
  it('should add errors to corresponding conditions object', () => {
    const conditions = OrderedMap({
      field1: { error: '' },
      field2: { error: '' },
    });
    const errors = [
      { refineKey: 'field1', error: 'Field 1 is required.' },
      { refineKey: 'field2', error: 'Field 2 is invalid.' },
    ];
    const expectedOutput = OrderedMap({
      field1: { error: 'Field 1 is required.' },
      field2: { error: 'Field 2 is invalid.' },
    });
    const result = bindValidateToCondition(conditions, errors);
    expect(result).toEqual(expectedOutput);
  });

  it('should not modify conditions object if errors array is empty', () => {
    const conditions = OrderedMap({
      field1: { error: '' },
      field2: { error: '' },
    });
    const errors = [];
    const expectedOutput = conditions;
    const result = bindValidateToCondition(conditions, errors);
    expect(result).toEqual(expectedOutput);
  });
});

describe('function genScoreCard', () => {
  it('genScoreCard with empty errors', () => {
    const received = genScoreCard(...fnGenScoreCard.withEmptyErrors.args);

    fnGenScoreCard.withEmptyErrors.expected.data.forEach(scoreRange =>
      expect(received.data).toContainEqual(scoreRange),
    );
  });

  it('genScoreCard with invalid label', () => {
    const received = genScoreCard(...fnGenScoreCard.withErrors.args);

    fnGenScoreCard.withErrors.expected.data.forEach(scoreRange =>
      expect(received.data).toContainEqual(scoreRange),
    );
  });
});

describe('function getMinMaxOfPersonasConfig', () => {
  it('should return correct array min-max', () => {
    const received = getMinMaxOfPersonasConfigScores(
      ...fnGetMinMaxOfPersonasConfig.default.args,
    );

    expect(received).toEqual(fnGetMinMaxOfPersonasConfig.default.expected);
  });
});

describe('function fnSerializeCustomInputRanges', () => {
  it.each(
    fnSerializeCustomInputRanges.cases.map((testCase, index) => [
      testCase.description || `test case ${index + 1}`,
      testCase.args.inputRanges,
      testCase.args.customInputRanges,
      testCase.args.limitType,
      testCase.expected,
    ]),
  )(
    '%s',
    (_title, inputRanges, customInputRanges, limitType, expectedValue) => {
      const received = serializeCustomInputRanges(
        customInputRanges,
        inputRanges,
        limitType,
      );

      expect(received).toEqual(expectedValue);
    },
  );
});

describe('function getScoreWithMaxValue', () => {
  it('should return the correct maximum score for each test case list', () => {
    fnGetScoreWithMaxValue.cases.forEach(testCase => {
      const { args, expected: expectedValue } = testCase;
      const received = getScoreWithMaxValue(args.inputRanges);

      expect(received).toEqual(expectedValue);
    });
  });
});

describe('function getMinMaxRangeFromInputRanges', () => {
  it('should return the correct min-max value for each test case list', () => {
    fnGetMinMaxFromInputRanges.cases.forEach(testCase => {
      const { args, expected: expectedValue } = testCase;

      const received = getMinMaxRangeFromInputRanges(args.inputRanges);

      expect(received).toEqual(expectedValue);
    });
  });
});

describe('function getRangesWithInputRanges', () => {
  it('should return the correct uiRanges for each test case list', () => {
    fnGetRangesWithInputRanges.cases.forEach(testCase => {
      const { args, expected: expectedValue } = testCase;

      const received = getRangesWithInputRanges(
        args.inputRanges,
        args.uiRanges,
      );

      expect(received).toEqual(expectedValue);
    });
  });
});

describe('function fnSerializeCustomInputRanges', () => {
  it.each(
    fnSerializeCustomInputRanges.cases.map((testCase, index) => [
      testCase.description || `test case ${index + 1}`,
      testCase.args.inputRanges,
      testCase.args.customInputRanges,
      testCase.args.limitType,
      testCase.expected,
    ]),
  )(
    '%s',
    (_title, inputRanges, customInputRanges, limitType, expectedValue) => {
      const received = serializeCustomInputRanges(
        customInputRanges,
        inputRanges,
        limitType,
      );

      expect(received).toEqual(expectedValue);
    },
  );
});

describe('function fnGetDisableSteps', () => {
  it.each(
    fnGetDisableSteps.cases.map((testCase, index) => [
      testCase.args.description || `test case ${index + 1}`,
      testCase.args.errors,
      testCase.args.activeStep,
      testCase.expected,
    ]),
  )('%s', (_, errors, activeStep, expected) => {
    const received = getDisableSteps(errors, activeStep);

    expect(received).toEqual(expected);
  });
});
