import { MODEL_MATRIX_TYPE } from '../../../config';
import { TRAINING_MODE } from '../../config';

export const SCORING_TYPE = {
  recency: 'r',
  frequency: 'f',
  monetary: 'm',
};

export const MAX_COLUMN_BAR_CHART = 120;

export const MAX_RANGE_LIMIT_TYPE_V1 = {
  eq: 1,
  gte: 2,
};

export const MAX_RANGE_LIMIT_TYPE = {
  eq: 'eq',
  gte: 'gte',
};

export const NUM_RANGES_BY_MATRIX = {
  [MODEL_MATRIX_TYPE.matrix_3x3]: 3,
  [MODEL_MATRIX_TYPE.matrix_5x5]: 5,
};

export const FIELD_PATH = {
  rfmName: ['rfm_name'],
  dataSource: ['data_source'],
  eventSource: ['data_source', 'event_source'],
  refine: ['data_source', 'refine'],
  customerIndetify: ['filter', 'customer_identity'],
  timeRanges: ['filter', 'time_range'],
  date: ['filter', 'date'],
  order: ['filter', 'order'],
  revenue: ['filter', 'revenue'],
  [SCORING_TYPE.recency]: {
    barchart: ['recency', 'barchart'],
    scorecard: ['recency', 'scorecard'],
  },
  [SCORING_TYPE.frequency]: {
    barchart: ['frequency', 'barchart'],
    scorecard: ['frequency', 'scorecard'],
  },
  [SCORING_TYPE.monetary]: {
    barchart: ['monetary', 'barchart'],
    scorecard: ['monetary', 'scorecard'],
  },
  applyModel: {
    dataUpdate: {
      segment: ['dataUpdate', 'apply_segment'],
      attribute: ['dataUpdate', 'apply_attribute'],
    },
  },
};

export const DEFAULT_MATRIX = MODEL_MATRIX_TYPE.matrix_5x5;

export const ATTRIBUTES_APPLY = [
  {
    id: 'recency',
    name: 'recency',
    label: 'Duration from last purchase',
    option: 'new',
  },
  {
    id: 'frequency',
    name: 'frequency',
    label: 'Number of orders',
    option: 'new',
  },
  {
    id: 'monetary',
    name: 'monetary',
    label: 'Amount of spending',
    option: 'new',
  },
  {
    id: 'personas',
    name: 'personas',
    label: 'RFM Personas',
    option: 'new',
  },
  {
    id: 'recency_score',
    name: 'recency_score',
    label: 'Recency scoring',
    option: 'new',
  },
  {
    id: 'frequency_score',
    name: 'frequency_score',
    label: 'Frequency scoring',
    option: 'new',
  },
  {
    id: 'monetary_score',
    name: 'monetary_score',
    label: 'Monetary scoring',
    option: 'new',
  },
];

export const TRAINING_STEP_KEYS = {
  RECENCY: 0,
  FREQUENCY: 1,
  MONETARY: 2,
  PERSONAS: 3,
};

export const TRAINING_STEPS = {
  [TRAINING_STEP_KEYS.RECENCY]: {
    key: TRAINING_STEP_KEYS.RECENCY,
    label: 'Recency Scoring',
  },
  [TRAINING_STEP_KEYS.FREQUENCY]: {
    key: TRAINING_STEP_KEYS.FREQUENCY,
    label: 'Frequency Scoring',
  },
  [TRAINING_STEP_KEYS.MONETARY]: {
    key: TRAINING_STEP_KEYS.MONETARY,
    label: 'Monetary Scoring',
  },
  [TRAINING_STEP_KEYS.PERSONAS]: {
    key: TRAINING_STEP_KEYS.PERSONAS,
    label: 'Personas',
  },
};

export const STEP_KEYS_BY_TRAINING_MODE = {
  [TRAINING_MODE.AI]: [TRAINING_STEP_KEYS.PERSONAS],
  [TRAINING_MODE.Expert]: [
    TRAINING_STEP_KEYS.RECENCY,
    TRAINING_STEP_KEYS.FREQUENCY,
    TRAINING_STEP_KEYS.MONETARY,
    TRAINING_STEP_KEYS.PERSONAS,
  ],
};
