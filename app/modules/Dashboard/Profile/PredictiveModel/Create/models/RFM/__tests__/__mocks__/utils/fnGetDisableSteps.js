import { STEP_SETTING_KEYS } from '../../../../../config';

const fnGetDisableSteps = {
  cases: [
    {
      args: {
        errors: [
          {
            properties: {
              step: STEP_SETTING_KEYS.trainModel,
            },
          },
        ],
        activeStep: STEP_SETTING_KEYS.trainModel,
      },
      expected: [STEP_SETTING_KEYS.scheduleUpdate],
    },
    {
      args: {
        errors: [
          {
            properties: {
              step: STEP_SETTING_KEYS.trainModel,
            },
          },
          {
            properties: {
              step: STEP_SETTING_KEYS.scheduleUpdate,
            },
          },
        ],
        activeStep: STEP_SETTING_KEYS.trainModel,
      },
      expected: [STEP_SETTING_KEYS.scheduleUpdate],
    },
    {
      args: {
        errors: [
          {
            properties: {
              step: STEP_SETTING_KEYS.scheduleUpdate,
            },
          },
          {
            properties: {
              step: STEP_SETTING_KEYS.trainModel,
            },
          },
        ],
        activeStep: STEP_SETTING_KEYS.scheduleUpdate,
      },
      expected: [],
    },
    {
      args: {
        errors: [
          {
            properties: {
              step: STEP_SETTING_KEYS.trainModel,
            },
          },
          {
            properties: {
              step: STEP_SETTING_KEYS.scheduleUpdate,
            },
          },
        ],
        activeStep: STEP_SETTING_KEYS.prepareData,
      },
      expected: [STEP_SETTING_KEYS.scheduleUpdate],
    },
  ],
};

export default fnGetDisableSteps;
