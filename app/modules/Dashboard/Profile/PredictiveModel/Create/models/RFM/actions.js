import ReduxTypes from 'redux/constants';
import { MODULE_CONFIG } from 'PredictiveModel/Create/config';

const TYPE_PREFIX = `${MODULE_CONFIG.key}`;

export const Types = {
  prepareData: {
    changeNameDescription: `${TYPE_PREFIX}@@UPDATE_NAME__DESCRIPTION@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    cachePrepareDataAPI: `${TYPE_PREFIX}@@CACHE_API_PREPARE_DATA_API`,
    updateEventSelect: `${TYPE_PREFIX}@@UPDATE_EVENT_SELECT@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateSourceSelected: `${TYPE_PREFIX}@@UPDATE_SOURCE_SELECTED@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateRefineCondition: `${TYPE_PREFIX}@@UPDATE_DATA_CONDITION@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateMultipleSelect: `${TYPE_PREFIX}@@UPDATE_MULTIPLE_SELECT@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateTimeRange: `${TYPE_PREFIX}@@UPDATE_TIMERANGE@@`,
  },
  trainModel: {
    recalculateWithZoom: `${TYPE_PREFIX}@@RECALCULATE_WITH_ZOOM`,
    updateZoom: `${TYPE_PREFIX}@@UPDATE_ZOOM`,
    updateActTrainingKey: `${TYPE_PREFIX}@@UPDATE_ACT_TRAINING_KEY`,
    // usingDefaultAllChart: `${TYPE_PREFIX}@@USING_DEFAULT_ALL_CHART${
    //   ReduxTypes.UPDATE_VALUE
    // }`,
    updateIsTrainingScoring: `${TYPE_PREFIX}@@IS_TRAINING_SCORING${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateIsTrainingScoringWithCusRange: `${TYPE_PREFIX}@@IS_TRAINING_SCORING_WITH_CUS_RANGE${
      ReduxTypes.UPDATE_VALUE
    }`,
    trainPersonas: `${TYPE_PREFIX}@@TRAIN_PERSONAS`,
    updateIsTranningPersonas: `${TYPE_PREFIX}@@IS_TRAINING_PERSONAS${
      ReduxTypes.UPDATE_VALUE
    }`,
    trainPersonasSucc: `${TYPE_PREFIX}@@TRAIN_PERSONAS_SUCC`,
    trainPersonasFail: `${TYPE_PREFIX}@@TRAIN_PERSONAS_FAIL`,
    trainWithCustomRangeSucc: `${TYPE_PREFIX}@@TRAIN_MODEL_WITH_CUSTOM_RANGE_SUCC`,
    trainWithCustomRangeFail: `${TYPE_PREFIX}@@TRAIN_MODEL_WITH_CUSTOM_RANGE_FAIL`,
    updateRanges: `${TYPE_PREFIX}@@UPDATE_RANGES${ReduxTypes.UPDATE_VALUE}`,
    usingDefaultPersonas: `${TYPE_PREFIX}@@USING_DEFAULT_PERSONAS${
      ReduxTypes.UPDATE_VALUE
    }`,
    changeMatrix: `${TYPE_PREFIX}@@CHANGE_MODEL_MATRIX${
      ReduxTypes.UPDATE_VALUE
    }`,
    changeNameDescription: `${TYPE_PREFIX}@@UPDATE_NAME__DESCRIPTION@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateEventSelect: `${TYPE_PREFIX}@@UPDATE_EVENT_SELECT@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateSourceSelected: `${TYPE_PREFIX}@@UPDATE_SOURCE_SELECTED@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateRefineCondition: `${TYPE_PREFIX}@@UPDATE_DATA_CONDITION@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateMultipleSelect: `${TYPE_PREFIX}@@UPDATE_MULTIPLE_SELECT@@${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateTimeRange: `${TYPE_PREFIX}@@UPDATE_TIMERANGE@@`,
    handleTrainModel: `${TYPE_PREFIX}@@HANDLE_TRAIN_MODEL`,
    handleTrainModelSuccess: `${TYPE_PREFIX}@@HANDLE_TRAIN_MODEL_SUCCESS`,
    handleTrainModelFail: `${TYPE_PREFIX}@@HANDLE_TRAIN_MODEL_FAIL`,
    prepareDataSuccess: `${TYPE_PREFIX}@@PREPARE_DATA_SUCCESS`,
    prepareDataFail: `${TYPE_PREFIX}@@PREPARE_DATA_FAIL`,
    buildDefaultRangesSuccess: `${TYPE_PREFIX}@@BUILD_DEFAULT_RANGES_SUCCESS`,
    buildDefaultRangesFail: `${TYPE_PREFIX}@@BUILD_DEFAULT_RANGES_FAIL`,
    aggregationSuccess: `${TYPE_PREFIX}@@BUILD_AGGREGATION_SUCCESS`,
    aggregationFail: `${TYPE_PREFIX}@@BUILD_AGGREGATION_FAIL`,
    trainModelWithCustomRange: `${TYPE_PREFIX}@@TRAIN_MODEL_WITH_CUSTOM_RANGE`,
    changeGranularity: `${TYPE_PREFIX}@@CHANGE__GRANULARITY@@`,
    changeAggregationFrequency: `${TYPE_PREFIX}@@CHANGE__AGGREGATION__FREQUENCY@@`,
    changeAggregationMonetary: `${TYPE_PREFIX}@@CHANGE__AGGREGATION__MONETARY@@`,
    togglePersonasConfig: `${TYPE_PREFIX}@@TOGGLE_MODAL_PERSONAS_CONFIG${
      ReduxTypes.UPDATE_VALUE
    }`,
    updatePersonasConfig: `${TYPE_PREFIX}@@UPDATE_PERSONAS_CONFIG${
      ReduxTypes.UPDATE_VALUE
    }`,
    changeRange: `${TYPE_PREFIX}@@CHANGE_RANGE${ReduxTypes.UPDATE_VALUE}`,
    changeScoreLabel: `${TYPE_PREFIX}@@CHANGE_SCORE_LABEL${
      ReduxTypes.UPDATE_VALUE
    }`,
    changeMaxRangeLimit: `${TYPE_PREFIX}@@CHANGE_MAX_RANGE_LIMIT${
      ReduxTypes.UPDATE_VALUE
    }`,
    updateScoring: `${TYPE_PREFIX}@@UPDATE_SCORING${ReduxTypes.UPDATE_VALUE}`,
  },

  fetchRFMPersonasData: `${TYPE_PREFIX}@@FETCH_RFM_PERSONAS_DATA`,
  initData: `${TYPE_PREFIX}${ReduxTypes.INIT}`,
  resetData: `${TYPE_PREFIX}${ReduxTypes.RESET}`,
};

export const Actions = {
  reset: () => ({
    type: Types.resetData,
  }),
  initData: data => ({
    type: Types.initData,
    payload: data,
  }),
  changeModelMatrix: modelMatrix => ({
    type: Types.trainModel.changeMatrix,
    payload: {
      matrix: modelMatrix,
    },
  }),
  changeGranularity: data => ({
    type: Types.trainModel.changeGranularity,
    payload: data,
  }),
  changeAggregationFrequency: data => ({
    type: Types.trainModel.changeAggregationFrequency,
    payload: data,
  }),
  changeAggregationMonetary: data => ({
    type: Types.trainModel.changeAggregationMonetary,
    payload: data,
  }),
  prepareData: {
    changeNameDescription: data => ({
      type: Types.prepareData.changeNameDescription,
      payload: data,
    }),
    updateEventSelect: data => ({
      type: Types.prepareData.updateEventSelect,
      payload: data,
    }),
    updateDataSourceSelected: data => ({
      type: Types.prepareData.updateSourceSelected,
      payload: data,
    }),
    updateRefineCondition: data => ({
      type: Types.prepareData.updateRefineCondition,
      payload: data,
    }),
    updateMultipleSelect: data => ({
      type: Types.prepareData.updateMultipleSelect,
      payload: data,
    }),
    updateDataTimeRange: data => ({
      type: Types.prepareData.updateTimeRange,
      payload: data,
    }),
    cachePrepareDataAPI: data => ({
      type: Types.prepareData.cachePrepareDataAPI,
      payload: data,
    }),
  },
  trainModel: {
    updateScoring: (scoreType, updated) => ({
      type: Types.trainModel.updateScoring,
      payload: { scoreType, updated },
    }),
    recalculateWithZoom: scoreType => ({
      type: Types.trainModel.recalculateWithZoom,
      payload: { scoreType },
    }),
    updateZoom: ({ min, max }, scoreType) => ({
      type: Types.trainModel.updateZoom,
      payload: { min, max, scoreType },
    }),
    updateActTrainingKey: key => ({
      type: Types.trainModel.updateActTrainingKey,
      payload: { key },
    }),
    // usingDefaultAllChart: (usingDefault, scoreType) => ({
    //   type: Types.trainModel.usingDefaultAllChart,
    //   payload: { usingDefault, scoreType },
    // }),
    updateIsTranningPersonas: isTraining => ({
      type: Types.trainModel.updateIsTranningPersonas,
      payload: { isTraining },
    }),
    updateIsTrainingScoring: (isTraining, scoreType) => ({
      type: Types.trainModel.updateIsTrainingScoring,
      payload: { isTraining, scoreType },
    }),
    updateIsTrainingScoringWithCusRange: (isTraining, scoreType) => ({
      type: Types.trainModel.updateIsTrainingScoringWithCusRange,
      payload: { isTraining, scoreType },
    }),
    usingDefaultPersonas: isUsingDefault => ({
      type: Types.trainModel.usingDefaultPersonas,
      payload: { isUsingDefault },
    }),
    trainPersonas: () => ({
      type: Types.trainModel.trainPersonas,
    }),
    trainPersonasSucc: data => ({
      type: Types.trainModel.trainPersonasSucc,
      payload: { data },
    }),
    trainPersonasFail: () => ({
      type: Types.trainModel.trainPersonasFail,
    }),
    trainWithCustomRangeSucc: (inputRanges, scoreType) => ({
      type: Types.trainModel.trainWithCustomRangeSucc,
      payload: { inputRanges, scoreType },
    }),
    trainWithCustomRangeFail: () => ({
      type: Types.trainModel.trainWithCustomRangeFail,
    }),
    updateRanges: ranges => ({
      type: Types.trainModel.updateRanges,
      payload: { ranges },
    }),
    changeMaxRangeLimit: (scoreType, value) => ({
      type: Types.trainModel.changeMaxRangeLimit,
      payload: { maxRangeLimit: value, scoreType },
    }),
    changeScoreLabel: (scoreType, changedScore) => ({
      type: Types.trainModel.changeScoreLabel,
      payload: { changedScore, scoreType },
    }),
    changeRange: (scoreType, range) => ({
      type: Types.trainModel.changeRange,
      payload: { scoreType, range },
    }),
    togglePersonasConfig: isOpen => ({
      type: Types.trainModel.togglePersonasConfig,
      payload: { isOpen },
    }),
    updatePersonasConfig: personasConfigs => ({
      type: Types.trainModel.updatePersonasConfig,
      payload: { personasConfigs },
    }),
    changeMatrix: modelMatrix => ({
      type: Types.trainModel.changeMatrix,
      payload: {
        matrix: modelMatrix,
      },
    }),
    buildDefaultRangesSuccess: (inputRanges, scoreType) => ({
      type: Types.trainModel.buildDefaultRangesSuccess,
      payload: { inputRanges, scoreType },
    }),
    buildDefaultRangesFail: scoreType => ({
      type: Types.trainModel.buildDefaultRangesFail,
      payload: { scoreType },
    }),
    aggregationSuccess: (data, scoreType) => ({
      type: Types.trainModel.aggregationSuccess,
      payload: { dataRanges: data, scoreType },
    }),
    aggregationFail: scoreType => ({
      type: Types.trainModel.aggregationFail,
      payload: { scoreType },
    }),
    handleTrainModel: (scoreType, options = {}) => ({
      type: Types.trainModel.handleTrainModel,
      payload: { scoreType, options },
    }),
    trainModelWithCustomRange: ({
      viewName,
      inputRanges,
      scoreInfo,
      scoreCardData,
      scoreType,
    }) => ({
      type: Types.trainModel.trainModelWithCustomRange,
      payload: { viewName, inputRanges, scoreInfo, scoreCardData, scoreType },
    }),
    handleTrainModelSuccess: (scoreType, inputRanges, dataRanges, opts) => ({
      type: Types.trainModel.handleTrainModelSuccess,
      payload: {
        dataRanges,
        inputRanges,
        scoreType,
        opts,
      },
    }),
    handleTrainModelFail: scoreType => ({
      type: Types.trainModel.handleTrainModelFail,
      payload: { scoreType },
    }),
  },
};
