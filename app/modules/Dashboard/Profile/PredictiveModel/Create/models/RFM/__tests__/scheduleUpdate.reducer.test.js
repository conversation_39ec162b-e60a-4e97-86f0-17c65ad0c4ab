import ReduxTypes from 'redux/constants';
import { MODULE_CONFIG } from '../../../config';
import scheduleUpdateReducerFor, {
  getInitState,
} from '../../../reducers/applyModel';

describe('scheduleUpdateReducer', () => {
  const initialState = getInitState();
  const PREFIX = MODULE_CONFIG.key;
  it('returns the initial state', () => {
    const reducer = scheduleUpdateReducerFor();
    const state = reducer(undefined, {});
    expect(state).toEqual(initialState);
  });

  it('updates dataUpdate attribute state correctly', () => {
    const reducer = scheduleUpdateReducerFor();
    const payload = {
      type: 'attribute',
      data: ['attribute1', 'attribute2'],
    };
    const action = {
      type: `${PREFIX}@@DATA_UPDATE@@${ReduxTypes.UPDATE_VALUE}`,
      payload,
    };
    const expectedState = {
      ...initialState,
      dataUpdate: {
        attribute: ['attribute1', 'attribute2'],
        segment: [],
      },
    };
    const state = reducer(initialState, action);
    expect(state).toEqual(expectedState);
  });

  it('updates dataUpdate segment state correctly', () => {
    const reducer = scheduleUpdateReducerFor();
    const payload = {
      type: 'segment',
      data: ['segment1', 'segment2'],
    };
    const action = {
      type: `${PREFIX}@@DATA_UPDATE@@${ReduxTypes.UPDATE_VALUE}`,
      payload,
    };
    const expectedState = {
      ...initialState,
      dataUpdate: {
        attribute: [],
        segment: ['segment1', 'segment2'],
      },
    };
    const state = reducer(initialState, action);
    expect(state).toEqual(expectedState);
  });

  it('updates computeSchedule state correctly', () => {
    const payload = {
      alertAccountIds: [*********, *********],
      alertScopes: {
        success: ['app_push'],
        failure: ['email'],
      },
    };
    const action = {
      type: `${PREFIX}@@NOTIFICATION_SETUP@@${ReduxTypes.UPDATE_VALUE}`,
      payload,
    };
    const state = scheduleUpdateReducerFor()(initialState, action);
    expect(state.notificationSetup).toEqual(payload);
  });
});
