/* eslint-disable indent */
import { createSelector } from 'reselect';
import { get } from 'lodash';
import { MODULE_CONFIG } from '../../config';
import DEFAULT_CHART_DATA from './defaultChartsData';
import {
  selectDesign,
  selectErrorsByPath,
  selectSavedData,
  selectValidateErrors,
  selectViewName,
} from '../../selector';
import {
  genBarChart,
  genPieChart,
  genScoreCard,
  genTreeMap,
  getRangeLimitType,
  rangesToInputRanges,
  toAPIData,
  toAPIPrepareData,
} from './utils';
import { validateInputRanges } from './validate';
import { makeSelectActiveRow } from '../../../Detail/selectors';
import { FIELD_PATH, SCORING_TYPE } from './config';
import { PROCESS_COMPUTE_STATUS } from 'utils/constants';
import { getErrorsByStep } from '../../utils';
import { omitDeep } from '../../../../../../../utils/common';

export const selectDomain = state => state.get(MODULE_CONFIG.key);

// Data input
export const selectRFMModelSettings = createSelector(
  selectDomain,
  substate => substate.settings,
);

export const selectRFMModelMain = createSelector(
  selectDomain,
  subState => subState.main,
);

export const selectModelId = createSelector(
  selectRFMModelMain,
  main => main.modelId,
);

export const selectDataUpdate = createSelector(
  selectRFMModelSettings,
  settings => settings.scheduleUpdate.dataUpdate,
);

export const selectPrepareData = createSelector(
  selectRFMModelSettings,
  settings => settings.prepareData,
);

export const selectEventSelect = createSelector(
  selectPrepareData,
  prepareData => prepareData.eventSelect,
);

// Train Model
export const selectTrainModel = createSelector(
  selectRFMModelSettings,
  settings => settings.trainModel,
);

export const selectMatrix = createSelector(
  selectTrainModel,
  trainModel => trainModel.matrix,
);

export const selectActTrainingKey = createSelector(
  selectTrainModel,
  trainModel => trainModel.active,
);

export const selectTrainingMode = createSelector(
  selectTrainModel,
  trainModel => trainModel.mode,
);

export const makeSelectErrorsByStep = step =>
  createSelector(
    selectValidateErrors,
    errors => getErrorsByStep(errors, step),
  );

export const makeSelectInputRanges = (scoreType, additionalField) =>
  createSelector(
    makeSelectRanges(scoreType),
    ranges => rangesToInputRanges(ranges, additionalField),
  );

export const selectRefineErrors = createSelector(
  selectErrorsByPath(FIELD_PATH.refine),
  errors =>
    errors.map(({ params }) => ({
      error: params.errorNumber,
      refineKey: params.refineKey,
    })),
);

export const makeSelectScoringByType = type =>
  createSelector(
    selectTrainModel,
    trainModel => trainModel[type],
  );

export const selectPersonas = createSelector(
  selectTrainModel,
  trainModel => trainModel.personas,
);

export const selectPersonasValues = createSelector(
  selectPersonas,
  personas => personas.values,
);

export const makeSelectMaxRangeLimit = scoreType =>
  createSelector(
    makeSelectZoombar(scoreType),
    zoombar => getRangeLimitType({ zoombar }),
  );

export const makeSelectRanges = scoreType =>
  createSelector(
    makeSelectScoringByType(scoreType),
    scoring => scoring.ranges,
  );

export const makeSelectDataRanges = scoreType =>
  createSelector(
    makeSelectScoringByType(scoreType),
    scoring => scoring.dataRanges,
  );

export const makeSelectZoombar = scoreType =>
  createSelector(
    makeSelectScoringByType(scoreType),
    scoring => scoring.zoombar,
  );

export const makeSelectChartKey = scoreType =>
  createSelector(
    makeSelectScoringByType(scoreType),
    scoring => scoring.chartKey,
  );

export const makeSelectBarChart = scoreType =>
  createSelector(
    makeSelectMaxRangeLimit(scoreType),
    makeSelectRanges(scoreType),
    makeSelectDataRanges(scoreType),
    makeSelectZoombar(scoreType),
    (maxRangeLimit, ranges, dataRanges, zoombar) =>
      genBarChart({
        maxRangeLimit,
        ranges,
        dataRanges,
        zoombar,
      }),
  );

export const makeSelectScoreCard = scoreType =>
  createSelector(
    selectErrorsByPath(FIELD_PATH[scoreType].scorecard),
    makeSelectRanges(scoreType),
    (errors, ranges) => {
      const result = genScoreCard({
        scoreType,
        ranges,
        errors,
      });

      return result;
    },
  );

export const makeSelectPieChart = scoreType =>
  createSelector(
    makeSelectScoreCard(scoreType),
    ({ data }) => genPieChart({ scoreRange: data }),
  );

export const selectDataForScoringTemplate = scoreType =>
  createSelector(
    makeSelectScoringByType(scoreType),
    makeSelectCharts(scoreType),
    (scoring, charts) => ({
      showLoadingAllChart: scoring.isUsingDefaultAllChart,
      isLoadingAllChart: scoring.isTraining,
      isLoadingScorecardAndPie: scoring.isTrainingWithCustomRange,
      needRecalculateBarchart: scoring.needRecalculateBarchart,
      showLoadingScorecardAndPie:
        !scoring.needRecalculateBarchart &&
        scoring.isUsingDefaultScorecardAndPie,
      charts,
    }),
  );

export const makeSelectCharts = scoreType =>
  createSelector(
    makeSelectScoreCard(scoreType),
    makeSelectPieChart(scoreType),
    makeSelectBarChart(scoreType),
    (scoreCard, pieChart, barChart) => ({
      scoreCard,
      pieChart,
      barChart,
    }),
  );

export const makeSelectOverWrite = scoreType =>
  createSelector(
    makeSelectScoringByType(scoreType),
    scoring => scoring.overWrite,
  );

export const makeSelectViewName = scoreType =>
  createSelector(
    makeSelectScoringByType(scoreType),
    scoring => scoring.viewName,
  );

export const makeSelectDataTrainModelCustomRanges = scoreType =>
  createSelector(
    selectMatrix,
    makeSelectInputRanges(scoreType),
    makeSelectViewName(scoreType),
    makeSelectOverWrite(scoreType),
    selectPrepareData,
    (matrix, inputRanges, viewName, overWrite, prepareData) => {
      const { timeRange } = prepareData;

      return {
        viewName,
        scoreType,
        inputRanges,
        scoreInfo: {
          score_type: scoreType,
          matrix_type: matrix,

          ...(scoreType === SCORING_TYPE.recency && {
            granularity: get(overWrite, 'granularity.value'),
          }),

          ...(scoreType === SCORING_TYPE.frequency && {
            aggregation: 'count',
          }),

          ...(scoreType === SCORING_TYPE.monetary && {
            aggregation: 'sum',
          }),

          // Temporary use of time range endDate here.
          end_date: get(timeRange, 'endDate.date'),
        },
      };
    },
  );

// The same properties are applied when build default ranges and custom ranges
export const makeSelectScoringAPI = scoreType =>
  createSelector(
    selectPrepareData,
    selectMatrix,
    makeSelectScoringByType(scoreType),
    (prepareData, matrix, scoring) => ({
      score_type: scoreType,
      matrix_type: matrix,

      ...(scoreType === SCORING_TYPE.recency && {
        granularity: get(scoring, 'overWrite.granularity.value'),
      }),

      ...(scoreType === SCORING_TYPE.frequency && {
        aggregation: 'count',
      }),

      ...(scoreType === SCORING_TYPE.monetary && {
        aggregation: 'sum',
      }),

      end_date: get(prepareData, 'timeRange.endDate.date'),
    }),
  );

export const makeSelectIsFirstTraning = scoreType =>
  createSelector(
    makeSelectScoringByType(scoreType),
    scoring => scoring.isUsingDefaultAllChart,
  );

export const makeSelectNeedRecalculateBarchart = scoreType =>
  createSelector(
    makeSelectScoringByType(scoreType),
    scoring => scoring.needRecalculateBarchart,
  );

export const makeSelectIsVisualized = scoreType =>
  createSelector(
    selectMatrix,
    makeSelectRanges(scoreType),
    (matrix, ranges) => validateInputRanges(ranges, matrix),
  );

export const selectIsDisableTabPersonas = createSelector(
  makeSelectIsVisualized(SCORING_TYPE.recency),
  makeSelectIsVisualized(SCORING_TYPE.frequency),
  makeSelectIsVisualized(SCORING_TYPE.monetary),
  makeSelectIsFirstTraning(SCORING_TYPE.frequency),
  makeSelectIsFirstTraning(SCORING_TYPE.recency),
  makeSelectIsFirstTraning(SCORING_TYPE.monetary),
  (rVisual, fVisual, mVisual, firstTrainingR, firstTrainingF, firstTrainingM) =>
    !rVisual ||
    !fVisual ||
    !mVisual ||
    firstTrainingR ||
    firstTrainingF ||
    firstTrainingM,
);

export const selectPersonasConfigs = createSelector(
  selectPersonas,
  personas => personas.configs,
);

export const selectAPIPersonasConfigs = createSelector(
  selectPersonasConfigs,
  configs => {
    const entries = Object.entries(configs).map(([key, value]) => [
      key,
      value.scores,
    ]);

    return Object.fromEntries(entries);
  },
);

export const selectRecencyGranularity = createSelector(
  makeSelectOverWrite(SCORING_TYPE.recency),
  overWrite => overWrite.granularity,
);

export const selectPrepareTimeRange = createSelector(
  selectPrepareData,
  prepareData => prepareData.timeRange,
);

export const selectTrainPersonasInfo = createSelector(
  selectViewName,
  selectIsDisableTabPersonas,
  selectAPIPersonasConfigs,
  makeSelectInputRanges(SCORING_TYPE.recency),
  makeSelectInputRanges(SCORING_TYPE.frequency),
  makeSelectInputRanges(SCORING_TYPE.monetary),
  makeSelectMaxRangeLimit(SCORING_TYPE.recency),
  makeSelectMaxRangeLimit(SCORING_TYPE.frequency),
  makeSelectMaxRangeLimit(SCORING_TYPE.monetary),
  selectRecencyGranularity,
  selectPrepareTimeRange,
  (
    viewName,
    isDisablePersonas,
    configs,
    rInputRanges,
    fInputRanges,
    mInputRanges,
    rLimitType,
    fLimitType,
    mLimitType,
    granularity,
    timeRange,
  ) => {
    if (isDisablePersonas || !viewName) return null;

    return {
      view_name: viewName,
      r_input_ranges: rInputRanges,
      f_input_ranges: fInputRanges,
      m_input_ranges: mInputRanges,

      r_limit_type: rLimitType,
      f_limit_type: fLimitType,
      m_limit_type: mLimitType,

      granularity: get(granularity, 'value'),
      end_date: get(timeRange, 'endDate.date'),
      personas_conf: configs,
    };
  },
);

export const makeSelectDataForTrainModel = scoreType =>
  createSelector(
    makeSelectScoringByType(scoreType),
    selectMatrix,
    selectPrepareDataAPI,
    selectPrepareTimeRange,
    (scoring, matrix, prepareDataAPI, timeRange) => ({
      prepareData: prepareDataAPI,
      scoreInfo: {
        score_type: scoreType,
        matrix_type: matrix,
        granularity: get(scoring, 'overWrite.granularity.value'),
        // aggregation: get(scoring, 'overWrite.aggregation.value'),
        // end_date: '2022-12-20 23:59:59',

        // Temporary use of time range endDate here.
        end_date: get(timeRange, 'endDate.date'),
      },
    }),
  );

export const selectIsUsingDefaultTreeMap = createSelector(
  selectPersonas,
  personas => personas.isUsingDefaultChart,
);

export const selectTreeMap = createSelector(
  selectIsUsingDefaultTreeMap,
  selectPersonasConfigs,
  selectPersonasValues,
  selectPrepareTimeRange,
  makeSelectRanges(SCORING_TYPE.recency),
  makeSelectRanges(SCORING_TYPE.frequency),
  makeSelectRanges(SCORING_TYPE.monetary),
  selectRecencyGranularity,
  (
    isUsingDefaultChart,
    configs,
    values,
    timeRange,
    rRanges,
    fRanges,
    mRanges,
    rGranularity,
  ) => {
    if (isUsingDefaultChart) return DEFAULT_CHART_DATA.TREE_MAP.DATA;

    return genTreeMap({
      configs,
      values,
      timeRange,
      rRanges,
      fRanges,
      mRanges,
      rGranularity,
    });
  },
);

export const selectPrepareDataAPI = createSelector(
  selectPrepareData,
  prepareData => toAPIPrepareData(prepareData),
);

export const selectBackupData = createSelector(
  selectSavedData({ serializeSettings: toAPIData }),
  savedData =>
    omitDeep(
      savedData,
      Object.values(SCORING_TYPE).map(
        scoreType => `properties.trainModel.${scoreType}.dataRanges`,
      ),
    ),
);

export const selectActiveRow = createSelector(
  selectDesign,
  makeSelectActiveRow(),
  (design, activeRow) => (design === 'update' ? activeRow : {}),
);

export const selectComputeStatus = createSelector(
  selectActiveRow,
  activeRow => get(activeRow, 'processStatus'),
);

export const selectIsComputing = createSelector(
  selectComputeStatus,
  computeStatus => +computeStatus === PROCESS_COMPUTE_STATUS.COMPUTING,
);
