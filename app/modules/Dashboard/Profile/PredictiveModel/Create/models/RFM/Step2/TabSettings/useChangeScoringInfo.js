/* eslint-disable consistent-return */
import { useSelector } from 'react-redux';
import {
  makeSelectChartKey,
  makeSelectIsVisualized,
  selectComputeStatus,
  selectDataForScoringTemplate,
  selectPrepareData,
} from '../../selector';
import { selectIsViewMode } from '../../../../selector';

function useTrackingScoringInfo({ scoringType }) {
  const isValidRanges = useSelector(makeSelectIsVisualized(scoringType));
  const dataTemplate = useSelector(selectDataForScoringTemplate(scoringType));
  const prepareData = useSelector(selectPrepareData);
  const computeStatus = useSelector(selectComputeStatus);
  const isViewMode = useSelector(selectIsViewMode);
  // const isInitDone = useSelector(selectIsInitDone);
  // const isVersionHistory = useSelector(selectIsVersionHistory);
  const chartKey = useSelector(makeSelectChartKey(scoringType));

  // const isChangeAfterInitDone = useRef(false);

  // useDeepCompareEffect(() => {
  //   if (isVersionHistory) return;
  //
  //   if (isInitDone && !isChangeAfterInitDone.current) {
  //     return () => {
  //       isChangeAfterInitDone.current = true;
  //     };
  //   }
  //
  //   if (isInitDone && isChangeAfterInitDone) {
  //     dispatch(Actions.trainModel.usingDefaultAllChart(true, scoringType));
  //   }
  // }, [dispatch, isInitDone, scoringType, isVersionHistory]);
  //
  // useDeepCompareEffect(() => {
  //   if (isVersionHistory) return;
  //
  //   if (get(prepareData, 'timeRange.mode') === 'user') {
  //     dispatch(Actions.trainModel.usingDefaultAllChart(true, scoringType));
  //   }
  // }, [dispatch, isVersionHistory, prepareData, scoringType]);

  return {
    isValidRanges,
    dataTemplate,
    prepareData,
    computeStatus,
    isViewMode,
    chartKey,
  };
}

export default useTrackingScoringInfo;
