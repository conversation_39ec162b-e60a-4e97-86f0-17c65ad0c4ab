/* eslint-disable no-param-reassign */
/* eslint-disable indent */
/* eslint-disable consistent-return */
/* eslint-disable react/prop-types */
import React, { memo, useState, useRef, useEffect, Fragment } from 'react';
import { get, isEmpty } from 'lodash';
import ErrorBoundary from 'components/common/ErrorBoundary';
import {
  UITextField,
  UILoading,
  UIWrapperDisableV2 as WrapperDisable,
} from '@xlab-team/ui-components';
import SegmentServices from 'services/Segment';
import BusinessObject from 'services/BusinessObject';
import SelectTree from 'components/form/UISelectCondition';
import { OrderedMap } from 'immutable';
import { initItemRefinePropertiesValue } from 'containers/Segment/Content/Condition/utils';
import { useImmer } from 'use-immer';
import {
  WrapperLabel,
  WrapperTextField,
  WrapperInput,
  WrapperEvent,
  WrapperMultiSelect,
  WrapperTitle,
  WrapperSelectTree,
  WrapperDisableCustom,
} from './styled';
import SelectDropdown from '../../../components/DataUpdate/SelectDropdown';
import {
  bindValidateToCondition,
  convertDataInput,
  getDefaultMultiple,
  mapDatatoSelect,
  PERF_EVENT_OPERATORS,
  toMapdataMultiple,
} from '../utils';
import BlockEvent from './Block';
import {
  getFirstOperator,
  getOperatorByProperty,
} from 'containers/Filters/utils';
import { generateKey, safeParse } from 'utils/common';
import { toEventTrackingFE } from '../../../../../Segment/Create/_reducer/utils';
import { addMessageToQueue } from 'utils/web/queue';
import './styles.scss';
// eslint-disable-next-line import/order
import { useDispatch, useSelector } from 'react-redux';
import { Actions } from '../actions';
import { Actions as MainActions } from '../../../actions';
import {
  selectComputeStatus,
  selectPrepareData,
  selectRefineErrors,
} from '../selector';
import Timerange from './Timerange';
import {
  selectErrorsByPath,
  selectIsInitDone,
  selectIsVersionHistory,
  selectModelDescription,
} from '../../../selector';
import { useDebounceValue, useDeepCompareEffect } from 'hooks';
import {
  WrapperTextError,
  WrapperTextWarning,
} from '../../../components/DataUpdate/styled';
import { PROCESS_COMPUTE_STATUS } from 'utils/constants';
import { getTranslateMessage } from 'containers/Translate/util';
import TRANSLATE_KEY from 'messages/constant';
import { FIELD_PATH } from '../config';

const PATH =
  'modules/Dashboard/Profile/PredictiveModel/Create/models/RFM/Step1/index';

const messageWarningText = moduleName =>
  getTranslateMessage(
    TRANSLATE_KEY._INFO_OBJECT_DISABLED,
    'Data-updating of this {module_name} has been disabled. You still can use the output value of the last time it is processed',
    { module_name: moduleName },
  );

const descriptionLabel = getTranslateMessage(
  TRANSLATE_KEY._TITLE_PLAN_DESCRIPTION,
  'Description',
);

const placeholderDescription = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_DESCRIBE_PLACEHOLDER,
  'Describe your RFM model',
);

const labelRFMDataSource = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_RFM_DATA_SOURCE,
  'RFM Data Source',
);

const messageErrorStatusBO = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_BO_DISABLE,
  'Data-updated of this Data Object has been disabled',
);

const labelCustomerIndentify = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_CUSTOMER_IDENTIFY,
  'Customer Identify',
);

const labelDate = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_DATE,
  'Date',
);

const labelOrder = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_ORDER,
  'Order',
);

const labelRevenue = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_REVENUE,
  'Revenue',
);

const labelTimeRange = getTranslateMessage(
  TRANSLATE_KEY._PREDICT_MODEL_TIME_RANGE,
  'Time range',
);

const Step1 = ({
  design = 'create',
  isViewMode = false,
  hiddenDescription = false,
}) => {
  const prepareData = useSelector(selectPrepareData);

  const autoSuggestionCondition = useRef(true);
  const isFirstUpdate = useRef(null);
  const computeStatus = useSelector(selectComputeStatus);
  const isInitDone = useSelector(selectIsInitDone);
  const isVersionHistory = useSelector(selectIsVersionHistory);

  const dataSourceErrors = useSelector(
    selectErrorsByPath(FIELD_PATH.dataSource),
  );

  const customIdentifyErrors = useSelector(
    selectErrorsByPath(FIELD_PATH.customerIndetify),
  );
  const dateErrors = useSelector(selectErrorsByPath(FIELD_PATH.date));
  const orderErrors = useSelector(selectErrorsByPath(FIELD_PATH.order));
  const revenueErrors = useSelector(selectErrorsByPath(FIELD_PATH.revenue));
  const refineErrors = useSelector(selectRefineErrors);

  const description = useSelector(selectModelDescription);

  const [listEvent, setListEvent] = useState([]);
  const [listBO, setListBO] = useState([]);
  const refApi = useRef(null);
  const [isLoading, setIsLoading] = useState(false);
  // dataSource call api

  const { timeRange, eventSelect, multipleSelect, conditions } = prepareData;

  const dispatch = useDispatch();

  const [dataMultiple, setDataMultiple] = useState({
    customer: [],
    date: [],
    order: [],
    revenue: [],
  });

  const [state, setState] = useImmer({
    eventSchema: {
      list: [],
      map: {},
      isLoading: true,
    },
  });

  const {
    value: stateDescription,
    setValue: setStateDescription,
  } = useDebounceValue({
    forceValue: description,
    onDebounceComplete: value => {
      dispatch(MainActions.updateModelDescription(value));
    },
  });

  useDeepCompareEffect(() => {
    const newRefinePropertyies = bindValidateToCondition(
      conditions.refineWithProperties,
      refineErrors,
    );

    dispatch(
      Actions.prepareData.updateRefineCondition({
        ...conditions,
        refineWithProperties: newRefinePropertyies,
      }),
    );
  }, [refineErrors]);

  useEffect(() => {
    if (isInitDone && !isEmpty(eventSelect)) {
      autoSuggestionCondition.current = false;
    }
  }, [isInitDone]);

  useEffect(() => {
    if (isInitDone && isEmpty(eventSelect) && listBO && listBO.length) {
      const eventDefault = listBO.find(
        list => list.itemTypeName === 'transaction',
      );

      if (eventDefault) {
        dispatch(
          Actions.prepareData.updateEventSelect({
            ...eventDefault,
            label: eventDefault.itemTypeDisplay,
            value: eventDefault.itemTypeName,
            type: 'comp_attr',
          }),
        );
        callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', {
          data: {
            ...eventDefault,
            label: eventDefault.itemTypeDisplay,
            value: eventDefault.itemTypeName,
            type: 'comp_attr',
          },
        });
      }
    }
  }, [listBO, isInitDone, isEmpty(eventSelect)]);

  const [optionEvent, setOptionEvent] = useState([
    {
      label: 'Event',
      value: 'perf_event',
      options: [],
    },
    {
      label: 'Data Object',
      value: 'comp_attr',
      options: [],
    },
  ]);
  // const initData = undefined;

  let _isMounted = true;

  const fetchApiEventSource = () => {
    const type = 'eventProperties';
    const property = eventSelect;
    SegmentServices.fetch[type](
      property,
      'undefined',
      conditions.dataSources,
    ).then(response => {
      if (response.code === 200) {
        const {
          customer = [],
          date = [],
          order = [],
          revenue = [],
        } = toMapdataMultiple(response.data, 'perf_event');

        if (autoSuggestionCondition.current && !isVersionHistory) {
          const results = getDefaultMultiple(
            {
              customer,
              date,
              order,
              revenue,
            },
            eventSelect.value,
            eventSelect.type,
          );

          handleDispatchMultiple(results);
        }

        setDataMultiple({
          customer,
          date,
          order,
          revenue,
        });

        setIsLoading(false);
        isFirstUpdate.current = true;
      }
    });
  };
  const handleDispatchMultiple = data => {
    dispatch(
      Actions.prepareData.updateMultipleSelect({
        key: 'customerIdentify',
        val: data.customer,
      }),
    );
    dispatch(
      Actions.prepareData.updateMultipleSelect({
        key: 'date',
        val: data.date,
      }),
    );
    dispatch(
      Actions.prepareData.updateMultipleSelect({
        key: 'order',
        val: data.order,
      }),
    );
    dispatch(
      Actions.prepareData.updateMultipleSelect({
        key: 'revenue',
        val: data.revenue,
      }),
    );
  };

  const fetchApiBo = itemTypeId => {
    const columns = [
      'status',
      'item_property_display',
      'type',
      'storage_type',
      'data_type',
      'process_status',
      'is_required',
      'last_updated_date',
      'last_updated_user',
      'compute_schedule_start',
      'compute_schedule_end',
      'description',
      'last_processed_date',
      'created_date',
      'created_user',
      'display_format',
      'auto_suggestion',
      'item_type_name',
    ];
    const paramsBO = {
      data: {
        page: 1,
        limit: 1000,
        search: '',
        sort: 'item_property_status',
        sd: 'asc',
        columns,
        perf_columns: [],
        filters: {
          OR: [
            {
              AND: [],
            },
          ],
        },
        item_type_id: parseInt(itemTypeId),
      },
    };

    BusinessObject.BOAttribute.getList(paramsBO).then(response => {
      if (response.code === 200) {
        const {
          customer = [],
          date = [],
          order = [],
          revenue = [],
        } = toMapdataMultiple(response.data, 'comp_attr');

        if (autoSuggestionCondition.current && !isVersionHistory) {
          const results = getDefaultMultiple(
            {
              customer,
              date,
              order,
              revenue,
            },
            eventSelect.value,
            eventSelect.type,
          );

          handleDispatchMultiple(results);
        }

        setDataMultiple({
          customer,
          date,
          order,
          revenue,
        });

        setIsLoading(false);
        isFirstUpdate.current = true;
      }
    });
  };

  useDeepCompareEffect(() => {
    if (isInitDone && eventSelect && eventSelect.type) {
      setIsLoading(true);

      if (
        eventSelect.type === 'perf_event' &&
        !isEmpty(conditions.dataSources)
      ) {
        fetchApiEventSource();
      } else if (eventSelect.type === 'comp_attr') {
        fetchApiBo(eventSelect.itemTypeId);
      }

      return () => {
        autoSuggestionCondition.current = true;
      };
    }
  }, [eventSelect, isEmpty(conditions.dataSources)]);

  const callback = (type, data) => {
    switch (type) {
      case 'CHANGE_SELECT': {
        dispatch(Actions.prepareData.updateEventSelect(data));
        break;
      }
      case 'CHANGE_TIMERANGE': {
        dispatch(Actions.prepareData.updateDataTimeRange(data));
        break;
      }
      case 'PERF_EVENT_CONDITION_CHANGE_SOURCE': {
        // eslint-disable-next-line no-shadow
        const { dataSourceSelected } = data;
        const newCondition = {
          errorDataSources: 0,
          isInitDataSources: false,
          dataSources: dataSourceSelected,
        };
        dispatch(
          Actions.prepareData.updateRefineCondition({
            ...conditions,
            ...newCondition,
          }),
        );
        break;
      }
      case 'COMP_PROP_CONDITION_CHANGE_PROPERTY': {
        const { value } = data.data;

        const oldItem = safeParse(conditions.property, null);

        if (oldItem === null || oldItem !== value) {
          const operators = PERF_EVENT_OPERATORS;
          const tempt = {
            property: data.data,
            isInitDataSources: false,
            dataSources: [],
            dataType: value.dataType,
            conditionType: {
              des: data.data && data.data.type,
              label: data.data && data.data.label,
              value: data.data && data.data.value,
            },
            operator: operators[0],
            operators,
            refineWithProperties: OrderedMap({}),
          };
          dispatch(
            Actions.prepareData.updateRefineCondition({
              ...conditions,
              ...tempt,
            }),
          );
        }
        // props.callback('COMP_PROP_CONDITION_CHANGE_PROPERTY', value);
        break;
      }
      case 'PERF_EVENT_CHANGE_REFINE_PROPERTIES': {
        const { type: typeChange } = data;
        const { refineIndex, value } = data.data;
        if (typeChange === 'INIT' && design !== 'update') {
          dispatch(
            Actions.prepareData.updateRefineCondition({
              ...conditions,
              refineWithProperties: data.data.conditions,
            }),
          );
        }
        if (typeChange === 'CHANGE_PROPERTY') {
          const oldItem = safeParse(
            conditions.refineWithProperties.getIn([refineIndex, 'property']),
            null,
          );
          if (oldItem === null || oldItem !== value) {
            const operators = getOperatorByProperty(value);
            const tempt = safeParse(
              conditions.refineWithProperties
                .getIn([refineIndex])
                .withMutations(map => {
                  map
                    .set('value', '')
                    .set('valueEnd', '')
                    .set('initValue', '')
                    .set('property', value)
                    .set('error', 0)
                    .set('dataType', value.dataType)
                    .set('operator', getFirstOperator(operators))
                    .set('operators', operators)
                    .set('syntax', value.propertySyntax);
                }),
              null,
            );
            dispatch(
              Actions.prepareData.updateRefineCondition({
                ...conditions,
                refineWithProperties: conditions.refineWithProperties.setIn(
                  [refineIndex],
                  tempt,
                ),
              }),
            );
          }
        } else if (typeChange === 'CHANGE_OPERATOR') {
          const tempt = safeParse(
            conditions.refineWithProperties
              .getIn([refineIndex])
              .withMutations(map => {
                map
                  .set('value', '')
                  .set('valueEnd', '')
                  .set('initValue', '')
                  .set('error', 0)
                  .set('operator', value);
              }),
            null,
          );

          dispatch(
            Actions.prepareData.updateRefineCondition({
              ...conditions,
              refineWithProperties: conditions.refineWithProperties.setIn(
                [refineIndex],
                tempt,
              ),
            }),
          );
        } else if (typeChange === 'CHANGE_VALUE') {
          const { name = 'value' } = data.data;
          // console.log('data', name, value);
          const tempt = safeParse(
            conditions.refineWithProperties
              .getIn([refineIndex])
              .withMutations(map => {
                map.set('error', 0).set(name, value);
              }),
            null,
          );
          dispatch(
            Actions.prepareData.updateRefineCondition({
              ...conditions,
              refineWithProperties: conditions.refineWithProperties.setIn(
                [refineIndex],
                tempt,
              ),
            }),
          );
        } else if (typeChange === 'ADD_ITEM') {
          dispatch(
            Actions.prepareData.updateRefineCondition({
              ...conditions,
              refineWithProperties: safeParse(
                conditions.refineWithProperties.setIn(
                  [generateKey()],
                  initItemRefinePropertiesValue(),
                ),
                null,
              ),
            }),
          );
        } else if (typeChange === 'DELETE_ITEM') {
          dispatch(
            Actions.prepareData.updateRefineCondition({
              ...conditions,
              refineWithProperties: conditions.refineWithProperties.deleteIn(
                [refineIndex],
                value,
              ),
            }),
          );
        }
        break;
      }
      case 'CHANGE_SELECT_DATA': {
        dispatch(Actions.prepareData.updateMultipleSelect(data));
        break;
      }
      default:
        break;
    }
  };

  const changeSource = data => {
    callback('PERF_EVENT_CONDITION_CHANGE_SOURCE', data);
  };

  useEffect(() => {
    if (listEvent.length && listBO.length && !refApi.current) {
      const dataEvents = mapDatatoSelect(listEvent, 'perf_event');
      const dataBOs = mapDatatoSelect(listBO, 'comp_attr');

      setOptionEvent(prev => [
        { ...prev[0], options: dataEvents },
        { ...prev[1], options: dataBOs },
      ]);

      refApi.current = true;
    }
  }, [listBO, listEvent, setOptionEvent]);

  const fetchDataEvent = () => {
    SegmentServices.fetch
      .getListEvent({ objectType: 'STORIES' })
      .then(res => {
        if (_isMounted) {
          if (res.code === 200) {
            const { list, map } = toEventTrackingFE(res.data);
            setListEvent(res.data);
            setState(draft => {
              draft.eventSchema.list = list;
              draft.eventSchema.map = map;
              draft.eventSchema.isLoading = false;
              draft.loadDataDone = true;
            });
          } else {
            setState(draft => {
              draft.eventSchema.list = [];
              draft.eventSchema.map = {};
              draft.eventSchema.isLoading = false;
            });
          }
        }
      })
      .catch(() => {
        if (_isMounted) {
          setState(draft => {
            draft.eventSchema.list = [];
            draft.eventSchema.map = {};
            draft.eventSchema.isLoading = false;
          });
          // console.log('err ===>', err);
        }
      })
      .catch(err => {
        addMessageToQueue({
          path: 'app/components/common/UIPerformEvent/index.jsx',
          func: 'fetchData',
          data: err.stack,
        });
        setState(draft => {
          draft.eventSchema.list = [];
          draft.eventSchema.map = {};
          draft.eventSchema.isLoading = false;
        });
      });
  };

  const fetchDataBo = () => {
    // 1 default, 2defined 3 custom
    BusinessObject.businessObject
      .getEventTracking({ types: [2, 3] })
      .then(res => {
        if (res.code === 200) {
          setListBO(res.data);
        }
      });
  };

  useEffect(() => {
    fetchDataEvent();
    fetchDataBo();

    return () => {
      _isMounted = false;
    };
  }, []);

  const showContent = rules => {
    if (!rules.isInitDataSources) {
      const result = [];
      if (rules.conditionType) {
        result.push(
          <BlockEvent
            isViewMode={isViewMode}
            moduleConfig="perform-event"
            // paramsFetchEvent={props.paramsFetchEvent}
            item={rules}
            property={eventSelect}
            options={state.eventSchema.list}
            callback={callback}
            showButtonClose={rules.size > 1}
            changeSource={changeSource}
          />,
        );
      }
      return result;
    }
    return null;
  };

  const onChangeMultiple = (value, keyProps) => {
    callback('CHANGE_SELECT_DATA', {
      key: keyProps,
      val: {
        ...(eventSelect.type === 'perf_event' && {
          itemTypeName: get(value, 'itemTypeName', ''),
        }),
        label: value.label,
        value: value.valueRef ? value.valueRef : value.value,
        status: value.status,
        valueCustom: value.value,
      },
    });
  };

  return (
    <ErrorBoundary path={PATH}>
      <div className="data-input-container">
        <WrapperTitle>Data Inputs</WrapperTitle>

        {!hiddenDescription && (
          <WrapperInput marginBtn="20px">
            <WrapperLabel>{descriptionLabel}</WrapperLabel>
            <WrapperTextField>
              <WrapperDisableCustom disabled={isViewMode} isNoOpacity>
                <UITextField
                  value={stateDescription}
                  onChange={setStateDescription}
                  placeholder={placeholderDescription}
                  maxLength={500}
                />
              </WrapperDisableCustom>
            </WrapperTextField>
          </WrapperInput>
        )}

        <WrapperDisable
          disabled={computeStatus === PROCESS_COMPUTE_STATUS.COMPUTING}
        >
          <WrapperInput>
            <WrapperLabel>
              {labelRFMDataSource} <span className="required"> *</span>
            </WrapperLabel>
            <WrapperEvent>
              <WrapperDisableCustom
                disabled={isViewMode}
                // className="disabled"
                isNoOpacity
              >
                <SelectDropdown
                  options={optionEvent}
                  type="event"
                  widthDropdown="150px"
                  callback={callback}
                  value={eventSelect}
                  messageErrors={
                    dataSourceErrors.length > 0 && dataSourceErrors[0].message
                  }
                  disabled={isViewMode}
                  messageWarning={
                    eventSelect.type === 'comp_attr' &&
                    eventSelect.status === '2' &&
                    messageErrorStatusBO
                  }
                />
              </WrapperDisableCustom>
            </WrapperEvent>
          </WrapperInput>
          {eventSelect && eventSelect.type && showContent(conditions)}
          <WrapperMultiSelect>
            {isLoading ? (
              <UILoading isLoading={isLoading} />
            ) : (
              <Fragment>
                <WrapperInput
                  flexStart={
                    multipleSelect.customerIdentify &&
                    multipleSelect.customerIdentify.status === 2
                  }
                >
                  <WrapperLabel>
                    {labelCustomerIndentify}{' '}
                    <span className="required"> *</span>
                  </WrapperLabel>
                  <WrapperSelectTree>
                    <WrapperDisableCustom
                      disabled={isViewMode}
                      className="disabled"
                      isNoOpacity
                    >
                      <SelectTree
                        displayFormat
                        use="tree"
                        // isMulti
                        // isSearchableconvertDataInput(
                        options={dataMultiple.customer}
                        value={convertDataInput(
                          multipleSelect.customerIdentify,
                        )}
                        onChange={value =>
                          onChangeMultiple(value, 'customerIdentify')
                        }
                        disabled={isViewMode}
                        isDisabledBorder={isViewMode}
                        // placeholder={labelSelectAttrs}
                      />
                    </WrapperDisableCustom>
                    {multipleSelect.customerIdentify &&
                      multipleSelect.customerIdentify.status === 2 && (
                        <WrapperTextWarning isWrapTextWarning>
                          {messageWarningText('attribute')}
                        </WrapperTextWarning>
                      )}
                    {customIdentifyErrors.length > 0 && (
                      <WrapperTextError>
                        {customIdentifyErrors[0].message}
                      </WrapperTextError>
                    )}
                  </WrapperSelectTree>
                </WrapperInput>

                <WrapperInput
                  flexStart={
                    multipleSelect.date && multipleSelect.date.status === 2
                  }
                >
                  <WrapperLabel>
                    {labelDate} <span className="required"> *</span>
                  </WrapperLabel>
                  <WrapperSelectTree>
                    <WrapperDisableCustom
                      disabled={isViewMode}
                      className="disabled"
                      isNoOpacity
                    >
                      <SelectTree
                        displayFormat
                        use="tree"
                        // isMulti
                        // isSearchableconvertDataInput(
                        options={dataMultiple.date}
                        value={convertDataInput(multipleSelect.date)}
                        onChange={value => onChangeMultiple(value, 'date')}
                        // placeholder={labelSelectAttrs}
                        disabled={isViewMode}
                        isDisabledBorder={isViewMode}
                      />
                    </WrapperDisableCustom>
                    {multipleSelect.date &&
                      multipleSelect.date.status === 2 && (
                        <WrapperTextWarning isWrapTextWarning>
                          {messageWarningText('attribute')}
                        </WrapperTextWarning>
                      )}
                    {dateErrors.length > 0 && (
                      <WrapperTextError>
                        {dateErrors[0].message}
                      </WrapperTextError>
                    )}
                  </WrapperSelectTree>
                </WrapperInput>

                <WrapperInput
                  flexStart={
                    multipleSelect.order && multipleSelect.order.status === 2
                  }
                >
                  <WrapperLabel>
                    {labelOrder} <span className="required"> *</span>
                  </WrapperLabel>
                  <WrapperSelectTree>
                    <WrapperDisableCustom
                      disabled={isViewMode}
                      className="disabled"
                      isNoOpacity
                    >
                      <SelectTree
                        displayFormat
                        use="tree"
                        // isMulti
                        // isSearchableconvertDataInput(
                        options={dataMultiple.order}
                        value={convertDataInput(multipleSelect.order)}
                        onChange={value => onChangeMultiple(value, 'order')}
                        disabled={isViewMode}
                        isDisabledBorder={isViewMode}
                        // placeholder={labelSelectAttrs}
                      />
                    </WrapperDisableCustom>
                    {multipleSelect.order &&
                      multipleSelect.order.status === 2 && (
                        <WrapperTextWarning isWrapTextWarning>
                          {messageWarningText('attribute')}
                        </WrapperTextWarning>
                      )}
                    {orderErrors.length > 0 && (
                      <WrapperTextError>
                        {orderErrors[0].message}
                      </WrapperTextError>
                    )}
                  </WrapperSelectTree>
                </WrapperInput>

                <WrapperInput
                  flexStart={
                    multipleSelect.revenue &&
                    multipleSelect.revenue.status === 2
                  }
                >
                  <WrapperLabel>
                    {labelRevenue} <span className="required"> *</span>
                  </WrapperLabel>
                  <WrapperSelectTree>
                    <WrapperDisableCustom
                      disabled={isViewMode}
                      className="disabled"
                      isNoOpacity
                    >
                      <SelectTree
                        displayFormat
                        use="tree"
                        // isMulti
                        // isSearchableconvertDataInput(
                        options={dataMultiple.revenue}
                        value={convertDataInput(multipleSelect.revenue)}
                        onChange={value => onChangeMultiple(value, 'revenue')}
                        disabled={isViewMode}
                        isDisabledBorder={isViewMode}
                        // placeholder={labelSelectAttrs}
                      />
                    </WrapperDisableCustom>
                    {multipleSelect.revenue &&
                      multipleSelect.revenue.status === 2 && (
                        <WrapperTextWarning isWrapTextWarning>
                          {messageWarningText('attribute')}
                        </WrapperTextWarning>
                      )}
                    {revenueErrors.length > 0 && (
                      <WrapperTextError>
                        {revenueErrors[0].message}
                      </WrapperTextError>
                    )}
                  </WrapperSelectTree>
                </WrapperInput>

                <WrapperInput flexStart marginBtn="0px">
                  <WrapperLabel className="time-range">
                    {labelTimeRange} <span className="required"> *</span>
                  </WrapperLabel>
                  <Timerange
                    callback={callback}
                    timeRange={timeRange}
                    isViewMode={isViewMode}
                  />
                </WrapperInput>
              </Fragment>
            )}
          </WrapperMultiSelect>
        </WrapperDisable>

        {/* <div style={{ backgroundColor: 'red', height: 900, width: 1200 }} /> */}
      </div>
    </ErrorBoundary>
  );
};

export default memo(Step1);
