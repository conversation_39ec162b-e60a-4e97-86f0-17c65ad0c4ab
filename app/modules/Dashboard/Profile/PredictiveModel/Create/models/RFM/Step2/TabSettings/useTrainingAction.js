import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Actions } from '../../actions';
import { LoadingKeys } from './ScoringTemplate/utils';
import { makeSelectDataTrainModelCustomRanges } from '../../selector';

const useTrainingAction = ({ scoringType }) => {
  const dispatch = useDispatch();

  const dataForTrainCustomRange = useSelector(
    makeSelectDataTrainModelCustomRanges(scoringType),
  );

  const handleClickLoadingBtn = useCallback(
    loadingKey => {
      if (loadingKey === LoadingKeys.ALL_CHART) {
        dispatch(
          Actions.trainModel.handleTrainModel(scoringType, {
            openModalProgress: true,
          }),
        );
      }

      if (loadingKey === LoadingKeys.SCORECARD_AND_PIE) {
        dispatch(
          Actions.trainModel.trainModelWithCustomRange(dataForTrainCustomRange),
        );
      }
    },
    [dataForTrainCustomRange, dispatch, scoringType],
  );

  const handleChangeBarRange = useCallback(
    range => {
      dispatch(Actions.trainModel.changeRange(scoringType, range));
    },
    [dispatch, scoringType],
  );

  const handleChangeScoreLabel = useCallback(
    changedScore => {
      dispatch(Actions.trainModel.changeScoreLabel(scoringType, changedScore));
    },
    [dispatch, scoringType],
  );

  const handleChangeMaxRangeLimit = useCallback(
    value => {
      dispatch(Actions.trainModel.changeMaxRangeLimit(scoringType, value));
    },
    [dispatch, scoringType],
  );

  const handleChangeZoombar = useCallback(
    value => {
      dispatch(Actions.trainModel.updateZoom(value, scoringType));
    },
    [dispatch, scoringType],
  );

  const handleRecalculate = useCallback(
    () => dispatch(Actions.trainModel.recalculateWithZoom(scoringType)),
    [dispatch, scoringType],
  );

  return {
    handleChangeMaxRangeLimit,
    handleClickLoadingBtn,
    handleChangeBarRange,
    handleChangeScoreLabel,
    handleChangeZoombar,
    handleRecalculate,
  };
};

export default useTrainingAction;
