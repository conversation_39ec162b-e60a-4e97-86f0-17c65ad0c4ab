/* eslint-disable indent */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import { isEmpty } from 'lodash';
import { Types } from '../actions';
import { Types as MainTypes } from '../../../actions';
import { mapRefineUI } from '../utils';
import { OrderedMap } from 'immutable';

export const initialState = {
  initialized: false,
  eventSelect: {},
  multipleSelect: {
    date: {},
    order: {},
    revenue: {},
    customerIdentify: {},
  },
  dataSourceSelected: [],
  conditions: {},
  timeRange: {
    startDate: {
      date: '',
      calculationDate: 'years',
      value: 1,
      calculationType: 'minus',
      dateType: 'today',
    },
    endDate: {
      date: '',
      calculationDate: 'days',
      value: 1,
      calculationType: 'minus',
      dateType: 'today',
    },
  },

  prevPrepareDataAPI: null,
};

const prepareDataReducerFor = () => {
  const prepareDataReducer = (state = initialState, action) =>
    produce(state, draft => {
      const { type = '' } = action;
      switch (type) {
        case MainTypes.initDone: {
          const { activeRow } = action.payload;

          if (!isEmpty(activeRow)) {
            draft.timeRange = activeRow.dataCondition.time_range;

            if (activeRow.dataCondition.condition) {
              draft.conditions = activeRow.dataCondition.condition;

              if (activeRow.dataCondition.condition.refineWithProperties) {
                if (
                  OrderedMap.isOrderedMap(
                    activeRow.dataCondition.condition.refineWithProperties,
                  )
                ) {
                  draft.conditions.refineWithProperties =
                    activeRow.dataCondition.condition.refineWithProperties;
                } else {
                  const objectTmp = {
                    ...activeRow.dataCondition.condition.refineWithProperties,
                  };

                  draft.conditions.refineWithProperties = mapRefineUI(
                    objectTmp,
                  );
                }
              }
            }

            if (activeRow.dataCondition.multip_select) {
              draft.multipleSelect = activeRow.dataCondition.multip_select;
            }

            if (activeRow.dataCondition.event_select) {
              draft.eventSelect = activeRow.dataCondition.event_select;
            }

            draft.initialized = true;
            return;
          }

          return { ...initialState, initialized: true };
        }
        case MainTypes.reset: {
          return initialState;
        }
        case Types.prepareData.updateEventSelect: {
          draft.eventSelect = action.payload;
          break;
        }
        case Types.prepareData.cachePrepareDataAPI: {
          draft.prevPrepareDataAPI = action.payload;
          break;
        }
        case Types.prepareData.updateSourceSelected: {
          draft.dataSourceSelected = action.payload;
          break;
        }
        case Types.prepareData.updateRefineCondition: {
          draft.conditions = action.payload;
          break;
        }
        case Types.prepareData.updateMultipleSelect: {
          const { key, val } = action.payload;
          draft.multipleSelect[key] = val;
          break;
        }
        case Types.prepareData.updateTimeRange: {
          draft.timeRange = action.payload;
          break;
        }
        default:
          break;
      }
    });
  return prepareDataReducer;
};

export default prepareDataReducerFor;
