/* eslint-disable indent */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import { generateKey } from 'utils/common';
import { Types } from '../actions';
import { Types as MainTypes } from '../../../actions';
import { MODEL_MATRIX } from '../../../../config';
import {
  initDefaultRanges,
  getRangesWithInputRanges,
  updateRangesByScore,
  mergeRangesWithChartRanges,
  handleInitTrainModel,
  initDataRanges,
  rangesToInputRanges,
  getMinMaxRangeFromInputRanges,
} from '../utils';
import {
  DEFAULT_MATRIX,
  SCORING_TYPE as S_TYPE,
  TRAINING_STEP_KEYS,
} from '../config';
import { TRAINING_MODE } from '../../../config';
import _ from 'lodash';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';

export default () => (state = initialState(), action) =>
  produce(state, draft => {
    try {
      const { type, payload } = action;

      const updateZoombar = (zoomRange, scoreType) => {
        const temp = [...zoomRange, ...draft[scoreType].zoombar.default];

        draft[scoreType].zoombar = {
          range: zoomRange,
          default: [_.min(temp), _.max(temp)],
        };
      };

      switch (type) {
        case MainTypes.resetViewName: {
          Object.values(S_TYPE).forEach(scoreType => {
            draft[scoreType].isUsingDefaultScorecardAndPie = false;
          });

          break;
        }
        case MainTypes.updateTrainingMode: {
          const { mode } = payload;

          draft.mode = mode;
          break;
        }
        case MainTypes.updateTrainModel: {
          const { data } = payload;

          return data;
        }
        case MainTypes.initDone: {
          const {
            activeRow,
            isVersionHistory,
            isInitFromNoti,
            design,
          } = action.payload;

          const initValue = handleInitTrainModel({
            modelDetail: activeRow,
            isVersionHistory,
            trainModelState: state,
            initialTrainModelState: initialState(),

            ...(isInitFromNoti && {
              isInitFromNoti,
              isCreatePage: design === 'create',
            }),
          });

          if (initValue) return { ...initValue, initialized: true };
          break;
        }
        case MainTypes.resetTrainModel: {
          return { ...initialState(), initialized: true };
        }
        case MainTypes.reset: {
          return initialState();
        }
        case Types.trainModel.changeMatrix: {
          const { matrix } = payload;

          draft.matrix = matrix;
          draft.personas.isUsingDefaultChart = true;
          draft.personas.configs = MODEL_MATRIX[matrix].defaultPersonas;

          Object.values(S_TYPE).forEach(scoreType => {
            draft[scoreType] = initialState()[scoreType];
          });
          break;
        }
        // case Types.trainModel.usingDefaultAllChart: {
        //   const { usingDefault, scoreType } = payload;
        //
        //   draft[scoreType].isUsingDefaultAllChart = usingDefault;
        //
        //   if (usingDefault) {
        //     draft[scoreType].isUsingDefaultScorecardAndPie = false;
        //
        //     const ranges = initDefaultRanges(
        //       state.matrix,
        //       state[scoreType].ranges,
        //     );
        //
        //     draft[scoreType].ranges = ranges;
        //     draft[scoreType].dataRanges = initDataRanges(
        //       rangesToInputRanges(ranges),
        //     );
        //   }
        //   break;
        // }
        case Types.trainModel.changeGranularity: {
          draft[S_TYPE.recency].overWrite.granularity = payload;
          break;
        }
        case Types.trainModel.changeAggregationFrequency: {
          draft[S_TYPE.frequency].overWrite.aggregation = payload;
          break;
        }
        case Types.trainModel.changeAggregationMonetary: {
          draft[S_TYPE.monetary].overWrite.aggregation = payload;
          break;
        }
        case Types.trainModel.togglePersonasConfig: {
          const { isOpen } = payload;

          draft.personas.isOpenModalConfig = isOpen;
          break;
        }
        case Types.trainModel.updatePersonasConfig: {
          const { personasConfigs } = payload;

          draft.personas.configs = personasConfigs;
          draft.personas.isUsingDefaultChart = true;
          break;
        }
        case Types.trainModel.changeRange: {
          const { scoreType, range } = payload;

          const newRanges = mergeRangesWithChartRanges(
            state[scoreType].ranges,
            range,
            {
              reverse: scoreType === S_TYPE.recency,
            },
          );

          const { min: newMin, max: newMax } = getMinMaxRangeFromInputRanges(
            newRanges,
          );

          const { min: oldMin, max: oldMax } = getMinMaxRangeFromInputRanges(
            state[scoreType].ranges,
          );

          // When user change first point or last point => recaculate
          if (oldMin !== newMin || oldMax !== newMax) {
            draft[scoreType].needRecalculateBarchart = true;
            draft[scoreType].isUsingDefaultScorecardAndPie = false;

            updateZoombar([newMin, newMax], scoreType);
          } else {
            draft[scoreType].isUsingDefaultScorecardAndPie = true;
          }

          draft[scoreType].ranges = newRanges;

          break;
        }
        case Types.trainModel.updatePieAndScoreCard: {
          const { scoreType, pieChart, scoreCard } = payload;

          if (state[S_TYPE[scoreType]]) {
            draft[S_TYPE[scoreType]].isUsingDefaultScorecardAndPie = false;
            draft[S_TYPE[scoreType]].charts.pieChart = pieChart;
            draft[S_TYPE[scoreType]].charts.scoreCard = scoreCard;
          }

          break;
        }
        case Types.trainModel.changeScoreLabel: {
          const { changedScore, scoreType } = payload;

          if (scoreType) {
            draft[scoreType].ranges = updateRangesByScore(
              state[scoreType].ranges,
              changedScore.score,
              { label: changedScore.label },
            );
          }
          break;
        }
        case Types.trainModel.handleTrainModelSuccess: {
          const { dataRanges, inputRanges, scoreType } = payload;

          const {
            min: newMinZoom,
            max: newMaxZoom,
          } = getMinMaxRangeFromInputRanges(inputRanges);

          const ranges = getRangesWithInputRanges(
            inputRanges,
            state[scoreType].ranges,
          );

          draft[scoreType].ranges = ranges;
          draft[scoreType].dataRanges = dataRanges;
          draft[scoreType].isUsingDefaultAllChart = false;
          draft[scoreType].isUsingDefaultScorecardAndPie = false;
          draft[scoreType].chartKey = generateKey();

          updateZoombar([newMinZoom, newMaxZoom], scoreType);
          break;
        }
        case Types.trainModel.trainWithCustomRangeSucc: {
          const { inputRanges, scoreType } = payload;

          const { ranges } = state[scoreType];

          const entries = Object.entries(ranges).map(([key, value]) => [
            key,
            { ...value, value: inputRanges[key].customers },
          ]);

          draft[scoreType].ranges = Object.fromEntries(entries);
          draft[scoreType].isUsingDefaultScorecardAndPie = false;
          break;
        }
        case Types.trainModel.trainPersonasSucc: {
          const { data } = payload;
          draft.personas.values = data;
          draft.personas.isUsingDefaultChart = false;
          break;
        }
        case Types.trainModel.usingDefaultPersonas: {
          const { isUsingDefault } = payload;
          draft.personas.isUsingDefaultChart = isUsingDefault;
          break;
        }
        case Types.trainModel.updateIsTrainingScoring: {
          const { isTraining, scoreType } = payload;

          if (scoreType) {
            draft[scoreType].isTraining = isTraining;
          }
          break;
        }
        case Types.trainModel.updateIsTrainingScoringWithCusRange: {
          const { isTraining, scoreType } = payload;

          if (scoreType) {
            draft[scoreType].isTrainingWithCustomRange = isTraining;
          }
          break;
        }
        case Types.trainModel.updateIsTranningPersonas: {
          const { isTraining } = payload;
          draft.personas.isTraining = isTraining;
          break;
        }
        case Types.trainModel.changeMaxRangeLimit: {
          const { maxRangeLimit, scoreType } = payload;

          draft[scoreType].maxRangeLimit = maxRangeLimit;
          break;
        }
        case Types.trainModel.updateActTrainingKey: {
          const { key } = payload;

          draft.active = key;
          break;
        }
        case Types.trainModel.updateZoom: {
          const { min, max, scoreType } = payload;

          draft[scoreType].needRecalculateBarchart = true;

          updateZoombar([min, max], scoreType);
          break;
        }
        case Types.trainModel.updateScoring: {
          const { scoreType, updated } = payload;

          draft[scoreType] = { ...state[scoreType], ...updated };
          break;
        }
        default:
          break;
      }
    } catch (error) {
      addMessageToQueue({
        error,
        func: 'RFM trainModel Reducer',
      });

      return draft;
    }
  });

const initialState = (defaultMatrix = DEFAULT_MATRIX) => {
  const defaultPeronasConfig = MODEL_MATRIX[defaultMatrix].defaultPersonas;

  const defaultRanges = initDefaultRanges(defaultMatrix);
  const defaultDataRanges = initDataRanges(rangesToInputRanges(defaultRanges));

  return {
    initialized: false,
    mode: TRAINING_MODE.AI,
    active: TRAINING_STEP_KEYS.PERSONAS,
    matrix: defaultMatrix,
    [S_TYPE.recency]: {
      isUsingDefaultAllChart: true,
      isUsingDefaultScorecardAndPie: false,
      needRecalculateBarchart: false,
      isTraining: false,
      isTrainingWithCustomRange: false,
      overWrite: {
        granularity: {
          value: 'day',
          label: 'Day',
        },
      },
      ranges: defaultRanges,
      dataRanges: defaultDataRanges,
      zoombar: {
        range: [0, 0],
        default: [0, 0],
      },
    },
    [S_TYPE.frequency]: {
      isUsingDefaultAllChart: true,
      isUsingDefaultScorecardAndPie: false,
      needRecalculateBarchart: false,
      isTraining: false,
      isTrainingWithCustomRange: false,
      overWrite: {
        aggregation: {
          value: 'count',
        },
      },
      ranges: defaultRanges,
      dataRanges: defaultDataRanges,
      zoombar: {
        range: [0, 0],
        default: [0, 0],
      },
    },
    [S_TYPE.monetary]: {
      isUsingDefaultAllChart: true,
      isUsingDefaultScorecardAndPie: false,
      isTraining: false,
      needRecalculateBarchart: false,
      isTrainingWithCustomRange: false,
      overWrite: {
        aggregation: {
          value: 'sum',
        },
      },
      ranges: defaultRanges,
      dataRanges: defaultDataRanges,
      zoombar: {
        range: [0, 0],
        default: [0, 0],
      },
    },
    personas: {
      isUsingDefaultChart: true,
      isTraining: false,
      isOpenModalConfig: false,
      configs: defaultPeronasConfig,
      values: {},
    },
  };
};
