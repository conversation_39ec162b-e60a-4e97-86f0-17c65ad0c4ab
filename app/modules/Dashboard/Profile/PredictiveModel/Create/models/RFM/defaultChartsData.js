import { MODEL_MATRIX_TYPE } from '../../../config';

export const DF_DATA_RANGES = JSON.parse(`
{"1":{"min":0,"max":3,"customers":0},"2":{"min":4,"max":7,"customers":0},"3":{"min":8,"max":10,"customers":0},"4":{"min":11,"max":14,"customers":0},"5":{"min":15,"max":18,"customers":0},"6":{"min":19,"max":21,"customers":0},"7":{"min":22,"max":25,"customers":0},"8":{"min":26,"max":29,"customers":0},"9":{"min":30,"max":32,"customers":0},"10":{"min":33,"max":36,"customers":0},"11":{"min":37,"max":39,"customers":0},"12":{"min":40,"max":43,"customers":0},"13":{"min":44,"max":47,"customers":0},"14":{"min":48,"max":50,"customers":0},"15":{"min":51,"max":54,"customers":0},"16":{"min":55,"max":58,"customers":0},"17":{"min":59,"max":61,"customers":0},"18":{"min":62,"max":65,"customers":0},"19":{"min":66,"max":68,"customers":0},"20":{"min":69,"max":72,"customers":0},"21":{"min":73,"max":76,"customers":0},"22":{"min":77,"max":79,"customers":0},"23":{"min":80,"max":83,"customers":0},"24":{"min":84,"max":87,"customers":0},"25":{"min":88,"max":90,"customers":0},"26":{"min":91,"max":94,"customers":0},"27":{"min":95,"max":98,"customers":0},"28":{"min":99,"max":101,"customers":0},"29":{"min":102,"max":105,"customers":0},"30":{"min":106,"max":108,"customers":0},"31":{"min":109,"max":112,"customers":0},"32":{"min":113,"max":116,"customers":0},"33":{"min":117,"max":119,"customers":0},"34":{"min":120,"max":123,"customers":0},"35":{"min":124,"max":127,"customers":0},"36":{"min":128,"max":130,"customers":0},"37":{"min":131,"max":134,"customers":0},"38":{"min":135,"max":137,"customers":0},"39":{"min":138,"max":141,"customers":0},"40":{"min":142,"max":145,"customers":0},"41":{"min":146,"max":148,"customers":0},"42":{"min":149,"max":152,"customers":0},"43":{"min":153,"max":156,"customers":0},"44":{"min":157,"max":159,"customers":0},"45":{"min":160,"max":163,"customers":0},"46":{"min":164,"max":166,"customers":0},"47":{"min":167,"max":170,"customers":0},"48":{"min":171,"max":174,"customers":0},"49":{"min":175,"max":177,"customers":0},"50":{"min":178,"max":181,"customers":0},"51":{"min":182,"max":185,"customers":0},"52":{"min":186,"max":188,"customers":0},"53":{"min":189,"max":192,"customers":0},"54":{"min":193,"max":196,"customers":0},"55":{"min":197,"max":199,"customers":0},"56":{"min":200,"max":203,"customers":0},"57":{"min":204,"max":206,"customers":0},"58":{"min":207,"max":210,"customers":0},"59":{"min":211,"max":214,"customers":0},"60":{"min":215,"max":217,"customers":0},"61":{"min":218,"max":221,"customers":0},"62":{"min":222,"max":225,"customers":0},"63":{"min":226,"max":228,"customers":0},"64":{"min":229,"max":232,"customers":0},"65":{"min":233,"max":235,"customers":0},"66":{"min":236,"max":239,"customers":0},"67":{"min":240,"max":243,"customers":0},"68":{"min":244,"max":246,"customers":0},"69":{"min":247,"max":250,"customers":0},"70":{"min":251,"max":254,"customers":0},"71":{"min":255,"max":257,"customers":0},"72":{"min":258,"max":261,"customers":0},"73":{"min":262,"max":264,"customers":0},"74":{"min":265,"max":268,"customers":0},"75":{"min":269,"max":272,"customers":0},"76":{"min":273,"max":275,"customers":0},"77":{"min":276,"max":279,"customers":0},"78":{"min":280,"max":283,"customers":0},"79":{"min":284,"max":286,"customers":0},"80":{"min":287,"max":290,"customers":0},"81":{"min":291,"max":294,"customers":0},"82":{"min":295,"max":297,"customers":0},"83":{"min":298,"max":301,"customers":0},"84":{"min":302,"max":304,"customers":0},"85":{"min":305,"max":308,"customers":0},"86":{"min":309,"max":312,"customers":0},"87":{"min":313,"max":315,"customers":0},"88":{"min":316,"max":319,"customers":0},"89":{"min":320,"max":323,"customers":0},"90":{"min":324,"max":326,"customers":0},"91":{"min":327,"max":330,"customers":0},"92":{"min":331,"max":333,"customers":0},"93":{"min":334,"max":337,"customers":0},"94":{"min":338,"max":341,"customers":0},"95":{"min":342,"max":344,"customers":0},"96":{"min":345,"max":348,"customers":0},"97":{"min":349,"max":352,"customers":0},"98":{"min":353,"max":355,"customers":0},"99":{"min":356,"max":359,"customers":0},"100":{"min":360,"max":362,"customers":0},"101":{"min":363,"max":363,"customers":0}}
`);

const BAR_CHART_RANGE = {
  [MODEL_MATRIX_TYPE.matrix_3x3]: {
    1: {
      min: 0,
      max: 132,
      value: 0,
      label: 'Low',
      color: '#76C969',
    },
    2: {
      min: 132,
      max: 260,
      value: 0,
      label: 'Medium',
      color: '#E67880',
    },
    3: {
      min: 260,
      max: 360,
      value: 0,
      label: 'High',
      color: '#5B9EDE',
    },
  },
  [MODEL_MATRIX_TYPE.matrix_5x5]: {
    5: {
      min: 0,
      max: 132,
      value: 0,
      label: 'Very high',
      color: '#5B9EDE',
    },
    4: {
      min: 132,
      max: 220,
      value: 0,
      label: 'High',
      color: '#E67880',
    },
    3: {
      min: 220,
      max: 260,
      value: 0,
      label: 'Medium',
      color: '#76C969',
    },
    2: {
      min: 260,
      max: 300,
      value: 0,
      label: 'Low',
      color: '#FBAE2B',
    },
    1: {
      min: 300,
      max: 360,
      value: 0,
      label: 'Very low',
      color: '#9982E5',
    },
  },
};

const TREE_MAP_DATA = {
  totalValue: 0,
  timeRange: ['Aug 20, 2022', 'Feb 20, 2023'],
  personas: [
    {
      id: 1,
      name: 'champions',
      label: 'Champions',
      color: '#77BBF9',
      value: 0,
      recency: {
        score: [3, 3],
        value: [19, 92],
      },
      frequency: {
        score: [3, 3],
        value: [19, 92],
      },
      monetary: {
        score: [1, 3],
        value: [19, 92],
      },
    },
    {
      id: 2,
      name: 'loyal',
      label: 'Loyal',
      color: '#A19AAE',
      value: 0,
      recency: {
        score: [2, 3],
        value: [19, 92],
      },
      frequency: {
        score: [2, 3],
        value: [19, 92],
      },
      monetary: {
        score: [1, 3],
        value: [19, 92],
      },
    },
    {
      id: 3,
      name: 'newCustomers',
      label: 'New Customers',
      color: '#CBDF7B',
      value: 0,
      recency: {
        score: [3, 1],
        value: [19, 92],
      },
      frequency: {
        score: [3, 1],
        value: [19, 92],
      },
      monetary: {
        score: [1, 3],
        value: [19, 92],
      },
    },
    {
      id: 4,
      name: 'promising',
      label: 'Promising',
      color: '#DDC679',
      value: 0,
      recency: {
        score: [3, 2],
        value: [19, 92],
      },
      frequency: {
        score: [3, 2],
        value: [19, 92],
      },
      monetary: {
        score: [1, 3],
        value: [19, 92],
      },
    },
    {
      id: 5,
      name: 'aboutToSleep',
      label: 'About To Sleep',
      color: '#A0D8DA',
      value: 0,
      recency: {
        score: [2, 2],
        value: [19, 92],
      },
      frequency: {
        score: [2, 2],
        value: [19, 92],
      },
      monetary: {
        score: [1, 3],
        value: [19, 92],
      },
    },
    {
      id: 6,
      name: 'atRisk',
      label: 'At Risk',
      color: '#F1A282',
      value: 0,
      recency: {
        score: [1, 2],
        value: [19, 92],
      },
      frequency: {
        score: [1, 2],
        value: [19, 92],
      },
      monetary: {
        score: [1, 3],
        value: [19, 92],
      },
    },
    {
      id: 7,
      name: 'cannotLoseThem',
      label: 'Cannot Lose Them',
      color: '#FA8086',
      value: 0,
      recency: {
        score: [1, 1],
        value: [19, 92],
      },
      frequency: {
        score: [3, 3],
        value: [19, 92],
      },
      monetary: {
        score: [1, 3],
        value: [200000, 3000000],
      },
    },
    {
      id: 8,
      name: 'hibernatingCustomers',
      label: 'Hibernating Customers',
      color: '#C5D2DD',
      value: 0,
      recency: {
        score: [2, 2],
        value: [19, 92],
      },
      frequency: {
        score: [3, 3],
        value: [19, 92],
      },
      monetary: {
        score: [1, 3],
        value: [19, 92],
      },
    },
    {
      id: 9,
      name: 'LostCustomers',
      label: 'Lost Customers',
      color: '#D0CEE2',
      value: 0,
      recency: {
        score: [1, 1],
        value: [19, 92],
      },
      frequency: {
        score: [1, 1],
        value: [19, 92],
      },
      monetary: {
        score: [1, 3],
        value: [19, 92],
      },
    },
  ],
};

export default {
  DATA_RANGES: DF_DATA_RANGES,
  RANGES: BAR_CHART_RANGE,
  TREE_MAP: {
    DATA: TREE_MAP_DATA,
  },
};
