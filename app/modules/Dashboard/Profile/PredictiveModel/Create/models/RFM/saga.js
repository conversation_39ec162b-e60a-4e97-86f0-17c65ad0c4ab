/* eslint-disable no-param-reassign */
// /* eslint-disable no-restricted-syntax */
/* eslint-disable indent */
/* eslint-disable func-names */
/* eslint-disable object-shorthand */
import {
  take,
  call,
  put,
  race,
  takeLatest,
  select,
  all,
} from 'redux-saga/effects';
import { isEmpty } from 'lodash';
import { produce } from 'immer';
import { Types, Actions } from './actions';
import { Actions as MainActions, Types as MainTypes } from '../../actions';
import { addMessageToQueue } from 'utils/web/queue';
import PredictiveModelService from 'services/PredictiveModel';
import {
  selectModalProgress,
  selectViewName,
  selectActiveStep,
  selectIsVersionHistory,
} from '../../selector';
import { MAX_COLUMN_BAR_CHART, SCORING_TYPE } from './config';
import {
  rangesToInputRanges,
  serializeCustomInputRanges,
  serializePersonasDataAPI,
} from './utils';
import {
  makeSelectInputRanges,
  makeSelectIsFirstTraning,
  makeSelectMaxRangeLimit,
  makeSelectRanges,
  makeSelectScoringAPI,
  selectPrepareDataAPI,
  selectTrainPersonasInfo,
  selectTrainModel,
  makeSelectZoombar,
  selectTrainingMode,
  selectPrepareData,
  selectBackupData,
} from './selector';
import { addNotification } from '../../../../../actions';
import TRANSLATE_KEY from 'messages/constant';
import {
  handleValidateModelSettings,
  executeModelTrainingWithProgress,
} from '../../saga';
import { STEP_SETTING_KEYS, TRAINING_MODE } from '../../config';
import { safeParse } from 'utils/common';
import isEqual from 'react-fast-compare';
import { serializeBasicBarchart } from '../../utils';

const ERROR_PATH =
  'app/modules/Dashboard/Profile/PredictiveModel/Create/RFMModel/saga.js';

export default function* workerRFMModelSaga() {
  yield takeLatest(
    Types.trainModel.trainModelWithCustomRange,
    handleTrainModelWithCustomRange,
  );

  yield takeLatest(Types.trainModel.handleTrainModel, handleTrainModel);

  yield takeLatest(Types.trainModel.changeMatrix, handleChangeMatrix);

  yield takeLatest(Types.trainModel.trainPersonas, handleTrainPersonas);

  yield takeLatest(MainTypes.changeStepSettings, handleChangeStepSettings);

  yield takeLatest(MainTypes.updateTrainingMode, handleUpdateTrainingMode);

  yield takeLatest(
    MainTypes.initFromNotiDone,
    handleChangeStepAfterInitFromNoti,
  );

  yield takeLatest(
    Types.trainModel.recalculateWithZoom,
    handleRecalculateWithZoom,
  );
}

function* handleChangeStepAfterInitFromNoti() {
  try {
    yield put(MainActions.updateIsLoading(true));

    yield call(trainAllScoringAndPersonas);

    yield put(MainActions.updateActiveStep(STEP_SETTING_KEYS.trainModel));
  } catch (err) {
    yield put(addNotification(NOTI.error(err)));

    addMessageToQueue({
      path: ERROR_PATH,
      func: handleChangeStepAfterInitFromNoti.name,
      data: err.stack,
    });
  } finally {
    yield put(MainActions.updateIsLoading(false));
  }
}

function* handleTrainModel(action) {
  const { scoreType, options = {} } = action?.payload || {};

  try {
    const viewName = yield select(selectViewName);
    const scoreInfo = yield select(makeSelectScoringAPI(scoreType));
    const ranges = yield select(makeSelectRanges(scoreType));
    const maxRangeLimitType = yield select(makeSelectMaxRangeLimit(scoreType));
    const isFirstTraning = yield select(makeSelectIsFirstTraning(scoreType));
    const zoombar = yield select(makeSelectZoombar(scoreType));

    yield put(Actions.trainModel.updateIsTrainingScoring(true, scoreType));

    let inputRanges;
    let dataRanges;

    if (isFirstTraning) {
      const effectParams = { viewName, scoreInfo };

      [inputRanges, dataRanges] = yield all([
        call(getDefaultRanges, effectParams),
        call(getDataAggregation, effectParams),
      ]);
    } else {
      const baseParams = {
        viewName,
        scoreInfo,
        limitType: maxRangeLimitType,
      };

      const getRangesEffect = function*() {
        if (options.recalculate && options.ranges) {
          yield options.ranges;
          return;
        }

        yield getCustomRanges({
          ...baseParams,
          inputRanges: options.ranges || rangesToInputRanges(ranges),
        });
      };

      const getDataAggregationEffect = function*() {
        yield getDataAggregation({
          ...baseParams,
          zoombarRange: zoombar.range,
        });
      };

      [inputRanges, dataRanges] = yield all([
        call(getRangesEffect),
        call(getDataAggregationEffect),
      ]);
    }

    yield put(
      Actions.trainModel.handleTrainModelSuccess(
        scoreType,
        inputRanges,
        dataRanges,
      ),
    );

    return { success: true };
  } catch (err) {
    yield put(Actions.trainModel.handleTrainModelFail(scoreType));

    addMessageToQueue({
      path: ERROR_PATH,
      func: handleTrainModel.name,
      data: err.stack,
    });

    return { success: false };
  } finally {
    yield put(Actions.trainModel.updateIsTrainingScoring(false, scoreType));
  }
}

function* handleChangeStepSettings(action) {
  const { stepIndex } = action.payload;

  let navigatedStep = stepIndex;
  let hasTrainModel = false;

  const activeStep = yield select(selectActiveStep);
  const isVersionHistory = yield select(selectIsVersionHistory);

  try {
    if (navigatedStep < activeStep || isVersionHistory) {
      yield put(MainActions.updateActiveStep(navigatedStep));
      return;
    }

    const errors = yield call(
      handleValidateModelSettings,
      MainActions.validateModelSettings({
        step: activeStep,
      }),
    );

    if (navigatedStep > activeStep && !isEmpty(errors)) {
      navigatedStep = activeStep;
    }

    const prepareDataInfo = yield select(selectPrepareDataAPI);

    const { prevPrepareDataAPI } = yield select(selectPrepareData);

    if (!!prevPrepareDataAPI && !isEqual(prepareDataInfo, prevPrepareDataAPI)) {
      yield put(MainActions.resetViewName());
    }

    const viewName = yield select(selectViewName);

    if (navigatedStep >= STEP_SETTING_KEYS.trainModel && isEmpty(viewName)) {
      hasTrainModel = true;

      const backupData = yield select(selectBackupData);

      yield call(executeModelTrainingWithProgress, {
        *sagaFunc() {
          yield put(
            MainActions.prepareData({
              prepareDataInfo,
              backupData,
            }),
          );

          const [prepareSuc, prepareFail] = yield race([
            take(MainTypes.prepareDataSuccess),
            take(MainTypes.prepareDataFail),
          ]);

          if (prepareFail) {
            navigatedStep = activeStep;

            throw new Error(
              'Failed to prepare data before navigating to the train model step',
            );
          }

          yield put(MainActions.setViewName(prepareSuc?.payload?.viewName));

          yield call(trainAllScoringAndPersonas);

          yield put(Actions.prepareData.cachePrepareDataAPI(prepareDataInfo));

          yield put(
            MainActions.validateModelSettings({
              step: STEP_SETTING_KEYS.trainModel,
            }),
          );
        },

        *onCancel() {
          navigatedStep = activeStep;
        },
      });
    }
  } catch (err) {
    navigatedStep = activeStep;

    if (hasTrainModel) {
      yield put(MainActions.setViewName(null));
    }

    yield put(addNotification(NOTI.error(err)));

    addMessageToQueue({
      path: ERROR_PATH,
      func: handleChangeStepSettings.name,
      data: err.stack,
    });
  } finally {
    const { isOpen } = yield select(selectModalProgress);

    if (isOpen) {
      yield put(
        MainActions.updateModalProgress({
          isOpen: false,
        }),
      );
    }

    yield put(MainActions.updateActiveStep(navigatedStep));
  }
}

function* handleChangeMatrix() {
  try {
    yield call(trainAllScoringAndPersonas);
  } catch (error) {
    addMessageToQueue({
      func: 'handleChangeMatrix',
      path: ERROR_PATH,
    });
  }
}

function* trainAllScoringAndPersonas() {
  yield put(Actions.trainModel.updateIsTranningPersonas(true));

  try {
    const results = yield all(
      Object.values(SCORING_TYPE).map(scoreType =>
        call(handleTrainModel, {
          payload: { scoreType },
        }),
      ),
    );

    const success = results.every(i => i.success);

    if (success) {
      const personasInfo = yield select(selectTrainPersonasInfo);

      if (personasInfo === null) return;

      yield call(handleTrainPersonas);

      return;
    }
  } catch (error) {
    throw new Error(
      'An error occurred while training the scoring and personas models.',
    );
  } finally {
    yield put(Actions.trainModel.updateIsTranningPersonas(false));
  }
}

function* handleTrainPersonas() {
  try {
    yield put(Actions.trainModel.updateIsTranningPersonas(true));

    const mode = yield select(selectTrainingMode);

    const personasInfo = yield select(selectTrainPersonasInfo);

    if (personasInfo === null && mode === TRAINING_MODE.AI) {
      yield call(trainAllScoringAndPersonas);

      return;
    }

    if (personasInfo === null) throw Error();

    const { data } = yield call(PredictiveModelService.RFMModel.getPersonas, {
      personasInfo,
    });

    if (data) {
      const personasKeys = Object.keys(personasInfo?.personas_conf || {});

      const serializeData = serializePersonasDataAPI(data, personasKeys);

      yield put(Actions.trainModel.trainPersonasSucc(serializeData));
    }
  } catch (err) {
    yield put(Actions.trainModel.trainPersonasFail(err.message));

    addMessageToQueue({
      path: ERROR_PATH,
      func: handleTrainPersonas.name,
      data: err.stack,
    });
  } finally {
    yield put(Actions.trainModel.updateIsTranningPersonas(false));
  }
}

function* handleTrainModelWithCustomRange(action) {
  const { scoreType } = action.payload;

  try {
    const viewName = yield select(selectViewName);
    const inputRanges = yield select(makeSelectInputRanges(scoreType));
    const scoreInfo = yield select(makeSelectScoringAPI(scoreType));
    const maxRangeLimitType = yield select(makeSelectMaxRangeLimit(scoreType));

    yield put(
      Actions.trainModel.updateIsTrainingScoringWithCusRange(true, scoreType),
    );

    const updatedInputRanges = yield call(getCustomRanges, {
      viewName,
      inputRanges,
      scoreInfo,
      limitType: maxRangeLimitType,
    });

    yield put(
      Actions.trainModel.trainWithCustomRangeSucc(
        updatedInputRanges,
        scoreType,
      ),
    );
  } catch (err) {
    yield put(Actions.trainModel.trainWithCustomRangeFail());

    addMessageToQueue({
      path: ERROR_PATH,
      func: handleTrainModelWithCustomRange.name,
      data: err.stack,
    });
  } finally {
    yield put(
      Actions.trainModel.updateIsTrainingScoringWithCusRange(false, scoreType),
    );
  }
}

function* getCustomRanges(params) {
  try {
    const {
      viewName,
      inputRanges: customRanges,
      scoreInfo,
      limitType,
      zoombarRange,
    } = params;

    const { data: apiRanges } = yield call(
      PredictiveModelService.RFMModel.buildCustomRange,
      {
        viewName,
        zoombarRange,
        scoreInfo,
        limitType,

        ...(zoombarRange ? { zoombarRange } : { inputRanges: customRanges }),
      },
    );

    return serializeCustomInputRanges({
      apiRanges,
      customRanges,
      limitType,
    });
  } catch (error) {
    throw Error(error);
  }
}

function* getDefaultRanges(params) {
  try {
    const { viewName, scoreInfo } = params;

    const { data } = yield call(
      PredictiveModelService.RFMModel.buildDefaultRange,
      {
        viewName,
        scoreInfo,
      },
    );

    return safeParse(data, {});
  } catch (error) {
    throw Error(error);
  }
}

function* getDataAggregation(params) {
  try {
    const { viewName, scoreInfo, zoombarRange, limitType } = params;

    const { data } = yield call(PredictiveModelService.RFMModel.aggregation, {
      viewName,
      scoreInfo,
      maxColumn: MAX_COLUMN_BAR_CHART,
      zoombarRange,
      limitType,
    });

    if (isEmpty(data)) {
      throw new Error('Get data ranges fail');
    }

    if (zoombarRange) {
      const zoombar = yield select(makeSelectZoombar(scoreInfo.score_type));

      return serializeBasicBarchart(data, zoombar.default).map(i => ({
        min: i.min,
        max: i.max,
        customers: i.value,
      }));
    }

    return data;
  } catch (error) {
    throw Error(error);
  }
}

function* handleUpdateTrainingMode(action) {
  try {
    const { mode } = action.payload;

    if (mode === TRAINING_MODE.AI) {
      yield put(MainActions.resetTrainModel());

      const trainModel = yield select(selectTrainModel);

      const updated = produce(trainModel, draft => {
        draft.personas.isUsingDefaultChart = true;
        draft.personas.isTraining = true;
      });

      yield put(MainActions.updateTrainModel(updated));

      yield call(trainAllScoringAndPersonas);
    }
  } catch (error) {
    addMessageToQueue({
      path: ERROR_PATH,
      func: handleUpdateTrainingMode.name,
      data: error.stack,
    });
  }
}

function* handleRecalculateWithZoom(action) {
  try {
    const { scoreType } = action.payload;

    const viewName = yield select(selectViewName);
    const scoreInfo = yield select(makeSelectScoringAPI(scoreType));
    const limitType = yield select(makeSelectMaxRangeLimit(scoreType));

    const { range: zoombarRange } = yield select(makeSelectZoombar(scoreType));

    yield put(Actions.trainModel.updateIsTrainingScoring(true, scoreType));

    const { data } = yield call(
      PredictiveModelService.RFMModel.buildRangeWithZoom,
      {
        viewName,
        zoombarRange,
        scoreInfo,
        limitType,
      },
    );

    if (isEmpty(data)) {
      throw new Error('Get data ranges fail');
    }

    const { success } = yield call(handleTrainModel, {
      payload: {
        scoreType,
        options: {
          recalculate: true,
          ranges: data,
        },
      },
    });

    if (success) {
      yield put(
        Actions.trainModel.updateScoring(scoreType, {
          needRecalculateBarchart: false,
        }),
      );
    }
  } catch (error) {
    addMessageToQueue({
      path: ERROR_PATH,
      func: handleRecalculateWithZoom.name,
      data: error.stack,
    });
  }
}

const NOTI = {
  error: err => ({
    id: 'error',
    message: err.message,
    translateCode: TRANSLATE_KEY._NOTIFICATION_ERROR_HAPPEN,
    timeout: 5000,
    timestamp: new Date().getTime(),
    type: 'danger',
  }),
};

// |/ |_   _. ._   _
// |\ | | (_| | | (_|
//                 _|
