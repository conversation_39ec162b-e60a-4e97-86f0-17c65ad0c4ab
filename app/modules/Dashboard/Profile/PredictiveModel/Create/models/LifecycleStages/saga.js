/* eslint-disable prefer-const */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-param-reassign */
/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
import { get, isEmpty, isEqual, omit } from 'lodash';
import {
  all,
  call,
  put,
  race,
  select,
  take,
  takeLatest,
} from 'redux-saga/effects';
import TRANSLATE_KEY from '../../../../../../../messages/constant';
import PredictiveModelService from '../../../../../../../services/PredictiveModel';
import { generateKey } from '../../../../../../../utils/common';
import { ARCHIVE_STATUS } from '../../../../../../../utils/constants';
import { addMessageToQueue } from '../../../../../../../utils/web/queue';
import { addNotification } from '../../../../../actions';
import { getListBOAttributes } from '../../../service';
import { Actions as MainActions, Types as MainTypes } from '../../actions';
import { STEP_SETTING_KEYS, TRAINING_MODE } from '../../config';
import {
  executeModelTrainingWithProgress,
  handleValidateModelSettings,
} from '../../saga';
import {
  selectActiveStep,
  selectIsAutoGenerate,
  selectIsVersionHistory,
  selectModalProgress,
  selectTrainingMode,
  selectViewName,
} from '../../selector';
import { Actions, Types } from './actions';
import {
  AUTO_GENERATE_ATTR_CONFIGS,
  DEFAULT_ITEM_TYPE_NAME,
  MAX_COLUMN_BAR_CHART,
  PREPARE_DATA_KEYS,
  SEGMENT_KEYS,
  TRAINING_CONTENT,
  TRAINING_STEP_KEYS,
} from './constant';
import {
  selectActTrainingField,
  selectActTrainingKey,
  selectBackupData,
  selectInputRangesByTrainingKey,
  selectPrepareDataAPI,
  selectPrepareDataField,
  selectRangesByTrainingKey,
  selectScoringAPI,
  selectSegmentsByKeys,
  selectSegmentsByTrainingKey,
  selectTrainingsByTrainingKey,
  selectTrainingsFieldByTrainingKey,
  selectZoombarRangeByTrainingKey,
  selectZoombarRangeDefaultByTrainingKey,
} from './selectors';
import {
  getTrainingStepsByContents,
  getZoombarRangeFromAggregation,
  getZoombarRangeFromSegments,
  serializeAggregationAPI,
  serializeRangesAPI,
  serializeTransactionStagesAPI,
  updateTrainingInfos,
} from './utils';
import { getErrorsByStep } from './validate.utils';

const PATH =
  'modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga';

const generatePatternForUpdatePrepareData = key => action =>
  action.type === Types.updatePrepareData && get(action.payload, 'key') === key;

export default function* workerLifecycleStageModelSaga() {
  yield takeLatest(
    generatePatternForUpdatePrepareData(PREPARE_DATA_KEYS.transaction),
    autoGenerateAttributes,
  );

  yield takeLatest(MainTypes.changeStepSettings, handleChangeStepSettings);

  yield takeLatest(MainTypes.resetViewName, handleResetViewName);

  yield takeLatest(Types.trainModel, handleTrainModel);

  yield takeLatest(Types.updateRange, handleUpdateRange);

  yield takeLatest(Types.updateZoombarRange, handleUpdateZoombarRange);

  yield takeLatest(Types.updateGranularity, handleUpdateGranularity);

  yield takeLatest(MainTypes.updateTrainingMode, handleUpdateTrainingMode);

  yield takeLatest(
    Types.trainModelWithCustomRange,
    handleTrainModelWithCustomRange,
  );

  yield takeLatest(
    MainTypes.initFromNotiDone,
    handleChangeStepAfterInitFromNoti,
  );
}

function* autoGenerateAttributes(action) {
  const { payload } = action;

  try {
    const { itemTypeName, itemTypeId } = payload.value;
    const isAutoGenerate = yield select(selectIsAutoGenerate);

    let clear = true;

    if (itemTypeName === DEFAULT_ITEM_TYPE_NAME && isAutoGenerate) {
      const res = yield call(getListBOAttributes, itemTypeId);

      if (res.code === 200) {
        clear = false;

        const data = res.data.filter(attr => attr.status !== ARCHIVE_STATUS);

        for (const [key, predicate] of Object.entries(
          AUTO_GENERATE_ATTR_CONFIGS,
        )) {
          const defaultAttr = data.find(predicate);

          yield put(Actions.updateTransactionConfigs(key, defaultAttr || null));
        }
      }
    }

    if (clear) {
      for (const key of Object.keys(AUTO_GENERATE_ATTR_CONFIGS)) {
        yield put(Actions.updateTransactionConfigs(key, null));
      }
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: autoGenerateAttributes.name,
      data: err.stack,
    });
  }
}

function* handleChangeStepAfterInitFromNoti() {
  const actTrainingKey = yield select(selectActTrainingKey);
  const contents = yield select(selectActTrainingField('contents'));

  try {
    yield put(
      Actions.updateTraining(actTrainingKey)(
        updateTrainingInfos(contents, true),
      ),
    );

    yield put(MainActions.updateActiveStep(STEP_SETTING_KEYS.trainModel));

    yield call(handleTrainModel);
  } catch (err) {
    yield put(addNotification(NOTI.error(err)));

    addMessageToQueue({
      path: PATH,
      func: handleChangeStepAfterInitFromNoti.name,
      data: err.stack,
    });
  } finally {
    yield put(
      Actions.updateTraining(actTrainingKey)(
        updateTrainingInfos(contents, false),
      ),
    );
  }
}

function* handleChangeStepSettings(action) {
  let hasTrainModel = false;

  let { stepIndex: navigatedStep } = action.payload;

  const activeStep = yield select(selectActiveStep);
  const isVersionHistory = yield select(selectIsVersionHistory);

  try {
    if (navigatedStep < activeStep || isVersionHistory) {
      yield put(MainActions.updateActiveStep(navigatedStep));
      return;
    }

    const errors = yield call(
      handleValidateModelSettings,
      MainActions.validateModelSettings({
        step: activeStep,
      }),
    );

    if (navigatedStep > activeStep && !isEmpty(errors)) {
      navigatedStep = activeStep;
    }

    const prepareDataInfo = yield select(selectPrepareDataAPI);

    const prevPrepareDataInfo = yield select(
      selectPrepareDataField('prevPrepareData'),
    );

    const changedPrepareData =
      !!prevPrepareDataInfo && !isEqual(prepareDataInfo, prevPrepareDataInfo);

    if (changedPrepareData) {
      yield put(MainActions.resetViewName());
    }

    const viewName = yield select(selectViewName);

    if (navigatedStep >= STEP_SETTING_KEYS.trainModel && isEmpty(viewName)) {
      hasTrainModel = true;

      const backupData = yield select(selectBackupData);

      yield call(executeModelTrainingWithProgress, {
        *sagaFunc() {
          yield put(
            MainActions.prepareData({
              prepareDataInfo,
              backupData,
            }),
          );

          const [prepareSuc, prepareFail] = yield race([
            take(MainTypes.prepareDataSuccess),
            take(MainTypes.prepareDataFail),
          ]);

          if (prepareFail) {
            navigatedStep = activeStep;

            throw new Error(
              'Failed to prepare data before navigating to the train model step',
            );
          }

          if (prepareSuc) {
            yield put(
              MainActions.setViewName(get(prepareSuc, 'payload.viewName')),
            );

            yield call(handleTrainModel);

            yield put(
              MainActions.validateModelSettings({
                selectErrors: errs =>
                  getErrorsByStep(errs, STEP_SETTING_KEYS.trainModel),
              }),
            );
          }
        },

        *onCancel() {
          navigatedStep = activeStep;
        },
      });
    } else {
      yield put(MainActions.updateActiveStep(navigatedStep));
    }
  } catch (err) {
    navigatedStep = activeStep;

    if (hasTrainModel) {
      yield put(MainActions.setViewName(null));
    }

    yield put(addNotification(NOTI.error(err)));

    addMessageToQueue({
      path: PATH,
      func: handleChangeStepSettings.name,
      data: err.stack,
    });

    // eslint-disable-next-line no-console
    console.error(err);
  } finally {
    const { isOpen } = yield select(selectModalProgress);

    if (isOpen) {
      yield put(
        MainActions.updateModalProgress({
          isOpen: false,
        }),
      );
    }

    yield put(MainActions.updateActiveStep(navigatedStep));
  }
}

function* getRangesForScoreAndPie(params) {
  const {
    trainingKey,
    scoreInfo,
    inputRanges,
    zoombarRange,
    recalculate,
    needToTrainAll,
  } = params;

  if (trainingKey === TRAINING_STEP_KEYS.CustomerStage) {
    return;
  }

  const contentKey = TRAINING_CONTENT.ScoreAndPie;

  yield put(
    Actions.updateTraining(trainingKey)([
      { path: `contents.${contentKey}.isTraining`, value: true },
    ]),
  );

  const viewName = yield select(selectViewName);

  const { data } = yield call(
    PredictiveModelService.LifecycleStages.getRanges,
    {
      viewName,
      scoreType: trainingKey,
      scoreInfo,
      ...(recalculate
        ? { zoombarRange: zoombarRange.join(',') }
        : { inputRanges }),
    },
  );

  const serializeData = serializeRangesAPI(data, trainingKey, inputRanges);

  for (const [segmentKey, range] of Object.entries(serializeData)) {
    const segmentUpdateInfos = [{ path: 'customers', value: range.customers }];

    if (trainingKey !== TRAINING_STEP_KEYS.TransactionOrder) {
      segmentUpdateInfos.push(
        { path: 'from', value: range.min },
        { path: 'to', value: range.max },
      );
    }

    yield put(Actions.updateSegment(segmentKey)(segmentUpdateInfos));
  }

  if (!needToTrainAll) {
    yield put(
      Actions.updateTraining(trainingKey)([
        { path: `contents.${contentKey}.isTraining`, value: false },
        { path: `contents.${contentKey}.trained`, value: true },
        { path: `contents.${contentKey}.needToReTraining`, value: false },
      ]),
    );
  }
}

function* getTransactionStages() {
  const trainingKey = TRAINING_STEP_KEYS.CustomerStage;

  const contentKey = TRAINING_CONTENT.TransactionStage;
  const pieContentKey = TRAINING_CONTENT.ScoreAndPie;

  yield put(
    Actions.updateTraining(trainingKey)([
      { path: `contents.${contentKey}.isTraining`, value: true },
      { path: `contents.${pieContentKey}.isTraining`, value: true },
    ]),
  );

  const viewName = yield select(selectViewName);
  const segments = yield select(selectSegmentsByKeys);
  const repeatBuyer = segments[SEGMENT_KEYS.RepeatBuyer];
  const regainedBuyer = segments[SEGMENT_KEYS.RegainedBuyer];

  const recencyTraining = yield select(
    selectTrainingsByTrainingKey(TRAINING_STEP_KEYS.Recency),
  );
  const recencyRanges = yield select(
    selectRangesByTrainingKey(TRAINING_STEP_KEYS.Recency),
  );
  const recencyGranularity = get(recencyTraining, 'granularity.value');
  const recencyUpperBound = recencyRanges[0];

  const { data } = yield call(
    PredictiveModelService.LifecycleStages.getTransactionStage,
    {
      viewName,
      retentionFilters: {
        repeat: { min: String(repeatBuyer.from), max: String(repeatBuyer.to) },
        regain: {
          min: String(regainedBuyer.from + 1),
          max: String(regainedBuyer.to),
        },
      },
      recencyUpperBound: {
        granularity: recencyGranularity,
        value: String(recencyUpperBound),
      },
    },
  );

  const serializeData = serializeTransactionStagesAPI(data);

  yield put(Actions.updateTransactionStageData(serializeData));

  yield put(
    Actions.updateTraining(trainingKey)([
      { path: `contents.${contentKey}.isTraining`, value: false },
      { path: `contents.${contentKey}.trained`, value: true },
      { path: `contents.${contentKey}.needToReTraining`, value: false },

      { path: `contents.${pieContentKey}.isTraining`, value: false },
      { path: `contents.${pieContentKey}.trained`, value: true },
      { path: `contents.${pieContentKey}.needToReTraining`, value: false },
    ]),
  );
}

function* getBarchartAggregation(params) {
  const { trainingKey, zoombarRange, scoreInfo, needToTrainAll } = params;

  const contentKey = TRAINING_CONTENT.Barchart;
  const viewName = yield select(selectViewName);

  yield put(
    Actions.updateTraining(trainingKey)({
      path: `contents.${contentKey}.isTraining`,
      value: true,
    }),
  );

  const { data } = yield call(
    PredictiveModelService.LifecycleStages.getAggregation,
    {
      viewName,
      scoreType: trainingKey,
      maxColumn: MAX_COLUMN_BAR_CHART,
      zoombarRange: zoombarRange.join(','),
      scoreInfo,
    },
  );

  const zoombarRangeDefault = yield select(
    selectZoombarRangeDefaultByTrainingKey(trainingKey),
  );

  const serializeData = serializeAggregationAPI({
    data,
    trainingKey,
    zoombarRangeDefault,
  });

  yield put(
    Actions.updateTraining(trainingKey)([
      { path: 'chartKey', value: generateKey() },
      { path: 'aggregation', value: serializeData },
    ]),
  );

  if (!needToTrainAll) {
    yield put(
      Actions.updateTraining(trainingKey)([
        { path: `contents.${contentKey}.isTraining`, value: false },
        { path: `contents.${contentKey}.trained`, value: true },
        { path: `contents.${contentKey}.needToReTraining`, value: false },
      ]),
    );
  }
}

function* handleTrainModelWithCustomRange(action) {
  const { scoreType } = action.payload;
  const { Barchart, ScoreAndPie, TransactionStage } = TRAINING_CONTENT;

  if (scoreType === Barchart) {
    yield call(getBarchartAggregationWithCustomRange);
  }

  if (scoreType === ScoreAndPie) {
    yield call(getRangesForScoreAndPieWithCustomRange);
  }

  if (scoreType === TransactionStage) {
    yield call(getTransactionStagesWithCustomRange);
  }
}

function* getBarchartAggregationWithCustomRange() {
  try {
    const actTrainingKey = yield select(selectActTrainingKey);
    const contentKey = TRAINING_CONTENT.Barchart;

    yield put(
      Actions.updateTraining(actTrainingKey)({
        path: `contents.${contentKey}.isTraining`,
        value: true,
      }),
    );

    const granularityChanged = yield select(
      selectActTrainingField('granularity.changed'),
    );
    const contents = yield select(selectActTrainingField('contents'));
    const scoreInfo = yield select(selectScoringAPI(actTrainingKey));
    const needToTrainAll = Object.values(contents).every(
      content => content.needToReTraining,
    );

    if (granularityChanged) {
      yield call(getRangesForScoreAndPie, {
        trainingKey: actTrainingKey,
        scoreInfo,
        inputRanges: undefined,
      });
      if (actTrainingKey !== TRAINING_STEP_KEYS.TransactionOrder) {
        yield call(getZoombarDefaultFromSegments, {
          trainingKey: actTrainingKey,
          updateZoombarRange: true,
        });
      }

      yield call(getBarchartAggregation, {
        trainingKey: actTrainingKey,
        scoreInfo,
        zoombarRange: [],
      });
      if (actTrainingKey === TRAINING_STEP_KEYS.TransactionOrder) {
        yield call(getZoombarDefaultFromAggregation, {
          trainingKey: actTrainingKey,
          updateZoombarRange: true,
        });
      }

      yield put(
        Actions.updateTraining(actTrainingKey)([
          { path: 'granularity.changed', value: false },
        ]),
      );
    } else {
      const zoombarRange = yield select(
        selectZoombarRangeByTrainingKey(actTrainingKey),
      );

      yield call(getRangesForScoreAndPie, {
        trainingKey: actTrainingKey,
        scoreInfo,
        zoombarRange,
        recalculate: true,
        needToTrainAll,
      });

      yield call(getBarchartAggregation, {
        trainingKey: actTrainingKey,
        scoreInfo,
        zoombarRange,
        needToTrainAll,
      });

      if (needToTrainAll) {
        yield put(
          Actions.updateTraining(actTrainingKey)(
            Object.keys(contents)
              .map(key => [
                { path: `contents.${key}.isTraining`, value: false },
                { path: `contents.${key}.trained`, value: true },
                { path: `contents.${key}.needToReTraining`, value: false },
              ])
              .flat(),
          ),
        );
      }
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: getBarchartAggregationWithCustomRange.name,
      data: err.stack,
    });
  }
}

function* getRangesForScoreAndPieWithCustomRange() {
  try {
    const actTrainingKey = yield select(selectActTrainingKey);

    const scoreInfo = yield select(selectScoringAPI(actTrainingKey));
    const inputRanges = yield select(
      selectInputRangesByTrainingKey(actTrainingKey),
    );

    yield call(getRangesForScoreAndPie, {
      trainingKey: actTrainingKey,
      scoreInfo,
      inputRanges,
    });
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: getRangesForScoreAndPieWithCustomRange.name,
      data: err.stack,
    });
  }
}

function* getTransactionStagesWithCustomRange() {
  try {
    const actTrainingKey = yield select(selectActTrainingKey);
    const pieContentKey = TRAINING_CONTENT.ScoreAndPie;

    yield put(
      Actions.updateTraining(actTrainingKey)({
        path: `contents.${pieContentKey}.isTraining`,
        value: true,
      }),
    );

    yield call(getTransactionStages);

    yield put(
      Actions.updateTraining(actTrainingKey)([
        { path: `contents.${pieContentKey}.isTraining`, value: false },
        { path: `contents.${pieContentKey}.trained`, value: true },
        { path: `contents.${pieContentKey}.needToReTraining`, value: false },
      ]),
    );
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: getRangesForScoreAndPieWithCustomRange.name,
      data: err.stack,
    });
  }
}

function* handleUpdateTrainingMode(action) {
  try {
    const { mode } = action.payload;

    if (mode === TRAINING_MODE.AI) {
      yield put(Actions.resetTrainingData());

      const trainingKey = TRAINING_STEP_KEYS.CustomerStage;
      const contents = yield select(
        selectTrainingsFieldByTrainingKey(trainingKey, 'contents'),
      );

      const updateInfos = Object.keys(contents)
        .map(key => [
          { path: `contents.${key}.needToReTraining`, value: true },
          { path: `contents.${key}.isTraining`, value: true },
        ])
        .flat();

      yield put(Actions.updateTraining(trainingKey)(updateInfos));

      yield call(handleTrainModel);
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleUpdateTrainingMode.name,
      data: err.stack,
    });
  }
}

function* handleTrainModel() {
  try {
    const trainingMode = yield select(selectTrainingMode);
    const isExpertMode = trainingMode === TRAINING_MODE.Expert;

    /* Training Score and Pie */
    let stepsNeedRanges = getTrainingStepsByContents([
      TRAINING_CONTENT.ScoreAndPie,
    ]);

    stepsNeedRanges = omit(stepsNeedRanges, [TRAINING_STEP_KEYS.CustomerStage]);

    // Need to call in order
    for (const trainingKey of Object.keys(stepsNeedRanges)) {
      const scoreInfo = yield select(selectScoringAPI(trainingKey));

      const rangesParams = {
        trainingKey,
        scoreInfo,
      };

      if (isExpertMode) {
        rangesParams.inputRanges = yield select(
          selectInputRangesByTrainingKey(trainingKey),
        );
      }

      yield call(getRangesForScoreAndPie, rangesParams);

      if (trainingKey !== TRAINING_STEP_KEYS.TransactionOrder) {
        yield call(getZoombarDefaultFromSegments, {
          trainingKey,

          ...(isExpertMode && {
            updateZoombarRange: false,
          }),
        });
      }
    }

    /* Training Barchart */
    const stepsNeedAggregation = getTrainingStepsByContents([
      TRAINING_CONTENT.Barchart,
    ]);

    yield all(
      Object.keys(stepsNeedAggregation).map(trainingKey =>
        call(handleGetBarchartAggregation, {
          trainingKey,
          ...(isExpertMode && { updateZoombarRange: false }),
        }),
      ),
    );

    /* Training Transaction Stages */
    yield call(getTransactionStages);

    /* Cache previous prepare data */
    const prepareDataInfo = yield select(selectPrepareDataAPI);

    yield put(
      Actions.updatePrepareData(
        'prevPrepareData',
        omit(prepareDataInfo, 'prevPrepareData'),
      ),
    );
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleTrainModel.name,
      data: err.stack,
    });
  }
}

function* handleGetBarchartAggregation(params) {
  try {
    const { trainingKey, updateZoombarRange = true } = params;

    const scoreInfo = yield select(selectScoringAPI(trainingKey));
    const zoombarDefault = yield select(
      selectZoombarRangeDefaultByTrainingKey(trainingKey),
    );

    yield call(getBarchartAggregation, {
      trainingKey,
      scoreInfo,
      zoombarRange: zoombarDefault,
    });

    if (trainingKey === TRAINING_STEP_KEYS.TransactionOrder) {
      yield call(getZoombarDefaultFromAggregation, {
        trainingKey,
        updateZoombarRange,
      });
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleGetBarchartAggregation.name,
      data: err.stack,
    });
  }
}

function* getZoombarDefaultFromSegments(params) {
  try {
    const { trainingKey, updateZoombarRange = true } = params;

    const segments = yield select(selectSegmentsByTrainingKey(trainingKey));
    const zoombarDefault = getZoombarRangeFromSegments(segments);

    if (zoombarDefault && zoombarDefault.length) {
      const zoombarRange = [...zoombarDefault];

      if (trainingKey === TRAINING_STEP_KEYS.Recency) {
        zoombarDefault[0] = 0;
      }

      const updateInfos = [{ path: 'zoombar.default', value: zoombarDefault }];

      if (updateZoombarRange) {
        updateInfos.push({ path: 'zoombar.range', value: zoombarRange });
      }

      yield put(Actions.updateTraining(trainingKey)(updateInfos));
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: getZoombarDefaultFromSegments.name,
      data: err.stack,
    });
  }
}

function* getZoombarDefaultFromAggregation(params) {
  try {
    const { trainingKey, updateZoombarRange = true } = params;

    const aggregation = yield select(
      selectTrainingsFieldByTrainingKey(trainingKey, 'aggregation'),
    );
    const zoombarDefault = getZoombarRangeFromAggregation(aggregation);

    if (zoombarDefault && zoombarDefault.length) {
      const updateInfos = [{ path: 'zoombar.default', value: zoombarDefault }];

      if (updateZoombarRange) {
        updateInfos.push({ path: 'zoombar.range', value: zoombarDefault });
      }

      yield put(Actions.updateTraining(trainingKey)(updateInfos));
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: getZoombarDefaultFromAggregation.name,
      data: err.stack,
    });
  }
}

function* handleUpdateRange(action) {
  try {
    const { range } = action.payload;

    const contentKey = TRAINING_CONTENT.ScoreAndPie;
    const actTrainingKey = yield select(selectActTrainingKey);

    yield put(
      Actions.updateTraining(actTrainingKey)([
        { path: `contents.${contentKey}.needToReTraining`, value: true },
      ]),
    );

    yield call(handleEffectsOnRangeChange, {
      trainingKey: actTrainingKey,
      range,
    });
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleUpdateRange.name,
      data: err,
    });
  }
}

function* handleUpdateZoombarRange(action) {
  try {
    const { min, max, range } = action.payload;
    const { Barchart, ScoreAndPie } = TRAINING_CONTENT;

    const actTrainingKey = yield select(selectActTrainingKey);

    yield put(
      Actions.updateTraining(actTrainingKey)([
        { path: `contents.${Barchart}.needToReTraining`, value: true },
        { path: `contents.${ScoreAndPie}.needToReTraining`, value: false },
        { path: 'zoombar.range', value: [min, max] },
      ]),
    );

    yield call(handleEffectsOnRangeChange, {
      trainingKey: actTrainingKey,
      range,
    });
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleUpdateZoombarRange.name,
      data: err,
    });
  }
}

function* handleEffectsOnRangeChange(params) {
  try {
    const { trainingKey, range } = params;

    yield call(updateSegmentsOnRangeChange, { trainingKey, range });
    yield call(updateLoadingsOnRangeChange, { trainingKey });
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleEffectsOnRangeChange.name,
      data: err,
    });
  }
}

function* updateSegmentsOnRangeChange(params) {
  try {
    const { trainingKey, range } = params;

    if (trainingKey === TRAINING_STEP_KEYS.TransactionOrder) {
      return;
    }

    const segments = yield select(selectSegmentsByTrainingKey(trainingKey));

    for (const [index, segment] of segments.entries()) {
      const from = range[index];
      const to = range[index + 1];

      yield put(
        Actions.updateSegment(segment.key)([
          { path: 'from', value: from },
          { path: 'to', value: to },
        ]),
      );
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: updateSegmentsOnRangeChange.name,
      data: err,
    });
  }
}

function* updateLoadingsOnRangeChange(params) {
  try {
    const { trainingKey } = params;

    const stepsNeedToLoading = [];

    if (trainingKey === TRAINING_STEP_KEYS.Recency) {
      stepsNeedToLoading.push(
        TRAINING_STEP_KEYS.TransactionOrder,
        TRAINING_STEP_KEYS.RetentionDuration,
      );
    }

    if (trainingKey !== TRAINING_STEP_KEYS.TransactionOrder) {
      stepsNeedToLoading.push(TRAINING_STEP_KEYS.CustomerStage);
    }

    for (const step of stepsNeedToLoading) {
      const contents = yield select(
        selectTrainingsFieldByTrainingKey(step, 'contents'),
      );

      yield put(
        Actions.updateTraining(step)(
          Object.keys(contents).map(key => ({
            path: `contents.${key}.needToReTraining`,
            value: true,
          })),
        ),
      );
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: updateLoadingsOnRangeChange.name,
      data: err,
    });
  }
}

function* handleUpdateGranularity(action) {
  try {
    const { value } = action.payload;
    const { Barchart, ScoreAndPie } = TRAINING_CONTENT;

    const actTrainingKey = yield select(selectActTrainingKey);

    yield put(
      Actions.updateTraining(actTrainingKey)([
        { path: 'granularity', value },
        { path: 'granularity.changed', value: true },
        { path: `contents.${Barchart}.needToReTraining`, value: true },
        { path: `contents.${ScoreAndPie}.needToReTraining`, value: false },
      ]),
    );
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleUpdateGranularity.name,
      data: err,
    });
  }
}

function* handleResetViewName() {
  try {
    yield put(Actions.resetTrainingData());
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleResetViewName.name,
      data: err,
    });
  }
}

const NOTI = {
  error: err => ({
    id: 'error',
    message: err.message,
    translateCode: TRANSLATE_KEY._,
    timeout: 5000,
    timestamp: new Date().getTime(),
    type: 'danger',
  }),
};
