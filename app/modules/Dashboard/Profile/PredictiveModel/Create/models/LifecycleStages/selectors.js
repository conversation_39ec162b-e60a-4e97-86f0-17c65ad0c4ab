/* eslint-disable prefer-const */
/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
import { createSelector } from 'reselect';
import {
  selectDomain,
  selectIsAutoGenerate,
  selectIsInitDone,
  selectPrepareData,
  selectSavedData,
  selectTrainingMode,
} from '../../selector';
import { get } from 'lodash';
import { TRAINING_CONTENT, TRAINING_STEP_KEYS } from './constant';
import {
  getSegmentsToCurrentStep,
  segmentsToScore,
  segmentsToInputRanges,
  toAPIPrepareData,
  toBarchart,
  toPiechart,
  toVennDiagram,
  transactionStagesToScores,
  getRangesFromSegments,
  validateInputRanges,
  toAPIData,
} from './utils';
import { TRAINING_MODE } from '../../config';
import { equalityCheck } from '../../utils';
import { omitDeep } from '../../../../../../../utils/common';

const resultEqualityCheck = equalityCheck;

/* PREPARE DATA SELECTOR */
const selectIsAutoGenerateDataTransaction = createSelector(
  selectIsAutoGenerate,
  selectIsInitDone,
  (isAutoGenerate, prepareDataInitDone) =>
    isAutoGenerate && prepareDataInitDone,
);

const selectPrepareDataField = fieldPath =>
  createSelector(
    selectPrepareData,
    prepareData => get(prepareData, fieldPath),
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

export const selectBackupData = createSelector(
  selectSavedData({ serializeSettings: toAPIData }),
  savedData => omitDeep(savedData, []),
);

const selectPrepareDataAPI = createSelector(
  selectPrepareData,
  prepareData => toAPIPrepareData(prepareData),
);

export {
  selectPrepareDataField,
  selectIsAutoGenerateDataTransaction,
  selectPrepareDataAPI,
};

/* TRAINING MODEL SELECTOR */
const selectTrainModel = state => selectDomain(state).settings.trainModel;

const selectActTrainingKey = state => selectTrainModel(state).active;

const selectTrainings = state => selectTrainModel(state).trainings;

const selectTrainingsByKeys = state => selectTrainings(state).byKeys;

const selectTrainingAllKeys = state => selectTrainings(state).allKeys;

const selectSegments = state => selectTrainModel(state).segments;

const selectSegmentsByKeys = state => selectSegments(state).byKeys;

const selectSegmentsAllKeys = state => selectSegments(state).allKeys;

const selectTransactionStages = state =>
  selectTrainModel(state).transactionStages;

const selectTransactionStagesByKeys = state =>
  selectTransactionStages(state).byKeys;

const selectTransactionStagesAllKeys = state =>
  selectTransactionStages(state).allKeys;

const selectTransactionStagesData = state =>
  selectTransactionStages(state).data;

const selectTransactionStagesVennDiagramKey = state =>
  selectTransactionStages(state).vennDiagramKey;

const selectActTrainingIndex = createSelector(
  selectActTrainingKey,
  state => selectTrainings(state).allKeys,
  (activeKey, allKeys) => allKeys.indexOf(activeKey),
);

const selectTrainingsByTrainingKey = trainingKey =>
  createSelector(
    selectTrainingsByKeys,
    byKeys => byKeys[trainingKey],
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

const selectTrainingsFieldByTrainingKey = (trainingKey, fieldPath) =>
  createSelector(
    selectTrainingsByTrainingKey(trainingKey),
    trainings => get(trainings, fieldPath),
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

const selectTransaciontStageScores = createSelector(
  selectTransactionStagesByKeys,
  selectTransactionStagesAllKeys,
  selectTransactionStagesData,
  (byKeys, allKeys, data) =>
    transactionStagesToScores({
      transactionByKeys: byKeys,
      transactionAllKeys: allKeys,
      transactionData: data,
    }),
);

const selectSegmentsByTrainingKey = trainingKey =>
  createSelector(
    selectSegmentsByKeys,
    selectSegmentsAllKeys,
    (segments, allKeys) =>
      allKeys
        .filter(
          segmentKey =>
            trainingKey === TRAINING_STEP_KEYS.CustomerStage ||
            segments[segmentKey].trainingKey === trainingKey,
        )
        .map(segmentKey => segments[segmentKey]),
  );

const selectZoombarRangeByTrainingKey = trainingKey =>
  createSelector(
    selectTrainingsFieldByTrainingKey(trainingKey, 'zoombar.range'),
    zoombarRange => zoombarRange,
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

const selectZoombarRangeDefaultByTrainingKey = trainingKey =>
  createSelector(
    selectTrainingsFieldByTrainingKey(trainingKey, 'zoombar.default'),
    zoombarDefault => zoombarDefault,
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

const selectSegmentScores = createSelector(
  selectActTrainingIndex,
  selectActTrainingKey,
  selectTrainingAllKeys,
  selectSegmentsByKeys,
  (actTrainingIndex, actTrainingKey, trainingAllKeys, segmentByKeys) => {
    const segments = getSegmentsToCurrentStep({
      actTrainingIndex,
      trainingAllKeys,
      segmentByKeys,
    });

    return segmentsToScore(segments, actTrainingKey);
  },
  {
    memoizeOptions: {
      resultEqualityCheck,
    },
  },
);

const selectActTraining = createSelector(
  selectActTrainingKey,
  selectTrainings,
  (activeKey, trainings) => trainings.byKeys[activeKey],
);

const selectTrainingTabsInfo = createSelector(
  selectTrainingMode,
  selectTrainingsByKeys,
  selectTrainingAllKeys,
  (mode, byKeys, allKeys) =>
    allKeys
      .map((trainingKey, index) => ({
        key: index,
        trainingKey,
        label: byKeys[trainingKey].label,
      }))
      .filter(
        ({ trainingKey }) =>
          mode !== TRAINING_MODE.AI ||
          trainingKey === TRAINING_STEP_KEYS.CustomerStage,
      ),
  {
    memoizeOptions: {
      resultEqualityCheck,
    },
  },
);

const selectActTrainingField = fieldPath =>
  createSelector(
    selectActTraining,
    actTraining => get(actTraining, fieldPath),
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

const selectChartContents = chartType =>
  createSelector(
    selectActTrainingField('contents'),
    contents => get(contents, `${chartType}`, {}),
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

const selectContentsNeedToRetraining = createSelector(
  selectActTrainingField('contents'),
  contents => {
    const contentKey = [
      TRAINING_CONTENT.TransactionStage,
      TRAINING_CONTENT.Barchart,
      TRAINING_CONTENT.ScoreAndPie,
    ].find(content => get(contents, `[${content}].needToReTraining`));

    return { contentKey, trainingContents: contents[contentKey] };
  },
  {
    memoizeOptions: {
      resultEqualityCheck,
    },
  },
);

const selectTrainingBarchart = trainingKey =>
  createSelector(
    selectActTrainingKey,
    selectSegmentsByTrainingKey(trainingKey),
    selectActTrainingField('aggregation'),
    selectActTrainingField('metrics'),
    selectActTrainingField('zoombar'),
    (actTrainingKey, segments, aggregation, metrics, zoombar) =>
      toBarchart({
        trainingKey: actTrainingKey,
        segments,
        aggregation,
        metrics,
        zoombar,
      }),
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

const selectTrainingPiechart = createSelector(
  selectActTrainingKey,
  selectSegmentsByKeys,
  (actTrainingKey, segments) =>
    toPiechart({ trainingKey: actTrainingKey, segments }),
  {
    memoizeOptions: {
      resultEqualityCheck,
    },
  },
);

const selectTransactionStagesVennDiagram = createSelector(
  selectTransactionStagesData,
  selectTransactionStagesByKeys,
  (data, byKeys) =>
    toVennDiagram({
      dataSets: data,
      byKeys,
    }),
  {
    memoizeOptions: {
      resultEqualityCheck,
    },
  },
);

const selectRangesByTrainingKey = trainingKey =>
  createSelector(
    selectSegmentsByTrainingKey(trainingKey),
    segments => getRangesFromSegments(segments),
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

const selectInputRangesByTrainingKey = trainingKey =>
  createSelector(
    selectSegmentsByTrainingKey(trainingKey),
    segments => segmentsToInputRanges(segments),
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

const selectIsVisualized = trainingKey =>
  createSelector(
    selectInputRangesByTrainingKey(trainingKey),
    inputRanges => validateInputRanges(inputRanges),
  );

const selectScoringAPI = trainingKey =>
  createSelector(
    selectTrainingsByTrainingKey(trainingKey),
    selectTrainingsByTrainingKey(TRAINING_STEP_KEYS.Recency),
    selectRangesByTrainingKey(TRAINING_STEP_KEYS.Recency),
    (training, recencyTraining, recencyRanges) => {
      let scoringAPI = {};

      switch (trainingKey) {
        case TRAINING_STEP_KEYS.Engagement:
        case TRAINING_STEP_KEYS.Recency: {
          const granularity = get(training, 'granularity.value');
          scoringAPI = { granularity };
          break;
        }
        case TRAINING_STEP_KEYS.TransactionOrder:
        case TRAINING_STEP_KEYS.RetentionDuration: {
          const granularity =
            trainingKey === TRAINING_STEP_KEYS.TransactionOrder
              ? 'day'
              : get(training, 'granularity.value');
          const recencyGranularity = get(recencyTraining, 'granularity.value');
          const recencyUpperBound = recencyRanges[0];

          scoringAPI = {
            granularity,
            recency_upper_bound: {
              value: String(recencyUpperBound),
              granularity: recencyGranularity,
            },
          };
          break;
        }
        default:
      }

      return scoringAPI;
    },
    {
      memoizeOptions: {
        resultEqualityCheck,
      },
    },
  );

export {
  selectActTraining,
  selectActTrainingField,
  selectActTrainingIndex,
  selectActTrainingKey,
  selectZoombarRangeDefaultByTrainingKey,
  selectInputRangesByTrainingKey,
  selectScoringAPI,
  selectSegmentScores,
  selectSegments,
  selectSegmentsAllKeys,
  selectSegmentsByKeys,
  selectSegmentsByTrainingKey,
  selectTrainModel,
  selectTrainingAllKeys,
  selectTrainingBarchart,
  selectTrainingPiechart,
  selectTrainingTabsInfo,
  selectTrainingsByKeys,
  selectTrainingsByTrainingKey,
  selectTrainingsFieldByTrainingKey,
  selectTransaciontStageScores,
  selectTransactionStages,
  selectTransactionStagesAllKeys,
  selectTransactionStagesByKeys,
  selectTransactionStagesVennDiagram,
  selectZoombarRangeByTrainingKey,
  selectChartContents,
  selectRangesByTrainingKey,
  selectContentsNeedToRetraining,
  selectTransactionStagesVennDiagramKey,
  selectIsVisualized,
};
