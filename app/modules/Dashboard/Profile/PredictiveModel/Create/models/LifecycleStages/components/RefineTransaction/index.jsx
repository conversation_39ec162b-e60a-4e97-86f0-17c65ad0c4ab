import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectPrepareDataField } from '../../selectors';
import { Actions } from '../../actions';
import { selectErrorsByPath } from '../../../../selector';
import { getErrorsByPath } from '../../../../utils';
import { get } from 'lodash';
import { PREPARE_DATA_KEYS } from '../../constant';
import { useLazyLoadComponent } from 'hooks';

const Filters = useLazyLoadComponent(() =>
  import('PredictiveModel/Create/components/Filters'),
);

const transationKey = PREPARE_DATA_KEYS.transaction;
const refineKey = PREPARE_DATA_KEYS.transactionRefine;

const RefineTranaction = () => {
  const dispatch = useDispatch();

  const dataTransaction = useSelector(selectPrepareDataField(transationKey));
  const refines = useSelector(selectPrepareDataField(refineKey));
  const errors = useSelector(selectErrorsByPath([refineKey]));

  useEffect(() => {
    let bindedRefineWithErrors = refines;

    refines.forEach((_, key) => {
      const errorsByKey = getErrorsByPath(errors, [key]);

      bindedRefineWithErrors = bindedRefineWithErrors.setIn(
        [key, 'error'],
        get(errorsByKey, '0.params.errorNumber', null),
      );
    });

    handleOnChange(bindedRefineWithErrors);
  }, [errors, refines, handleOnChange]);

  const handleOnChange = useCallback(
    filters => {
      dispatch(Actions.updatePrepareData(refineKey, filters));
    },
    [dispatch],
  );

  if (!dataTransaction) {
    return null;
  }

  return (
    <Filters
      objectProperty={dataTransaction}
      onChange={handleOnChange}
      rules={refines}
    />
  );
};

export default RefineTranaction;
