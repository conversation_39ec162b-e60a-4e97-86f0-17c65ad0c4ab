/* eslint-disable indent */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import { isEmpty } from 'lodash';
import { Types } from '../actions';
import { Types as MainTypes } from '../../../actions';
import { OrderedMap } from 'immutable';
import { PREPARE_DATA_KEYS } from '../constant';
import { DEFAULT_EVENT_TIME_RANGE } from '../../../../config';
import { ALL_TIME } from 'components/Organisms/AddvanceTimeRange';
import { serializeDataConditionAPI } from '../utils';

export const initialState = {
  initialized: false,

  [PREPARE_DATA_KEYS.transaction]: null,
  [PREPARE_DATA_KEYS.transactionRefine]: OrderedMap({}),
  [PREPARE_DATA_KEYS.transactionConfigs]: { timeRange: ALL_TIME },
  [PREPARE_DATA_KEYS.customerRegister]: null,
  [PREPARE_DATA_KEYS.events]: OrderedMap({}),
  [PREPARE_DATA_KEYS.eventTimeRange]: DEFAULT_EVENT_TIME_RANGE,
};

const prepareDataReducerFor = () => {
  const reducer = (state = initialState, action) =>
    produce(state, draft => {
      const { type = '', payload } = action;

      switch (type) {
        case MainTypes.initDone: {
          const { activeRow } = payload;

          if (!isEmpty(activeRow)) {
            const { dataCondition } = activeRow;

            const prepareData = serializeDataConditionAPI(dataCondition);

            Object.entries(prepareData).forEach(([key, value]) => {
              draft[key] = value;
            });
          }

          draft.initialized = true;

          return draft;
        }

        case MainTypes.reset: {
          return initialState;
        }

        case Types.updateTransactionConfigs: {
          const { key, value } = payload;

          draft.transactionConfigs[key] = value;
          break;
        }

        case Types.updatePrepareData: {
          const { key, value } = payload;

          draft[key] = value;

          if (key === PREPARE_DATA_KEYS.transaction) {
            draft[PREPARE_DATA_KEYS.transactionRefine] = OrderedMap({});
          }
          break;
        }

        default:
          break;
      }

      return draft;
    });

  return reducer;
};

export default prepareDataReducerFor;
