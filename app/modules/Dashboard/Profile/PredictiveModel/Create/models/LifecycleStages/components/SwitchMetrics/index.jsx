import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectActTrainingField } from '../../selectors';
import { isEmpty } from 'lodash';
import { Actions } from '../../actions';
import { useLazyLoadComponent } from 'hooks';

const UISwitchGroup = useLazyLoadComponent(() =>
  import('components/common/UISwitchGroup'),
);

const SwitchMetrics = () => {
  const dispatch = useDispatch();

  const metrics = useSelector(selectActTrainingField('metrics'));

  const disabled = useMemo(() => {
    if (!isEmpty(metrics)) {
      const curDisabled = metrics.filter(({ show }) => !show).length;
      const maxDisabled = metrics.length - 1;

      return curDisabled >= maxDisabled;
    }

    return false;
  }, [metrics]);

  const toggleMetric = (_, index, e) => {
    dispatch(
      Actions.updateActTraining({
        path: `metrics.${index}.show`,
        value: e.target.checked,
      }),
    );
  };

  if (isEmpty(metrics)) return null;

  return (
    <UISwitchGroup
      formGroupProps={{ row: true }}
      options={metrics.map(metric => ({
        ...metric,
        checked: metric.show,
        disabled: disabled && metric.show,
      }))}
      onChange={toggleMetric}
    />
  );
};

export default SwitchMetrics;
