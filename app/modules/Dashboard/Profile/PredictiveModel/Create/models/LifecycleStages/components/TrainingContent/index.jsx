import React, { useCallback } from 'react';
import { isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import cls from 'classnames';

// Selectors
import {
  selectActTrainingField,
  selectActTrainingKey,
  selectIsVisualized,
} from '../../selectors';
import {
  selectComputeStatus,
  selectIsVersionHistory,
} from '../../../../selector';

// Constant
import { TRAINING_CONTENT } from '../../constant';
import { PROCESS_COMPUTE_STATUS } from '../../../../../../../../../utils/constants';

// Hooks
import { useLazyLoadComponent } from '../../../../../../../../../hooks';

// Styled
import { StepTitle, WrapperDisable } from '../../../../components/styled';
import {
  BlockContent,
  TrainingContentContainer,
  WrapperSection,
} from './styled';

// Utils
import { TrainingMode } from '../../../../components';
import TransactionStage from '../TransactionStage';
import WarningVisualized from 'components/Molecules/WarningVisualized';

// Lazy
const Barchart = useLazyLoadComponent(() => import('../Barchart'));
const Overlays = useLazyLoadComponent(() => import('../Overlays'));
const Score = useLazyLoadComponent(() => import('../Score'));
const OverideDate = useLazyLoadComponent(() => import('../OverideDate'));
const PieChart = useLazyLoadComponent(() => import('../PieChart'));
const SwitchMetrics = useLazyLoadComponent(() => import('../SwitchMetrics'));

const TrainingContent = props => {
  const { label } = props;

  const actTrainingKey = useSelector(selectActTrainingKey);
  const trainingContents = useSelector(selectActTrainingField('contents'));
  const isValidRanges = useSelector(selectIsVisualized(actTrainingKey));
  const computeStatus = useSelector(selectComputeStatus);
  const isVersionHistory = useSelector(selectIsVersionHistory);

  const isCustomStage = !!trainingContents[TRAINING_CONTENT.TransactionStage];

  const barChartInfo = trainingContents[TRAINING_CONTENT.Barchart];
  const scoreAndPieInfo = trainingContents[TRAINING_CONTENT.ScoreAndPie];

  const renderBarchartContent = useCallback(() => {
    if (isEmpty(barChartInfo) || !barChartInfo.trained) {
      return null;
    }

    return (
      <BlockContent id={`training-content-${TRAINING_CONTENT.Barchart}`}>
        <Barchart>
          <OverideDate field="granularity" label="Granularity" />

          <SwitchMetrics />
        </Barchart>
      </BlockContent>
    );
  }, [barChartInfo]);

  const renderScoreAndPieContent = useCallback(() => {
    if (isEmpty(scoreAndPieInfo) || !scoreAndPieInfo.trained) return null;

    return (
      <BlockContent id={`training-content-${TRAINING_CONTENT.ScoreAndPie}`}>
        <Score hiddenLabel={isCustomStage} />

        <PieChart />

        {isCustomStage && <TrainingMode />}
      </BlockContent>
    );
  }, [scoreAndPieInfo, isCustomStage]);

  const renderWarningVisualized = useCallback(
    () => (
      <BlockContent>
        <WarningVisualized />

        {isCustomStage && <TrainingMode />}
      </BlockContent>
    ),
    [isCustomStage],
  );

  const disabled =
    isVersionHistory || computeStatus === PROCESS_COMPUTE_STATUS.COMPUTING;

  if (!actTrainingKey) return null;

  return (
    <>
      <TrainingContentContainer className={cls(`training-${actTrainingKey}`)}>
        <StepTitle>{`${label}`}</StepTitle>

        <WrapperSection>
          <WrapperDisable disabled={disabled}>
            {!isValidRanges ? (
              renderWarningVisualized()
            ) : (
              <>
                {renderBarchartContent()}
                {renderScoreAndPieContent()}
              </>
            )}

            {isCustomStage && <TransactionStage />}
          </WrapperDisable>
        </WrapperSection>
      </TrainingContentContainer>

      <Overlays />
    </>
  );
};

TrainingContent.propTypes = {
  label: PropTypes.string,
};

export default TrainingContent;
