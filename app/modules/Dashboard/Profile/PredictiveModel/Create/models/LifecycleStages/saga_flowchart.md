# Lifecycle Stages Saga Flow Chart

```mermaid
flowchart TD
    %% Entry Points / Component Triggers
    START([Component Triggers]) --> PREPARE_DATA[updatePrepareData<br/>transaction key]
    START --> STEP_CHANGE[changeStepSettings]
    START --> RESET[resetViewName]
    START --> TRAIN[trainModel]
    START --> UPDATE_RANGE[updateRange]
    START --> UPDATE_ZOOM[updateZoombarRange]
    START --> UPDATE_GRAN[updateGranularity]
    START --> TRAIN_MODE[updateTrainingMode]
    START --> TRAIN_CUSTOM[trainModelWithCustomRange]
    START --> INIT_NOTI[initFromNotiDone]

    %% Main Saga Handlers
    PREPARE_DATA --> AUTO_GEN[autoGenerateAttributes]
    STEP_CHANGE --> HANDLE_STEP[handleChangeStepSettings]
    RESET --> HAND<PERSON>_RESET[handleResetViewName]
    TRAIN --> HAN<PERSON><PERSON>_TRAIN[handleTrainModel]
    UPDATE_RANGE --> HAND<PERSON>_RANGE[handleUpdateRange]
    UPDATE_ZOOM --> HANDLE_ZOOM[handleUpdateZoombarRange]
    UPDATE_GRAN --> HANDLE_GRAN[handleUpdateGranularity]
    TRAIN_MODE --> HANDLE_MODE[handleUpdateTrainingMode]
    TRAIN_CUSTOM --> HANDLE_CUSTOM[handleTrainModelWithCustomRange]
    INIT_NOTI --> HANDLE_NOTI[handleChangeStepAfterInitFromNoti]

    %% Auto Generate Attributes Flow
    AUTO_GEN --> CHECK_AUTO{isAutoGenerate?}
    CHECK_AUTO -->|Yes| GET_ATTR[getListBOAttributes]
    CHECK_AUTO -->|No| CLEAR_ATTR[Clear Attributes]
    GET_ATTR --> UPDATE_CONFIGS[updateTransactionConfigs]
    CLEAR_ATTR --> UPDATE_CONFIGS

    %% Handle Step Settings Flow
    HANDLE_STEP --> CHECK_NAV{navigatedStep < activeStep<br/>or isVersionHistory?}
    CHECK_NAV -->|Yes| UPDATE_STEP[updateActiveStep]
    CHECK_NAV -->|No| VALIDATE[validateModelSettings]
    VALIDATE --> CHECK_ERRORS{Has errors?}
    CHECK_ERRORS -->|Yes| STAY_STEP[Stay on current step]
    CHECK_ERRORS -->|No| CHECK_PREPARE{Need prepare data?}
    CHECK_PREPARE -->|Yes| PREPARE_FLOW[prepareData Flow]
    CHECK_PREPARE -->|No| UPDATE_STEP
    
    PREPARE_FLOW --> RACE_MODAL[Race with Modal Progress]
    RACE_MODAL --> PREP_DATA[MainActions.prepareData]
    PREP_DATA --> RACE_RESULT{Prepare Result}
    RACE_RESULT -->|Success| SET_VIEW[setViewName]
    RACE_RESULT -->|Failure| ERROR_HANDLE[Error Handling]
    SET_VIEW --> TRAIN_MODEL_CALL[Call handleTrainModel]
    TRAIN_MODEL_CALL --> VALIDATE_MODEL[validateModelSettings]

    %% Handle Train Model Flow
    HANDLE_TRAIN --> CHECK_MODE{Training Mode}
    CHECK_MODE -->|Expert| EXPERT_FLOW[Expert Mode Flow]
    CHECK_MODE -->|AI| AI_FLOW[AI Mode Flow]
    
    AI_FLOW --> SCORE_PIE_TRAINING[Training Score and Pie]
    EXPERT_FLOW --> SCORE_PIE_TRAINING
    SCORE_PIE_TRAINING --> GET_RANGES[getRangesForScoreAndPie]
    GET_RANGES --> ZOOM_SEGMENTS[getZoombarDefaultFromSegments]
    
    ZOOM_SEGMENTS --> BAR_TRAINING[Training Barchart]
    BAR_TRAINING --> GET_AGGREGATION[getBarchartAggregation]
    GET_AGGREGATION --> ZOOM_AGG[getZoombarDefaultFromAggregation]
    
    ZOOM_AGG --> TRANSACTION_TRAINING[Training Transaction Stages]
    TRANSACTION_TRAINING --> GET_TRANSACTION[getTransactionStages]
    GET_TRANSACTION --> CACHE_PREPARE[Cache Previous Prepare Data]

    %% API Calls and Data Processing
    GET_RANGES --> API_RANGES[PredictiveModelService<br/>LifecycleStages.getRanges]
    API_RANGES --> SERIALIZE_RANGES[serializeRangesAPI]
    SERIALIZE_RANGES --> UPDATE_SEGMENTS[updateSegment]

    GET_AGGREGATION --> API_AGG[PredictiveModelService<br/>LifecycleStages.getAggregation]
    API_AGG --> SERIALIZE_AGG[serializeAggregationAPI]
    SERIALIZE_AGG --> UPDATE_TRAINING[updateTraining]

    GET_TRANSACTION --> API_TRANS[PredictiveModelService<br/>LifecycleStages.getTransactionStage]
    API_TRANS --> SERIALIZE_TRANS[serializeTransactionStagesAPI]
    SERIALIZE_TRANS --> UPDATE_TRANS_DATA[updateTransactionStageData]

    %% Range Update Flow
    HANDLE_RANGE --> MARK_RETRAIN[Mark needToReTraining]
    MARK_RETRAIN --> EFFECTS_RANGE[handleEffectsOnRangeChange]
    EFFECTS_RANGE --> UPDATE_SEG_RANGE[updateSegmentsOnRangeChange]
    UPDATE_SEG_RANGE --> UPDATE_LOAD_RANGE[updateLoadingsOnRangeChange]

    %% Zoom Range Update Flow
    HANDLE_ZOOM --> MARK_ZOOM_RETRAIN[Mark Barchart needToReTraining]
    MARK_ZOOM_RETRAIN --> EFFECTS_ZOOM[handleEffectsOnRangeChange]

    %% Granularity Update Flow
    HANDLE_GRAN --> UPDATE_GRAN_VALUE[Update granularity value]
    UPDATE_GRAN_VALUE --> MARK_GRAN_RETRAIN[Mark contents needToReTraining]

    %% Training Mode Update Flow
    HANDLE_MODE --> CHECK_AI_MODE{mode === AI?}
    CHECK_AI_MODE -->|Yes| RESET_TRAINING[resetTrainingData]
    RESET_TRAINING --> MARK_CUSTOMER_RETRAIN[Mark CustomerStage needToReTraining]
    MARK_CUSTOMER_RETRAIN --> CALL_TRAIN[Call handleTrainModel]

    %% Custom Range Training Flow
    HANDLE_CUSTOM --> CHECK_SCORE_TYPE{scoreType}
    CHECK_SCORE_TYPE -->|Barchart| CUSTOM_BAR[getBarchartAggregationWithCustomRange]
    CHECK_SCORE_TYPE -->|ScoreAndPie| CUSTOM_PIE[getRangesForScoreAndPieWithCustomRange]
    CHECK_SCORE_TYPE -->|TransactionStage| CUSTOM_TRANS[getTransactionStagesWithCustomRange]

    %% Reset View Name Flow
    HANDLE_RESET --> RESET_DATA[resetTrainingData]

    %% Notification Init Flow
    HANDLE_NOTI --> UPDATE_TRAINING_INFO[updateTrainingInfos]
    UPDATE_TRAINING_INFO --> SET_TRAIN_STEP[updateActiveStep to trainModel]
    SET_TRAIN_STEP --> CALL_TRAIN_NOTI[Call handleTrainModel]

    %% Styling
    classDef triggerClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef sagaClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef apiClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dataClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef decisionClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class START,PREPARE_DATA,STEP_CHANGE,RESET,TRAIN,UPDATE_RANGE,UPDATE_ZOOM,UPDATE_GRAN,TRAIN_MODE,TRAIN_CUSTOM,INIT_NOTI triggerClass
    class AUTO_GEN,HANDLE_STEP,HANDLE_RESET,HANDLE_TRAIN,HANDLE_RANGE,HANDLE_ZOOM,HANDLE_GRAN,HANDLE_MODE,HANDLE_CUSTOM,HANDLE_NOTI sagaClass
    class API_RANGES,API_AGG,API_TRANS apiClass
    class SERIALIZE_RANGES,SERIALIZE_AGG,SERIALIZE_TRANS,UPDATE_SEGMENTS,UPDATE_TRAINING,UPDATE_TRANS_DATA dataClass
    class CHECK_AUTO,CHECK_NAV,CHECK_ERRORS,CHECK_PREPARE,RACE_RESULT,CHECK_MODE,CHECK_AI_MODE,CHECK_SCORE_TYPE decisionClass
```

## Key Components and Triggers

### 1. Main Saga Entry Points
- **[`updatePrepareData`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:86)** (transaction key) → [`autoGenerateAttributes`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:115)
- **[`changeStepSettings`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:90)** → [`handleChangeStepSettings`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:187)
- **[`resetViewName`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:92)** → [`handleResetViewName`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:1012)
- **[`trainModel`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:94)** → [`handleTrainModel`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:689)
- **[`updateRange`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:96)** → [`handleUpdateRange`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:849)
- **[`updateZoombarRange`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:98)** → [`handleUpdateZoombarRange`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:875)
- **[`updateGranularity`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:100)** → [`handleUpdateGranularity`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:988)
- **[`updateTrainingMode`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:102)** → [`handleUpdateTrainingMode`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:657)
- **[`trainModelWithCustomRange`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:105)** → [`handleTrainModelWithCustomRange`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:493)
- **[`initFromNotiDone`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:110)** → [`handleChangeStepAfterInitFromNoti`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:156)

### 2. Core Training Flow
The main training orchestrator [`handleTrainModel`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:689) coordinates three parallel training processes:

1. **Score and Pie Training** → [`getRangesForScoreAndPie`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:317)
2. **Barchart Training** → [`getBarchartAggregation`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:441)  
3. **Transaction Stages Training** → [`getTransactionStages`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:379)

### 3. API Integration Points
- **[`PredictiveModelService.LifecycleStages.getRanges`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:342)** - Gets scoring ranges
- **[`PredictiveModelService.LifecycleStages.getAggregation`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:455)** - Gets bar chart aggregation
- **[`PredictiveModelService.LifecycleStages.getTransactionStage`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:407)** - Gets transaction stage data

### 4. Training Modes
- **AI Mode**: Automatic training with system-generated ranges
- **Expert Mode**: Manual training with user-defined input ranges

### 5. Content Types
- **[`TRAINING_CONTENT.ScoreAndPie`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:331)** - Pie chart and scoring data
- **[`TRAINING_CONTENT.Barchart`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:444)** - Bar chart visualization  
- **[`TRAINING_CONTENT.TransactionStage`](app/modules/Dashboard/Profile/PredictiveModel/Create/models/LifecycleStages/saga.js:382)** - Transaction lifecycle stages