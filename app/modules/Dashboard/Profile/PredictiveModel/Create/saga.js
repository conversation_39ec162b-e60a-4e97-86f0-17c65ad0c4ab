/* eslint-disable no-param-reassign */
/* eslint-disable indent */
import { capitalize, cloneDeep, get, isEmpty, set } from 'lodash';
import {
  all,
  call,
  cancel,
  cancelled,
  delay,
  fork,
  put,
  race,
  select,
  spawn,
  take,
  takeLatest,
} from 'redux-saga/effects';
import group from 'utils/group';
import injectSaga from 'utils/injectSaga';
import SourceServices from '../../../../../services/EventSources';
import NotificationService from '../../../../../services/Notification';
import PredictiveModelService from '../../../../../services/PredictiveModel';
import history from '../../../../../utils/history';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { Actions, ScheduleUpdateActions, Types } from './actions';
import { Actions as ListingActions } from '../ModelListing/actions';
import {
  NOTI,
  REPEAT_BY_OPTIONS,
  TRANSLATE_MAP,
  checkIsNewVersion,
  genAttrCode,
  generateApplyOptionName,
  generateModelName,
  isValidProgressRange,
  reassignListApply,
  reassignListAttrApply,
} from './utils';
import { validateSettings } from './validate';

import { addNotification } from '../../../actions';

import { calculateBuildProgress } from '../../../../../utils/number';
import { QS_KEYS } from '../config';
import { TAB } from '../Main/config';
import {
  getListPredictiveAttributes,
  getListPredictiveSegment,
} from '../service';
import {
  MAP_PERCENT_BY_STATUS,
  MODULE_CONFIG,
  S_PREPARE_DATA_JOB,
} from './config';
import {
  selectAttrApplyOptions,
  selectDataUpdateAttributes,
  selectDataUpdateSegments,
  selectDesign,
  selectListExistedAttributes,
  selectListExistedSegments,
  selectListTriggerEvents,
  selectMain,
  selectModalConfirmSave,
  selectModalProgressPercent,
  selectModelId,
  selectModelName,
  selectModelSettings,
  selectModelType,
  selectSavedData,
  selectSegApplyOptions,
  selectTriggerEvent,
} from './selector';
import { RESTART_ON_REMOUNT } from '../../../../../utils/constants'; //
import { OPTION_TYPE } from './components/DataUpdate/utils';

const PATH = 'app/modules/Dashboard/Profile/PredictiveModel/Create/saga.js';

export const injectSagaByModelType = ({ modelType, saga: settingsSaga }) =>
  injectSaga({
    key: `${MODULE_CONFIG.key}-${modelType}`,
    saga: () => workerCreateSaga({ settingsSaga }),
    mode: RESTART_ON_REMOUNT,
  });

export default function* workerCreateSaga({ settingsSaga }) {
  yield fork(settingsSaga);

  yield takeLatest(Types.validateModelSettings, handleValidateModelSettings);

  yield takeLatest(Types.saveModel, handleSaveModel);

  yield takeLatest(Types.init, handleInit);

  yield takeLatest(Types.initFromNoti, handleInitFromNotificationInfo);

  yield takeLatest(Types.updateModelName, handleChangeModelName);

  yield takeLatest(Types.prepareData, handlePrepareData);
}

function* handleInit(action) {
  const { design, isVersionHistory, activeRow, modelType } =
    action.payload || {};

  let modelDetail = activeRow;

  try {
    yield all([
      call(handleGetTriggerEvents, {
        payload: {
          from: 'handleInit',
        },
      }),
      call(handleGetListAttributes, {
        payload: {
          modelDetail,
          from: 'handleInit',
        },
      }),
      call(handleGetListSegments, {
        payload: {
          modelDetail,
          from: 'handleInit',
        },
      }),
    ]);

    const search = new URLSearchParams(window.location.search);
    const notificationId = search.get('notification_id');
    const copyId = search.get('copyId');

    if (notificationId) {
      yield put(Actions.initFromNoti(notificationId));

      return;
    }

    const main = yield select(selectMain);

    if (design === 'update' && modelDetail) {
      yield put(
        Actions.initDone({
          main,
          activeRow: modelDetail,
          isVersionHistory,
        }),
      );

      return;
    }

    if (design === 'create' && copyId) {
      const res = yield call(PredictiveModelService.RFMModel.getDetail, {
        modelId: copyId,
      });

      modelDetail = res.data;
      modelDetail.modelName = search.get('name');

      yield put(Actions.initDone({ main, activeRow: modelDetail }));

      return;
    }

    if (design === 'create') {
      yield put(Actions.initDone({ main }));

      yield put(Actions.updateModelName(generateModelName(modelType)));

      return;
    }
  } catch (err) {
    yield put(Actions.initFail());

    addMessageToQueue({
      path: PATH,
      func: 'handleInit',
      data: err.stack,
    });

    // eslint-disable-next-line no-console
    console.log({ err });
  }
}

function* handleInitFromNotificationInfo(action) {
  const { notificationId } = action.payload;

  const design = yield select(selectDesign);
  const main = yield select(selectMain);

  try {
    const { data } = yield call(NotificationService.detail, {
      notificationId,
    });

    const activeRow = JSON.parse(get(data, 'info.properties', ''));
    const viewName = get(data, 'info.alert_id', '');

    if (!isEmpty(activeRow) && !isEmpty(viewName)) {
      yield put(
        Actions.initDone({
          main,
          activeRow,
          isInitFromNoti: true,
          design,
        }),
      );

      yield put(Actions.setViewName(viewName));
      yield put(Actions.initFromNotiDone());
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleInitFromNotificationInfo.name,
      data: err.stack,
    });
  }
}

export function* updateTrainingProgress(args) {
  const {
    increaseActionTypes = [
      {
        type: Types.pollingGetStatusPD.received,
        randomBetween: [1, 8],
        getRange: atcPayload =>
          MAP_PERCENT_BY_STATUS[atcPayload.status] || [0, 80],
      },
    ],
    doneActionTypes = [],
    defaultRandomBetween = [1, 10],
    autoIncreseAfter = 3000,
    doneInCancelled = false,
    maxRetries = 30,
  } = args || {};

  try {
    let continuePolling = true;
    let range = { min: 0, max: 0 };
    let retryCount = 0;

    while (continuePolling && retryCount < maxRetries) {
      let newPercentProgress;

      const curProgressInPercent = yield select(selectModalProgressPercent);

      const [isIncrease, isDone, timeOut] = yield race([
        take(({ type }) => increaseActionTypes.some(i => i.type === type)),
        take(({ type }) => doneActionTypes.some(t => t === type)),
        delay(autoIncreseAfter),
      ]);

      if (isIncrease) {
        const {
          getRange,
          randomBetween = defaultRandomBetween,
        } = increaseActionTypes.find(({ type }) => type === isIncrease.type);

        const newRange = getRange(isIncrease.payload);

        if (newRange && isValidProgressRange(newRange)) {
          range = newRange;
        }

        newPercentProgress = calculateBuildProgress({
          currentProgress: curProgressInPercent,
          min: range.min,
          max: range.max,
          randomBetween,
        });
      }

      if (isDone) {
        continuePolling = false;
        newPercentProgress = 100;
      }

      if (timeOut && curProgressInPercent < 100) {
        retryCount += 1;

        newPercentProgress = calculateBuildProgress({
          currentProgress: curProgressInPercent,
          min: range.min,
          max: range.max,
          randomBetween: defaultRandomBetween,
        });
      }

      newPercentProgress = Math.min(100, Math.max(0, newPercentProgress));

      yield put(
        Actions.updateModalProgress({
          progressInPercent: newPercentProgress,
        }),
      );
    }

    // Handle max retries reached
    if (retryCount >= maxRetries) {
      throw new Error('Progress update timeout');
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: updateTrainingProgress.name,
      data: err.stack,
    });
  } finally {
    if (yield cancelled() && doneInCancelled) {
      yield put(
        Actions.updateModalProgress({
          progressInPercent: 100,
        }),
      );
    }
  }
}

export function* executeModelTrainingWithProgress({
  sagaFunc,
  onDone = () => {},
  onCancel = () => {},
  onError,
  autoProgressOptions = {},
}) {
  let sagaPollingUpdateProgress;

  try {
    yield put(
      Actions.updateModalProgress({ isOpen: true, progressInPercent: 0 }),
    );

    sagaPollingUpdateProgress = yield fork(
      updateTrainingProgress,
      autoProgressOptions,
    );

    const [fnDone, fnCanceled] = yield race([
      call(sagaFunc),
      take(
        ({ type, payload }) =>
          type === Types.updateModalProgress &&
          get(payload, 'updated.isOpen') === false,
      ),
    ]);

    if (fnDone) {
      yield call(onDone, fnDone);
    }

    if (fnCanceled) {
      yield put(Actions.setViewName(null));

      if (onCancel) {
        yield call(onCancel);
      }
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'executeModelTrainingWithProgress',
      data: err.stack,
    });

    if (onError) {
      yield call(onError, err);
      return;
    }

    throw err;
  } finally {
    yield put(Actions.updateModalProgress({ isOpen: false }));

    if (sagaPollingUpdateProgress) {
      yield cancel(sagaPollingUpdateProgress);
    }
  }
}

export function* incrementProgressByRange({ min, max }) {
  try {
    const curProgressInPercent = yield select(selectModalProgressPercent);

    const newPercentProgress = calculateBuildProgress({
      currentProgress: curProgressInPercent,
      min,
      max,
      randomBetween: [0, 10],
    });

    yield put(
      Actions.updateModalProgress({
        progressInPercent: newPercentProgress,
      }),
    );
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'incrementProgressByRange',
      data: err.stack,
    });
  }
}

export function* handleValidateModelSettings(action) {
  const options = get(action, 'payload.options', {});

  const {
    updateAfterValidate = true,
    enableValidateWhenError = true,
    selectErrors = errors => errors,
    ...restOptions
  } = options;

  let errors = [];

  try {
    const settings = yield select(selectModelSettings);
    const modelType = yield select(selectModelType);

    const { errors: errorsValidate } = yield call(validateSettings, {
      settings,
      modelType,
      opts: restOptions,
    });

    errors.push(...errorsValidate);
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleValidateModelSettings.name,
      data: err.stack,
    });
  } finally {
    errors = selectErrors(errors);

    yield put(Actions.validateModelSettingsDone(errors));

    if (updateAfterValidate) {
      yield put(Actions.updateValidateSettings(errors));
    }

    if (isEmpty(errors)) {
      yield put(Actions.toggleEnableValidate(false));
    }

    if (!isEmpty(errors) && enableValidateWhenError) {
      yield put(Actions.toggleEnableValidate(true));
    }
  }

  return errors;
}

function* handleSaveModel(action) {
  const { serializeSettings } = action.payload;

  if (!serializeSettings) return;

  const modelId = yield select(selectModelId);
  const design = yield select(selectDesign);

  try {
    yield call(handleBeforeSave, {
      serializeSettings,
    });

    const modalConfirmSave = yield select(selectModalConfirmSave);

    // If the user cancels the modal, stop the saving process
    if (modalConfirmSave.isCancel) {
      return;
    }

    yield put(Actions.updateIsSaving(true));

    yield put(Actions.validateModelSettings());

    const { payload: validatePayload } = yield take(
      Types.validateModelSettingsDone,
    );

    const { errors } = validatePayload;

    if (!isEmpty(errors)) {
      yield put(Actions.updateValidateSettings(errors));
      yield put(Actions.toggleEnableValidate(true));

      return;
    }

    let res;

    const dataSave = yield select(
      selectSavedData({
        serializeSettings,
      }),
    );

    const isSchedule =
      dataSave.computeSchedule.repeatType !== REPEAT_BY_OPTIONS.none.value;

    dataSave.isBuild = !isSchedule ? true : modalConfirmSave.isBuild;

    if (design === 'create') {
      dataSave.isNewVersion = true;

      res = yield call(PredictiveModelService.create, { data: dataSave });
    }

    if (design === 'update') {
      const { data: oldModel } = yield call(
        PredictiveModelService.RFMModel.getDetail,
        {
          modelId,
        },
      );

      dataSave.isNewVersion = checkIsNewVersion(oldModel, dataSave);

      res = yield call(PredictiveModelService.update, {
        modelId,
        data: dataSave,
      });
    }

    if (res.code === 200) {
      yield put(addNotification(NOTI.saveSuccess(design)));
      yield put(ListingActions.getListDataTable());

      yield call(handleGoToList);
    }

    if (
      res.code === 400 &&
      get(res, 'data.errors.modelName') === 'This model name already existed'
    ) {
      yield put(addNotification(NOTI.nameExist));
    }
  } catch (err) {
    yield put(
      addNotification({
        id: 'error',
        message: `${capitalize(design)} model fail!!!`,
        timeout: 1500,
        timestamp: new Date().getTime(),
        type: 'danger',
      }),
    );

    addMessageToQueue({
      path: PATH,
      func: handleSaveModel.name,
      data: err.stack,
    });
  } finally {
    yield put(Actions.updateIsSaving(false));
  }
}

function* handleGoToList() {
  yield put(ListingActions.refreshListDataTable());

  const newSearchParams = new URLSearchParams();

  newSearchParams.set(QS_KEYS.mainTab, TAB.LISTING);

  history.replace({ search: newSearchParams.toString() });
}

function* handleChangeModelName(action) {
  try {
    const modelType = yield select(selectModelType);
    const segments = yield select(selectDataUpdateSegments);
    const attributes = yield select(selectDataUpdateAttributes);
    const segmentOptions = yield select(selectSegApplyOptions);
    const attributeOptions = yield select(selectAttrApplyOptions);

    const { name } = action.payload;

    const mappingNewName = options => item => {
      const temp = cloneDeep(item);

      const { option, value } = temp;

      const needToUpdateName =
        option === OPTION_TYPE.NEW && value?.isReNamed !== true;

      if (!needToUpdateName) return temp;

      const createOption = options.find(opt => opt.id === temp.id);

      if (!createOption) return temp;

      return set(
        temp,
        'value.name',
        generateApplyOptionName({ modelType, modelName: name })(
          createOption.label,
        ),
      );
    };

    const mappingNewAttributeName = options => item => {
      const tempItem = mappingNewName(options)(item);

      tempItem.itemPropertyName = genAttrCode(tempItem.value.name);

      return tempItem;
    };

    if (segments) {
      yield put(
        ScheduleUpdateActions.updateApplyDataByField({
          field: 'segments',
          data: segments.map(mappingNewName(segmentOptions)),
        }),
      );
    }

    if (attributes) {
      yield put(
        ScheduleUpdateActions.updateApplyDataByField({
          field: 'attributes',
          data: attributes.map(mappingNewAttributeName(attributeOptions)),
        }),
      );
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleChangeModelName',
      data: err.stack,
    });
  }
}

export function* handlePrepareData(action) {
  const { prepareDataInfo, backupData } = action.payload;

  let sagaPollingGetStatusPD;

  try {
    const modelType = yield select(selectModelType);

    yield put(
      Actions.updateModalProgress({
        isOpen: true,
        options: {
          subTitle: TRANSLATE_MAP.progressPrepareDataSTitle,
        },
      }),
    );

    const { data } = yield call(
      PredictiveModelService.PrepareData.byModelType[modelType],
      {
        data: {
          ...prepareDataInfo,
          model_type: modelType,
          link: window.location.href,
          properties: backupData,
        },
      },
    );

    const viewName = get(data, 'view_name');

    if (!viewName) throw new Error('Get invalid viewName');

    sagaPollingGetStatusPD = yield fork(pollingGetStatusPrepareData, {
      viewName,
    });

    const predicateActionCloseModalProgress = anyAction => {
      return (
        anyAction.type === Types.updateModalProgress &&
        get(anyAction, 'payload.updated.isOpen') === false
      );
    };

    const { done, fail, closeModalProgress } = yield race({
      done: take(Types.pollingGetStatusPD.done),
      fail: take(Types.pollingGetStatusPD.fail),
      closeModalProgress: take(predicateActionCloseModalProgress),
    });

    if (closeModalProgress) {
      yield cancel(sagaPollingGetStatusPD);
    }

    if (fail) {
      yield put(Actions.prepareDataFail());
    }

    if (done) {
      yield put(Actions.prepareDataSuccess(viewName));
    }
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handlePrepareData',
      data: err.stack,
    });

    yield put(Actions.prepareDataFail());
  }
}

function* pollingGetStatusPrepareData({ viewName }) {
  try {
    const modelType = yield select(selectModelType);

    let continuePolling = true;
    let status;

    while (continuePolling) {
      const { data } = yield call(
        PredictiveModelService.PrepareData.checkPrepareStatus,
        {
          modelType,
          viewName,
        },
      );

      status = get(data, 'process_status');

      yield put(Actions.receivedPPDataStatus(status));

      switch (status) {
        case S_PREPARE_DATA_JOB.DONE:
          continuePolling = false;

          yield put({
            type: Types.pollingGetStatusPD.done,
          });
          break;
        case S_PREPARE_DATA_JOB.FAILED:
          continuePolling = false;

          yield put({
            type: Types.pollingGetStatusPD.fail,
          });
          break;
        default:
          break;
      }

      yield delay(2000);
    }
  } catch (err) {
    yield put({
      type: Types.pollingGetStatusPD.fail,
    });

    addMessageToQueue({
      path: PATH,
      func: pollingGetStatusPrepareData.name,
      data: err.stack,
    });
  }
}

export function* handleGetTriggerEvents(action) {
  const { eventTrackingNames = [], from } = action?.payload || {};

  if (isEmpty(eventTrackingNames)) {
    const triggerEvent = yield select(selectTriggerEvent);

    triggerEvent.forEach(e => {
      eventTrackingNames.push(e.eventTrackingName);
    });
  }

  if (isEmpty(eventTrackingNames)) return;

  try {
    let { data = [] } = yield call(
      SourceServices.event.data.getByEventTrackingName,
      { data: { eventTrackingNames } },
    );

    data = data.map(i => ({
      ...i,
      insightPropertyType: +i.insightPropertyType,
      status: +i.status,
    }));

    yield put(Actions.updatelistTriggerEvents(data));

    yield spawn(function* reAssignTriggerEvents() {
      let needHandler = true;

      if (from === 'handleInit') {
        const [done] = yield race([take(Types.initDone), take(Types.initFail)]);

        needHandler = !!done;
      }

      if (needHandler) {
        const listTriggerEvents = yield select(selectListTriggerEvents);
        const defaultTriggerEvents = yield select(selectTriggerEvent);

        const groupListTriggerEvent = group(
          listTriggerEvents,
          i => i.eventTrackingName,
        );

        const triggerEvents = [];

        defaultTriggerEvents.forEach(triggerEvent => {
          const { eventTrackingName } = triggerEvent;

          const eventInfo = groupListTriggerEvent
            ?.get(eventTrackingName)
            ?.at(0);

          if (eventInfo) {
            triggerEvents.push({
              ...eventInfo,
              enabled: triggerEvent.enabled || false,
            });
          }
        });

        yield put(
          ScheduleUpdateActions.updateTriggerEvent({
            data: triggerEvents,
          }),
        );
      }
    });
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: handleGetTriggerEvents.name,
      data: err,
    });
  }
}

export function* handleGetListSegments(action) {
  try {
    const { modelDetail, from } = action.payload || {};

    const segments = yield call(
      getListPredictiveSegment,
      +modelDetail?.modelId || null,
    );

    yield put(Actions.updateListExistedSegments(segments));

    yield spawn(function* reAssignListApplySegments() {
      let needHandler = true;

      if (from === 'handleInit') {
        const [done] = yield race([take(Types.initDone), take(Types.initFail)]);

        needHandler = !!done;
      }

      if (needHandler) {
        const modelName = yield select(selectModelName);
        const existedSegments = yield select(selectListExistedSegments);
        const applySegments = yield select(selectDataUpdateSegments);
        const allSegmentOptions = yield select(selectSegApplyOptions);

        yield put(
          ScheduleUpdateActions.updateApplyDataByField({
            field: 'segments',
            data: reassignListApply({
              listExistedObjs: existedSegments,
              listApplyObjs: applySegments,
              allOptions: allSegmentOptions,
              opts: {
                modelName,
              },
            }),
          }),
        );
      }
    });
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleGetListSegments',
      data: err,
    });
  }
}

export function* handleGetListAttributes(action) {
  try {
    const { modelDetail, from } = action.payload || {};

    if (modelDetail?.modelId) {
      const attributes = yield call(
        getListPredictiveAttributes,
        +modelDetail.modelId,
      );

      yield put(Actions.updateListExistedAttributes(attributes));
    }

    yield spawn(function* reAssignListApplyAttributes() {
      let needHandler = true;

      if (from === 'handleInit') {
        const [done] = yield race([take(Types.initDone), take(Types.initFail)]);

        needHandler = !!done;
      }

      if (needHandler) {
        const modelName = yield select(selectModelName);
        const existedAttributes = yield select(selectListExistedAttributes);
        const applyAttributes = yield select(selectDataUpdateAttributes);
        const allAttributeOptions = yield select(selectAttrApplyOptions);

        yield put(
          ScheduleUpdateActions.updateApplyDataByField({
            field: 'attributes',
            data: reassignListAttrApply({
              listExistedAttrs: existedAttributes,
              listApplyAttrs: applyAttributes,
              allOptions: allAttributeOptions,
              opts: {
                modelName,
              },
            }),
          }),
        );
      }
    });
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleGetListAttributes',
      data: err,
    });
  }
}

function* handleBeforeSave(payload) {
  const { serializeSettings } = payload;
  const design = yield select(selectDesign);

  try {
    const dataSave = yield select(
      selectSavedData({
        serializeSettings,
      }),
    );

    const isSchedule =
      dataSave.computeSchedule.repeatType !== REPEAT_BY_OPTIONS.none.value;

    if (isSchedule) {
      yield put(
        Actions.toggleModalConfirmSave({
          isOpen: true,
          mode: design,
          isCancel: false,
        }),
      );

      yield race([
        take(Types.saveAndBuildSchedule),
        take(Types.saveAndBuildNow),
        take(Types.cancelModalConfirmSave),
      ]);
    }

    return;
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'handleBeforeSave',
      data: err,
    });
  }
}
