export const MODE = {
  AI: 'ai',
  Expert: 'expert',
};

export const MODULE_CONFIG = {
  key: 'profile-predictive-model-create',
};

export const STEP_SETTING_KEYS = {
  prepareData: 0,
  trainModel: 1,
  applyModel: 2,
};

export const STEP_SETTING_KEYS_VERSION_HISTORY = {
  prepareDataTrainModel: 0,
  applyModel: 1,
};

export const STEP_LABEL_VERSION_HISTORY = {
  [STEP_SETTING_KEYS_VERSION_HISTORY.prepareDataTrainModel]:
    'Prepare Data & Train model',
  [STEP_SETTING_KEYS_VERSION_HISTORY.applyModel]: 'Apply Model',
};

export const STEP_LABEL = {
  [STEP_SETTING_KEYS.prepareData]: 'Prepare Data',
  [STEP_SETTING_KEYS.trainModel]: 'Train Model',
  [STEP_SETTING_KEYS.applyModel]: 'Apply Model',
};

export const STEP_LABEL_EDIT = {
  [STEP_SETTING_KEYS.prepareData]: 'Settings',
  [STEP_SETTING_KEYS.trainModel]: 'Train Model',
  [STEP_SETTING_KEYS.applyModel]: 'Apply Model',
};

export const S_PREPARE_DATA_JOB = {
  SUBMIT: 'SUBMIT',
  RUNNING: 'RUNNING',
  DONE: 'DONE',
  FAILED: 'FAILED',
};

export const MAP_PERCENT_BY_STATUS = {
  [S_PREPARE_DATA_JOB.SUBMIT]: {
    min: 0,
    max: 40,
  },
  [S_PREPARE_DATA_JOB.RUNNING]: {
    min: 40,
    max: 80,
  },
  [S_PREPARE_DATA_JOB.DONE]: {
    min: 80,
    max: 80,
  },
};

export const TRIGGER_EVENT = {
  AudienceMovement: 'audience_movement',
  PersonasMovement: 'personas_movement',
};

export const TRAINING_MODE = { ...MODE };

export const RESET_REASON = Object.freeze({
  UNMOUNT: 'unmount',
  MODEL_TYPE_CHANGED: 'model_type_changed',
});
