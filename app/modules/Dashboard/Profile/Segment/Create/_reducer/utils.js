/* eslint-disable no-plusplus */
import moment from 'moment';
import { MAP_SELECTION } from 'containers/Segment/Content/GeneralSettings/utils';
import {
  FORMAT_DATE_CALENDAR,
  getTimeRangeCalendar,
  getCurrentDate,
} from 'components/Organisms/CalendarTable/utils';
import {
  safeParseDisplayFormat,
  initNumberWithoutDecimal,
} from '../../../../../../utils/web/portalSetting';
import { safeParse } from '../../../../../../utils/common';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import {
  getStatusItemCode,
  getUntitledName,
} from '../../../../../../utils/web/properties';
import { defaultComputationSchedule } from '../../../../../../components/Organisms/ComputationSchedule/constant';
import { getNotificationDefault } from '../../../../../../components/Organisms/NotificationSetup/constant';

export const ACTION_LIMIT_TYPES = {
  TOTAL_LIMIT: 'TOTAL_LIMIT',
  COMPUTATIONS_LIMIT: 'COMPUTATIONS_LIMIT',
};

export function initValueComputeSchedule() {
  return {
    type: 'static',
    isLoading: false,
    ...defaultComputationSchedule,
  };
  // {
  //   "time_unit": "day" || "hour" ,
  //   "value": 1,
  //   "type":"static/dynamic"
  // }
}

export const toEventTrackingFE = (events, use = 'info', limitEvent = []) => {
  const validatedData = safeParse(events, []);
  const data = {
    list: [],
    map: {},
  };
  if (Array.isArray(validatedData) && validatedData.length === 0) {
    return data;
  }

  validatedData.forEach(event => {
    if (
      limitEvent.includes(event.eventTrackingCode) ||
      limitEvent.includes(Number(event.eventGroup)) ||
      limitEvent.length === 0
    ) {
      const temp = toEventTracking(event, use);
      // const dataType = 'number';
      // const temp = {
      //   eventTrackingName: event.eventTrackingName,
      //   eventTrackingCode: event.eventTrackingCode,
      //   label: event.translateLabel,
      //   // label: event.eventTrackingName,
      //   // value: {
      //   //   eventCategoryId: event.eventCategoryId,
      //   //   eventActionId: event.eventActionId,
      //   // },
      //   statusItemCode: getStatusItemCode(use, parseInt(event.status)),
      //   value: `${event.eventCategoryId}-${event.eventActionId}`,
      //   eventCategoryId: event.eventCategoryId,
      //   eventActionId: event.eventActionId,
      //   // value: `${event.eventCategoryId}-${event.eventActionId}`,
      //   // displayFormat: safeParseDisplayFormat(
      //   //   safeParse(event.displayFormat, null),
      //   //   { dataType },
      //   // ),
      //   displayFormat: initNumberWithoutDecimal(),
      //   dataType,
      // };
      data.list.push(temp);
      data.map[temp.value] = temp;
    }
  });
  return data;
};
export const toEventTracking = (event, use = 'info') => {
  const dataType = 'number';
  const temp = {
    eventTrackingName: event.eventTrackingName,
    eventTrackingCode: event.eventTrackingCode,
    label: event.translateLabel,
    // label: event.eventTrackingName,
    // value: {
    //   eventCategoryId: event.eventCategoryId,
    //   eventActionId: event.eventActionId,
    // },
    statusItemCode: getStatusItemCode(use, parseInt(event.status)),
    value: `${event.eventCategoryId}-${event.eventActionId}`,
    eventCategoryId: event.eventCategoryId,
    eventActionId: event.eventActionId,
    // value: `${event.eventCategoryId}-${event.eventActionId}`,
    // displayFormat: safeParseDisplayFormat(
    //   safeParse(event.displayFormat, null),
    //   { dataType },
    // ),
    displayFormat: initNumberWithoutDecimal(),
    dataType,
    available: event.available,
    ttl: parseInt(event.ttl),
  };
  return temp;
};
export const initialStateConfigure = () => ({
  //   activeTypeSegment: { label: 'User', value: '-1007' },
  isLoading: true,
  name: getUntitledName(
    getTranslateMessage(TRANSLATE_KEY._UNTITLED_SEGMENT, 'Untitled Segment'),
  ),
  initRuleId: null,
  description: '',
  computeSchedule: initValueComputeSchedule(),
  cacheComputeSchedule: {},
  notificationSetup: getNotificationDefault(),
  segmentMember: {},
  type: 1,
  errors: { name: [], limit_schedule: [] },
  dataSource: {
    list: [],
    map: {},
  },
  eventSchema: {
    list: [],
    map: {},
  },
  modalCalendarTable: {
    isLoading: false,
    isOpen: false,
    selectedTime: '',
    dataEvents: [],
    dataEventsGroupByDate: {},
    disableTimes: [],
    limitHour: 1,
    currentDate: getCurrentDate(),
    // time range for validate schedule
    timeRange: getTimeRangeCalendar(),
  },
  eventProperty: {},
  // computedProperties: {
  //   list: [],
  //   map: {},
  // },

  values: {
    dataSource: null,
  },
  validate: {
    total: 0,
    miss: 0,
  },
  notSaveRuleWhenUpdateCondition: false,
  accessInfo: { general: {}, listAccess: [] },
  viewInfoMapping: {},
  viewData: {},
  viewErrors: [],
  mapInfo: {
    dataSource: {},
    eventSchema: {},
    eventProperty: {},
    itemAttribute: {},
  },

  // eventSchema: {
  //   list: [],
  //   map: {},
  // },

  groupItemAttrs: {
    list: [],
    map: {},
  },

  dataSources: {},
  isOpenModal: false,
  isOpenModalReset: false,
  isOpenModalWarningTotalLimit: false,
  isOpenModalComputationLimit: false,
  isValidTotalLimit: false,
  isValidTotalLimitTabShareAccess: false,
  messageWarningLimit: '',
  messageWarningComputationLimit: '',
  pagingRelationship: {
    page: 1,
    limit: 25,
  },
  searchRelationship: '',
  filterTypeRelationship: '',
  tmpComputeScheduleType: '',
  nextPage: '',
});

export const INIT_FORECAST_SS_DETAIL = {
  id: null,
  isShow: false,
  isSendingNotify: false,
  sessionName: '',
  itemTypeId: null,
  progressInPercent: 0,
  lastBuildTime: null,
};

export const initialStateMainCreateSegment = () => ({
  // action: 'create',
  design: 'create',
  modePreview: 'sample',
  // modePreview: 'table',
  itemTypeId: 0,
  isLoading: true,
  isDoing: false,
  activeRow: {},
  disabled: true,
  isJourneySuccess: false,
  previewDisabled: true,
  isCollapse: false,
  disabledDynamicSchedule: false,
  forecastSessionDetail: INIT_FORECAST_SS_DETAIL,
});

export const isValidName = name => {
  let countValidCharacter = 0;
  for (let i = 0; i < name.length; i++) {
    if (name[i] !== ' ') {
      countValidCharacter += 1;
    }
  }
  return countValidCharacter > 0;
};

export const labelCantBlank = getTranslateMessage(
  TRANSLATE_KEY._NOTI_EMPTY_NAME,
  'Name can’t be blank',
);

export const labelInvalidName = getTranslateMessage(
  TRANSLATE_KEY._NOTI_INVALID_NAME,
  'Invailid name',
);

export const labelMaxLength = getTranslateMessage(
  TRANSLATE_KEY._INVALID_NAME_MAX_LENGTH,
  'Valid name is no more than 255 characters',
);
