/**
 * Dashboard selectors
 */

import { createSelector } from 'reselect';

// import { createSelector } from 'reselect';
import { initialState } from './reducer';
import { getInitialPersonalizations } from '../../components/common/UIEditorPersonalization/WrapperPersonalization/utils';

const selectDashboard = state => state.get('dashboard');
const makeSelectPortal = state => state.get('dashboard').userInfo.portal;
const makeSelectUser = state => state.get('dashboard').userInfo.user;
const makeSelectUserInfo = state => state.get('dashboard').userInfo;
const makeSelectRoles = state => state.get('dashboard').roles;

// const makeSelectDashboardLoading = state => state.get('dashboard').isLoading;

const selectRouter = state => state.get('router');

const selectLocation = createSelector(
  selectRouter,
  router => router.get('location'),
);

const selectPathname = createSelector(
  selectLocation,
  location => location.get('pathname'),
);

const selectSearch = createSelector(
  selectLocation,
  location => new URLSearchParams(location.get('search')),
);

const makeSelectAccountsDashboard = () => {
  return createSelector(
    selectDashboard,
    substate => substate.accounts || {},
  );
};

export {
  selectDashboard,
  makeSelectUserInfo,
  makeSelectUser,
  makeSelectPortal,
  makeSelectAccountsDashboard,
  makeSelectRoles,
  // makeSelectDashboardLoading,
  selectRouter,
  selectLocation,
  selectPathname,
  selectSearch,
};

/**
 * Direct selector to the Story state domain
 */

const selectDomain = state => state.get('dashboard') || initialState;

/**
 * Other specific selectors
 */

/**
 * Default selector used by Story
 */

const makeSelectDashboard = () =>
  createSelector(
    selectDomain,
    substate => substate,
  );

const makeSelectDashboardLoading = () =>
  createSelector(
    selectDomain,
    substate => substate.isLoading,
  );

const makeSelectDashboardDataEncrypt = () =>
  createSelector(
    selectDomain,
    substate => substate.dataEncrypt,
  );

const makeSelectDashboardModalSelectAccount = () =>
  createSelector(
    selectDomain,
    substate => substate.modalSelectAccount,
  );

const makeSelectDashboardUserInfoPortal = () =>
  createSelector(
    selectDomain,
    substate => substate.userInfo.portal,
  );

const makeSelectDashboardUserInfoUser = () =>
  createSelector(
    selectDomain,
    substate => substate.userInfo.user,
  );

const makeSelectDashboardPortalId = () =>
  createSelector(
    selectDomain,
    substate => substate.userInfo.portal.portalId,
  );

const makeSelectDashboardAvailabelMenus = () =>
  createSelector(
    selectDomain,
    substate => substate.availabelMenus,
  );

const makeSelectDashboardNetworkInfo = () =>
  createSelector(
    selectDomain,
    substate => substate.networkInfo,
  );

const makeSelectDashboardAccounts = () =>
  createSelector(
    selectDomain,
    substate => substate.accounts,
  );

const makeSelectDashboardOwnerId = () =>
  createSelector(
    selectDomain,
    substate => substate.ownerId,
  );

const makeSelectDashboardAccessUserId = () =>
  createSelector(
    selectDomain,
    substate => substate.shareAccessUserId,
  );

const makeSelectDashboardMenuCodeActive = () =>
  createSelector(
    selectDomain,
    substate => substate.menuCodeActive,
  );

const makeSelectDashboardUpdateHeaderKey = () =>
  createSelector(
    selectDomain,
    substate => substate.updateHeaderKey,
  );

const makeSelectTmpOwnerId = () =>
  createSelector(
    selectDomain,
    substate => substate.tmpOwnerId,
  );

const makeSelectPersonalizations = () => {
  return createSelector(
    selectDashboard,
    substate => substate.personalizations || getInitialPersonalizations(),
  );
};

const makeSelectSettingPersonalizations = () => {
  return createSelector(
    makeSelectPersonalizations(),
    substate => substate.settings,
  );
};

export {
  makeSelectDashboardLoading,
  makeSelectDashboardDataEncrypt,
  makeSelectDashboardPortalId,
  makeSelectDashboardAvailabelMenus,
  makeSelectDashboardNetworkInfo,
  makeSelectDashboardUserInfoPortal,
  makeSelectDashboardUserInfoUser,
  makeSelectDashboardAccounts,
  makeSelectDashboardOwnerId,
  makeSelectDashboardMenuCodeActive,
  makeSelectDashboardAccessUserId,
  makeSelectDashboardModalSelectAccount,
  makeSelectDashboardUpdateHeaderKey,
  makeSelectDashboard,
  makeSelectTmpOwnerId,
  makeSelectPersonalizations,
  makeSelectSettingPersonalizations,
};
