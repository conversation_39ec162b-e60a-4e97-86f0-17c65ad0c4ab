/* eslint-disable indent */
/* eslint-disable camelcase */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
/* eslint-disable no-undef */
import produce from 'immer';
import { fromJS } from 'immutable';
import _ from 'lodash';
import Types from './constants';
import ReduxTypes from '../../redux/constants';
import {
  generateKey,
  mapAtributes,
  safeParse,
  setSessionStorage,
} from '../../utils/common';
import { addMessageToQueue } from '../../utils/web/queue';
import { setActiveMenu } from './utils';
import {
  APP_ACTION,
  APP_ROLE_SCOPE,
  checkingRoleScope,
  ALLOW_OWNER_MENUS
} from '../../utils/web/permission';
import {
  getAppSession,
  getCurrentAccessUserId,
  getLastOwnerId,
  setCurrentLastOwnerName,
  setCurrentOwnerId,
  setCurrentOwnerName,
  setLastOwnerId,
} from '../../utils/web/cookie';
import {
  buildMergeTag,
  getInitialPersonalizations,
  isCommonGroupPersonalize,
} from '../../components/common/UIEditorPersonalization/WrapperPersonalization/utils';
import { actionPersonalKeys } from '../../components/common/UIEditorPersonalization/WrapperPersonalization/libs/action';
import {
  COMMON_CODE,
  CUSTOMER_CODE,
  PROMOTION_CODE,
  VISITOR_CODE,
} from '../../components/common/UIEditorPersonalization/WrapperPersonalization/constants';

export const defaultLabelsSelectAccount = {
  title: '',
  okText: '',
};

export const defaultOptionsSelectAccount = {
  isHideSubTitle: false,
  labels: defaultLabelsSelectAccount,
};

export const initialState = {
  isLoading: true,
  config: {
    screen: '',
  },
  userInfo: {
    cookie: {
      portalId: '',
      token: '',
    },
    portal: {
      portalId: '',
      portalName: '',
      defaultConfig: {},
      config: {},
    },
    user: {
      userId: '',
      email: '',
      fullName: '',
      ctime: '',
      utime: '',
      scopes: [],
    },
  },

  shareAccessUserId: 0,
  ownerId: {
    initValue: 0, // value init by way get url and init for BreadCrumdOwner component
    value: 0, // value save when user change BreadCrumbOwner component. Current use write and read session storate, not using this param
  },
  accounts: {
    list: [],
    map: {},
  },

  menuCodeActive: '', // menu code active for validate breadcrumb and account sharing component

  portals: [],
  mapPortals: {},

  attributes: {},
  toasts: [],
  notifications: [
    // {
    //   id: 'handleFetchListData-destination-error',
    //   message: 'Fetch list destination error!',
    //   timeout: 500000,
    //   type: 'info',
    //   type: 'warning',
    // },
  ],
  dataEncrypt: {
    isOpenModal: false,
    cacheData: null,
  },

  // personalizations
  personalizations: getInitialPersonalizations(),

  // abstract using ModalSelectAccount when AddComponent
  modalSelectAccount: {
    type: '',
    isOpenModal: false,
    cacheData: null, // data cache khi toggle
    cacheDataConfirm: null, // data cache khi confirm
    preSelectUserId: null, // Using to pre-select user when open modal
    options: defaultOptionsSelectAccount,
  },
  /*
    ThanhNT
    2021-11-01
    using availableMenus check both permission + left-menu fail, because data contains menu show_hide === 0
    show_hide === 0: has permission but left-menu will not show
  */
  availabelMenus: new Set([]), // set menu code = module code validate hiển thị left menu
  availabelModule: new Set([]), // set module code = menu code validate module permission
  networkInfo: {
    logo: PORTAL_CONFIG.LOGO_HEADER,
    subLogo: '',
    favicon: '',
  },
  // key for trigger when header change layout need get element width to update absolute position
  // app/components/Organisms/CustomHeader/index.jsx
  updateHeaderKey: '',

  // key for trigger when journey detail change
  tmpOwnerId: NaN,
};

function dashboardReducer(state = initialState, action) {
  // console.log('dashboardReducer', action.type);
  return produce(state, draft => {
    switch (action.type) {
      case `@@DASHBOARD@@LOADING@@${ReduxTypes.UPDATE_VALUE}`: {
        // console.log('update is loading', action.payload);
        draft.isLoading = action.payload;
        return;
      }
      case Types.USER_VERIFY_SUCCESS: {
        const userInfoTempt = state.userInfo;
        const { data, portal } = action.output;
        userInfoTempt.portal = portal;

        try {
          setSessionStorage('api_pid', userInfoTempt.portal.portalId);
        } catch (err) {
          addMessageToQueue({
            path: 'app/modules/Dashboard/reducer.js',
            func: 'dashboardReducer',
            data: err.stack,
          });
          console.log(err);
        }

        userInfoTempt.user = data.user;
        draft.userInfo = userInfoTempt;
        // draft.isLoading = false;
        return;
      }
      case Types.FETCH_PORTALS_SUCCESS: {
        const { payload } = action;
        draft.portals = payload.mapPortal.list;
        draft.mapPortals = payload.mapPortal.map;
        return;
      }
      case Types.APP_INIT_SUCCESS: {
        const { userInfo } = state;
        userInfo.cookie = action.payload;
        draft.userInfo = userInfo;

        return;
      }
      case Types.FETCH_USER_ATTRIBUTES_DONE: {
        const attributes = mapAtributes(action.output.attributes);
        draft.attributes = attributes;
        return;
      }
      case Types.ADD_TOAST: {
        const { toasts } = state;
        const newToasts = [action.input, ...toasts];
        // newToasts.push(action.input);
        draft.toasts = newToasts;
        return;
      }
      case Types.REMOVE_TOAST: {
        const toasts = draft.toasts.filter(el => el.id !== action.id);
        draft.toasts = toasts;
        return;
      }
      case Types.ADD_NOTIFICATION: {
        const timestamp = new Date().getTime();
        const tmp = { ...action.input, id: timestamp, timestamp };
        const newNotifications = [tmp];
        draft.notifications = newNotifications;
        return;
      }
      case Types.REMOVE_NOTIFICATION: {
        const notifications = draft.notifications.filter(
          el => el.id !== action.id,
        );
        draft.notifications = notifications;
        return;
      }
      case Types.FETCH_USER_ROLES_DONE: {
        draft.roles = fromJS(action.payload);
        return;
      }
      // case Types.UPDATE_DECRYPT_DATA:
      case Types.CB_UPDATE_DECRYPT_DATA: {
        // Call back UPDATE_DECRYPT_DATA
        const data = action.payload;
        if (data.type === 'DECRYPT_DATA') {
          if (data.isOpenModal !== undefined) {
            draft.dataEncrypt.isOpenModal = data.isOpenModal;
          }
          if (data.cacheData !== undefined) {
            draft.dataEncrypt.cacheData = data.cacheData;
          }
          draft.dataEncrypt.actionType = data.actionType || '';
        }

        return;
      }

      case `@@DASHBOARD_MODAL_SELECT_ACCOUNT@@${ReduxTypes.UPDATE_VALUE}`: {
        const data = action.payload || {};
        const { type } = data;
        draft.modalSelectAccount.type = type;
        if (type === 'TOGGLE') {
          draft.modalSelectAccount.isOpenModal = data.isOpenModal;
          draft.modalSelectAccount.cacheData = data.cacheData;
        }
        if (type === 'CONFIRM') {
          draft.modalSelectAccount.isOpenModal = false;
          draft.modalSelectAccount.cacheDataConfirm = data.cacheDataConfirm;
        }
        return;
      }

      case `@@DASHBOARD_PRE_SELECT_USER_ACCOUNT_MODAL${
        ReduxTypes.UPDATE_VALUE
      }`: {
        const newUserId = action.payload;

        if (+state.modalSelectAccount.preSelectUserId !== +newUserId) {
          const id = !_.isNull(newUserId) ? +newUserId : null;
          draft.modalSelectAccount.preSelectUserId = id;
        }

        return;
      }

      case `@@DASHBOARD_MODAL_CACHE_CONFIRM_SELECT_ACCOUNT@@${
        ReduxTypes.UPDATE_VALUE
      }`: {
        const cacheDataConfirm = action.payload || {};

        if (cacheDataConfirm) {
          draft.modalSelectAccount.cacheDataConfirm = cacheDataConfirm;
        }
        return;
      }

      case `@@DASHBOARD_MODAL_SELECT_ACCOUNT_OPTIONS${
        ReduxTypes.UPDATE_VALUE
      }`: {
        const { options = {} } = action.payload || {};
        draft.modalSelectAccount.options = {
          ...state.modalSelectAccount.options,
          ...options,
        };
        return;
      }

      case `@@DASHBOARD_AVAILABEL_MENUS@@${ReduxTypes.UPDATE_VALUE}`: {
        draft.availabelMenus = action.payload;
        return;
      }
      case `@@DASHBOARD@@NETWORK_INFO@@${ReduxTypes.UPDATE_VALUE}`: {
        draft.networkInfo = action.payload;
        return;
      }

      case `@@DASHBOARD_ACCOUNT_SHARE_ACCESS_ID@@${ReduxTypes.UPDATE_VALUE}`: {
        const user = safeParse(action.payload, {});
        const userId = Number(user.user_id);
        draft.shareAccessUserId = userId;
        return;
      }

      case `@@DASHBOARD_OWNER_BREADCRUMB@@${ReduxTypes.UPDATE_VALUE}`: {
        const user = safeParse(action.payload, {});
        const owner_id = Number(user.user_id);
        console.log({ action });
        draft.ownerId.value = owner_id;
        draft.ownerId.initValue = owner_id;
        return;
      }

      case `@@DASHBOARD_INIT_OWNER_BREADCRUMB@@${ReduxTypes.UPDATE_VALUE}`: {
        const user = safeParse(action.payload, {});
        const owner_id = Number(user.user_id);
        draft.ownerId.value = owner_id;
        draft.ownerId.initValue = owner_id;
        return;
      }

      case `@@DASHBOARD_SET_OWNER_ID_FROM_DATA@@${ReduxTypes.UPDATE_VALUE}`: {
        const user_id = safeParse(action.payload, '');
        // console.log('DASHBOARD_SET_OWNER_ID_FROM_DATA', user_id);
        const owner_id = Number(user_id);
        const onwner_name = draft.accounts.map[owner_id]
          ? draft.accounts.map[owner_id].full_name
          : 'all';
        draft.ownerId.value = owner_id;
        draft.ownerId.initValue = owner_id;
        setCurrentOwnerId(Number.isNaN(owner_id) ? 'all' : owner_id);
        setLastOwnerId(owner_id);
        setCurrentOwnerName(onwner_name);
        setCurrentLastOwnerName(onwner_name);
        return;
      }

      case `@@DASHBOARD_ACTIVE_MENU_CODE@@${ReduxTypes.UPDATE_VALUE}`: {
        const menuCodeActive = action.payload;
        draft.menuCodeActive = action.payload;
        const hasRoleViewEverything = checkingRoleScope(
          menuCodeActive,
          APP_ACTION.VIEW,
          APP_ROLE_SCOPE.EVERYTHING,
        );
        const allowOwner = ALLOW_OWNER_MENUS.includes(menuCodeActive);

        if (hasRoleViewEverything && allowOwner) {
          setCurrentOwnerId(getLastOwnerId());
          setCurrentOwnerName(getAppSession('last_owner_name'));
        } else {
          setCurrentOwnerId(getCurrentAccessUserId());
          setCurrentOwnerName('all');
        }
        setActiveMenu(action.payload);
        return;
      }

      case `@@DASHBOARD_ACCOUNTS@@${ReduxTypes.GET_LIST_DONE}`: {
        draft.accounts = action.payload;
        return;
      }

      case `@@UPDATE_LAYOUT_HEADER_RENDER@@${ReduxTypes.UPDATE_VALUE}`: {
        draft.updateHeaderKey = generateKey();
        return;
      }

      case `@@DASHBOARD_UPDATE_TMP_OWNER_ID@@${ReduxTypes.UPDATE_VALUE}`: {
        draft.tmpOwnerId = action.payload;
        return;
      }

      case actionPersonalKeys.INIT_DONE_PERSONALIZATIONS: {
        draft.personalizations.isInitDone = true;
        return;
      }

      case actionPersonalKeys.STT_PERSONALIZATIONS: {
        const newSTT = action.payload;

        if (_.isBoolean(newSTT)) {
          draft.personalizations.isLoading = newSTT;
        }

        return;
      }

      case actionPersonalKeys.UPDATE_SETTING_PERSONALIZATION: {
        const {
          metadata = {},
          entries: newSettingPersonalizations,
        } = action.payload;

        if (
          _.has(newSettingPersonalizations, 'personalizationData') &&
          _.has(newSettingPersonalizations, 'personalizationType')
        ) {
          const {
            personalizationData = {},
            personalizationType = {},
          } = state.personalizations.settings;

          const personalizeDataEntries = Object.entries(
            newSettingPersonalizations.personalizationData || {},
          );

          const mergeTags = [];

          personalizeDataEntries.forEach(([key, value]) => {
            let retrieveCode = key;

            const isCommonGroup = isCommonGroupPersonalize(key);
            if (isCommonGroup) {
              retrieveCode = COMMON_CODE;
            }

            const isForceUpdateNew = _.get(
              metadata,
              [retrieveCode, 'isForceUpdateNew'],
              false,
            );

            if (
              isForceUpdateNew ||
              !_.has(personalizationData, key) ||
              _.isEmpty(personalizationData[key]?.map)
            ) {
              draft.personalizations.settings.personalizationData[key] = value;
            }

            if ([VISITOR_CODE, CUSTOMER_CODE, PROMOTION_CODE].includes(key)) {
              const tmp = buildMergeTag(
                key,
                newSettingPersonalizations.personalizationData[key].list,
              );
              mergeTags.push(tmp);
            }
          });

          draft.personalizations.settings.personalizationType.map = {
            ...personalizationType.map,
            ...(newSettingPersonalizations?.personalizationType?.map || {}),
          };
          draft.personalizations.settings.personalizationType.list = _.unionBy(
            personalizationType.list,
            newSettingPersonalizations?.personalizationType?.list || [],
            'value',
          );

          draft.personalizations.settings.mergeTags = _.unionBy(
            state.personalizations.settings.mergeTags,
            mergeTags,
            'name',
          );
        }

        /** Update personalizations data error */
        if (_.has(newSettingPersonalizations, 'personalizationDataError')) {
          draft.personalizations.settings.personalizationDataError = {
            ...(state.personalizations.settings.personalizationDataError || {}),
            ...newSettingPersonalizations.personalizationDataError,
          };
        }

        return;
      }

      case actionPersonalKeys.UPDATE_PROMOTION_CODE_ATTR: {
        const newPromotionCodeAttr = action.payload;

        if (_.isObject(newPromotionCodeAttr)) {
          draft.personalizations.settings.promotionCodeAttr = newPromotionCodeAttr;
        }
        return;
      }

      case actionPersonalKeys.STT_CONTENT_SOURCE: {
        const newSTT = action.payload;

        if (_.isBoolean(newSTT)) {
          draft.personalizations.isLoadingContentSources = newSTT;
        }

        return;
      }

      case actionPersonalKeys.UPDATE_CONTENT_SOURCE: {
        const {
          personalizeMap = {},
          contentSources = [],
          personalizationData = {},
        } = action.payload;

        if (_.isObject(personalizeMap) && _.isObject(personalizationData)) {
          draft.personalizations.settings.personalizationType.list.push(
            ...Object.values(personalizeMap || {}),
          );
          draft.personalizations.settings.personalizationType.map = {
            ...state.personalizations.settings.personalizationType.map,
            ...personalizeMap,
          };
          draft.personalizations.settings.personalizationData = {
            ...state.personalizations.settings.personalizationData,
            ...personalizationData,
          };
          draft.personalizations.settings.contentSources = contentSources;
        }
        return;
      }

      case actionPersonalKeys.REMOVE_RELATED_CONTENT_SOURCE: {
        const contentSourceGroupIds = state.personalizations.settings.contentSources.map(
          cs => cs.groupId,
        );

        contentSourceGroupIds.forEach(groupId => {
          if (
            _.has(state.personalizations.settings.personalizationData, groupId)
          ) {
            draft.personalizations.settings.personalizationData = _.omit(
              draft.personalizations.settings.personalizationData,
              [groupId],
            );
            draft.personalizations.settings.personalizationType.list = draft.personalizations.settings.personalizationType.list.filter(
              item => item.value !== groupId,
            );
            draft.personalizations.settings.personalizationType.map = _.omit(
              draft.personalizations.settings.personalizationType.map,
              [groupId],
            );
          }
        });

        draft.personalizations.settings.contentSources = [];
        return;
      }

      default:
        return state;
    }
  });
}

export default dashboardReducer;
